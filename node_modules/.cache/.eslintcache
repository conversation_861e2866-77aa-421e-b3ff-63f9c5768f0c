[{"/var/www/html/gwm.tj/src/index.js": "1", "/var/www/html/gwm.tj/src/App.js": "2", "/var/www/html/gwm.tj/src/components/GoogleAnalytics.js": "3", "/var/www/html/gwm.tj/src/layout/Layout.jsx": "4", "/var/www/html/gwm.tj/src/components/ErrorBoundary/ErrorBoundary.jsx": "5", "/var/www/html/gwm.tj/src/pages/Home/Home.jsx": "6", "/var/www/html/gwm.tj/src/pages/Models/Models.jsx": "7", "/var/www/html/gwm.tj/src/pages/Owners/Owners.jsx": "8", "/var/www/html/gwm.tj/src/pages/Privacy/Privacy.jsx": "9", "/var/www/html/gwm.tj/src/pages/Offer/Offers.jsx": "10", "/var/www/html/gwm.tj/src/pages/Book-a-test-drive/Book-a-test-dieve.jsx": "11", "/var/www/html/gwm.tj/src/pages/NoPage/NoPage.jsx": "12", "/var/www/html/gwm.tj/src/pages/Offer/OfferPage/OfferPage.jsx": "13", "/var/www/html/gwm.tj/src/pages/Discover/News/NewsList.jsx": "14", "/var/www/html/gwm.tj/src/pages/Discover/News/NewsPage.jsx/NewsPage.jsx": "15", "/var/www/html/gwm.tj/src/pages/Discover/About/About.jsx": "16", "/var/www/html/gwm.tj/src/pages/Discover/About/History/History.jsx": "17", "/var/www/html/gwm.tj/src/pages/Discover/Contact/Contact.jsx": "18", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Warranty/Warranty.jsx": "19", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Assistance/Assistance.jsx": "20", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Service/Service.jsx": "21", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Parts/Parts.jsx": "22", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Accessories/Accessories.jsx": "23", "/var/www/html/gwm.tj/src/pages/Owners/Pages/ServicePlan/ServicePlan.jsx": "24", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Vehicle-reference-table/Table.jsx": "25", "/var/www/html/gwm.tj/src/pages/Owners/Pages/Care/Care.jsx": "26", "/var/www/html/gwm.tj/src/pages/Models/Pages/DynamicModelPage/DynamicModelPage.jsx": "27", "/var/www/html/gwm.tj/src/layout/Navbar/Navbar.jsx": "28", "/var/www/html/gwm.tj/src/layout/Footer/Footer.jsx": "29", "/var/www/html/gwm.tj/src/hooks/useSEO.js": "30", "/var/www/html/gwm.tj/src/data/seoData.js": "31", "/var/www/html/gwm.tj/src/components/SimpleCard/SimpleCard.jsx": "32", "/var/www/html/gwm.tj/src/components/ToolBar/ToolBar.jsx": "33", "/var/www/html/gwm.tj/src/components/FilterSlide/FilterSwiper.jsx": "34", "/var/www/html/gwm.tj/src/components/Notification/Notification.jsx": "35", "/var/www/html/gwm.tj/src/components/Form/Form.jsx": "36", "/var/www/html/gwm.tj/src/components/SkeletonCard/SkeletonCard.jsx": "37", "/var/www/html/gwm.tj/src/pages/Home/components/header-slider/HeroSliderGWM.jsx": "38", "/var/www/html/gwm.tj/src/pages/Home/components/Models/OurModels.jsx": "39", "/var/www/html/gwm.tj/src/pages/Home/components/Image-card/ImageCard.jsx": "40", "/var/www/html/gwm.tj/src/pages/Home/components/video-card/VideoCard.jsx": "41", "/var/www/html/gwm.tj/src/pages/Discover/Contact/ContactForm.jsx": "42", "/var/www/html/gwm.tj/src/data/modelLoader.js": "43", "/var/www/html/gwm.tj/src/pages/Owners/components/sidebar/Sidebar.jsx": "44", "/var/www/html/gwm.tj/src/pages/Owners/components/form/OwnersForm.jsx": "45", "/var/www/html/gwm.tj/src/pages/Owners/components/PartsList/PartsList.jsx": "46", "/var/www/html/gwm.tj/src/pages/Models/Pages/components/Specifications.jsx": "47", "/var/www/html/gwm.tj/src/pages/Models/Pages/components/Modal.jsx": "48", "/var/www/html/gwm.tj/src/data/models/tank-300.js": "49", "/var/www/html/gwm.tj/src/data/models/tank-500.js": "50", "/var/www/html/gwm.tj/src/asset/data/navbarData.js": "51", "/var/www/html/gwm.tj/src/asset/data/footerData.js": "52", "/var/www/html/gwm.tj/src/data/models/tank-700.js": "53", "/var/www/html/gwm.tj/src/data/models/haval-h6-hev.js": "54", "/var/www/html/gwm.tj/src/data/models/haval-h6.js": "55", "/var/www/html/gwm.tj/src/data/models/haval-jolion-new.js": "56", "/var/www/html/gwm.tj/src/data/models/haval-m6.js": "57", "/var/www/html/gwm.tj/src/data/models/gwm-commercial-poer.js": "58", "/var/www/html/gwm.tj/src/data/models/gwm-wingle-7.js": "59", "/var/www/html/gwm.tj/src/data/models/tank-700-phev.js": "60", "/var/www/html/gwm.tj/src/asset/data/ownersMenu.js": "61", "/var/www/html/gwm.tj/src/utils/validation.js": "62", "/var/www/html/gwm.tj/src/utils/api.js": "63", "/var/www/html/gwm.tj/src/pages/Home/components/header-slider/SkeletonSlide.jsx": "64", "/var/www/html/gwm.tj/src/layout/Navbar/components/models/NavModels.jsx": "65", "/var/www/html/gwm.tj/src/layout/Navbar/components/mobPrimaryBar/PrimaryBar.jsx": "66", "/var/www/html/gwm.tj/src/layout/Navbar/components/discover/NavDiscover.jsx": "67", "/var/www/html/gwm.tj/src/asset/data/accessoriesData.js": "68", "/var/www/html/gwm.tj/src/pages/Home/components/Models/FilterSlide/FilterSwiper.jsx": "69", "/var/www/html/gwm.tj/src/pages/Models/Pages/components/FilterSlide/FilterSwiper.jsx": "70"}, {"size": 660, "mtime": 1755081805000, "results": "71", "hashOfConfig": "72"}, {"size": 4465, "mtime": 1755220842000, "results": "73", "hashOfConfig": "72"}, {"size": 1280, "mtime": 1755078391000, "results": "74", "hashOfConfig": "72"}, {"size": 298, "mtime": 1755084494000, "results": "75", "hashOfConfig": "72"}, {"size": 5424, "mtime": 1753181481000, "results": "76", "hashOfConfig": "72"}, {"size": 1800, "mtime": 1753265042000, "results": "77", "hashOfConfig": "72"}, {"size": 9545, "mtime": 1755238823049, "results": "78", "hashOfConfig": "72"}, {"size": 4117, "mtime": 1753330761000, "results": "79", "hashOfConfig": "72"}, {"size": 15404, "mtime": 1753330898000, "results": "80", "hashOfConfig": "72"}, {"size": 5284, "mtime": 1755233426344, "results": "81", "hashOfConfig": "72"}, {"size": 1968, "mtime": 1755161686000, "results": "82", "hashOfConfig": "72"}, {"size": 1512, "mtime": 1749355645000, "results": "83", "hashOfConfig": "72"}, {"size": 2133, "mtime": 1755233318383, "results": "84", "hashOfConfig": "72"}, {"size": 5382, "mtime": 1755219711000, "results": "85", "hashOfConfig": "72"}, {"size": 2153, "mtime": 1753061230000, "results": "86", "hashOfConfig": "72"}, {"size": 12913, "mtime": 1755075792000, "results": "87", "hashOfConfig": "72"}, {"size": 9146, "mtime": 1755076221000, "results": "88", "hashOfConfig": "72"}, {"size": 5550, "mtime": 1755238109466, "results": "89", "hashOfConfig": "72"}, {"size": 4949, "mtime": 1755076625000, "results": "90", "hashOfConfig": "72"}, {"size": 4165, "mtime": 1755078981000, "results": "91", "hashOfConfig": "72"}, {"size": 6445, "mtime": 1755076651000, "results": "92", "hashOfConfig": "72"}, {"size": 5328, "mtime": 1755076564000, "results": "93", "hashOfConfig": "72"}, {"size": 3505, "mtime": 1753330770000, "results": "94", "hashOfConfig": "72"}, {"size": 5637, "mtime": 1755076667000, "results": "95", "hashOfConfig": "72"}, {"size": 4948, "mtime": 1755079170000, "results": "96", "hashOfConfig": "72"}, {"size": 5611, "mtime": 1755078992000, "results": "97", "hashOfConfig": "72"}, {"size": 21701, "mtime": 1755159822000, "results": "98", "hashOfConfig": "72"}, {"size": 8864, "mtime": 1753326411000, "results": "99", "hashOfConfig": "72"}, {"size": 8210, "mtime": 1755076526000, "results": "100", "hashOfConfig": "72"}, {"size": 6059, "mtime": 1753249973000, "results": "101", "hashOfConfig": "72"}, {"size": 11693, "mtime": 1755146870000, "results": "102", "hashOfConfig": "72"}, {"size": 1896, "mtime": 1755076419000, "results": "103", "hashOfConfig": "72"}, {"size": 1365, "mtime": 1749363366000, "results": "104", "hashOfConfig": "72"}, {"size": 1821, "mtime": 1750214167000, "results": "105", "hashOfConfig": "72"}, {"size": 681, "mtime": 1753060331000, "results": "106", "hashOfConfig": "72"}, {"size": 16425, "mtime": 1755222799000, "results": "107", "hashOfConfig": "72"}, {"size": 355, "mtime": 1750559873000, "results": "108", "hashOfConfig": "72"}, {"size": 4367, "mtime": 1755076289000, "results": "109", "hashOfConfig": "72"}, {"size": 4489, "mtime": 1755221740000, "results": "110", "hashOfConfig": "72"}, {"size": 615, "mtime": 1755076295000, "results": "111", "hashOfConfig": "72"}, {"size": 1764, "mtime": 1755162160000, "results": "112", "hashOfConfig": "72"}, {"size": 5606, "mtime": 1755223850000, "results": "113", "hashOfConfig": "72"}, {"size": 1175, "mtime": 1755146872000, "results": "114", "hashOfConfig": "72"}, {"size": 2218, "mtime": 1755146591000, "results": "115", "hashOfConfig": "72"}, {"size": 9126, "mtime": 1753433603000, "results": "116", "hashOfConfig": "72"}, {"size": 3752, "mtime": 1748666393000, "results": "117", "hashOfConfig": "72"}, {"size": 3369, "mtime": 1753868667000, "results": "118", "hashOfConfig": "72"}, {"size": 668, "mtime": 1753868657000, "results": "119", "hashOfConfig": "72"}, {"size": 23120, "mtime": 1755075353000, "results": "120", "hashOfConfig": "72"}, {"size": 17528, "mtime": 1755075363000, "results": "121", "hashOfConfig": "72"}, {"size": 1640, "mtime": 1753264117000, "results": "122", "hashOfConfig": "72"}, {"size": 2486, "mtime": 1753245712000, "results": "123", "hashOfConfig": "72"}, {"size": 22894, "mtime": 1755159238000, "results": "124", "hashOfConfig": "72"}, {"size": 19378, "mtime": 1755084623000, "results": "125", "hashOfConfig": "72"}, {"size": 18390, "mtime": 1755075325000, "results": "126", "hashOfConfig": "72"}, {"size": 19563, "mtime": 1755161881000, "results": "127", "hashOfConfig": "72"}, {"size": 20406, "mtime": 1755075339000, "results": "128", "hashOfConfig": "72"}, {"size": 11730, "mtime": 1755074714000, "results": "129", "hashOfConfig": "72"}, {"size": 11903, "mtime": 1755075401000, "results": "130", "hashOfConfig": "72"}, {"size": 19864, "mtime": 1755075412000, "results": "131", "hashOfConfig": "72"}, {"size": 859, "mtime": 1748626070000, "results": "132", "hashOfConfig": "72"}, {"size": 5329, "mtime": 1755080363000, "results": "133", "hashOfConfig": "72"}, {"size": 8429, "mtime": 1755148276000, "results": "134", "hashOfConfig": "72"}, {"size": 595, "mtime": 1755076292000, "results": "135", "hashOfConfig": "72"}, {"size": 8382, "mtime": 1755238820130, "results": "136", "hashOfConfig": "72"}, {"size": 419, "mtime": 1748244963000, "results": "137", "hashOfConfig": "72"}, {"size": 2068, "mtime": 1750485713000, "results": "138", "hashOfConfig": "72"}, {"size": 404, "mtime": 1755085274000, "results": "139", "hashOfConfig": "72"}, {"size": 1478, "mtime": 1755160898000, "results": "140", "hashOfConfig": "72"}, {"size": 1455, "mtime": 1755161457000, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cs3anf", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/var/www/html/gwm.tj/src/index.js", [], [], "/var/www/html/gwm.tj/src/App.js", [], [], "/var/www/html/gwm.tj/src/components/GoogleAnalytics.js", [], [], "/var/www/html/gwm.tj/src/layout/Layout.jsx", [], [], "/var/www/html/gwm.tj/src/components/ErrorBoundary/ErrorBoundary.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Home/Home.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Models/Models.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Owners.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Privacy/Privacy.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Offer/Offers.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Book-a-test-drive/Book-a-test-dieve.jsx", ["352", "353", "354"], [], "/var/www/html/gwm.tj/src/pages/NoPage/NoPage.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Offer/OfferPage/OfferPage.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/News/NewsList.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/News/NewsPage.jsx/NewsPage.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/About/About.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/About/History/History.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/Contact/Contact.jsx", ["355"], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Warranty/Warranty.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Assistance/Assistance.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Service/Service.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Parts/Parts.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Accessories/Accessories.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/ServicePlan/ServicePlan.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Vehicle-reference-table/Table.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/Pages/Care/Care.jsx", ["356"], [], "/var/www/html/gwm.tj/src/pages/Models/Pages/DynamicModelPage/DynamicModelPage.jsx", ["357"], [], "/var/www/html/gwm.tj/src/layout/Navbar/Navbar.jsx", ["358"], [], "/var/www/html/gwm.tj/src/layout/Footer/Footer.jsx", [], [], "/var/www/html/gwm.tj/src/hooks/useSEO.js", [], [], "/var/www/html/gwm.tj/src/data/seoData.js", ["359"], [], "/var/www/html/gwm.tj/src/components/SimpleCard/SimpleCard.jsx", [], [], "/var/www/html/gwm.tj/src/components/ToolBar/ToolBar.jsx", [], [], "/var/www/html/gwm.tj/src/components/FilterSlide/FilterSwiper.jsx", [], [], "/var/www/html/gwm.tj/src/components/Notification/Notification.jsx", [], [], "/var/www/html/gwm.tj/src/components/Form/Form.jsx", [], [], "/var/www/html/gwm.tj/src/components/SkeletonCard/SkeletonCard.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Home/components/header-slider/HeroSliderGWM.jsx", ["360"], [], "/var/www/html/gwm.tj/src/pages/Home/components/Models/OurModels.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Home/components/Image-card/ImageCard.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Home/components/video-card/VideoCard.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Discover/Contact/ContactForm.jsx", [], [], "/var/www/html/gwm.tj/src/data/modelLoader.js", [], [], "/var/www/html/gwm.tj/src/pages/Owners/components/sidebar/Sidebar.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/components/form/OwnersForm.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Owners/components/PartsList/PartsList.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Models/Pages/components/Specifications.jsx", [], [], "/var/www/html/gwm.tj/src/pages/Models/Pages/components/Modal.jsx", [], [], "/var/www/html/gwm.tj/src/data/models/tank-300.js", [], [], "/var/www/html/gwm.tj/src/data/models/tank-500.js", [], [], "/var/www/html/gwm.tj/src/asset/data/navbarData.js", ["361", "362"], [], "/var/www/html/gwm.tj/src/asset/data/footerData.js", [], [], "/var/www/html/gwm.tj/src/data/models/tank-700.js", [], [], "/var/www/html/gwm.tj/src/data/models/haval-h6-hev.js", [], [], "/var/www/html/gwm.tj/src/data/models/haval-h6.js", [], [], "/var/www/html/gwm.tj/src/data/models/haval-jolion-new.js", [], [], "/var/www/html/gwm.tj/src/data/models/haval-m6.js", [], [], "/var/www/html/gwm.tj/src/data/models/gwm-commercial-poer.js", [], [], "/var/www/html/gwm.tj/src/data/models/gwm-wingle-7.js", [], [], "/var/www/html/gwm.tj/src/data/models/tank-700-phev.js", [], [], "/var/www/html/gwm.tj/src/asset/data/ownersMenu.js", [], [], "/var/www/html/gwm.tj/src/utils/validation.js", [], [], "/var/www/html/gwm.tj/src/utils/api.js", ["363"], [], "/var/www/html/gwm.tj/src/pages/Home/components/header-slider/SkeletonSlide.jsx", [], [], "/var/www/html/gwm.tj/src/layout/Navbar/components/models/NavModels.jsx", [], [], "/var/www/html/gwm.tj/src/layout/Navbar/components/mobPrimaryBar/PrimaryBar.jsx", [], [], "/var/www/html/gwm.tj/src/layout/Navbar/components/discover/NavDiscover.jsx", [], [], "/var/www/html/gwm.tj/src/asset/data/accessoriesData.js", [], [], "/var/www/html/gwm.tj/src/pages/Home/components/Models/FilterSlide/FilterSwiper.jsx", ["364"], [], "/var/www/html/gwm.tj/src/pages/Models/Pages/components/FilterSlide/FilterSwiper.jsx", [], [], {"ruleId": "365", "severity": 1, "message": "366", "line": 4, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 24}, {"ruleId": "365", "severity": 1, "message": "369", "line": 4, "column": 26, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 33}, {"ruleId": "365", "severity": 1, "message": "370", "line": 4, "column": 35, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 45}, {"ruleId": "365", "severity": 1, "message": "371", "line": 19, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 19, "endColumn": 16}, {"ruleId": "365", "severity": 1, "message": "372", "line": 4, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "374", "line": 507, "column": 21, "nodeType": "375", "messageId": "376", "endLine": 511, "endColumn": 22, "fix": "377"}, {"ruleId": "365", "severity": 1, "message": "378", "line": 26, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 26, "endColumn": 18}, {"ruleId": "365", "severity": 1, "message": "379", "line": 6, "column": 7, "nodeType": "367", "messageId": "368", "endLine": 6, "endColumn": 17}, {"ruleId": "380", "severity": 1, "message": "381", "line": 116, "column": 13, "nodeType": "375", "endLine": 123, "endColumn": 14}, {"ruleId": "365", "severity": 1, "message": "382", "line": 4, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 22}, {"ruleId": "365", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 5, "endColumn": 20}, {"ruleId": "384", "severity": 1, "message": "385", "line": 268, "column": 1, "nodeType": "386", "endLine": 274, "endColumn": 3}, {"ruleId": "365", "severity": 1, "message": "387", "line": 9, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 9, "endColumn": 14}, "no-unused-vars", "'FaMapMarkerAlt' is defined but never used.", "Identifier", "unusedVar", "'FaClock' is defined but never used.", "'FaPhoneAlt' is defined but never used.", "'mapSrc' is assigned a value but never used.", "'Link' is defined but never used.", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "388", "text": "389"}, "'isMobile' is assigned a value but never used.", "'siteConfig' is assigned a value but never used.", "jsx-a11y/no-redundant-roles", "The element button has an implicit role of button. Defining this explicitly is redundant and should be avoided.", "'RiGlobalLine' is defined but never used.", "'GrLanguage' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'count' is assigned a value but never used.", [19762, 19762], " rel=\"noreferrer\""]