{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\nconst numberValueTypes = _objectSpread(_objectSpread({\n  // Border props\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Misc\n  backgroundPositionX: px,\n  backgroundPositionY: px\n}, transformValueTypes), {}, {\n  zIndex: int,\n  // SVG\n  fillOpacity: alpha,\n  strokeOpacity: alpha,\n  numOctaves: int\n});\nexport { numberValueTypes };", "map": {"version": 3, "names": ["int", "alpha", "px", "transformValueTypes", "numberValueTypes", "_objectSpread", "borderWidth", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "borderRadius", "radius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "top", "right", "bottom", "left", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "marginTop", "marginRight", "marginBottom", "marginLeft", "backgroundPositionX", "backgroundPositionY", "zIndex", "fillOpacity", "strokeOpacity", "numOctaves"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/value/types/maps/number.mjs"], "sourcesContent": ["import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,EAAE,QAAQ,sBAAsB;AACzC,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD,MAAMC,gBAAgB,GAAAC,aAAA,CAAAA,aAAA;EAClB;EACAC,WAAW,EAAEJ,EAAE;EACfK,cAAc,EAAEL,EAAE;EAClBM,gBAAgB,EAAEN,EAAE;EACpBO,iBAAiB,EAAEP,EAAE;EACrBQ,eAAe,EAAER,EAAE;EACnBS,YAAY,EAAET,EAAE;EAChBU,MAAM,EAAEV,EAAE;EACVW,mBAAmB,EAAEX,EAAE;EACvBY,oBAAoB,EAAEZ,EAAE;EACxBa,uBAAuB,EAAEb,EAAE;EAC3Bc,sBAAsB,EAAEd,EAAE;EAC1B;EACAe,KAAK,EAAEf,EAAE;EACTgB,QAAQ,EAAEhB,EAAE;EACZiB,MAAM,EAAEjB,EAAE;EACVkB,SAAS,EAAElB,EAAE;EACbmB,GAAG,EAAEnB,EAAE;EACPoB,KAAK,EAAEpB,EAAE;EACTqB,MAAM,EAAErB,EAAE;EACVsB,IAAI,EAAEtB,EAAE;EACR;EACAuB,OAAO,EAAEvB,EAAE;EACXwB,UAAU,EAAExB,EAAE;EACdyB,YAAY,EAAEzB,EAAE;EAChB0B,aAAa,EAAE1B,EAAE;EACjB2B,WAAW,EAAE3B,EAAE;EACf4B,MAAM,EAAE5B,EAAE;EACV6B,SAAS,EAAE7B,EAAE;EACb8B,WAAW,EAAE9B,EAAE;EACf+B,YAAY,EAAE/B,EAAE;EAChBgC,UAAU,EAAEhC,EAAE;EACd;EACAiC,mBAAmB,EAAEjC,EAAE;EACvBkC,mBAAmB,EAAElC;AAAE,GACpBC,mBAAmB;EACtBkC,MAAM,EAAErC,GAAG;EACX;EACAsC,WAAW,EAAErC,KAAK;EAClBsC,aAAa,EAAEtC,KAAK;EACpBuC,UAAU,EAAExC;AAAG,EAClB;AAED,SAASI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}