{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Discover/About/About.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styles from './about.module.css';\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n//\nimport img_1 from '../../../asset/imgs/about/1.webp';\nimport img_2 from '../../../asset/imgs/about/5-pc.webp';\nimport img_3 from '../../../asset/imgs/about/3.webp';\nimport img_4 from '../../../asset/imgs/about/6-1.webp';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"100\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u0412\\u0421\\u0415 \\u041E GWM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"100\",\n            className: \"redLine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"underText\",\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"150\",\n            children: \"GWM \\u2014 \\u044D\\u0442\\u043E \\u0433\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0430\\u044F \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044F, \\u0447\\u0435\\u0439 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u0435\\u0442 \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0438 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0435\\u0439, \\u041D\\u0418\\u041E\\u041A\\u0420, \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E, \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435. \\u0421\\u043E\\u0441\\u0442\\u043E\\u0438\\u0442 \\u0438\\u0437 \\u0442\\u0430\\u043A\\u0438\\u0445 \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432, \\u043A\\u0430\\u043A HAVAL, ORA, STEED, TANK, P300 \\u0438 P500.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"200\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 173,2 \\u043C\\u043B\\u0440\\u0434 \\u0434\\u043E\\u043B\\u043B\\u0430\\u0440\\u043E\\u0432 \\u0421\\u0428\\u0410 \\u043E\\u043F\\u0435\\u0440\\u0430\\u0446\\u0438\\u043E\\u043D\\u043D\\u043E\\u0439 \\u043F\\u0440\\u0438\\u0431\\u044B\\u043B\\u0438 \\u0432 2023 \\u0433\\u043E\\u0434\\u0443\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 8 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0434\\u0440\\u044F\\u0434 \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u0430 \\u0431\\u043E\\u043B\\u0435\\u0435 1 \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D\\u0430 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 14 \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D\\u043E\\u0432 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 7000 \\u0434\\u0438\\u0441\\u0442\\u0440\\u0438\\u0431\\u044C\\u044E\\u0442\\u043E\\u0440\\u0441\\u043A\\u0438\\u0445 \\u0438 \\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043D\\u0442\\u0440\\u043E\\u0432 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 90 000 \\u0441\\u043E\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u043A\\u043E\\u0432 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"+ 170 \\u0441\\u0442\\u0440\\u0430\\u043D \\u0438 \\u0440\\u0435\\u0433\\u0438\\u043E\\u043D\\u043E\\u0432-\\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0435\\u0440\\u043E\\u0432\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flexContent,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.img,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img_1,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.text,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"250\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: \"\\u041F\\u043E\\u0447\\u0435\\u043C\\u0443 GWM?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0421 \\u043D\\u043E\\u0432\\u043E\\u0439 \\u044D\\u043D\\u0435\\u0440\\u0433\\u0438\\u0435\\u0439 \\u0438 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u043C\\u0438 \\u043C\\u044B \\u043D\\u0430\\u0434\\u0435\\u0435\\u043C\\u0441\\u044F \\u0441\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441 \\u0446\\u0435\\u043D\\u043D\\u044B\\u0439 \\u043E\\u043F\\u044B\\u0442.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0418\\u043C\\u0435\\u044F \\u0434\\u043E\\u043B\\u0433\\u043E\\u0441\\u0440\\u043E\\u0447\\u043D\\u0443\\u044E \\u0446\\u0435\\u043B\\u044C \\u0441\\u043E\\u043A\\u0440\\u0430\\u0442\\u0438\\u0442\\u044C \\u0432\\u044B\\u0431\\u0440\\u043E\\u0441\\u044B \\u0443\\u0433\\u043B\\u0435\\u0440\\u043E\\u0434\\u0430 \\u0434\\u043E \\u043D\\u0443\\u043B\\u044F, \\u043C\\u044B \\u0441\\u043E\\u0437\\u0434\\u0430\\u043B\\u0438 \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0430 \\u0433\\u0438\\u0431\\u0440\\u0438\\u0434\\u043D\\u043E\\u0439, \\u044D\\u043B\\u0435\\u043A\\u0442\\u0440\\u0438\\u0447\\u0435\\u0441\\u043A\\u043E\\u0439 \\u0438 \\u0432\\u043E\\u0434\\u043E\\u0440\\u043E\\u0434\\u043D\\u043E\\u0439 \\u044D\\u043D\\u0435\\u0440\\u0433\\u0438\\u0438, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0438\\u0442\\u044C \\u0431\\u043E\\u043B\\u0435\\u0435 \\u0447\\u0438\\u0441\\u0442\\u044B\\u0435 \\u0438 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044F\\u043C \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flexContent,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.img,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img_2,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.text,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"250\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: \"\\u0422\\u0432\\u043E\\u0440\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0431\\u0435\\u0437 \\u0433\\u0440\\u0430\\u043D\\u0438\\u0446\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u041C\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u043C \\u0432\\u0430\\u043C \\u043B\\u0443\\u0447\\u0448\\u0438\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u0441 \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C\\u044E \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0438\\u0441\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439 \\u0438 \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u043E\\u043A.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0428\\u0442\\u0430\\u0431-\\u043A\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430 GWM \\u043D\\u0430\\u0445\\u043E\\u0434\\u0438\\u0442\\u0441\\u044F \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435, \\u0430 \\u0435\\u0435 \\u0433\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 R&D-\\u043E\\u0442\\u0434\\u0435\\u043B \\u043E\\u0445\\u0432\\u0430\\u0442\\u044B\\u0432\\u0430\\u0435\\u0442 \\u0415\\u0432\\u0440\\u043E\\u043F\\u0443, \\u0410\\u0437\\u0438\\u044E, \\u0421\\u0435\\u0432\\u0435\\u0440\\u043D\\u0443\\u044E \\u0438 \\u042E\\u0436\\u043D\\u0443\\u044E \\u0410\\u043C\\u0435\\u0440\\u0438\\u043A\\u0443. R&D-\\u0446\\u0435\\u043D\\u0442\\u0440\\u044B \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0431\\u044B\\u043B\\u0438 \\u0441\\u043E\\u0437\\u0434\\u0430\\u043D\\u044B \\u0432 \\u042F\\u043F\\u043E\\u043D\\u0438\\u0438, \\u0421\\u0428\\u0410, \\u0413\\u0435\\u0440\\u043C\\u0430\\u043D\\u0438\\u0438, \\u0418\\u043D\\u0434\\u0438\\u0438, \\u0410\\u0432\\u0441\\u0442\\u0440\\u0438\\u0438 \\u0438 \\u042E\\u0436\\u043D\\u043E\\u0439 \\u041A\\u043E\\u0440\\u0435\\u0435.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flexContent,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.img,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img_3,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.text,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"250\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: \"\\u0413\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u044B\\u0439 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0412 \\u041A\\u0438\\u0442\\u0430\\u0435 GWM \\u0438\\u043C\\u0435\\u0435\\u0442 10 \\u0431\\u0430\\u0437 \\u043F\\u043E \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0443 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0446\\u0438\\u043A\\u043B\\u0430. \\u041D\\u0430 \\u0437\\u0430\\u0440\\u0443\\u0431\\u0435\\u0436\\u043D\\u043E\\u043C \\u0440\\u044B\\u043D\\u043A\\u0435 GWM \\u043E\\u0442\\u043A\\u0440\\u044B\\u043B\\u0430 \\u0437\\u0430\\u0432\\u043E\\u0434\\u044B \\u043F\\u043E \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0443 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0446\\u0438\\u043A\\u043B\\u0430 \\u0432 \\u0422\\u0430\\u0438\\u043B\\u0430\\u043D\\u0434\\u0435 \\u0438 \\u0411\\u0440\\u0430\\u0437\\u0438\\u043B\\u0438\\u0438, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043D\\u0435\\u0441\\u043A\\u043E\\u043B\\u044C\\u043A\\u043E \\u0437\\u0430\\u0432\\u043E\\u0434\\u043E\\u0432 KD \\u0432 \\u042D\\u043A\\u0432\\u0430\\u0434\\u043E\\u0440\\u0435, \\u041F\\u0430\\u043A\\u0438\\u0441\\u0442\\u0430\\u043D\\u0435 \\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0430\\u0445.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flexContent,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.img,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"200\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img_4,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.text,\n              \"data-aos\": \"fade-up\",\n              \"data-aos-delay\": \"250\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: \"\\u0413\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043C\\u0431\\u0438\\u0446\\u0438\\u0438, \\u043D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u043C\\u0438\\u0441\\u0441\\u0438\\u044F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u041D\\u0430\\u0448\\u0430 \\u0446\\u0435\\u043B\\u044C \\u2014 \\u043F\\u0440\\u043E\\u0434\\u043E\\u043B\\u0436\\u0430\\u0442\\u044C \\u0440\\u0430\\u0441\\u0448\\u0438\\u0440\\u044F\\u0442\\u044C \\u0433\\u0440\\u0430\\u043D\\u0438\\u0446\\u044B \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0435\\u0439 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F. \\u0410\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0440\\u044B\\u043D\\u043E\\u043A \\u2014 \\u043E\\u0434\\u0438\\u043D \\u0438\\u0437 \\u0441\\u0430\\u043C\\u044B\\u0445 \\u043A\\u043E\\u043D\\u043A\\u0443\\u0440\\u0435\\u043D\\u0442\\u043D\\u044B\\u0445 \\u0432 \\u043C\\u0438\\u0440\\u0435, \\u0438 \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0434\\u043B\\u044F \\u043D\\u043E\\u0432\\u043E\\u0433\\u043E \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430, \\u0440\\u043E\\u0436\\u0434\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u0435\\u0439, \\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\\u0442\\u044C \\u0441\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0435\\u0434 \\u043D\\u0438\\u043A\\u043E\\u0433\\u0434\\u0430 \\u043D\\u0435 \\u0431\\u044B\\u043B\\u0438 \\u0431\\u043E\\u043B\\u044C\\u0448\\u0438\\u043C\\u0438.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u041F\\u043E \\u043C\\u0435\\u0440\\u0435 \\u0440\\u0430\\u0437\\u0432\\u0438\\u0442\\u0438\\u044F \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0439 \\u043B\\u044E\\u0434\\u0438 \\u0438\\u0449\\u0443\\u0442 \\u043D\\u043E\\u0432\\u044B\\u0435 \\u0440\\u0435\\u0448\\u0435\\u043D\\u0438\\u044F \\u0434\\u043B\\u044F \\u043B\\u0443\\u0447\\u0448\\u0435\\u0433\\u043E \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0433\\u043E. \\u041C\\u044B \\u0437\\u043D\\u0430\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043B\\u044E\\u0431\\u043E\\u0432\\u044C \\u043A \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0441\\u043E\\u043F\\u0440\\u044F\\u0436\\u0435\\u043D\\u0430 \\u0441 \\u0432\\u044B\\u0441\\u043E\\u043A\\u0438\\u043C\\u0438 \\u043E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u044F\\u043C\\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u043D\\u0430\\u0434\\u0435\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438. \\u0412\\u043E\\u0442 \\u043F\\u043E\\u0447\\u0435\\u043C\\u0443 GWM \\u0441\\u043E\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0430\\u0435\\u0442 \\u0441 \\u043B\\u0443\\u0447\\u0448\\u0438\\u043C\\u0438 \\u0438\\u043D\\u0436\\u0435\\u043D\\u0435\\u0440\\u0430\\u043C\\u0438 \\u0438 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0435\\u0440\\u0430\\u043C\\u0438, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0442\\u044C \\u044D\\u043B\\u0435\\u0433\\u0430\\u043D\\u0442\\u043D\\u044B\\u0435, \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438, \\u0438\\u0434\\u0435\\u0430\\u043B\\u044C\\u043D\\u043E \\u043F\\u043E\\u0434\\u0445\\u043E\\u0434\\u044F\\u0449\\u0438\\u0435 \\u0434\\u043B\\u044F \\u0440\\u0430\\u0437\\u043D\\u044B\\u0445 \\u0441\\u0442\\u0438\\u043B\\u0435\\u0439 \\u0436\\u0438\\u0437\\u043D\\u0438 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u0439\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: styles.text,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"150\",\n            children: \"\\u041F\\u043E\\u0447\\u0435\\u0441\\u0442\\u0438 \\u0438 \\u043D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"200\",\n            children: [\"\\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0443, \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044E, \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u044F\\u043C \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u043F\\u0440\\u0438\\u0432\\u043E\\u0434\\u0430 \\u0438 \\u043B\\u0443\\u0447\\u0448\\u0435\\u043C\\u0443 \\u0441\\u043E\\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u044E \\u0446\\u0435\\u043D\\u044B \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044F GWM \\u043D\\u0430 \\u043F\\u0440\\u043E\\u0442\\u044F\\u0436\\u0435\\u043D\\u0438\\u0438 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u043D\\u043E \\u043F\\u043E\\u043B\\u0443\\u0447\\u0430\\u043B\\u0430 \\u043F\\u0440\\u0438\\u0437\\u043D\\u0430\\u043D\\u0438\\u0435 \\u0438 \\u043D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443. \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 23\n            }, this), \" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 61\n            }, this), \" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043C\\u0430\\u0448\\u0438\\u043D\\u043E\\u0441\\u0442\\u0440\\u043E\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 54\n            }, this), \" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043F\\u0440\\u043E\\u043C\\u044B\\u0448\\u043B\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 48\n            }, this), \" \\u2022 \\u041B\\u0443\\u0447\\u0448\\u0430\\u044F \\u0438\\u0437 \\u043A\\u0440\\u0443\\u043F\\u043D\\u0435\\u0439\\u0448\\u0438\\u0445 \\u043B\\u0438\\u0441\\u0442\\u0438\\u043D\\u0433\\u0443\\u0435\\u043C\\u044B\\u0445 \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u0439 \\u0410\\u0437\\u0438\\u0430\\u0442\\u0441\\u043A\\u043E-\\u0422\\u0438\\u0445\\u043E\\u043E\\u043A\\u0435\\u0430\\u043D\\u0441\\u043A\\u043E\\u0433\\u043E \\u0440\\u0435\\u0433\\u0438\\u043E\\u043D\\u0430 \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 71\n            }, this), \" \\u2022 Forbes Global 2000 \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 36\n            }, this), \" \\u2022 Fortune China 500s \\u2022 \\u0411\\u0440\\u0435\\u043D\\u0434 \\u0432\\u0445\\u043E\\u0434\\u0438\\u0442 \\u0432 \\u0441\\u043E\\u0442\\u043D\\u044E \\u0441\\u0430\\u043C\\u044B\\u0445 \\u0446\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u0445 \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432 \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 54\n            }, this), \" \\u2022 \\u041F\\u0440\\u0438\\u0437\\u043D\\u0430\\u043D \\xAB\\u0420\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0443\\u0435\\u043C\\u044B\\u043C \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u043C \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u043C\\xBB \\u041A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u043E\\u0439 \\u0442\\u043E\\u0440\\u0433\\u043E\\u0432\\u043E\\u0439 \\u043F\\u0430\\u043B\\u0430\\u0442\\u043E\\u0439 \\u043F\\u043E \\u0438\\u043C\\u043F\\u043E\\u0440\\u0442\\u0443 \\u0438 \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0443 \\u043C\\u0430\\u0448\\u0438\\u043D\\u043E\\u0441\\u0442\\u0440\\u043E\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438 \\u044D\\u043B\\u0435\\u043A\\u0442\\u0440\\u043E\\u043D\\u043D\\u043E\\u0439 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438\", ' ', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), \" \\u2022 \\u041D\\u0430\\u0437\\u0432\\u0430\\u043D \\xAB\\u041D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0435\\u0440\\u043E\\u043C \\u0442\\u0440\\u0430\\u043D\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u0445 \\u0441\\u0440\\u0435\\u0434\\u0441\\u0442\\u0432\\xBB \\u041C\\u0438\\u043D\\u0438\\u0441\\u0442\\u0435\\u0440\\u0441\\u0442\\u0432\\u043E\\u043C \\u0442\\u043E\\u0440\\u0433\\u043E\\u0432\\u043B\\u0438 \\u0438 \\u041D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u043A\\u043E\\u043C\\u0438\\u0441\\u0441\\u0438\\u0435\\u0439 \\u043F\\u043E \\u0440\\u0430\\u0437\\u0432\\u0438\\u0442\\u0438\\u044E \\u0438 \\u0440\\u0435\\u0444\\u043E\\u0440\\u043C\\u0430\\u043C \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 26\n            }, this), \" \\u2022 \\u041F\\u0435\\u0440\\u0432\\u043E\\u0435 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u043E\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0435, \\u0441\\u0442\\u0430\\u0432\\u0448\\u0435\\u0435 \\u0447\\u043B\\u0435\\u043D\\u043E\\u043C \\u0421\\u043E\\u0432\\u0435\\u0442\\u0430 \\u043F\\u043E \\u0432\\u043E\\u0434\\u043E\\u0440\\u043E\\u0434\\u0443\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: styles.text,\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"150\",\n            children: \"\\u0423\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"200\",\n            children: \"\\u0412 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0435 \\u0433\\u043E\\u0434\\u044B GWM \\u0443\\u0434\\u0435\\u043B\\u044F\\u0435\\u0442 \\u043F\\u0435\\u0440\\u0432\\u043E\\u0441\\u0442\\u0435\\u043F\\u0435\\u043D\\u043D\\u043E\\u0435 \\u0432\\u043D\\u0438\\u043C\\u0430\\u043D\\u0438\\u0435 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432, \\u043F\\u043E\\u0432\\u044B\\u0448\\u0430\\u044F \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0441\\u0432\\u043E\\u0438\\u0445 \\u0443\\u0441\\u043B\\u0443\\u0433 \\u0432 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0438\\u0438 \\u0441 \\u0438\\u043D\\u0434\\u0438\\u0432\\u0438\\u0434\\u0443\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E \\u0441\\u0432\\u043E\\u0435\\u0433\\u043E \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430 \\u0438 \\u0441\\u0442\\u0440\\u0435\\u043C\\u044F\\u0441\\u044C \\u043F\\u0440\\u0435\\u0432\\u0437\\u043E\\u0439\\u0442\\u0438 \\u043E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u044F \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432. \\u042D\\u0442\\u0430 \\u043F\\u0440\\u0435\\u0434\\u0430\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u0434\\u0435\\u043B\\u0443 \\u0441\\u043F\\u043E\\u0441\\u043E\\u0431\\u0441\\u0442\\u0432\\u043E\\u0432\\u0430\\u043B\\u0430 \\u0443\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044E \\u043F\\u0440\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0441\\u0432\\u044F\\u0437\\u0435\\u0439 \\u0441 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430\\u043C\\u0438 \\u0438 \\u0443\\u0441\\u0438\\u043B\\u0438\\u043B\\u0430 \\u0435\\u0435 \\u0441\\u0442\\u0440\\u0435\\u043C\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043A \\u0441\\u043E\\u0432\\u0435\\u0440\\u0448\\u0435\\u043D\\u0441\\u0442\\u0432\\u0443.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"250\",\n            children: \"\\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u043E\\u0439 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0435 GWM \\u0441\\u0442\\u0430\\u043B\\u0430 \\u043B\\u0438\\u0434\\u0435\\u0440\\u043E\\u043C \\u043F\\u043E \\u043E\\u0431\\u044A\\u0435\\u043C\\u0443 \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436 \\u0438 \\u0438\\u043C\\u0438\\u0434\\u0436\\u0443 \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430. \\u0415\\u0435 \\u0441\\u0443\\u0431\\u0431\\u0440\\u0435\\u043D\\u0434\\u044B \\u043D\\u0435\\u0443\\u043A\\u043B\\u043E\\u043D\\u043D\\u043E \\u043F\\u0440\\u043E\\u0434\\u0432\\u0438\\u0433\\u0430\\u044E\\u0442\\u0441\\u044F \\u043A \\u0440\\u044B\\u043D\\u043A\\u0443 \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u0433\\u043E \\u043A\\u043B\\u0430\\u0441\\u0441\\u0430, \\u0434\\u0432\\u0438\\u0436\\u0438\\u043C\\u044B\\u0435 \\u043F\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u043D\\u044B\\u043C\\u0438 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0438\\u043D\\u043D\\u043E\\u0432\\u0430\\u0446\\u0438\\u044F\\u043C\\u0438 \\u0438 \\u043F\\u043E\\u0432\\u044B\\u0448\\u0435\\u043D\\u0438\\u0435\\u043C \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u0430. \\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u043D\\u0435\\u0437\\u0430\\u0432\\u0438\\u0441\\u0438\\u043C\\u044B\\u043C \\u043E\\u0441\\u043D\\u043E\\u0432\\u043D\\u044B\\u043C \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u043C \\u0438 \\u0438\\u0441\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u043C\\u0443 \\u0441\\u043E\\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u044E \\u0446\\u0435\\u043D\\u044B \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 GWM \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u0442 \\u0448\\u0438\\u0440\\u043E\\u043A\\u0438\\u0439 \\u0430\\u0441\\u0441\\u043E\\u0440\\u0442\\u0438\\u043C\\u0435\\u043D\\u0442 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438, \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u043D\\u043E\\u0439 \\u0441 \\u0443\\u0447\\u0435\\u0442\\u043E\\u043C \\u043C\\u0435\\u043D\\u044F\\u044E\\u0449\\u0438\\u0445\\u0441\\u044F \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u043D\\u043E\\u0441\\u0442\\u0435\\u0439 \\u0441\\u0432\\u043E\\u0438\\u0445 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(About, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "AOS", "img_1", "img_2", "img_3", "img_4", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "About", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexContent", "img", "src", "alt", "text", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/About/About.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './about.module.css';\n// animation\nimport A<PERSON> from 'aos';\nimport 'aos/dist/aos.css';\n//\nimport img_1 from '../../../asset/imgs/about/1.webp';\nimport img_2 from '../../../asset/imgs/about/5-pc.webp';\nimport img_3 from '../../../asset/imgs/about/3.webp';\nimport img_4 from '../../../asset/imgs/about/6-1.webp';\n\nconst About = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"100\">\n                <strong>ВСЕ О GWM</strong>\n              </h1>\n              <i\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n                className=\"redLine\"\n              ></i>\n              <p className=\"underText\" data-aos=\"fade-up\" data-aos-delay=\"150\">\n                GWM — это глобальная интеллектуальная технологическая компания,\n                чей бизнес включает проектирование автомобилей и деталей, НИОКР,\n                производство, продажи и обслуживание. Состоит из таких брендов,\n                как HAVAL, ORA, STEED, TANK, P300 и P500.\n              </p>\n\n              <div data-aos=\"fade-up\" data-aos-delay=\"200\">\n                <p>\n                  + 173,2 млрд долларов США операционной прибыли в 2023 году\n                </p>\n                <p>+ 8 лет подряд продажа более 1 миллиона автомобилей</p>\n                <p>+ 14 миллионов пользователей по всему миру</p>\n                <p>+ 7000 дистрибьюторских и сервисных центров по всему миру</p>\n                <p>+ 90 000 сотрудников по всему миру</p>\n                <p>+ 170 стран и регионов-экспортеров</p>\n              </div>\n            </div>\n            <section>\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_1} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Почему GWM?</h2>\n                  <p>\n                    С новой энергией и интеллектуальными технологиями мы\n                    надеемся создать для вас ценный опыт.\n                  </p>\n                  <p>\n                    Имея долгосрочную цель сократить выбросы углерода до нуля,\n                    мы создали модель дизайна гибридной, электрической и\n                    водородной энергии, чтобы предложить более чистые и\n                    интеллектуальные автомобили пользователям по всему миру.\n                  </p>\n                </div>\n              </div>\n              {/* 2 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_2} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Творчество без границ</h2>\n                  <p>\n                    Мы предлагаем вам лучшие автомобили с помощью\n                    проектирования, исследований и разработок.\n                  </p>\n                  <p>\n                    Штаб-квартира GWM находится в Китае, а ее глобальный\n                    R&D-отдел охватывает Европу, Азию, Северную и Южную Америку.\n                    R&D-центры также были созданы в Японии, США, Германии,\n                    Индии, Австрии и Южной Корее.\n                  </p>\n                </div>\n              </div>\n              {/* 3 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_3} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Глобальный производственный дизайн</h2>\n\n                  <p>\n                    В Китае GWM имеет 10 баз по производству автомобилей полного\n                    цикла. На зарубежном рынке GWM открыла заводы по\n                    производству автомобилей полного цикла в Таиланде и\n                    Бразилии, а также несколько заводов KD в Эквадоре, Пакистане\n                    и других странах.\n                  </p>\n                </div>\n              </div>\n              {/* 3 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_4} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Глобальные амбиции, национальная миссия</h2>\n\n                  <p>\n                    Наша цель — продолжать расширять границы возможностей\n                    автомобиля. Автомобильный рынок — один из самых конкурентных\n                    в мире, и возможности для нового бренда, рожденного\n                    специализацией, оставить свой след никогда не были большими.\n                  </p>\n                  <p>\n                    По мере развития технологий люди ищут новые решения для\n                    лучшего будущего. Мы знаем, что любовь к автомобилям\n                    сопряжена с высокими ожиданиями качества и надежности. Вот\n                    почему GWM сотрудничает с лучшими инженерами и дизайнерами,\n                    чтобы предлагать элегантные, функциональные автомобили,\n                    идеально подходящие для разных стилей жизни и условий\n                  </p>\n                </div>\n              </div>\n            </section>\n            <section className={styles.text}>\n              <h2 data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Почести и награды\n              </h2>\n              <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                Благодаря дизайну, проектированию, возможностям полного привода\n                и лучшему соотношению цены и качества компания GWM на протяжении\n                последних лет постоянно получала признание и награды по всему\n                миру. <br /> • 500 лучших предприятий Китая <br /> • 500 лучших\n                машиностроительных предприятий Китая <br /> • 500 лучших\n                промышленных предприятий Китая <br /> • Лучшая из крупнейших\n                листингуемых компаний Азиатско-Тихоокеанского региона <br /> •\n                Forbes Global 2000 <br /> • Fortune China 500s • Бренд входит в\n                сотню самых ценных китайских брендов <br /> • Признан\n                «Рекомендуемым экспортным брендом» Китайской торговой палатой по\n                импорту и экспорту машиностроительной и электронной продукции{' '}\n                <br /> • Назван «Национальным экспортером транспортных средств»\n                Министерством торговли и Национальной комиссией по развитию и\n                реформам <br /> • Первое китайское автомобильное предприятие,\n                ставшее членом Совета по водороду\n              </p>\n            </section>\n            <section className={styles.text}>\n              <h2 data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Удовлетворенность клиентов\n              </h2>\n              <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                В последние годы GWM уделяет первостепенное внимание\n                удовлетворенности клиентов, повышая качество своих услуг в\n                соответствии с индивидуальностью своего бренда и стремясь\n                превзойти ожидания клиентов. Эта преданность делу способствовала\n                установлению прочных связей с клиентами и усилила ее стремление\n                к совершенству.\n              </p>\n              <p data-aos=\"fade-up\" data-aos-delay=\"250\">\n                Благодаря последовательной высококачественной работе GWM стала\n                лидером по объему продаж и имиджу бренда. Ее суббренды неуклонно\n                продвигаются к рынку высокого класса, движимые постоянными\n                технологическими инновациями и повышением ценности продукта.\n                Благодаря независимым основным технологиям и исключительному\n                соотношению цены и качества GWM предлагает широкий ассортимент\n                продукции, разработанной с учетом меняющихся потребностей своих\n                клиентов.\n              </p>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC;AACA,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AACzB;AACA,OAAOC,KAAK,MAAM,kCAAkC;AACpD,OAAOC,KAAK,MAAM,qCAAqC;AACvD,OAAOC,KAAK,MAAM,kCAAkC;AACpD,OAAOC,KAAK,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdG,GAAG,CAACa,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;IAEzC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBnB,OAAA;UAAKoB,SAAS,EAAC,SAAS;UAAAD,QAAA,gBACtBnB,OAAA;YAAIoB,SAAS,EAAC,OAAO;YAAC,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAD,QAAA,eAC3DnB,OAAA;cAAAmB,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACLxB,OAAA;YACE,YAAS,SAAS;YAClB,kBAAe,KAAK;YACpBoB,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACLxB,OAAA;YAAGoB,SAAS,EAAC,WAAW;YAAC,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAD,QAAA,EAAC;UAKjE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJxB,OAAA;YAAK,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,gBAC1CnB,OAAA;cAAAmB,QAAA,EAAG;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxB,OAAA;cAAAmB,QAAA,EAAG;YAAmD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1DxB,OAAA;cAAAmB,QAAA,EAAG;YAA0C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjDxB,OAAA;cAAAmB,QAAA,EAAG;YAAyD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChExB,OAAA;cAAAmB,QAAA,EAAG;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzCxB,OAAA;cAAAmB,QAAA,EAAG;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAKoB,SAAS,EAAE3B,MAAM,CAACgC,WAAY;YAAAN,QAAA,gBACjCnB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACiC,GAAI;cACtB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAP,QAAA,eAEpBnB,OAAA;gBAAK2B,GAAG,EAAEhC,KAAM;gBAACiC,GAAG,EAAC;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;cACvB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAV,QAAA,gBAEpBnB,OAAA;gBAAAmB,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxB,OAAA;gBAAAmB,QAAA,EAAG;cAGH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxB,OAAA;gBAAAmB,QAAA,EAAG;cAKH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKoB,SAAS,EAAE3B,MAAM,CAACgC,WAAY;YAAAN,QAAA,gBACjCnB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACiC,GAAI;cACtB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAP,QAAA,eAEpBnB,OAAA;gBAAK2B,GAAG,EAAE/B,KAAM;gBAACgC,GAAG,EAAC;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;cACvB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAV,QAAA,gBAEpBnB,OAAA;gBAAAmB,QAAA,EAAI;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BxB,OAAA;gBAAAmB,QAAA,EAAG;cAGH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxB,OAAA;gBAAAmB,QAAA,EAAG;cAKH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKoB,SAAS,EAAE3B,MAAM,CAACgC,WAAY;YAAAN,QAAA,gBACjCnB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACiC,GAAI;cACtB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAP,QAAA,eAEpBnB,OAAA;gBAAK2B,GAAG,EAAE9B,KAAM;gBAAC+B,GAAG,EAAC;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;cACvB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAV,QAAA,gBAEpBnB,OAAA;gBAAAmB,QAAA,EAAI;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE3CxB,OAAA;gBAAAmB,QAAA,EAAG;cAMH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKoB,SAAS,EAAE3B,MAAM,CAACgC,WAAY;YAAAN,QAAA,gBACjCnB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACiC,GAAI;cACtB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAP,QAAA,eAEpBnB,OAAA;gBAAK2B,GAAG,EAAE7B,KAAM;gBAAC8B,GAAG,EAAC;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNxB,OAAA;cACEoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;cACvB,YAAS,SAAS;cAClB,kBAAe,KAAK;cAAAV,QAAA,gBAEpBnB,OAAA;gBAAAmB,QAAA,EAAI;cAAuC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDxB,OAAA;gBAAAmB,QAAA,EAAG;cAKH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxB,OAAA;gBAAAmB,QAAA,EAAG;cAOH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACVxB,OAAA;UAASoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;UAAAV,QAAA,gBAC9BnB,OAAA;YAAI,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,EAAC;UAE5C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAG,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,GAAC,ggCAInC,eAAAnB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,uJAAgC,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,oQACb,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gOACZ,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,saACiB,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,+BACzC,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,sTACY,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,suBAEkB,EAAC,GAAG,eACjExB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,srBAEG,eAAAxB,OAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,6aAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACVxB,OAAA;UAASoB,SAAS,EAAE3B,MAAM,CAACoC,IAAK;UAAAV,QAAA,gBAC9BnB,OAAA;YAAI,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,EAAC;UAE5C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAG,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,EAAC;UAO3C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJxB,OAAA;YAAG,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAmB,QAAA,EAAC;UAS3C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CA1NID,KAAK;AAAA2B,EAAA,GAAL3B,KAAK;AA4NX,eAAeA,KAAK;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}