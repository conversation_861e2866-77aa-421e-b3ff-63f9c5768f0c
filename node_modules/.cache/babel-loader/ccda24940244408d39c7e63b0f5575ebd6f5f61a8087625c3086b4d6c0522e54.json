{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options) {\n  let scale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100;\n  let createGenerator = arguments.length > 2 ? arguments[2] : undefined;\n  const generator = createGenerator(_objectSpread(_objectSpread({}, options), {}, {\n    keyframes: [0, scale]\n  }));\n  const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n  return {\n    type: \"keyframes\",\n    ease: progress => {\n      return generator.next(duration * progress).value / scale;\n    },\n    duration: millisecondsToSeconds(duration)\n  };\n}\nexport { createGeneratorEasing };", "map": {"version": 3, "names": ["millisecondsToSeconds", "calcGeneratorDuration", "maxGeneratorDuration", "createGeneratorEasing", "options", "scale", "arguments", "length", "undefined", "createGenerator", "generator", "_objectSpread", "keyframes", "duration", "Math", "min", "type", "ease", "progress", "next", "value"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "mappings": ";AAAA,SAASA,qBAAqB,QAAQ,cAAc;AACpD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,qBAAqB;;AAEjF;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAgC;EAAA,IAA9BC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EAAA,IAAEG,eAAe,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAChE,MAAME,SAAS,GAAGD,eAAe,CAAAE,aAAA,CAAAA,aAAA,KAAMP,OAAO;IAAEQ,SAAS,EAAE,CAAC,CAAC,EAAEP,KAAK;EAAC,EAAE,CAAC;EACxE,MAAMQ,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACd,qBAAqB,CAACS,SAAS,CAAC,EAAER,oBAAoB,CAAC;EACjF,OAAO;IACHc,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAGC,QAAQ,IAAK;MAChB,OAAOR,SAAS,CAACS,IAAI,CAACN,QAAQ,GAAGK,QAAQ,CAAC,CAACE,KAAK,GAAGf,KAAK;IAC5D,CAAC;IACDQ,QAAQ,EAAEb,qBAAqB,CAACa,QAAQ;EAC5C,CAAC;AACL;AAEA,SAASV,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}