{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Models/Pages/components/Modal.jsx\";\nimport React from 'react';\nimport styles from './modal.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Modal = ({\n  title,\n  desc,\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.overlay,\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.modal,\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.flex,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: styles.title,\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: styles.close,\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.desc,\n        children: desc.split('\\n').map((line, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n          children: line\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Modal;\nexport default Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["React", "styles", "jsxDEV", "_jsxDEV", "Modal", "title", "desc", "onClose", "className", "overlay", "onClick", "children", "modal", "e", "stopPropagation", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "close", "split", "map", "line", "index", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/components/Modal.jsx"], "sourcesContent": ["import React from 'react';\nimport styles from './modal.module.css';\n\nconst Modal = ({ title, desc, onClose }) => {\n  return (\n    <div className={styles.overlay} onClick={onClose}>\n      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>\n        <div className={styles.flex}>\n          <h2 className={styles.title}>{title}</h2>\n          <button className={styles.close} onClick={onClose}>\n            ×\n          </button>\n        </div>\n        <div className={styles.desc}>\n          {desc.split('\\n').map((line, index) => (\n            <p key={index}>{line}</p>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAK,GAAGA,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAC1C,oBACEJ,OAAA;IAAKK,SAAS,EAAEP,MAAM,CAACQ,OAAQ;IAACC,OAAO,EAAEH,OAAQ;IAAAI,QAAA,eAC/CR,OAAA;MAAKK,SAAS,EAAEP,MAAM,CAACW,KAAM;MAACF,OAAO,EAAGG,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAH,QAAA,gBAChER,OAAA;QAAKK,SAAS,EAAEP,MAAM,CAACc,IAAK;QAAAJ,QAAA,gBAC1BR,OAAA;UAAIK,SAAS,EAAEP,MAAM,CAACI,KAAM;UAAAM,QAAA,EAAEN;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzChB,OAAA;UAAQK,SAAS,EAAEP,MAAM,CAACmB,KAAM;UAACV,OAAO,EAAEH,OAAQ;UAAAI,QAAA,EAAC;QAEnD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNhB,OAAA;QAAKK,SAAS,EAAEP,MAAM,CAACK,IAAK;QAAAK,QAAA,EACzBL,IAAI,CAACe,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChCrB,OAAA;UAAAQ,QAAA,EAAgBY;QAAI,GAAZC,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAlBIrB,KAAK;AAoBX,eAAeA,KAAK;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}