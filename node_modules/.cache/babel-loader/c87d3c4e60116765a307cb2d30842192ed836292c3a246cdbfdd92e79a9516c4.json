{"ast": null, "code": "import React,{useEffect,useState}from'react';import Sidebar from'../../components/sidebar/Sidebar';import AOS from'aos';import'aos/dist/aos.css';import styles from'../../owners.module.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const tableData=[{MODEL:'JOLION',WARRANTY:'5 лет / 100 000 км',HIGH_VOLTAGE:'',BODY_PAINT:'5 лет / 100 000 км',ROADSIDE_ASSIST:'5 лет / Неограниченный пробег',SERVICE_PLAN:'5 лет / 60 000 км',SERVICE_INTERVAL:'Каждые 15 000 км или 12 мес.',NOTES:'Все'},{MODEL:'HAVAL Н6',WARRANTY:'5 лет / 100 000 км',HIGH_VOLTAGE:'',BODY_PAINT:'5 лет / 100 000 км',ROADSIDE_ASSIST:'5 лет / Неограниченный пробег',SERVICE_PLAN:'5 лет / 60 000 км',SERVICE_INTERVAL:'Каждые 15 000 км или 12 мес.',NOTES:'Все'},{MODEL:'ТАНК 300',WARRANTY:'7 лет / 200 000 км',HIGH_VOLTAGE:'',BODY_PAINT:'7 лет / 200 000 км',ROADSIDE_ASSIST:'7 лет / Неограниченный пробег',SERVICE_PLAN:'5 лет / 75 000 км',SERVICE_INTERVAL:'Каждые 15 000 км или 12 мес.',NOTES:'Все'}];const Table=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0422\\u0410\\u0411\\u041B\\u0418\\u0426\\u0410 \\u0421\\u0421\\u042B\\u041B\\u041E\\u041A \\u0422\\u0420\\u0410\\u041D\\u0421\\u041F\\u041E\\u0420\\u0422\\u041D\\u042B\\u0425 \\u0421\\u0420\\u0415\\u0414\\u0421\\u0422\\u0412\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:styles.redLine}),/*#__PURE__*/_jsx(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"400\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u041F\\u0430\\u0440\\u0430\\u043C\\u0435\\u0442\\u0440\\u044B \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u043E\\u0439 \\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u044B \\u043D\\u0438\\u0436\\u0435 \\u043E\\u0442\\u043D\\u043E\\u0441\\u044F\\u0442\\u0441\\u044F \\u043A \\u0442\\u043E\\u043C\\u0443, \\u0447\\u0442\\u043E \\u043F\\u0440\\u043E\\u0438\\u0437\\u043E\\u0439\\u0434\\u0435\\u0442 \\u043F\\u0435\\u0440\\u0432\\u044B\\u043C (\\u0432\\u0440\\u0435\\u043C\\u044F \\u0438\\u043B\\u0438 \\u043F\\u0440\\u043E\\u0431\\u0435\\u0433) \\u0438 \\u043A \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u043D\\u044B\\u043C \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u044F\\u0442\\u044C\\u0441\\u044F \\u0432 \\u0437\\u0430\\u0432\\u0438\\u0441\\u0438\\u043C\\u043E\\u0441\\u0442\\u0438 \\u043E\\u0442 \\u043B\\u044E\\u0431\\u044B\\u0445 \\u0442\\u0435\\u043A\\u0443\\u0449\\u0438\\u0445 \\u0438\\u0437\\u043C\\u0435\\u043D\\u0435\\u043D\\u0438\\u0439. \\u041C\\u044B \\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u044F\\u0435\\u043C \\u0437\\u0430 \\u0441\\u043E\\u0431\\u043E\\u0439 \\u043F\\u0440\\u0430\\u0432\\u043E \\u0432\\u043D\\u043E\\u0441\\u0438\\u0442\\u044C \\u043B\\u044E\\u0431\\u044B\\u0435 \\u0438\\u0437\\u043C\\u0435\\u043D\\u0435\\u043D\\u0438\\u044F, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u044F\\u0442\\u044C\\u0441\\u044F.\"})}),/*#__PURE__*/_jsx(\"div\",{className:styles.tableWrapper,children:/*#__PURE__*/_jsxs(\"table\",{className:styles.customTable,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u0413\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u0412\\u044B\\u0441\\u043E\\u043A\\u043E\\u0435 \\u043D\\u0430\\u043F\\u0440\\u044F\\u0436\\u0435\\u043D\\u0438\\u0435\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u041A\\u0443\\u0437\\u043E\\u0432\\u0430 \\u0438 \\u041A\\u0440\\u0430\\u0441\\u043A\\u0430\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u041F\\u043E\\u043C\\u043E\\u0449\\u044C \\u043D\\u0430 \\u0434\\u043E\\u0440\\u043E\\u0433\\u0435\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u041F\\u043B\\u0430\\u043D \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u0418\\u043D\\u0442\\u0435\\u0440\\u0432\\u0430\\u043B \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"th\",{children:\"\\u041F\\u0440\\u0438\\u043C\\u0435\\u0447\\u0430\\u043D\\u0438\\u044F\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:tableData.map(item=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:item.MODEL}),/*#__PURE__*/_jsx(\"td\",{children:item.WARRANTY}),/*#__PURE__*/_jsx(\"td\",{children:item.HIGH_VOLTAGE}),/*#__PURE__*/_jsx(\"td\",{children:item.BODY_PAINT}),/*#__PURE__*/_jsx(\"td\",{children:item.ROADSIDE_ASSIST}),/*#__PURE__*/_jsx(\"td\",{children:item.SERVICE_PLAN}),/*#__PURE__*/_jsx(\"td\",{children:item.SERVICE_INTERVAL}),/*#__PURE__*/_jsx(\"td\",{children:item.NOTES})]},item.id))})]})})]})})]})})})});};export default Table;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "AOS", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "tableData", "MODEL", "WARRANTY", "HIGH_VOLTAGE", "BODY_PAINT", "ROADSIDE_ASSIST", "SERVICE_PLAN", "SERVICE_INTERVAL", "NOTES", "Table", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "redLine", "textContent", "tableWrapper", "customTable", "map", "item", "id"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Vehicle-reference-table/Table.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport styles from '../../owners.module.css';\n\nconst tableData = [\n  {\n    MODEL: 'JOLION',\n    WARRANTY: '5 лет / 100 000 км',\n    HIGH_VOLTAGE: '',\n    BODY_PAINT: '5 лет / 100 000 км',\n    ROADSIDE_ASSIST: '5 лет / Неограниченный пробег',\n    SERVICE_PLAN: '5 лет / 60 000 км',\n    SERVICE_INTERVAL: 'Каждые 15 000 км или 12 мес.',\n    NOTES: 'Все',\n  },\n  {\n    MODEL: 'HAVAL Н6',\n    WARRANTY: '5 лет / 100 000 км',\n    HIGH_VOLTAGE: '',\n    BODY_PAINT: '5 лет / 100 000 км',\n    ROADSIDE_ASSIST: '5 лет / Неограниченный пробег',\n    SERVICE_PLAN: '5 лет / 60 000 км',\n    SERVICE_INTERVAL: 'Каждые 15 000 км или 12 мес.',\n    NOTES: 'Все',\n  },\n  {\n    MODEL: 'ТАНК 300',\n    WARRANTY: '7 лет / 200 000 км',\n    HIGH_VOLTAGE: '',\n    BODY_PAINT: '7 лет / 200 000 км',\n    ROADSIDE_ASSIST: '7 лет / Неограниченный пробег',\n    SERVICE_PLAN: '5 лет / 75 000 км',\n    SERVICE_INTERVAL: 'Каждые 15 000 км или 12 мес.',\n    NOTES: 'Все',\n  },\n];\n\nconst Table = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false,\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ТАБЛИЦА ССЫЛОК ТРАНСПОРТНЫХ СРЕДСТВ</strong>\n                  </h1>\n\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"400\"\n                  >\n                    <p>\n                      Параметры справочной таблицы ниже относятся к тому, что\n                      произойдет первым (время или пробег) и к производным\n                      модели, которые могут применяться в зависимости от любых\n                      текущих изменений. Мы оставляем за собой право вносить\n                      любые изменения, которые могут применяться.\n                    </p>\n                  </div>\n                  {/* table */}\n                  <div className={styles.tableWrapper}>\n                    <table className={styles.customTable}>\n                      <thead>\n                        <tr>\n                          <th>Модель</th>\n                          <th>Гарантия</th>\n                          <th>Высокое напряжение</th>\n                          <th>Кузова и Краска</th>\n                          <th>Помощь на дороге</th>\n                          <th>План обслуживания</th>\n                          <th>Интервал обслуживания</th>\n                          <th>Примечания</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {tableData.map((item) => (\n                          <tr key={item.id}>\n                            <td>{item.MODEL}</td>\n                            <td>{item.WARRANTY}</td>\n                            <td>{item.HIGH_VOLTAGE}</td>\n                            <td>{item.BODY_PAINT}</td>\n                            <td>{item.ROADSIDE_ASSIST}</td>\n                            <td>{item.SERVICE_PLAN}</td>\n                            <td>{item.SERVICE_INTERVAL}</td>\n                            <td>{item.NOTES}</td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Table;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7C,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,QAAQ,CACfC,QAAQ,CAAE,oBAAoB,CAC9BC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,oBAAoB,CAChCC,eAAe,CAAE,+BAA+B,CAChDC,YAAY,CAAE,mBAAmB,CACjCC,gBAAgB,CAAE,8BAA8B,CAChDC,KAAK,CAAE,KACT,CAAC,CACD,CACEP,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,oBAAoB,CAC9BC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,oBAAoB,CAChCC,eAAe,CAAE,+BAA+B,CAChDC,YAAY,CAAE,mBAAmB,CACjCC,gBAAgB,CAAE,8BAA8B,CAChDC,KAAK,CAAE,KACT,CAAC,CACD,CACEP,KAAK,CAAE,UAAU,CACjBC,QAAQ,CAAE,oBAAoB,CAC9BC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,oBAAoB,CAChCC,eAAe,CAAE,+BAA+B,CAChDC,YAAY,CAAE,mBAAmB,CACjCC,gBAAgB,CAAE,8BAA8B,CAChDC,KAAK,CAAE,KACT,CAAC,CACF,CAED,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdG,GAAG,CAACoB,IAAI,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,KACR,CAAC,CAAC,CAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEzB,IAAA,CAAAI,SAAA,EAAAyB,QAAA,CACGd,OAAO,cACNf,IAAA,QAAK8B,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5B7B,IAAA,QAAK8B,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAEN9B,IAAA,QAAK8B,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtB7B,IAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxB3B,KAAA,QAAK4B,SAAS,CAAEhC,MAAM,CAACiC,MAAO,CAAAF,QAAA,eAC5B7B,IAAA,CAACJ,OAAO,GAAE,CAAC,cACXI,IAAA,SAAM8B,SAAS,CAAEhC,MAAM,CAACkC,IAAK,CAAAH,QAAA,cAC3B3B,KAAA,QAAK4B,SAAS,CAAEhC,MAAM,CAACmC,aAAc,CAAAJ,QAAA,eACnC7B,IAAA,OAAI,WAAS,SAAS,CAAA6B,QAAA,cACpB7B,IAAA,WAAA6B,QAAA,CAAQ,qMAAmC,CAAQ,CAAC,CAClD,CAAC,cAEL7B,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpB8B,SAAS,CAAEhC,MAAM,CAACoC,OAAQ,CACxB,CAAC,cAELlC,IAAA,QACE8B,SAAS,CAAEhC,MAAM,CAACqC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAN,QAAA,cAEpB7B,IAAA,MAAA6B,QAAA,CAAG,21CAMH,CAAG,CAAC,CACD,CAAC,cAEN7B,IAAA,QAAK8B,SAAS,CAAEhC,MAAM,CAACsC,YAAa,CAAAP,QAAA,cAClC3B,KAAA,UAAO4B,SAAS,CAAEhC,MAAM,CAACuC,WAAY,CAAAR,QAAA,eACnC7B,IAAA,UAAA6B,QAAA,cACE3B,KAAA,OAAA2B,QAAA,eACE7B,IAAA,OAAA6B,QAAA,CAAI,sCAAM,CAAI,CAAC,cACf7B,IAAA,OAAA6B,QAAA,CAAI,kDAAQ,CAAI,CAAC,cACjB7B,IAAA,OAAA6B,QAAA,CAAI,yGAAkB,CAAI,CAAC,cAC3B7B,IAAA,OAAA6B,QAAA,CAAI,kFAAe,CAAI,CAAC,cACxB7B,IAAA,OAAA6B,QAAA,CAAI,wFAAgB,CAAI,CAAC,cACzB7B,IAAA,OAAA6B,QAAA,CAAI,mGAAiB,CAAI,CAAC,cAC1B7B,IAAA,OAAA6B,QAAA,CAAI,2HAAqB,CAAI,CAAC,cAC9B7B,IAAA,OAAA6B,QAAA,CAAI,8DAAU,CAAI,CAAC,EACjB,CAAC,CACA,CAAC,cACR7B,IAAA,UAAA6B,QAAA,CACGxB,SAAS,CAACiC,GAAG,CAAEC,IAAI,eAClBrC,KAAA,OAAA2B,QAAA,eACE7B,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAACjC,KAAK,CAAK,CAAC,cACrBN,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAChC,QAAQ,CAAK,CAAC,cACxBP,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC/B,YAAY,CAAK,CAAC,cAC5BR,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC9B,UAAU,CAAK,CAAC,cAC1BT,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC7B,eAAe,CAAK,CAAC,cAC/BV,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC5B,YAAY,CAAK,CAAC,cAC5BX,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC3B,gBAAgB,CAAK,CAAC,cAChCZ,IAAA,OAAA6B,QAAA,CAAKU,IAAI,CAAC1B,KAAK,CAAK,CAAC,GARd0B,IAAI,CAACC,EASV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAA1B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}