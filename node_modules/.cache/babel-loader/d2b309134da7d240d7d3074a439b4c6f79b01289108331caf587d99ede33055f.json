{"ast": null, "code": "import{useRef,useState}from'react';import styles from'../modelPage.module.css';import FilterSwiper from'./FilterSlide/FilterSwiper';import{HiChevronUp,HiChevronDown}from'react-icons/hi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Specifications=_ref=>{var _tableData$activeTab,_tabData$tableBtn$fin;let{title,desc,tabData,tableData}=_ref;const[activeTab,setActiveTab]=useState(1);const[activeTable,setActiveTable]=useState(1);const[isOpen,setIsOpen]=useState(false);const dropdownRef=useRef(null);const currentData=((_tableData$activeTab=tableData[activeTab])===null||_tableData$activeTab===void 0?void 0:_tableData$activeTab[activeTable])||[];const toggleDropdown=()=>{setIsOpen(prev=>!prev);};const handleSelect=id=>{setActiveTable(id);setIsOpen(false);};const activeTableTitle=(_tabData$tableBtn$fin=tabData.tableBtn.find(btn=>btn.id===activeTable))===null||_tabData$tableBtn$fin===void 0?void 0:_tabData$tableBtn$fin.title;return/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"div\",{className:styles.contant,children:/*#__PURE__*/_jsxs(\"div\",{className:\"title\",children:[/*#__PURE__*/_jsx(\"h2\",{children:title}),/*#__PURE__*/_jsx(\"p\",{children:desc})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(FilterSwiper,{activeModel:activeTab,setActiveModel:setActiveTab,cars:tabData.tabsBtn}),/*#__PURE__*/_jsxs(\"div\",{className:styles.tabWrapper,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.tabButtons,children:tabData.tableBtn.map(btn=>/*#__PURE__*/_jsx(\"button\",{className:\"\".concat(styles.tabBtn,\" \").concat(btn.id===activeTable?styles.active:''),onClick:()=>setActiveTable(btn.id),children:btn.title},btn.id))}),/*#__PURE__*/_jsxs(\"div\",{className:styles.dropdownWrapper,children:[/*#__PURE__*/_jsxs(\"div\",{ref:dropdownRef,className:styles.dropdownHeader,onClick:toggleDropdown,children:[/*#__PURE__*/_jsx(\"span\",{children:activeTableTitle}),isOpen?/*#__PURE__*/_jsx(HiChevronUp,{size:20}):/*#__PURE__*/_jsx(HiChevronDown,{size:20})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.dropdownList,\" \").concat(isOpen?styles.dropdownListActive:''),children:tabData.tableBtn.map(btn=>/*#__PURE__*/_jsx(\"button\",{className:styles.dropdownItem,onClick:()=>handleSelect(btn.id),children:btn.title},btn.id))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.tabContent,\" \").concat(isOpen?styles.tabContentActive:''),children:currentData.map((_ref2,idx)=>{let[label,value]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:styles.tabRow,children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.tabCell,\" \").concat(styles.label),children:label}),/*#__PURE__*/_jsx(\"div\",{className:styles.tabCell,children:value})]},idx);})})]})]})]})});};export default Specifications;", "map": {"version": 3, "names": ["useRef", "useState", "styles", "FilterSwiper", "HiChevronUp", "HiChevronDown", "jsx", "_jsx", "jsxs", "_jsxs", "Specifications", "_ref", "_tableData$activeTab", "_tabData$tableBtn$fin", "title", "desc", "tabData", "tableData", "activeTab", "setActiveTab", "activeTable", "setActiveTable", "isOpen", "setIsOpen", "dropdownRef", "currentData", "toggleDropdown", "prev", "handleSelect", "id", "activeTableTitle", "tableBtn", "find", "btn", "className", "section", "children", "contant", "activeModel", "setActiveModel", "cars", "tabsBtn", "tabWrapper", "tabButtons", "map", "concat", "tabBtn", "active", "onClick", "dropdownWrapper", "ref", "dropdownHeader", "size", "dropdownList", "dropdownListActive", "dropdownItem", "tab<PERSON>ontent", "tabContentActive", "_ref2", "idx", "label", "value", "tabRow", "tabCell"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/components/Specifications.jsx"], "sourcesContent": ["import { useRef, useState } from 'react';\nimport styles from '../modelPage.module.css';\nimport FilterSwiper from './FilterSlide/FilterSwiper';\nimport { HiChevronUp, HiChevronDown } from 'react-icons/hi';\n\nconst Specifications = ({ title, desc, tabData, tableData }) => {\n  const [activeTab, setActiveTab] = useState(1);\n  const [activeTable, setActiveTable] = useState(1);\n  const [isOpen, setIsOpen] = useState(false);\n\n  const dropdownRef = useRef(null);\n\n  const currentData = tableData[activeTab]?.[activeTable] || [];\n\n  const toggleDropdown = () => {\n    setIsOpen((prev) => !prev);\n  };\n\n  const handleSelect = (id) => {\n    setActiveTable(id);\n    setIsOpen(false);\n  };\n\n  const activeTableTitle = tabData.tableBtn.find(\n    (btn) => btn.id === activeTable\n  )?.title;\n\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.contant}>\n          <div className=\"title\">\n            <h2>{title}</h2>\n            <p>{desc}</p>\n          </div>\n        </div>\n\n        <div>\n          <FilterSwiper\n            activeModel={activeTab}\n            setActiveModel={setActiveTab}\n            cars={tabData.tabsBtn}\n          />\n\n          <div className={styles.tabWrapper}>\n            {/* Кнопки - десктоп */}\n            <div className={styles.tabButtons}>\n              {tabData.tableBtn.map((btn) => (\n                <button\n                  key={btn.id}\n                  className={`${styles.tabBtn} ${btn.id === activeTable ? styles.active : ''\n                    }`}\n                  onClick={() => setActiveTable(btn.id)}\n                >\n                  {btn.title}\n                </button>\n              ))}\n            </div>\n\n            {/* Мобильный дропдаун */}\n            <div className={styles.dropdownWrapper}>\n              <div\n                ref={dropdownRef}\n                className={styles.dropdownHeader}\n                onClick={toggleDropdown}\n              >\n                <span>{activeTableTitle}</span>\n                {isOpen ? (\n                  <HiChevronUp size={20} />\n                ) : (\n                  <HiChevronDown size={20} />\n                )}\n              </div>\n\n              <div\n                className={`${styles.dropdownList} ${isOpen ? styles.dropdownListActive : ''\n                  }`}\n              >\n                {tabData.tableBtn.map((btn) => (\n                  <button\n                    key={btn.id}\n                    className={styles.dropdownItem}\n                    onClick={() => handleSelect(btn.id)}\n                  >\n                    {btn.title}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Таблица */}\n            <div\n              className={`${styles.tabContent} ${isOpen ? styles.tabContentActive : ''\n                }`}\n            >\n              {currentData.map(([label, value], idx) => (\n                <div className={styles.tabRow} key={idx}>\n                  <div className={`${styles.tabCell} ${styles.label}`}>\n                    {label}\n                  </div>\n                  <div className={styles.tabCell}>{value}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Specifications;\n"], "mappings": "AAAA,OAASA,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CACxC,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CACrD,OAASC,WAAW,CAAEC,aAAa,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAyC,KAAAC,oBAAA,CAAAC,qBAAA,IAAxC,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAO,CAAEC,SAAU,CAAC,CAAAN,IAAA,CACzD,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACqB,MAAM,CAAEC,SAAS,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAAAuB,WAAW,CAAGxB,MAAM,CAAC,IAAI,CAAC,CAEhC,KAAM,CAAAyB,WAAW,CAAG,EAAAb,oBAAA,CAAAK,SAAS,CAACC,SAAS,CAAC,UAAAN,oBAAA,iBAApBA,oBAAA,CAAuBQ,WAAW,CAAC,GAAI,EAAE,CAE7D,KAAM,CAAAM,cAAc,CAAGA,CAAA,GAAM,CAC3BH,SAAS,CAAEI,IAAI,EAAK,CAACA,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAC,YAAY,CAAIC,EAAE,EAAK,CAC3BR,cAAc,CAACQ,EAAE,CAAC,CAClBN,SAAS,CAAC,KAAK,CAAC,CAClB,CAAC,CAED,KAAM,CAAAO,gBAAgB,EAAAjB,qBAAA,CAAGG,OAAO,CAACe,QAAQ,CAACC,IAAI,CAC3CC,GAAG,EAAKA,GAAG,CAACJ,EAAE,GAAKT,WACtB,CAAC,UAAAP,qBAAA,iBAFwBA,qBAAA,CAEtBC,KAAK,CAER,mBACEP,IAAA,YAAS2B,SAAS,CAAEhC,MAAM,CAACiC,OAAQ,CAAAC,QAAA,cACjC3B,KAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxB7B,IAAA,QAAK2B,SAAS,CAAEhC,MAAM,CAACmC,OAAQ,CAAAD,QAAA,cAC7B3B,KAAA,QAAKyB,SAAS,CAAC,OAAO,CAAAE,QAAA,eACpB7B,IAAA,OAAA6B,QAAA,CAAKtB,KAAK,CAAK,CAAC,cAChBP,IAAA,MAAA6B,QAAA,CAAIrB,IAAI,CAAI,CAAC,EACV,CAAC,CACH,CAAC,cAENN,KAAA,QAAA2B,QAAA,eACE7B,IAAA,CAACJ,YAAY,EACXmC,WAAW,CAAEpB,SAAU,CACvBqB,cAAc,CAAEpB,YAAa,CAC7BqB,IAAI,CAAExB,OAAO,CAACyB,OAAQ,CACvB,CAAC,cAEFhC,KAAA,QAAKyB,SAAS,CAAEhC,MAAM,CAACwC,UAAW,CAAAN,QAAA,eAEhC7B,IAAA,QAAK2B,SAAS,CAAEhC,MAAM,CAACyC,UAAW,CAAAP,QAAA,CAC/BpB,OAAO,CAACe,QAAQ,CAACa,GAAG,CAAEX,GAAG,eACxB1B,IAAA,WAEE2B,SAAS,IAAAW,MAAA,CAAK3C,MAAM,CAAC4C,MAAM,MAAAD,MAAA,CAAIZ,GAAG,CAACJ,EAAE,GAAKT,WAAW,CAAGlB,MAAM,CAAC6C,MAAM,CAAG,EAAE,CACrE,CACLC,OAAO,CAAEA,CAAA,GAAM3B,cAAc,CAACY,GAAG,CAACJ,EAAE,CAAE,CAAAO,QAAA,CAErCH,GAAG,CAACnB,KAAK,EALLmB,GAAG,CAACJ,EAMH,CACT,CAAC,CACC,CAAC,cAGNpB,KAAA,QAAKyB,SAAS,CAAEhC,MAAM,CAAC+C,eAAgB,CAAAb,QAAA,eACrC3B,KAAA,QACEyC,GAAG,CAAE1B,WAAY,CACjBU,SAAS,CAAEhC,MAAM,CAACiD,cAAe,CACjCH,OAAO,CAAEtB,cAAe,CAAAU,QAAA,eAExB7B,IAAA,SAAA6B,QAAA,CAAON,gBAAgB,CAAO,CAAC,CAC9BR,MAAM,cACLf,IAAA,CAACH,WAAW,EAACgD,IAAI,CAAE,EAAG,CAAE,CAAC,cAEzB7C,IAAA,CAACF,aAAa,EAAC+C,IAAI,CAAE,EAAG,CAAE,CAC3B,EACE,CAAC,cAEN7C,IAAA,QACE2B,SAAS,IAAAW,MAAA,CAAK3C,MAAM,CAACmD,YAAY,MAAAR,MAAA,CAAIvB,MAAM,CAAGpB,MAAM,CAACoD,kBAAkB,CAAG,EAAE,CACvE,CAAAlB,QAAA,CAEJpB,OAAO,CAACe,QAAQ,CAACa,GAAG,CAAEX,GAAG,eACxB1B,IAAA,WAEE2B,SAAS,CAAEhC,MAAM,CAACqD,YAAa,CAC/BP,OAAO,CAAEA,CAAA,GAAMpB,YAAY,CAACK,GAAG,CAACJ,EAAE,CAAE,CAAAO,QAAA,CAEnCH,GAAG,CAACnB,KAAK,EAJLmB,GAAG,CAACJ,EAKH,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNtB,IAAA,QACE2B,SAAS,IAAAW,MAAA,CAAK3C,MAAM,CAACsD,UAAU,MAAAX,MAAA,CAAIvB,MAAM,CAAGpB,MAAM,CAACuD,gBAAgB,CAAG,EAAE,CACnE,CAAArB,QAAA,CAEJX,WAAW,CAACmB,GAAG,CAAC,CAAAc,KAAA,CAAiBC,GAAG,OAAnB,CAACC,KAAK,CAAEC,KAAK,CAAC,CAAAH,KAAA,oBAC9BjD,KAAA,QAAKyB,SAAS,CAAEhC,MAAM,CAAC4D,MAAO,CAAA1B,QAAA,eAC5B7B,IAAA,QAAK2B,SAAS,IAAAW,MAAA,CAAK3C,MAAM,CAAC6D,OAAO,MAAAlB,MAAA,CAAI3C,MAAM,CAAC0D,KAAK,CAAG,CAAAxB,QAAA,CACjDwB,KAAK,CACH,CAAC,cACNrD,IAAA,QAAK2B,SAAS,CAAEhC,MAAM,CAAC6D,OAAQ,CAAA3B,QAAA,CAAEyB,KAAK,CAAM,CAAC,GAJXF,GAK/B,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAjD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}