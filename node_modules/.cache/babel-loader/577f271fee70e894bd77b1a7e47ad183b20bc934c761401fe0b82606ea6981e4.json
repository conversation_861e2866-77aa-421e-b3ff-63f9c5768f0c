{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Models/Models.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState, useMemo } from 'react';\nimport styles from './models.module.css';\nimport { Link } from 'react-router-dom';\nimport ToolBar from '../../components/ToolBar/ToolBar';\nimport Notification from '../../components/Notification/Notification';\nimport FilterSwiper from '../../components/FilterSlide/FilterSwiper';\nimport SkeletonCard from '../../components/SkeletonCard/SkeletonCard';\nimport SEO from '../../hooks/useSEO';\nimport seoData from '../../data/seoData';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst typeOptions = [{\n  id: 1,\n  title: 'Электрический'\n}, {\n  id: 2,\n  title: 'Гибрид'\n}, {\n  id: 3,\n  title: 'Бензин'\n}, {\n  id: 4,\n  title: 'Дизель'\n}];\nconst bodyTypeOptions = [{\n  id: 1,\n  title: 'Седан'\n}, {\n  id: 2,\n  title: 'Кроссовер'\n}, {\n  id: 3,\n  title: 'Внедорожник'\n}, {\n  id: 4,\n  title: 'Пикапы'\n}];\nconst noCarMessage = 'К сожалению, модель, которую вы ищете, в настоящее время недоступна. Попробуйте использовать другие критерии поиска.';\nconst Models = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [activeType, setActiveType] = useState(null);\n  const [activeBodyType, setActiveBodyType] = useState(null);\n  const [activeModel, setActiveModel] = useState('Все модели');\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n  const [cars, setCars] = useState([]);\n  const [loadingCard, setLoadingCard] = useState(true);\n  const [notification, setNotification] = useState({\n    message: '',\n    type: ''\n  });\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification({\n      message: '',\n      type: ''\n    }), 3000);\n  };\n  useEffect(() => {\n    // AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  useEffect(() => {\n    const fetchCars = async () => {\n      try {\n        const response = await fetch('https://api.gwm.tj/api/v1/models');\n        const data = await response.json();\n        setCars(data.models);\n      } catch (error) {\n        showNotification('Не удалось загрузить список.', 'error');\n      } finally {\n        setLoadingCard(false);\n      }\n    };\n    fetchCars();\n  }, []);\n  const filteredCars = useMemo(() => {\n    return cars.filter(car => {\n      var _typeOptions$find, _car$type, _bodyTypeOptions$find, _car$body_type;\n      const matchModel = activeModel === 'Все модели' || car.category === activeModel;\n      const matchType = !activeType || ((_typeOptions$find = typeOptions.find(t => t.id === activeType)) === null || _typeOptions$find === void 0 ? void 0 : _typeOptions$find.title.toLowerCase()) === ((_car$type = car.type) === null || _car$type === void 0 ? void 0 : _car$type.toLowerCase());\n      const matchBody = !activeBodyType || ((_bodyTypeOptions$find = bodyTypeOptions.find(b => b.id === activeBodyType)) === null || _bodyTypeOptions$find === void 0 ? void 0 : _bodyTypeOptions$find.title.toLowerCase()) === ((_car$body_type = car.body_type) === null || _car$body_type === void 0 ? void 0 : _car$body_type.toLowerCase());\n      return matchModel && matchType && matchBody;\n    });\n  }, [cars, activeModel, activeType, activeBodyType]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SEO, {\n      ...seoData.models\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Notification, {\n      message: notification.message,\n      type: notification.type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"topmenu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u041C\\u041E\\u0414\\u0415\\u041B\\u0418 GWM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"redLine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.modelFilter,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.setting,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.left,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u041F\\u0430\\u0440\\u0430\\u043C\\u0435\\u0442\\u0440\\u044B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: styles.types,\n                children: typeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: activeType === item.id ? styles.active : '',\n                  onClick: () => setActiveType(activeType === item.id ? null : item.id),\n                  children: item.title\n                }, item.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.right,\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\u0422\\u0438\\u043F \\u043A\\u0443\\u0437\\u043E\\u0432\\u0430\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: styles.bodyTypes,\n                children: bodyTypeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: activeBodyType === item.id ? styles.active : '',\n                  onClick: () => setActiveBodyType(activeBodyType === item.id ? null : item.id),\n                  children: item.title\n                }, item.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.filterBar,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.filterSelected,\n            onClick: () => setIsFilterOpen(prev => !prev),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0424\\u0438\\u043B\\u044C\\u0442\\u0440\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: isFilterOpen ? styles.iconMinus : styles.iconPlus,\n              children: isFilterOpen ? '-' : '+'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${styles.filterDropdownContainer} ${isFilterOpen ? styles.show : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.filterList,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.filterItem,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.mobseting,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.left,\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"\\u041F\\u0430\\u0440\\u0430\\u043C\\u0435\\u0442\\u0440\\u044B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: styles.types,\n                      children: typeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: activeType === item.id ? styles.active : '',\n                        onClick: () => setActiveType(activeType === item.id ? null : item.id),\n                        children: item.title\n                      }, item.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.right,\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: \"\\u0422\\u0438\\u043F \\u043A\\u0443\\u0437\\u043E\\u0432\\u0430\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                      className: styles.bodyTypes,\n                      children: bodyTypeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                        className: activeBodyType === item.id ? styles.active : '',\n                        onClick: () => setActiveBodyType(activeBodyType === item.id ? null : item.id),\n                        children: item.title\n                      }, item.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FilterSwiper, {\n          activeModel: activeModel,\n          setActiveModel: setActiveModel,\n          cars: cars\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.grid,\n          children: loadingCard ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: Array.from({\n              length: 6\n            }).map((_, index) => /*#__PURE__*/_jsxDEV(SkeletonCard, {}, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 21\n            }, this))\n          }, void 0, false) : filteredCars.length > 0 ? filteredCars.map(car => /*#__PURE__*/_jsxDEV(Link, {\n            to: car.slug,\n            className: styles.card,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: car.preview_show,\n              alt: car.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.cardInfo,\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: car.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 21\n            }, this), car.in_stock && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: car.in_stock === 'Yes' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.inStok,\n                children: \"\\u0412 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0435\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 27\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.noStok,\n                children: \"\\u041D\\u0435 \\u0432 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0438\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.label,\n              children: car.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 21\n            }, this)]\n          }, car.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 19\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.noCarText,\n            children: noCarMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ToolBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Models, \"miHDSCXlTLPyLSKjRF4qr5KIoPw=\");\n_c = Models;\nexport default Models;\nvar _c;\n$RefreshReg$(_c, \"Models\");", "map": {"version": 3, "names": ["useEffect", "useState", "useMemo", "styles", "Link", "<PERSON><PERSON><PERSON><PERSON>", "Notification", "FilterSwiper", "SkeletonCard", "SEO", "seoData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "typeOptions", "id", "title", "bodyTypeOptions", "noCarMessage", "Models", "_s", "loading", "setLoading", "activeType", "setActiveType", "activeBodyType", "setActiveBodyType", "activeModel", "setActiveModel", "isFilterOpen", "setIsFilterOpen", "cars", "setCars", "loadingCard", "setLoadingCard", "notification", "setNotification", "message", "type", "showNotification", "setTimeout", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "clearTimeout", "fetchCars", "response", "fetch", "data", "json", "models", "error", "filteredCars", "filter", "car", "_typeOptions$find", "_car$type", "_bodyTypeOptions$find", "_car$body_type", "matchModel", "category", "matchType", "find", "t", "toLowerCase", "matchBody", "b", "body_type", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "modelFilter", "setting", "left", "types", "map", "item", "active", "onClick", "right", "bodyTypes", "filterBar", "filterSelected", "prev", "iconMinus", "iconPlus", "filterDropdownContainer", "show", "filterList", "filterItem", "mobseting", "grid", "Array", "from", "length", "_", "index", "to", "slug", "card", "src", "preview_show", "alt", "cardInfo", "in_stock", "inStok", "noStok", "label", "noCarText", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Models.jsx"], "sourcesContent": ["import { useEffect, useState, useMemo } from 'react';\nimport styles from './models.module.css';\nimport { Link } from 'react-router-dom';\nimport ToolBar from '../../components/ToolBar/ToolBar';\nimport Notification from '../../components/Notification/Notification';\nimport FilterSwiper from '../../components/FilterSlide/FilterSwiper';\nimport SkeletonCard from '../../components/SkeletonCard/SkeletonCard';\nimport SEO from '../../hooks/useSEO';\nimport seoData from '../../data/seoData';\n\nconst typeOptions = [\n  { id: 1, title: 'Электрический' },\n  { id: 2, title: 'Гибрид' },\n  { id: 3, title: 'Бензин' },\n  { id: 4, title: 'Дизель' },\n];\n\nconst bodyTypeOptions = [\n  { id: 1, title: 'Седан' },\n  { id: 2, title: 'Кроссовер' },\n  { id: 3, title: 'Внедорожник' },\n  { id: 4, title: 'Пикапы' },\n];\n\nconst noCarMessage =\n  'К сожалению, модель, которую вы ищете, в настоящее время недоступна. Попробуйте использовать другие критерии поиска.';\n\nconst Models = () => {\n  const [loading, setLoading] = useState(true);\n  const [activeType, setActiveType] = useState(null);\n  const [activeBodyType, setActiveBodyType] = useState(null);\n  const [activeModel, setActiveModel] = useState('Все модели');\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n  const [cars, setCars] = useState([]);\n  const [loadingCard, setLoadingCard] = useState(true);\n  const [notification, setNotification] = useState({ message: '', type: '' });\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  };\n\n  useEffect(() => {\n    // AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  useEffect(() => {\n    const fetchCars = async () => {\n      try {\n        const response = await fetch('https://api.gwm.tj/api/v1/models');\n        const data = await response.json();\n\n        setCars(data.models);\n      } catch (error) {\n        showNotification('Не удалось загрузить список.', 'error');\n      } finally {\n        setLoadingCard(false);\n      }\n    };\n\n    fetchCars();\n  }, []);\n\n  const filteredCars = useMemo(() => {\n    return cars.filter((car) => {\n      const matchModel =\n        activeModel === 'Все модели' || car.category === activeModel;\n\n      const matchType =\n        !activeType ||\n        typeOptions.find((t) => t.id === activeType)?.title.toLowerCase() ===\n          car.type?.toLowerCase();\n\n      const matchBody =\n        !activeBodyType ||\n        bodyTypeOptions\n          .find((b) => b.id === activeBodyType)\n          ?.title.toLowerCase() === car.body_type?.toLowerCase();\n\n      return matchModel && matchType && matchBody;\n    });\n  }, [cars, activeModel, activeType, activeBodyType]);\n\n  return (\n    <>\n      <SEO {...seoData.models} />\n      <Notification message={notification.message} type={notification.type} />\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <main className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\">\n                <strong>МОДЕЛИ GWM</strong>\n              </h1>\n              <i className=\"redLine\" />\n            </div>\n            {/* pc filter  */}\n            <div className={styles.modelFilter}>\n              <div className={styles.setting}>\n                <div className={styles.left}>\n                  <h4>Параметры</h4>\n\n                  <ul className={styles.types}>\n                    {typeOptions.map((item) => (\n                      <li\n                        key={item.id}\n                        className={activeType === item.id ? styles.active : ''}\n                        onClick={() =>\n                          setActiveType(activeType === item.id ? null : item.id)\n                        }\n                      >\n                        {item.title}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                <div className={styles.right}>\n                  <h4>Тип кузова</h4>\n                  <ul className={styles.bodyTypes}>\n                    {bodyTypeOptions.map((item) => (\n                      <li\n                        key={item.id}\n                        className={\n                          activeBodyType === item.id ? styles.active : ''\n                        }\n                        onClick={() =>\n                          setActiveBodyType(\n                            activeBodyType === item.id ? null : item.id\n                          )\n                        }\n                      >\n                        {item.title}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            {/* mobile filter bar  */}\n            <div className={styles.filterBar}>\n              <div\n                className={styles.filterSelected}\n                onClick={() => setIsFilterOpen((prev) => !prev)}\n              >\n                <span>Фильтр</span>\n                <div\n                  className={isFilterOpen ? styles.iconMinus : styles.iconPlus}\n                >\n                  {isFilterOpen ? '-' : '+'}\n                </div>\n              </div>\n\n              <div\n                className={`${styles.filterDropdownContainer} ${\n                  isFilterOpen ? styles.show : ''\n                }`}\n              >\n                <div className={styles.filterList}>\n                  <div className={styles.filterItem}>\n                    <div className={styles.mobseting}>\n                      <div className={styles.left}>\n                        <h4>Параметры</h4>\n\n                        <ul className={styles.types}>\n                          {typeOptions.map((item) => (\n                            <li\n                              key={item.id}\n                              className={\n                                activeType === item.id ? styles.active : ''\n                              }\n                              onClick={() =>\n                                setActiveType(\n                                  activeType === item.id ? null : item.id\n                                )\n                              }\n                            >\n                              {item.title}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n\n                      <div className={styles.right}>\n                        <h4>Тип кузова</h4>\n                        <ul className={styles.bodyTypes}>\n                          {bodyTypeOptions.map((item) => (\n                            <li\n                              key={item.id}\n                              className={\n                                activeBodyType === item.id ? styles.active : ''\n                              }\n                              onClick={() =>\n                                setActiveBodyType(\n                                  activeBodyType === item.id ? null : item.id\n                                )\n                              }\n                            >\n                              {item.title}\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <FilterSwiper\n              activeModel={activeModel}\n              setActiveModel={setActiveModel}\n              cars={cars}\n            />\n\n            <div className={styles.grid}>\n              {loadingCard ? (\n                <>\n                  {Array.from({ length: 6 }).map((_, index) => (\n                    <SkeletonCard key={index} />\n                  ))}\n                </>\n              ) : filteredCars.length > 0 ? (\n                filteredCars.map((car) => (\n                  <Link to={car.slug} key={car.id} className={styles.card}>\n                    <img src={car.preview_show} alt={car.title} />\n                    <div className={styles.cardInfo}>\n                      <h4>{car.title}</h4>\n                    </div>\n                    {car.in_stock && (\n                      <div>\n                        {car.in_stock === 'Yes' ? (\n                          <span className={styles.inStok}>В наличие</span>\n                        ) : (\n                          <span className={styles.noStok}>Не в наличии</span>\n                        )}\n                      </div>\n                    )}\n                    <span className={styles.label}>{car.type}</span>\n                  </Link>\n                ))\n              ) : (\n                <div className={styles.noCarText}>{noCarMessage}</div>\n              )}\n            </div>\n          </div>\n          {/* <SimpleCard /> */}\n          <ToolBar />\n        </main>\n      )}\n    </>\n  );\n};\n\nexport default Models;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AACpD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,YAAY,MAAM,4CAA4C;AACrE,OAAOC,YAAY,MAAM,2CAA2C;AACpE,OAAOC,YAAY,MAAM,4CAA4C;AACrE,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAOC,OAAO,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,WAAW,GAAG,CAClB;EAAEC,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACjC;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,CAC3B;AAED,MAAMC,eAAe,GAAG,CACtB;EAAEF,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACzB;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAY,CAAC,EAC7B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC/B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,CAC3B;AAED,MAAME,YAAY,GAChB,sHAAsH;AAExH,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,YAAY,CAAC;EAC5D,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+B,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC;IAAEqC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE3E,MAAMC,gBAAgB,GAAGA,CAACF,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDF,eAAe,CAAC;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCE,UAAU,CAAC,MAAMJ,eAAe,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACd;IACA0C,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGP,UAAU,CAAC,MAAM;MAC7BlB,UAAU,CAAC,KAAK,CAAC;MACjBqB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXE,YAAY,CAACD,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN/C,SAAS,CAAC,MAAM;IACd,MAAMkD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElCrB,OAAO,CAACoB,IAAI,CAACE,MAAM,CAAC;MACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdhB,gBAAgB,CAAC,8BAA8B,EAAE,OAAO,CAAC;MAC3D,CAAC,SAAS;QACRL,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAEDe,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,YAAY,GAAGvD,OAAO,CAAC,MAAM;IACjC,OAAO8B,IAAI,CAAC0B,MAAM,CAAEC,GAAG,IAAK;MAAA,IAAAC,iBAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,cAAA;MAC1B,MAAMC,UAAU,GACdpC,WAAW,KAAK,YAAY,IAAI+B,GAAG,CAACM,QAAQ,KAAKrC,WAAW;MAE9D,MAAMsC,SAAS,GACb,CAAC1C,UAAU,IACX,EAAAoC,iBAAA,GAAA7C,WAAW,CAACoD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpD,EAAE,KAAKQ,UAAU,CAAC,cAAAoC,iBAAA,uBAA5CA,iBAAA,CAA8C3C,KAAK,CAACoD,WAAW,CAAC,CAAC,QAAAR,SAAA,GAC/DF,GAAG,CAACpB,IAAI,cAAAsB,SAAA,uBAARA,SAAA,CAAUQ,WAAW,CAAC,CAAC;MAE3B,MAAMC,SAAS,GACb,CAAC5C,cAAc,IACf,EAAAoC,qBAAA,GAAA5C,eAAe,CACZiD,IAAI,CAAEI,CAAC,IAAKA,CAAC,CAACvD,EAAE,KAAKU,cAAc,CAAC,cAAAoC,qBAAA,uBADvCA,qBAAA,CAEI7C,KAAK,CAACoD,WAAW,CAAC,CAAC,QAAAN,cAAA,GAAKJ,GAAG,CAACa,SAAS,cAAAT,cAAA,uBAAbA,cAAA,CAAeM,WAAW,CAAC,CAAC;MAE1D,OAAOL,UAAU,IAAIE,SAAS,IAAII,SAAS;IAC7C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtC,IAAI,EAAEJ,WAAW,EAAEJ,UAAU,EAAEE,cAAc,CAAC,CAAC;EAEnD,oBACEd,OAAA,CAAAE,SAAA;IAAA2D,QAAA,gBACE7D,OAAA,CAACH,GAAG;MAAA,GAAKC,OAAO,CAAC6C;IAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC3BjE,OAAA,CAACN,YAAY;MAACgC,OAAO,EAAEF,YAAY,CAACE,OAAQ;MAACC,IAAI,EAAEH,YAAY,CAACG;IAAK;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvEvD,OAAO,gBACNV,OAAA;MAAKkE,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC5B7D,OAAA;QAAKkE,SAAS,EAAC;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENjE,OAAA;MAAMkE,SAAS,EAAC,SAAS;MAAAL,QAAA,gBACvB7D,OAAA;QAAKkE,SAAS,EAAC,WAAW;QAAAL,QAAA,gBACxB7D,OAAA;UAAKkE,SAAS,EAAC,SAAS;UAAAL,QAAA,gBACtB7D,OAAA;YAAIkE,SAAS,EAAC,OAAO;YAAAL,QAAA,eACnB7D,OAAA;cAAA6D,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACLjE,OAAA;YAAGkE,SAAS,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAENjE,OAAA;UAAKkE,SAAS,EAAE3E,MAAM,CAAC4E,WAAY;UAAAN,QAAA,eACjC7D,OAAA;YAAKkE,SAAS,EAAE3E,MAAM,CAAC6E,OAAQ;YAAAP,QAAA,gBAC7B7D,OAAA;cAAKkE,SAAS,EAAE3E,MAAM,CAAC8E,IAAK;cAAAR,QAAA,gBAC1B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAElBjE,OAAA;gBAAIkE,SAAS,EAAE3E,MAAM,CAAC+E,KAAM;gBAAAT,QAAA,EACzB1D,WAAW,CAACoE,GAAG,CAAEC,IAAI,iBACpBxE,OAAA;kBAEEkE,SAAS,EAAEtD,UAAU,KAAK4D,IAAI,CAACpE,EAAE,GAAGb,MAAM,CAACkF,MAAM,GAAG,EAAG;kBACvDC,OAAO,EAAEA,CAAA,KACP7D,aAAa,CAACD,UAAU,KAAK4D,IAAI,CAACpE,EAAE,GAAG,IAAI,GAAGoE,IAAI,CAACpE,EAAE,CACtD;kBAAAyD,QAAA,EAEAW,IAAI,CAACnE;gBAAK,GANNmE,IAAI,CAACpE,EAAE;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENjE,OAAA;cAAKkE,SAAS,EAAE3E,MAAM,CAACoF,KAAM;cAAAd,QAAA,gBAC3B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBjE,OAAA;gBAAIkE,SAAS,EAAE3E,MAAM,CAACqF,SAAU;gBAAAf,QAAA,EAC7BvD,eAAe,CAACiE,GAAG,CAAEC,IAAI,iBACxBxE,OAAA;kBAEEkE,SAAS,EACPpD,cAAc,KAAK0D,IAAI,CAACpE,EAAE,GAAGb,MAAM,CAACkF,MAAM,GAAG,EAC9C;kBACDC,OAAO,EAAEA,CAAA,KACP3D,iBAAiB,CACfD,cAAc,KAAK0D,IAAI,CAACpE,EAAE,GAAG,IAAI,GAAGoE,IAAI,CAACpE,EAC3C,CACD;kBAAAyD,QAAA,EAEAW,IAAI,CAACnE;gBAAK,GAVNmE,IAAI,CAACpE,EAAE;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjE,OAAA;UAAKkE,SAAS,EAAE3E,MAAM,CAACsF,SAAU;UAAAhB,QAAA,gBAC/B7D,OAAA;YACEkE,SAAS,EAAE3E,MAAM,CAACuF,cAAe;YACjCJ,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAAE4D,IAAI,IAAK,CAACA,IAAI,CAAE;YAAAlB,QAAA,gBAEhD7D,OAAA;cAAA6D,QAAA,EAAM;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBjE,OAAA;cACEkE,SAAS,EAAEhD,YAAY,GAAG3B,MAAM,CAACyF,SAAS,GAAGzF,MAAM,CAAC0F,QAAS;cAAApB,QAAA,EAE5D3C,YAAY,GAAG,GAAG,GAAG;YAAG;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YACEkE,SAAS,EAAE,GAAG3E,MAAM,CAAC2F,uBAAuB,IAC1ChE,YAAY,GAAG3B,MAAM,CAAC4F,IAAI,GAAG,EAAE,EAC9B;YAAAtB,QAAA,eAEH7D,OAAA;cAAKkE,SAAS,EAAE3E,MAAM,CAAC6F,UAAW;cAAAvB,QAAA,eAChC7D,OAAA;gBAAKkE,SAAS,EAAE3E,MAAM,CAAC8F,UAAW;gBAAAxB,QAAA,eAChC7D,OAAA;kBAAKkE,SAAS,EAAE3E,MAAM,CAAC+F,SAAU;kBAAAzB,QAAA,gBAC/B7D,OAAA;oBAAKkE,SAAS,EAAE3E,MAAM,CAAC8E,IAAK;oBAAAR,QAAA,gBAC1B7D,OAAA;sBAAA6D,QAAA,EAAI;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAElBjE,OAAA;sBAAIkE,SAAS,EAAE3E,MAAM,CAAC+E,KAAM;sBAAAT,QAAA,EACzB1D,WAAW,CAACoE,GAAG,CAAEC,IAAI,iBACpBxE,OAAA;wBAEEkE,SAAS,EACPtD,UAAU,KAAK4D,IAAI,CAACpE,EAAE,GAAGb,MAAM,CAACkF,MAAM,GAAG,EAC1C;wBACDC,OAAO,EAAEA,CAAA,KACP7D,aAAa,CACXD,UAAU,KAAK4D,IAAI,CAACpE,EAAE,GAAG,IAAI,GAAGoE,IAAI,CAACpE,EACvC,CACD;wBAAAyD,QAAA,EAEAW,IAAI,CAACnE;sBAAK,GAVNmE,IAAI,CAACpE,EAAE;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWV,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAENjE,OAAA;oBAAKkE,SAAS,EAAE3E,MAAM,CAACoF,KAAM;oBAAAd,QAAA,gBAC3B7D,OAAA;sBAAA6D,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBjE,OAAA;sBAAIkE,SAAS,EAAE3E,MAAM,CAACqF,SAAU;sBAAAf,QAAA,EAC7BvD,eAAe,CAACiE,GAAG,CAAEC,IAAI,iBACxBxE,OAAA;wBAEEkE,SAAS,EACPpD,cAAc,KAAK0D,IAAI,CAACpE,EAAE,GAAGb,MAAM,CAACkF,MAAM,GAAG,EAC9C;wBACDC,OAAO,EAAEA,CAAA,KACP3D,iBAAiB,CACfD,cAAc,KAAK0D,IAAI,CAACpE,EAAE,GAAG,IAAI,GAAGoE,IAAI,CAACpE,EAC3C,CACD;wBAAAyD,QAAA,EAEAW,IAAI,CAACnE;sBAAK,GAVNmE,IAAI,CAACpE,EAAE;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWV,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjE,OAAA,CAACL,YAAY;UACXqB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BG,IAAI,EAAEA;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEFjE,OAAA;UAAKkE,SAAS,EAAE3E,MAAM,CAACgG,IAAK;UAAA1B,QAAA,EACzBvC,WAAW,gBACVtB,OAAA,CAAAE,SAAA;YAAA2D,QAAA,EACG2B,KAAK,CAACC,IAAI,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC,CAAC,CAACnB,GAAG,CAAC,CAACoB,CAAC,EAAEC,KAAK,kBACtC5F,OAAA,CAACJ,YAAY,MAAMgG,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC5B;UAAC,gBACF,CAAC,GACDpB,YAAY,CAAC6C,MAAM,GAAG,CAAC,GACzB7C,YAAY,CAAC0B,GAAG,CAAExB,GAAG,iBACnB/C,OAAA,CAACR,IAAI;YAACqG,EAAE,EAAE9C,GAAG,CAAC+C,IAAK;YAAc5B,SAAS,EAAE3E,MAAM,CAACwG,IAAK;YAAAlC,QAAA,gBACtD7D,OAAA;cAAKgG,GAAG,EAAEjD,GAAG,CAACkD,YAAa;cAACC,GAAG,EAAEnD,GAAG,CAAC1C;YAAM;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CjE,OAAA;cAAKkE,SAAS,EAAE3E,MAAM,CAAC4G,QAAS;cAAAtC,QAAA,eAC9B7D,OAAA;gBAAA6D,QAAA,EAAKd,GAAG,CAAC1C;cAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACLlB,GAAG,CAACqD,QAAQ,iBACXpG,OAAA;cAAA6D,QAAA,EACGd,GAAG,CAACqD,QAAQ,KAAK,KAAK,gBACrBpG,OAAA;gBAAMkE,SAAS,EAAE3E,MAAM,CAAC8G,MAAO;gBAAAxC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEhDjE,OAAA;gBAAMkE,SAAS,EAAE3E,MAAM,CAAC+G,MAAO;gBAAAzC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACnD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eACDjE,OAAA;cAAMkE,SAAS,EAAE3E,MAAM,CAACgH,KAAM;cAAA1C,QAAA,EAAEd,GAAG,CAACpB;YAAI;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAdzBlB,GAAG,CAAC3C,EAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAezB,CACP,CAAC,gBAEFjE,OAAA;YAAKkE,SAAS,EAAE3E,MAAM,CAACiH,SAAU;YAAA3C,QAAA,EAAEtD;UAAY;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACtD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAACP,OAAO;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACP;EAAA,eACD,CAAC;AAEP,CAAC;AAACxD,EAAA,CAlPID,MAAM;AAAAiG,EAAA,GAANjG,MAAM;AAoPZ,eAAeA,MAAM;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}