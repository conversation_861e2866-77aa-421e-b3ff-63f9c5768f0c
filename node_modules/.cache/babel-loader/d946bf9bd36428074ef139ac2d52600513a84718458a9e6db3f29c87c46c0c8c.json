{"ast": null, "code": "import React from'react';import styles from'./header.module.css';// Используем тот же CSS-модуль\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SkeletonSlide=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.slide,\" \").concat(styles.skeletonSlide),children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.slideContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.skeletonTag}),/*#__PURE__*/_jsx(\"div\",{className:styles.skeletonTitle}),/*#__PURE__*/_jsx(\"div\",{className:styles.skeletonDescription}),/*#__PURE__*/_jsx(\"div\",{className:styles.skeletonButton})]})})});};export default SkeletonSlide;", "map": {"version": 3, "names": ["React", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "SkeletonSlide", "className", "concat", "slide", "skeletonSlide", "children", "slideContent", "skeletonTag", "skeleton<PERSON>itle", "skeletonDescription", "skeleton<PERSON><PERSON><PERSON>"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/header-slider/SkeletonSlide.jsx"], "sourcesContent": ["import React from 'react';\nimport styles from './header.module.css'; // Используем тот же CSS-модуль\n\nconst SkeletonSlide = () => {\n  return (\n    <div className={`${styles.slide} ${styles.skeletonSlide}`}>\n      <div className=\"container\">\n        <div className={styles.slideContent}>\n          <div className={styles.skeletonTag} />\n          <div className={styles.skeletonTitle} />\n          <div className={styles.skeletonDescription} />\n          <div className={styles.skeletonButton} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SkeletonSlide;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,mBACEH,IAAA,QAAKI,SAAS,IAAAC,MAAA,CAAKP,MAAM,CAACQ,KAAK,MAAAD,MAAA,CAAIP,MAAM,CAACS,aAAa,CAAG,CAAAC,QAAA,cACxDR,IAAA,QAAKI,SAAS,CAAC,WAAW,CAAAI,QAAA,cACxBN,KAAA,QAAKE,SAAS,CAAEN,MAAM,CAACW,YAAa,CAAAD,QAAA,eAClCR,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACY,WAAY,CAAE,CAAC,cACtCV,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACa,aAAc,CAAE,CAAC,cACxCX,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACc,mBAAoB,CAAE,CAAC,cAC9CZ,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACe,cAAe,CAAE,CAAC,EACtC,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}