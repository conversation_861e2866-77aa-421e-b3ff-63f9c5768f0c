{"ast": null, "code": "import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\nfunction parseHex(v) {\n  let r = \"\";\n  let g = \"\";\n  let b = \"\";\n  let a = \"\";\n  // If we have 6 characters, ie #FF0000\n  if (v.length > 5) {\n    r = v.substring(1, 3);\n    g = v.substring(3, 5);\n    b = v.substring(5, 7);\n    a = v.substring(7, 9);\n    // Or we have 3 characters, ie #F00\n  } else {\n    r = v.substring(1, 2);\n    g = v.substring(2, 3);\n    b = v.substring(3, 4);\n    a = v.substring(4, 5);\n    r += r;\n    g += g;\n    b += b;\n    a += a;\n  }\n  return {\n    red: parseInt(r, 16),\n    green: parseInt(g, 16),\n    blue: parseInt(b, 16),\n    alpha: a ? parseInt(a, 16) / 255 : 1\n  };\n}\nconst hex = {\n  test: /*@__PURE__*/isColorString(\"#\"),\n  parse: parseHex,\n  transform: rgba.transform\n};\nexport { hex };", "map": {"version": 3, "names": ["rgba", "isColorString", "parseHex", "v", "r", "g", "b", "a", "length", "substring", "red", "parseInt", "green", "blue", "alpha", "hex", "test", "parse", "transform"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/value/types/color/hex.mjs"], "sourcesContent": ["import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: /*@__PURE__*/ isColorString(\"#\"),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AACjC,SAASC,aAAa,QAAQ,aAAa;AAE3C,SAASC,QAAQA,CAACC,CAAC,EAAE;EACjB,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,CAAC,GAAG,EAAE;EACV;EACA,IAAIJ,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;IACdJ,CAAC,GAAGD,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBJ,CAAC,GAAGF,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBH,CAAC,GAAGH,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBF,CAAC,GAAGJ,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB;EACJ,CAAC,MACI;IACDL,CAAC,GAAGD,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBJ,CAAC,GAAGF,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBH,CAAC,GAAGH,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBF,CAAC,GAAGJ,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBL,CAAC,IAAIA,CAAC;IACNC,CAAC,IAAIA,CAAC;IACNC,CAAC,IAAIA,CAAC;IACNC,CAAC,IAAIA,CAAC;EACV;EACA,OAAO;IACHG,GAAG,EAAEC,QAAQ,CAACP,CAAC,EAAE,EAAE,CAAC;IACpBQ,KAAK,EAAED,QAAQ,CAACN,CAAC,EAAE,EAAE,CAAC;IACtBQ,IAAI,EAAEF,QAAQ,CAACL,CAAC,EAAE,EAAE,CAAC;IACrBQ,KAAK,EAAEP,CAAC,GAAGI,QAAQ,CAACJ,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG;EACvC,CAAC;AACL;AACA,MAAMQ,GAAG,GAAG;EACRC,IAAI,EAAE,aAAcf,aAAa,CAAC,GAAG,CAAC;EACtCgB,KAAK,EAAEf,QAAQ;EACfgB,SAAS,EAAElB,IAAI,CAACkB;AACpB,CAAC;AAED,SAASH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}