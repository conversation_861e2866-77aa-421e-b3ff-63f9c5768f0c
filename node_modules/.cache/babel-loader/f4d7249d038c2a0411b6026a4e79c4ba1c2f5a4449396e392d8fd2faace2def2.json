{"ast": null, "code": "/**\n * API utility functions for secure HTTP requests\n */\n\n// Get API base URL from environment variables\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'https://api.gwm.tj/api/v1';\n\n/**\n * Get CSRF token from meta tag or cookie\n * @returns {string|null} CSRF token\n */\nconst getCSRFToken = () => {\n  var _document$querySelect;\n  // Try to get from meta tag first\n  const metaToken = (_document$querySelect = document.querySelector('meta[name=\"csrf-token\"]')) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.getAttribute('content');\n  if (metaToken) return metaToken;\n\n  // Try to get from cookie\n  const cookies = document.cookie.split(';');\n  for (let cookie of cookies) {\n    const [name, value] = cookie.trim().split('=');\n    if (name === 'XSRF-TOKEN') {\n      return decodeURIComponent(value);\n    }\n  }\n  return null;\n};\n\n/**\n * Create default headers for API requests\n * @returns {Object} Default headers\n */\nconst getDefaultHeaders = () => {\n  const headers = {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest'\n  };\n  const csrfToken = getCSRFToken();\n  if (csrfToken) {\n    headers['X-CSRF-TOKEN'] = csrfToken;\n  }\n  return headers;\n};\n\n/**\n * Generic API request function with error handling and security\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise} API response\n */\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n  const defaultOptions = {\n    headers: getDefaultHeaders(),\n    credentials: 'same-origin',\n    // Include cookies for CSRF protection\n    ...options\n  };\n\n  // Merge headers\n  if (options.headers) {\n    defaultOptions.headers = {\n      ...defaultOptions.headers,\n      ...options.headers\n    };\n  }\n  try {\n    const response = await fetch(url, defaultOptions);\n\n    // Handle different HTTP status codes\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Проверьте правильность заполнения формы');\n        case 401:\n          throw new Error('Ошибка авторизации. Обновите страницу и попробуйте снова');\n        case 403:\n          throw new Error('Доступ запрещен');\n        case 404:\n          throw new Error('Запрашиваемый ресурс не найден');\n        case 422:\n          throw new Error(errorData.message || 'Ошибка валидации данных');\n        case 429:\n          throw new Error('Слишком много запросов. Попробуйте позже');\n        case 500:\n          throw new Error('Ошибка сервера. Попробуйте позже');\n        case 502:\n        case 503:\n        case 504:\n          throw new Error('Сервис временно недоступен. Попробуйте позже');\n        default:\n          throw new Error(`Ошибка ${response.status}: ${errorData.message || 'Неизвестная ошибка'}`);\n      }\n    }\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('application/json')) {\n      return await response.json();\n    }\n    return response;\n  } catch (error) {\n    // Log error for debugging (in development)\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Request Error:', {\n        url,\n        options: defaultOptions,\n        error: error.message,\n        timestamp: new Date().toISOString()\n      });\n    }\n\n    // Re-throw the error for handling by the calling code\n    throw error;\n  }\n};\n\n/**\n * API request with retry logic for network errors\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @param {number} maxRetries - Maximum number of retries\n * @returns {Promise} API response\n */\nconst apiRequestWithRetry = async (endpoint, options = {}, maxRetries = 2) => {\n  let lastError;\n  for (let attempt = 0; attempt <= maxRetries; attempt++) {\n    try {\n      return await apiRequest(endpoint, options);\n    } catch (error) {\n      lastError = error;\n\n      // Only retry on network errors, not on HTTP errors\n      if (error.name === 'TypeError' && attempt < maxRetries) {\n        // Wait before retrying (exponential backoff)\n        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);\n        await new Promise(resolve => setTimeout(resolve, delay));\n        continue;\n      }\n\n      // Don't retry on HTTP errors or if max retries reached\n      break;\n    }\n  }\n  throw lastError;\n};\n\n/**\n * Fetch car models from API\n * @returns {Promise<Array>} Array of car models\n */\nexport const fetchModels = async () => {\n  try {\n    const response = await apiRequestWithRetry('/models');\n    return response.models || [];\n  } catch (error) {\n    console.error('Failed to fetch models:', error);\n    throw new Error('Не удалось загрузить список моделей. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Submit feedback form to GWM API\n * @param {Object} formData - Form data to submit\n * @param {string} formData.firstName - User's first name\n * @param {string} formData.lastName - User's last name (optional)\n * @param {string} formData.phone - User's phone number\n * @param {string} formData.topic - Selected car model\n * @param {string} formData.message - User's message (optional)\n * @param {string} formData.formType - Type of form (default: 'contact')\n * @returns {Promise<Object>} API response\n */\nexport const submitFeedback = async formData => {\n  try {\n    // Prepare payload according to API specification\n    const payload = {\n      formid: generateFormId(),\n      // Generate unique form ID\n      form_name: formData.formType || 'contact',\n      // Form type identifier\n      name: `${formData.firstName}${formData.lastName ? ' ' + formData.lastName : ''}`.trim(),\n      phone: formData.phone.replace(/[^\\d+]/g, ''),\n      model: formData.topic || '',\n      // Selected car model\n      message: formData.message || '' // Optional message\n    };\n    console.log(payload);\n    const response = await apiRequestWithRetry('/feedback', {\n      method: 'POST',\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(payload)\n    });\n    return response;\n  } catch (error) {\n    console.error('Failed to submit feedback:', error);\n    throw error; // Re-throw to let the component handle the specific error message\n  }\n};\n\n/**\n * Generate unique form ID for tracking\n * @returns {integer} Unique form ID\n */\nconst generateFormId = () => {\n  const random = Math.floor(Math.random() * 1000); // 3-digit random number (0 to 999)\n  return random;\n};\n\n/**\n * Submit test drive booking\n * @param {Object} bookingData - Booking data to submit\n * @returns {Promise<Object>} API response\n */\nexport const submitTestDriveBooking = async bookingData => {\n  try {\n    const response = await apiRequestWithRetry('/test-drive', {\n      method: 'POST',\n      body: JSON.stringify(bookingData)\n    });\n    return response;\n  } catch (error) {\n    console.error('Failed to submit test drive booking:', error);\n    throw error;\n  }\n};\n\n/**\n * Fetch news articles\n * @param {Object} params - Query parameters\n * @returns {Promise<Array>} Array of news articles\n */\nexport const fetchNews = async (params = {}) => {\n  try {\n    const queryString = new URLSearchParams(params).toString();\n    const endpoint = `/news${queryString ? `?${queryString}` : ''}`;\n    const response = await apiRequestWithRetry(endpoint);\n    return response.news || [];\n  } catch (error) {\n    console.error('Failed to fetch news:', error);\n    throw new Error('Не удалось загрузить новости. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Fetch single news article\n * @param {string} id - News article ID\n * @returns {Promise<Object>} News article\n */\nexport const fetchNewsById = async id => {\n  try {\n    const response = await apiRequestWithRetry(`/news/${id}`);\n    return response;\n  } catch (error) {\n    console.error('Failed to fetch news article:', error);\n    throw new Error('Не удалось загрузить статью. Попробуйте обновить страницу.');\n  }\n};\nexport default {\n  fetchModels,\n  submitFeedback,\n  submitTestDriveBooking,\n  fetchNews,\n  fetchNewsById\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "getCSRFToken", "_document$querySelect", "metaToken", "document", "querySelector", "getAttribute", "cookies", "cookie", "split", "name", "value", "trim", "decodeURIComponent", "getDefaultHeaders", "headers", "csrfToken", "apiRequest", "endpoint", "options", "url", "defaultOptions", "credentials", "response", "fetch", "ok", "errorData", "json", "catch", "status", "Error", "message", "contentType", "get", "includes", "error", "NODE_ENV", "console", "timestamp", "Date", "toISOString", "apiRequestWithRetry", "maxRetries", "lastError", "attempt", "delay", "Math", "min", "pow", "Promise", "resolve", "setTimeout", "fetchModels", "models", "submitFeedback", "formData", "payload", "formid", "generateFormId", "form_name", "formType", "firstName", "lastName", "phone", "replace", "model", "topic", "log", "method", "body", "JSON", "stringify", "random", "floor", "submitTestDriveBooking", "bookingData", "fetchNews", "params", "queryString", "URLSearchParams", "toString", "news", "fetchNewsById", "id"], "sources": ["/var/www/html/gwm.tj/src/utils/api.js"], "sourcesContent": ["/**\n * API utility functions for secure HTTP requests\n */\n\n// Get API base URL from environment variables\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'https://api.gwm.tj/api/v1';\n\n/**\n * Get CSRF token from meta tag or cookie\n * @returns {string|null} CSRF token\n */\nconst getCSRFToken = () => {\n  // Try to get from meta tag first\n  const metaToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n  if (metaToken) return metaToken;\n\n  // Try to get from cookie\n  const cookies = document.cookie.split(';');\n  for (let cookie of cookies) {\n    const [name, value] = cookie.trim().split('=');\n    if (name === 'XSRF-TOKEN') {\n      return decodeURIComponent(value);\n    }\n  }\n\n  return null;\n};\n\n/**\n * Create default headers for API requests\n * @returns {Object} Default headers\n */\nconst getDefaultHeaders = () => {\n  const headers = {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest',\n  };\n\n  const csrfToken = getCSRFToken();\n  if (csrfToken) {\n    headers['X-CSRF-TOKEN'] = csrfToken;\n  }\n\n  return headers;\n};\n\n/**\n * Generic API request function with error handling and security\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise} API response\n */\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  const defaultOptions = {\n    headers: getDefaultHeaders(),\n    credentials: 'same-origin', // Include cookies for CSRF protection\n    ...options,\n  };\n\n  // Merge headers\n  if (options.headers) {\n    defaultOptions.headers = { ...defaultOptions.headers, ...options.headers };\n  }\n\n  try {\n    const response = await fetch(url, defaultOptions);\n\n    // Handle different HTTP status codes\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Проверьте правильность заполнения формы');\n        case 401:\n          throw new Error('Ошибка авторизации. Обновите страницу и попробуйте снова');\n        case 403:\n          throw new Error('Доступ запрещен');\n        case 404:\n          throw new Error('Запрашиваемый ресурс не найден');\n        case 422:\n          throw new Error(errorData.message || 'Ошибка валидации данных');\n        case 429:\n          throw new Error('Слишком много запросов. Попробуйте позже');\n        case 500:\n          throw new Error('Ошибка сервера. Попробуйте позже');\n        case 502:\n        case 503:\n        case 504:\n          throw new Error('Сервис временно недоступен. Попробуйте позже');\n        default:\n          throw new Error(`Ошибка ${response.status}: ${errorData.message || 'Неизвестная ошибка'}`);\n      }\n    }\n\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('application/json')) {\n      return await response.json();\n    }\n\n    return response;\n  } catch (error) {\n    // Log error for debugging (in development)\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Request Error:', {\n        url,\n        options: defaultOptions,\n        error: error.message,\n        timestamp: new Date().toISOString(),\n      });\n    }\n\n    // Re-throw the error for handling by the calling code\n    throw error;\n  }\n};\n\n/**\n * API request with retry logic for network errors\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @param {number} maxRetries - Maximum number of retries\n * @returns {Promise} API response\n */\nconst apiRequestWithRetry = async (endpoint, options = {}, maxRetries = 2) => {\n  let lastError;\n\n  for (let attempt = 0; attempt <= maxRetries; attempt++) {\n    try {\n      return await apiRequest(endpoint, options);\n    } catch (error) {\n      lastError = error;\n\n      // Only retry on network errors, not on HTTP errors\n      if (error.name === 'TypeError' && attempt < maxRetries) {\n        // Wait before retrying (exponential backoff)\n        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);\n        await new Promise(resolve => setTimeout(resolve, delay));\n        continue;\n      }\n\n      // Don't retry on HTTP errors or if max retries reached\n      break;\n    }\n  }\n\n  throw lastError;\n};\n\n/**\n * Fetch car models from API\n * @returns {Promise<Array>} Array of car models\n */\nexport const fetchModels = async () => {\n  try {\n    const response = await apiRequestWithRetry('/models');\n    return response.models || [];\n  } catch (error) {\n    console.error('Failed to fetch models:', error);\n    throw new Error('Не удалось загрузить список моделей. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Submit feedback form to GWM API\n * @param {Object} formData - Form data to submit\n * @param {string} formData.firstName - User's first name\n * @param {string} formData.lastName - User's last name (optional)\n * @param {string} formData.phone - User's phone number\n * @param {string} formData.topic - Selected car model\n * @param {string} formData.message - User's message (optional)\n * @param {string} formData.formType - Type of form (default: 'contact')\n * @returns {Promise<Object>} API response\n */\nexport const submitFeedback = async (formData) => {\n  try {\n    // Prepare payload according to API specification\n    const payload = {\n      formid: generateFormId(), // Generate unique form ID\n      form_name: formData.formType || 'contact', // Form type identifier\n      name: `${formData.firstName}${formData.lastName ? ' ' + formData.lastName : ''}`.trim(),\n      phone: formData.phone.replace(/[^\\d+]/g, ''),\n      model: formData.topic || '', // Selected car model\n      message: formData.message || '', // Optional message\n    };\n\n    console.log(payload)\n\n    const response = await apiRequestWithRetry('/feedback', {\n      method: 'POST',\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(payload),\n    });\n\n    return response;\n  } catch (error) {\n    console.error('Failed to submit feedback:', error);\n    throw error; // Re-throw to let the component handle the specific error message\n  }\n};\n\n/**\n * Generate unique form ID for tracking\n * @returns {integer} Unique form ID\n */\nconst generateFormId = () => {\n  const random = Math.floor(Math.random() * 1000); // 3-digit random number (0 to 999)\n  return random;\n};\n\n\n/**\n * Submit test drive booking\n * @param {Object} bookingData - Booking data to submit\n * @returns {Promise<Object>} API response\n */\nexport const submitTestDriveBooking = async (bookingData) => {\n  try {\n    const response = await apiRequestWithRetry('/test-drive', {\n      method: 'POST',\n      body: JSON.stringify(bookingData),\n    });\n\n    return response;\n  } catch (error) {\n    console.error('Failed to submit test drive booking:', error);\n    throw error;\n  }\n};\n\n/**\n * Fetch news articles\n * @param {Object} params - Query parameters\n * @returns {Promise<Array>} Array of news articles\n */\nexport const fetchNews = async (params = {}) => {\n  try {\n    const queryString = new URLSearchParams(params).toString();\n    const endpoint = `/news${queryString ? `?${queryString}` : ''}`;\n\n    const response = await apiRequestWithRetry(endpoint);\n    return response.news || [];\n  } catch (error) {\n    console.error('Failed to fetch news:', error);\n    throw new Error('Не удалось загрузить новости. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Fetch single news article\n * @param {string} id - News article ID\n * @returns {Promise<Object>} News article\n */\nexport const fetchNewsById = async (id) => {\n  try {\n    const response = await apiRequestWithRetry(`/news/${id}`);\n    return response;\n  } catch (error) {\n    console.error('Failed to fetch news article:', error);\n    throw new Error('Не удалось загрузить статью. Попробуйте обновить страницу.');\n  }\n};\n\nexport default {\n  fetchModels,\n  submitFeedback,\n  submitTestDriveBooking,\n  fetchNews,\n  fetchNewsById,\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;;AAEtF;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAA,IAAAC,qBAAA;EACzB;EACA,MAAMC,SAAS,IAAAD,qBAAA,GAAGE,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC,cAAAH,qBAAA,uBAAjDA,qBAAA,CAAmDI,YAAY,CAAC,SAAS,CAAC;EAC5F,IAAIH,SAAS,EAAE,OAAOA,SAAS;;EAE/B;EACA,MAAMI,OAAO,GAAGH,QAAQ,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;EAC1C,KAAK,IAAID,MAAM,IAAID,OAAO,EAAE;IAC1B,MAAM,CAACG,IAAI,EAAEC,KAAK,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIC,IAAI,KAAK,YAAY,EAAE;MACzB,OAAOG,kBAAkB,CAACF,KAAK,CAAC;IAClC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,kBAAkB,EAAE;EACtB,CAAC;EAED,MAAMC,SAAS,GAAGf,YAAY,CAAC,CAAC;EAChC,IAAIe,SAAS,EAAE;IACbD,OAAO,CAAC,cAAc,CAAC,GAAGC,SAAS;EACrC;EAEA,OAAOD,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,UAAU,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,MAAMC,GAAG,GAAG,GAAGvB,YAAY,GAAGqB,QAAQ,EAAE;EAExC,MAAMG,cAAc,GAAG;IACrBN,OAAO,EAAED,iBAAiB,CAAC,CAAC;IAC5BQ,WAAW,EAAE,aAAa;IAAE;IAC5B,GAAGH;EACL,CAAC;;EAED;EACA,IAAIA,OAAO,CAACJ,OAAO,EAAE;IACnBM,cAAc,CAACN,OAAO,GAAG;MAAE,GAAGM,cAAc,CAACN,OAAO;MAAE,GAAGI,OAAO,CAACJ;IAAQ,CAAC;EAC5E;EAEA,IAAI;IACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEC,cAAc,CAAC;;IAEjD;IACA,IAAI,CAACE,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MAEzD,QAAQL,QAAQ,CAACM,MAAM;QACrB,KAAK,GAAG;UACN,MAAM,IAAIC,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yCAAyC,CAAC;QACjF,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAAC,0DAA0D,CAAC;QAC7E,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CAAC,iBAAiB,CAAC;QACpC,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CAAC,gCAAgC,CAAC;QACnD,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CAACJ,SAAS,CAACK,OAAO,IAAI,yBAAyB,CAAC;QACjE,KAAK,GAAG;UACN,MAAM,IAAID,KAAK,CAAC,0CAA0C,CAAC;QAC7D,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CAAC,kCAAkC,CAAC;QACrD,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,GAAG;UACN,MAAM,IAAIA,KAAK,CAAC,8CAA8C,CAAC;QACjE;UACE,MAAM,IAAIA,KAAK,CAAC,UAAUP,QAAQ,CAACM,MAAM,KAAKH,SAAS,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;MAC9F;IACF;IAEA,MAAMC,WAAW,GAAGT,QAAQ,CAACR,OAAO,CAACkB,GAAG,CAAC,cAAc,CAAC;IACxD,IAAID,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC3D,OAAO,MAAMX,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC9B;IAEA,OAAOJ,QAAQ;EACjB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACd;IACA,IAAIrC,OAAO,CAACC,GAAG,CAACqC,QAAQ,KAAK,aAAa,EAAE;MAC1CC,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAE;QAClCf,GAAG;QACHD,OAAO,EAAEE,cAAc;QACvBc,KAAK,EAAEA,KAAK,CAACJ,OAAO;QACpBO,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ;;IAEA;IACA,MAAML,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,mBAAmB,GAAG,MAAAA,CAAOvB,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEuB,UAAU,GAAG,CAAC,KAAK;EAC5E,IAAIC,SAAS;EAEb,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIF,UAAU,EAAEE,OAAO,EAAE,EAAE;IACtD,IAAI;MACF,OAAO,MAAM3B,UAAU,CAACC,QAAQ,EAAEC,OAAO,CAAC;IAC5C,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdQ,SAAS,GAAGR,KAAK;;MAEjB;MACA,IAAIA,KAAK,CAACzB,IAAI,KAAK,WAAW,IAAIkC,OAAO,GAAGF,UAAU,EAAE;QACtD;QACA,MAAMG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE,IAAI,CAAC;QACzD,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEL,KAAK,CAAC,CAAC;QACxD;MACF;;MAEA;MACA;IACF;EACF;EAEA,MAAMF,SAAS;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAM7B,QAAQ,GAAG,MAAMkB,mBAAmB,CAAC,SAAS,CAAC;IACrD,OAAOlB,QAAQ,CAAC8B,MAAM,IAAI,EAAE;EAC9B,CAAC,CAAC,OAAOlB,KAAK,EAAE;IACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAM,IAAIL,KAAK,CAAC,oEAAoE,CAAC;EACvF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMwB,cAAc,GAAG,MAAOC,QAAQ,IAAK;EAChD,IAAI;IACF;IACA,MAAMC,OAAO,GAAG;MACdC,MAAM,EAAEC,cAAc,CAAC,CAAC;MAAE;MAC1BC,SAAS,EAAEJ,QAAQ,CAACK,QAAQ,IAAI,SAAS;MAAE;MAC3ClD,IAAI,EAAE,GAAG6C,QAAQ,CAACM,SAAS,GAAGN,QAAQ,CAACO,QAAQ,GAAG,GAAG,GAAGP,QAAQ,CAACO,QAAQ,GAAG,EAAE,EAAE,CAAClD,IAAI,CAAC,CAAC;MACvFmD,KAAK,EAAER,QAAQ,CAACQ,KAAK,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;MAC5CC,KAAK,EAAEV,QAAQ,CAACW,KAAK,IAAI,EAAE;MAAE;MAC7BnC,OAAO,EAAEwB,QAAQ,CAACxB,OAAO,IAAI,EAAE,CAAE;IACnC,CAAC;IAEDM,OAAO,CAAC8B,GAAG,CAACX,OAAO,CAAC;IAEpB,MAAMjC,QAAQ,GAAG,MAAMkB,mBAAmB,CAAC,WAAW,EAAE;MACtD2B,MAAM,EAAE,MAAM;MACdrD,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDsD,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACf,OAAO;IAC9B,CAAC,CAAC;IAEF,OAAOjC,QAAQ;EACjB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMc,MAAM,GAAG1B,IAAI,CAAC2B,KAAK,CAAC3B,IAAI,CAAC0B,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACjD,OAAOA,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,sBAAsB,GAAG,MAAOC,WAAW,IAAK;EAC3D,IAAI;IACF,MAAMpD,QAAQ,GAAG,MAAMkB,mBAAmB,CAAC,aAAa,EAAE;MACxD2B,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOpD,QAAQ;EACjB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyC,SAAS,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EAC9C,IAAI;IACF,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,MAAM9D,QAAQ,GAAG,QAAQ4D,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;IAE/D,MAAMvD,QAAQ,GAAG,MAAMkB,mBAAmB,CAACvB,QAAQ,CAAC;IACpD,OAAOK,QAAQ,CAAC0D,IAAI,IAAI,EAAE;EAC5B,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,MAAM,IAAIL,KAAK,CAAC,6DAA6D,CAAC;EAChF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoD,aAAa,GAAG,MAAOC,EAAE,IAAK;EACzC,IAAI;IACF,MAAM5D,QAAQ,GAAG,MAAMkB,mBAAmB,CAAC,SAAS0C,EAAE,EAAE,CAAC;IACzD,OAAO5D,QAAQ;EACjB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAM,IAAIL,KAAK,CAAC,4DAA4D,CAAC;EAC/E;AACF,CAAC;AAED,eAAe;EACbsB,WAAW;EACXE,cAAc;EACdoB,sBAAsB;EACtBE,SAAS;EACTM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}