{"ast": null, "code": "/* underscore in name -> watch for changes */\nconst paramsList = ['eventsPrefix', 'injectStyles', 'injectStylesUrls', 'modules', 'init', '_direction', 'oneWayMovement', 'swiperElementNodeName', 'touchEventsTarget', 'initialSlide', '_speed', 'cssMode', 'updateOnWindowResize', 'resizeObserver', 'nested', 'focusableElements', '_enabled', '_width', '_height', 'preventInteractionOnTransition', 'userAgent', 'url', '_edgeSwipeDetection', '_edgeSwipeThreshold', '_freeMode', '_autoHeight', 'setWrapperSize', 'virtualTranslate', '_effect', 'breakpoints', 'breakpointsBase', '_spaceBetween', '_slidesPerView', 'maxBackfaceHiddenSlides', '_grid', '_slidesPerGroup', '_slidesPerGroupSkip', '_slidesPerGroupAuto', '_centeredSlides', '_centeredSlidesBounds', '_slidesOffsetBefore', '_slidesOffsetAfter', 'normalizeSlideIndex', '_centerInsufficientSlides', '_watchOverflow', 'roundLengths', 'touchRatio', 'touchAngle', 'simulateTouch', '_shortSwipes', '_longSwipes', 'longSwipesRatio', 'longSwipesMs', '_followFinger', 'allowTouchMove', '_threshold', 'touchMoveStopPropagation', 'touchStartPreventDefault', 'touchStartForcePreventDefault', 'touchReleaseOnEdges', 'uniqueNavElements', '_resistance', '_resistanceRatio', '_watchSlidesProgress', '_grabCursor', 'preventClicks', 'preventClicksPropagation', '_slideToClickedSlide', '_loop', 'loopAdditionalSlides', 'loopAddBlankSlides', 'loopPreventsSliding', '_rewind', '_allowSlidePrev', '_allowSlideNext', '_swipeHandler', '_noSwiping', 'noSwipingClass', 'noSwipingSelector', 'passiveListeners', 'containerModifierClass', 'slideClass', 'slideActiveClass', 'slideVisibleClass', 'slideFullyVisibleClass', 'slideNextClass', 'slidePrevClass', 'slideBlankClass', 'wrapperClass', 'lazyPreloaderClass', 'lazyPreloadPrevNext', 'runCallbacksOnInit', 'observer', 'observeParents', 'observeSlideChildren',\n// modules\n'a11y', '_autoplay', '_controller', 'coverflowEffect', 'cubeEffect', 'fadeEffect', 'flipEffect', 'creativeEffect', 'cardsEffect', 'hashNavigation', 'history', 'keyboard', 'mousewheel', '_navigation', '_pagination', 'parallax', '_scrollbar', '_thumbs', 'virtual', 'zoom', 'control'];\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object' && !o.__swiper__;\n}\nfunction extend(target, src) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      if (src[key].__swiper__) target[key] = src[key];else extend(target[key], src[key]);\n    } else {\n      target[key] = src[key];\n    }\n  });\n}\nfunction needsNavigation(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.navigation && typeof params.navigation.nextEl === 'undefined' && typeof params.navigation.prevEl === 'undefined';\n}\nfunction needsPagination(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.pagination && typeof params.pagination.el === 'undefined';\n}\nfunction needsScrollbar(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.scrollbar && typeof params.scrollbar.el === 'undefined';\n}\nfunction uniqueClasses(classNames) {\n  if (classNames === void 0) {\n    classNames = '';\n  }\n  const classes = classNames.split(' ').map(c => c.trim()).filter(c => !!c);\n  const unique = [];\n  classes.forEach(c => {\n    if (unique.indexOf(c) < 0) unique.push(c);\n  });\n  return unique.join(' ');\n}\nfunction attrToProp(attrName) {\n  if (attrName === void 0) {\n    attrName = '';\n  }\n  return attrName.replace(/-[a-z]/g, l => l.toUpperCase().replace('-', ''));\n}\nfunction wrapperClass(className) {\n  if (className === void 0) {\n    className = '';\n  }\n  if (!className) return 'swiper-wrapper';\n  if (!className.includes('swiper-wrapper')) return \"swiper-wrapper \".concat(className);\n  return className;\n}\nfunction updateSwiper(_ref) {\n  let {\n    swiper,\n    slides,\n    passedParams,\n    changedParams,\n    nextEl,\n    prevEl,\n    scrollbarEl,\n    paginationEl\n  } = _ref;\n  const updateParams = changedParams.filter(key => key !== 'children' && key !== 'direction' && key !== 'wrapperClass');\n  const {\n    params: currentParams,\n    pagination,\n    navigation,\n    scrollbar,\n    virtual,\n    thumbs\n  } = swiper;\n  let needThumbsInit;\n  let needControllerInit;\n  let needPaginationInit;\n  let needScrollbarInit;\n  let needNavigationInit;\n  let loopNeedDestroy;\n  let loopNeedEnable;\n  let loopNeedReloop;\n  if (changedParams.includes('thumbs') && passedParams.thumbs && passedParams.thumbs.swiper && !passedParams.thumbs.swiper.destroyed && currentParams.thumbs && (!currentParams.thumbs.swiper || currentParams.thumbs.swiper.destroyed)) {\n    needThumbsInit = true;\n  }\n  if (changedParams.includes('controller') && passedParams.controller && passedParams.controller.control && currentParams.controller && !currentParams.controller.control) {\n    needControllerInit = true;\n  }\n  if (changedParams.includes('pagination') && passedParams.pagination && (passedParams.pagination.el || paginationEl) && (currentParams.pagination || currentParams.pagination === false) && pagination && !pagination.el) {\n    needPaginationInit = true;\n  }\n  if (changedParams.includes('scrollbar') && passedParams.scrollbar && (passedParams.scrollbar.el || scrollbarEl) && (currentParams.scrollbar || currentParams.scrollbar === false) && scrollbar && !scrollbar.el) {\n    needScrollbarInit = true;\n  }\n  if (changedParams.includes('navigation') && passedParams.navigation && (passedParams.navigation.prevEl || prevEl) && (passedParams.navigation.nextEl || nextEl) && (currentParams.navigation || currentParams.navigation === false) && navigation && !navigation.prevEl && !navigation.nextEl) {\n    needNavigationInit = true;\n  }\n  const destroyModule = mod => {\n    if (!swiper[mod]) return;\n    swiper[mod].destroy();\n    if (mod === 'navigation') {\n      if (swiper.isElement) {\n        swiper[mod].prevEl.remove();\n        swiper[mod].nextEl.remove();\n      }\n      currentParams[mod].prevEl = undefined;\n      currentParams[mod].nextEl = undefined;\n      swiper[mod].prevEl = undefined;\n      swiper[mod].nextEl = undefined;\n    } else {\n      if (swiper.isElement) {\n        swiper[mod].el.remove();\n      }\n      currentParams[mod].el = undefined;\n      swiper[mod].el = undefined;\n    }\n  };\n  if (changedParams.includes('loop') && swiper.isElement) {\n    if (currentParams.loop && !passedParams.loop) {\n      loopNeedDestroy = true;\n    } else if (!currentParams.loop && passedParams.loop) {\n      loopNeedEnable = true;\n    } else {\n      loopNeedReloop = true;\n    }\n  }\n  updateParams.forEach(key => {\n    if (isObject(currentParams[key]) && isObject(passedParams[key])) {\n      Object.assign(currentParams[key], passedParams[key]);\n      if ((key === 'navigation' || key === 'pagination' || key === 'scrollbar') && 'enabled' in passedParams[key] && !passedParams[key].enabled) {\n        destroyModule(key);\n      }\n    } else {\n      const newValue = passedParams[key];\n      if ((newValue === true || newValue === false) && (key === 'navigation' || key === 'pagination' || key === 'scrollbar')) {\n        if (newValue === false) {\n          destroyModule(key);\n        }\n      } else {\n        currentParams[key] = passedParams[key];\n      }\n    }\n  });\n  if (updateParams.includes('controller') && !needControllerInit && swiper.controller && swiper.controller.control && currentParams.controller && currentParams.controller.control) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (changedParams.includes('children') && slides && virtual && currentParams.virtual.enabled) {\n    virtual.slides = slides;\n    virtual.update(true);\n  } else if (changedParams.includes('virtual') && virtual && currentParams.virtual.enabled) {\n    if (slides) virtual.slides = slides;\n    virtual.update(true);\n  }\n  if (changedParams.includes('children') && slides && currentParams.loop) {\n    loopNeedReloop = true;\n  }\n  if (needThumbsInit) {\n    const initialized = thumbs.init();\n    if (initialized) thumbs.update(true);\n  }\n  if (needControllerInit) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (needPaginationInit) {\n    if (swiper.isElement && (!paginationEl || typeof paginationEl === 'string')) {\n      paginationEl = document.createElement('div');\n      paginationEl.classList.add('swiper-pagination');\n      paginationEl.part.add('pagination');\n      swiper.el.appendChild(paginationEl);\n    }\n    if (paginationEl) currentParams.pagination.el = paginationEl;\n    pagination.init();\n    pagination.render();\n    pagination.update();\n  }\n  if (needScrollbarInit) {\n    if (swiper.isElement && (!scrollbarEl || typeof scrollbarEl === 'string')) {\n      scrollbarEl = document.createElement('div');\n      scrollbarEl.classList.add('swiper-scrollbar');\n      scrollbarEl.part.add('scrollbar');\n      swiper.el.appendChild(scrollbarEl);\n    }\n    if (scrollbarEl) currentParams.scrollbar.el = scrollbarEl;\n    scrollbar.init();\n    scrollbar.updateSize();\n    scrollbar.setTranslate();\n  }\n  if (needNavigationInit) {\n    if (swiper.isElement) {\n      if (!nextEl || typeof nextEl === 'string') {\n        nextEl = document.createElement('div');\n        nextEl.classList.add('swiper-button-next');\n        nextEl.innerHTML = swiper.hostEl.constructor.nextButtonSvg;\n        nextEl.part.add('button-next');\n        swiper.el.appendChild(nextEl);\n      }\n      if (!prevEl || typeof prevEl === 'string') {\n        prevEl = document.createElement('div');\n        prevEl.classList.add('swiper-button-prev');\n        prevEl.innerHTML = swiper.hostEl.constructor.prevButtonSvg;\n        prevEl.part.add('button-prev');\n        swiper.el.appendChild(prevEl);\n      }\n    }\n    if (nextEl) currentParams.navigation.nextEl = nextEl;\n    if (prevEl) currentParams.navigation.prevEl = prevEl;\n    navigation.init();\n    navigation.update();\n  }\n  if (changedParams.includes('allowSlideNext')) {\n    swiper.allowSlideNext = passedParams.allowSlideNext;\n  }\n  if (changedParams.includes('allowSlidePrev')) {\n    swiper.allowSlidePrev = passedParams.allowSlidePrev;\n  }\n  if (changedParams.includes('direction')) {\n    swiper.changeDirection(passedParams.direction, false);\n  }\n  if (loopNeedDestroy || loopNeedReloop) {\n    swiper.loopDestroy();\n  }\n  if (loopNeedEnable || loopNeedReloop) {\n    swiper.loopCreate();\n  }\n  swiper.update();\n}\nexport { needsPagination as a, needsScrollbar as b, attrToProp as c, uniqueClasses as d, extend as e, isObject as i, needsNavigation as n, paramsList as p, updateSwiper as u, wrapperClass as w };", "map": {"version": 3, "names": ["paramsList", "isObject", "o", "constructor", "Object", "prototype", "toString", "call", "slice", "__swiper__", "extend", "target", "src", "noExtend", "keys", "filter", "key", "indexOf", "for<PERSON>ach", "length", "needsNavigation", "params", "navigation", "nextEl", "prevEl", "needsPagination", "pagination", "el", "needsScrollbar", "scrollbar", "uniqueClasses", "classNames", "classes", "split", "map", "c", "trim", "unique", "push", "join", "attrToProp", "attrName", "replace", "l", "toUpperCase", "wrapperClass", "className", "includes", "concat", "updateSwiper", "_ref", "swiper", "slides", "passedParams", "changedParams", "scrollbarEl", "paginationEl", "updateParams", "currentParams", "virtual", "thumbs", "needThumbsInit", "needControllerInit", "needPaginationInit", "needScrollbarInit", "needNavigationInit", "loopNeedDestroy", "loopNeedEnable", "loopNeedReloop", "destroyed", "controller", "control", "destroyModule", "mod", "destroy", "isElement", "remove", "undefined", "loop", "assign", "enabled", "newValue", "update", "initialized", "init", "document", "createElement", "classList", "add", "part", "append<PERSON><PERSON><PERSON>", "render", "updateSize", "setTranslate", "innerHTML", "hostEl", "nextButtonSvg", "prevButtonSvg", "allowSlideNext", "allowSlidePrev", "changeDirection", "direction", "loop<PERSON><PERSON><PERSON>", "loopCreate", "a", "b", "d", "e", "i", "n", "p", "u", "w"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/shared/update-swiper.mjs"], "sourcesContent": ["/* underscore in name -> watch for changes */\nconst paramsList = ['eventsPrefix', 'injectStyles', 'injectStylesUrls', 'modules', 'init', '_direction', 'oneWayMovement', 'swiperElementNodeName', 'touchEventsTarget', 'initialSlide', '_speed', 'cssMode', 'updateOnWindowResize', 'resizeObserver', 'nested', 'focusableElements', '_enabled', '_width', '_height', 'preventInteractionOnTransition', 'userAgent', 'url', '_edgeSwipeDetection', '_edgeSwipeThreshold', '_freeMode', '_autoHeight', 'setWrapperSize', 'virtualTranslate', '_effect', 'breakpoints', 'breakpointsBase', '_spaceBetween', '_slidesPerView', 'maxBackfaceHiddenSlides', '_grid', '_slidesPerGroup', '_slidesPerGroupSkip', '_slidesPerGroupAuto', '_centeredSlides', '_centeredSlidesBounds', '_slidesOffsetBefore', '_slidesOffsetAfter', 'normalizeSlideIndex', '_centerInsufficientSlides', '_watchOverflow', 'roundLengths', 'touchRatio', 'touchAngle', 'simulateTouch', '_shortSwipes', '_longSwipes', 'longSwipesRatio', 'longSwipesMs', '_followFinger', 'allowTouchMove', '_threshold', 'touchMoveStopPropagation', 'touchStartPreventDefault', 'touchStartForcePreventDefault', 'touchReleaseOnEdges', 'uniqueNavElements', '_resistance', '_resistanceRatio', '_watchSlidesProgress', '_grabCursor', 'preventClicks', 'preventClicksPropagation', '_slideToClickedSlide', '_loop', 'loopAdditionalSlides', 'loopAddBlankSlides', 'loopPreventsSliding', '_rewind', '_allowSlidePrev', '_allowSlideNext', '_swipeHandler', '_noSwiping', 'noSwipingClass', 'noSwipingSelector', 'passiveListeners', 'containerModifierClass', 'slideClass', 'slideActiveClass', 'slideVisibleClass', 'slideFullyVisibleClass', 'slideNextClass', 'slidePrevClass', 'slideBlankClass', 'wrapperClass', 'lazyPreloaderClass', 'lazyPreloadPrevNext', 'runCallbacksOnInit', 'observer', 'observeParents', 'observeSlideChildren',\n// modules\n'a11y', '_autoplay', '_controller', 'coverflowEffect', 'cubeEffect', 'fadeEffect', 'flipEffect', 'creativeEffect', 'cardsEffect', 'hashNavigation', 'history', 'keyboard', 'mousewheel', '_navigation', '_pagination', 'parallax', '_scrollbar', '_thumbs', 'virtual', 'zoom', 'control'];\n\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object' && !o.__swiper__;\n}\nfunction extend(target, src) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      if (src[key].__swiper__) target[key] = src[key];else extend(target[key], src[key]);\n    } else {\n      target[key] = src[key];\n    }\n  });\n}\nfunction needsNavigation(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.navigation && typeof params.navigation.nextEl === 'undefined' && typeof params.navigation.prevEl === 'undefined';\n}\nfunction needsPagination(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.pagination && typeof params.pagination.el === 'undefined';\n}\nfunction needsScrollbar(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.scrollbar && typeof params.scrollbar.el === 'undefined';\n}\nfunction uniqueClasses(classNames) {\n  if (classNames === void 0) {\n    classNames = '';\n  }\n  const classes = classNames.split(' ').map(c => c.trim()).filter(c => !!c);\n  const unique = [];\n  classes.forEach(c => {\n    if (unique.indexOf(c) < 0) unique.push(c);\n  });\n  return unique.join(' ');\n}\nfunction attrToProp(attrName) {\n  if (attrName === void 0) {\n    attrName = '';\n  }\n  return attrName.replace(/-[a-z]/g, l => l.toUpperCase().replace('-', ''));\n}\nfunction wrapperClass(className) {\n  if (className === void 0) {\n    className = '';\n  }\n  if (!className) return 'swiper-wrapper';\n  if (!className.includes('swiper-wrapper')) return `swiper-wrapper ${className}`;\n  return className;\n}\n\nfunction updateSwiper(_ref) {\n  let {\n    swiper,\n    slides,\n    passedParams,\n    changedParams,\n    nextEl,\n    prevEl,\n    scrollbarEl,\n    paginationEl\n  } = _ref;\n  const updateParams = changedParams.filter(key => key !== 'children' && key !== 'direction' && key !== 'wrapperClass');\n  const {\n    params: currentParams,\n    pagination,\n    navigation,\n    scrollbar,\n    virtual,\n    thumbs\n  } = swiper;\n  let needThumbsInit;\n  let needControllerInit;\n  let needPaginationInit;\n  let needScrollbarInit;\n  let needNavigationInit;\n  let loopNeedDestroy;\n  let loopNeedEnable;\n  let loopNeedReloop;\n  if (changedParams.includes('thumbs') && passedParams.thumbs && passedParams.thumbs.swiper && !passedParams.thumbs.swiper.destroyed && currentParams.thumbs && (!currentParams.thumbs.swiper || currentParams.thumbs.swiper.destroyed)) {\n    needThumbsInit = true;\n  }\n  if (changedParams.includes('controller') && passedParams.controller && passedParams.controller.control && currentParams.controller && !currentParams.controller.control) {\n    needControllerInit = true;\n  }\n  if (changedParams.includes('pagination') && passedParams.pagination && (passedParams.pagination.el || paginationEl) && (currentParams.pagination || currentParams.pagination === false) && pagination && !pagination.el) {\n    needPaginationInit = true;\n  }\n  if (changedParams.includes('scrollbar') && passedParams.scrollbar && (passedParams.scrollbar.el || scrollbarEl) && (currentParams.scrollbar || currentParams.scrollbar === false) && scrollbar && !scrollbar.el) {\n    needScrollbarInit = true;\n  }\n  if (changedParams.includes('navigation') && passedParams.navigation && (passedParams.navigation.prevEl || prevEl) && (passedParams.navigation.nextEl || nextEl) && (currentParams.navigation || currentParams.navigation === false) && navigation && !navigation.prevEl && !navigation.nextEl) {\n    needNavigationInit = true;\n  }\n  const destroyModule = mod => {\n    if (!swiper[mod]) return;\n    swiper[mod].destroy();\n    if (mod === 'navigation') {\n      if (swiper.isElement) {\n        swiper[mod].prevEl.remove();\n        swiper[mod].nextEl.remove();\n      }\n      currentParams[mod].prevEl = undefined;\n      currentParams[mod].nextEl = undefined;\n      swiper[mod].prevEl = undefined;\n      swiper[mod].nextEl = undefined;\n    } else {\n      if (swiper.isElement) {\n        swiper[mod].el.remove();\n      }\n      currentParams[mod].el = undefined;\n      swiper[mod].el = undefined;\n    }\n  };\n  if (changedParams.includes('loop') && swiper.isElement) {\n    if (currentParams.loop && !passedParams.loop) {\n      loopNeedDestroy = true;\n    } else if (!currentParams.loop && passedParams.loop) {\n      loopNeedEnable = true;\n    } else {\n      loopNeedReloop = true;\n    }\n  }\n  updateParams.forEach(key => {\n    if (isObject(currentParams[key]) && isObject(passedParams[key])) {\n      Object.assign(currentParams[key], passedParams[key]);\n      if ((key === 'navigation' || key === 'pagination' || key === 'scrollbar') && 'enabled' in passedParams[key] && !passedParams[key].enabled) {\n        destroyModule(key);\n      }\n    } else {\n      const newValue = passedParams[key];\n      if ((newValue === true || newValue === false) && (key === 'navigation' || key === 'pagination' || key === 'scrollbar')) {\n        if (newValue === false) {\n          destroyModule(key);\n        }\n      } else {\n        currentParams[key] = passedParams[key];\n      }\n    }\n  });\n  if (updateParams.includes('controller') && !needControllerInit && swiper.controller && swiper.controller.control && currentParams.controller && currentParams.controller.control) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (changedParams.includes('children') && slides && virtual && currentParams.virtual.enabled) {\n    virtual.slides = slides;\n    virtual.update(true);\n  } else if (changedParams.includes('virtual') && virtual && currentParams.virtual.enabled) {\n    if (slides) virtual.slides = slides;\n    virtual.update(true);\n  }\n  if (changedParams.includes('children') && slides && currentParams.loop) {\n    loopNeedReloop = true;\n  }\n  if (needThumbsInit) {\n    const initialized = thumbs.init();\n    if (initialized) thumbs.update(true);\n  }\n  if (needControllerInit) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (needPaginationInit) {\n    if (swiper.isElement && (!paginationEl || typeof paginationEl === 'string')) {\n      paginationEl = document.createElement('div');\n      paginationEl.classList.add('swiper-pagination');\n      paginationEl.part.add('pagination');\n      swiper.el.appendChild(paginationEl);\n    }\n    if (paginationEl) currentParams.pagination.el = paginationEl;\n    pagination.init();\n    pagination.render();\n    pagination.update();\n  }\n  if (needScrollbarInit) {\n    if (swiper.isElement && (!scrollbarEl || typeof scrollbarEl === 'string')) {\n      scrollbarEl = document.createElement('div');\n      scrollbarEl.classList.add('swiper-scrollbar');\n      scrollbarEl.part.add('scrollbar');\n      swiper.el.appendChild(scrollbarEl);\n    }\n    if (scrollbarEl) currentParams.scrollbar.el = scrollbarEl;\n    scrollbar.init();\n    scrollbar.updateSize();\n    scrollbar.setTranslate();\n  }\n  if (needNavigationInit) {\n    if (swiper.isElement) {\n      if (!nextEl || typeof nextEl === 'string') {\n        nextEl = document.createElement('div');\n        nextEl.classList.add('swiper-button-next');\n        nextEl.innerHTML = swiper.hostEl.constructor.nextButtonSvg;\n        nextEl.part.add('button-next');\n        swiper.el.appendChild(nextEl);\n      }\n      if (!prevEl || typeof prevEl === 'string') {\n        prevEl = document.createElement('div');\n        prevEl.classList.add('swiper-button-prev');\n        prevEl.innerHTML = swiper.hostEl.constructor.prevButtonSvg;\n        prevEl.part.add('button-prev');\n        swiper.el.appendChild(prevEl);\n      }\n    }\n    if (nextEl) currentParams.navigation.nextEl = nextEl;\n    if (prevEl) currentParams.navigation.prevEl = prevEl;\n    navigation.init();\n    navigation.update();\n  }\n  if (changedParams.includes('allowSlideNext')) {\n    swiper.allowSlideNext = passedParams.allowSlideNext;\n  }\n  if (changedParams.includes('allowSlidePrev')) {\n    swiper.allowSlidePrev = passedParams.allowSlidePrev;\n  }\n  if (changedParams.includes('direction')) {\n    swiper.changeDirection(passedParams.direction, false);\n  }\n  if (loopNeedDestroy || loopNeedReloop) {\n    swiper.loopDestroy();\n  }\n  if (loopNeedEnable || loopNeedReloop) {\n    swiper.loopCreate();\n  }\n  swiper.update();\n}\n\nexport { needsPagination as a, needsScrollbar as b, attrToProp as c, uniqueClasses as d, extend as e, isObject as i, needsNavigation as n, paramsList as p, updateSwiper as u, wrapperClass as w };\n"], "mappings": "AAAA;AACA,MAAMA,UAAU,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,gCAAgC,EAAE,WAAW,EAAE,KAAK,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,iBAAiB,EAAE,eAAe,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,2BAA2B,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,0BAA0B,EAAE,0BAA0B,EAAE,+BAA+B,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,aAAa,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,aAAa,EAAE,eAAe,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,OAAO,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,YAAY,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,YAAY,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,UAAU,EAAE,gBAAgB,EAAE,sBAAsB;AACxvD;AACA,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC;AAEzR,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACC,WAAW,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAACN,CAAC,CAACO,UAAU;AAC7I;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC3B,MAAMC,QAAQ,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;EAC1DT,MAAM,CAACU,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,CAACC,GAAG,IAAIH,QAAQ,CAACI,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,CAAC,CAACE,OAAO,CAACF,GAAG,IAAI;IACvE,IAAI,OAAOL,MAAM,CAACK,GAAG,CAAC,KAAK,WAAW,EAAEL,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAK,IAAIf,QAAQ,CAACW,GAAG,CAACI,GAAG,CAAC,CAAC,IAAIf,QAAQ,CAACU,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIZ,MAAM,CAACU,IAAI,CAACF,GAAG,CAACI,GAAG,CAAC,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvJ,IAAIP,GAAG,CAACI,GAAG,CAAC,CAACP,UAAU,EAAEE,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAKN,MAAM,CAACC,MAAM,CAACK,GAAG,CAAC,EAAEJ,GAAG,CAACI,GAAG,CAAC,CAAC;IACpF,CAAC,MAAM;MACLL,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC;IACxB;EACF,CAAC,CAAC;AACJ;AACA,SAASI,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,OAAOA,MAAM,CAACC,UAAU,IAAI,OAAOD,MAAM,CAACC,UAAU,CAACC,MAAM,KAAK,WAAW,IAAI,OAAOF,MAAM,CAACC,UAAU,CAACE,MAAM,KAAK,WAAW;AAChI;AACA,SAASC,eAAeA,CAACJ,MAAM,EAAE;EAC/B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,OAAOA,MAAM,CAACK,UAAU,IAAI,OAAOL,MAAM,CAACK,UAAU,CAACC,EAAE,KAAK,WAAW;AACzE;AACA,SAASC,cAAcA,CAACP,MAAM,EAAE;EAC9B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,OAAOA,MAAM,CAACQ,SAAS,IAAI,OAAOR,MAAM,CAACQ,SAAS,CAACF,EAAE,KAAK,WAAW;AACvE;AACA,SAASG,aAAaA,CAACC,UAAU,EAAE;EACjC,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IACzBA,UAAU,GAAG,EAAE;EACjB;EACA,MAAMC,OAAO,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACrB,MAAM,CAACoB,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;EACzE,MAAME,MAAM,GAAG,EAAE;EACjBL,OAAO,CAACd,OAAO,CAACiB,CAAC,IAAI;IACnB,IAAIE,MAAM,CAACpB,OAAO,CAACkB,CAAC,CAAC,GAAG,CAAC,EAAEE,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC;EAC3C,CAAC,CAAC;EACF,OAAOE,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC;AACzB;AACA,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC5B,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IACvBA,QAAQ,GAAG,EAAE;EACf;EACA,OAAOA,QAAQ,CAACC,OAAO,CAAC,SAAS,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAACF,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC3E;AACA,SAASG,YAAYA,CAACC,SAAS,EAAE;EAC/B,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,EAAE;EAChB;EACA,IAAI,CAACA,SAAS,EAAE,OAAO,gBAAgB;EACvC,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,yBAAAC,MAAA,CAAyBF,SAAS;EAC7E,OAAOA,SAAS;AAClB;AAEA,SAASG,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACFC,MAAM;IACNC,MAAM;IACNC,YAAY;IACZC,aAAa;IACb/B,MAAM;IACNC,MAAM;IACN+B,WAAW;IACXC;EACF,CAAC,GAAGN,IAAI;EACR,MAAMO,YAAY,GAAGH,aAAa,CAACvC,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,cAAc,CAAC;EACrH,MAAM;IACJK,MAAM,EAAEqC,aAAa;IACrBhC,UAAU;IACVJ,UAAU;IACVO,SAAS;IACT8B,OAAO;IACPC;EACF,CAAC,GAAGT,MAAM;EACV,IAAIU,cAAc;EAClB,IAAIC,kBAAkB;EACtB,IAAIC,kBAAkB;EACtB,IAAIC,iBAAiB;EACrB,IAAIC,kBAAkB;EACtB,IAAIC,eAAe;EACnB,IAAIC,cAAc;EAClB,IAAIC,cAAc;EAClB,IAAId,aAAa,CAACP,QAAQ,CAAC,QAAQ,CAAC,IAAIM,YAAY,CAACO,MAAM,IAAIP,YAAY,CAACO,MAAM,CAACT,MAAM,IAAI,CAACE,YAAY,CAACO,MAAM,CAACT,MAAM,CAACkB,SAAS,IAAIX,aAAa,CAACE,MAAM,KAAK,CAACF,aAAa,CAACE,MAAM,CAACT,MAAM,IAAIO,aAAa,CAACE,MAAM,CAACT,MAAM,CAACkB,SAAS,CAAC,EAAE;IACrOR,cAAc,GAAG,IAAI;EACvB;EACA,IAAIP,aAAa,CAACP,QAAQ,CAAC,YAAY,CAAC,IAAIM,YAAY,CAACiB,UAAU,IAAIjB,YAAY,CAACiB,UAAU,CAACC,OAAO,IAAIb,aAAa,CAACY,UAAU,IAAI,CAACZ,aAAa,CAACY,UAAU,CAACC,OAAO,EAAE;IACvKT,kBAAkB,GAAG,IAAI;EAC3B;EACA,IAAIR,aAAa,CAACP,QAAQ,CAAC,YAAY,CAAC,IAAIM,YAAY,CAAC3B,UAAU,KAAK2B,YAAY,CAAC3B,UAAU,CAACC,EAAE,IAAI6B,YAAY,CAAC,KAAKE,aAAa,CAAChC,UAAU,IAAIgC,aAAa,CAAChC,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,IAAI,CAACA,UAAU,CAACC,EAAE,EAAE;IACvNoC,kBAAkB,GAAG,IAAI;EAC3B;EACA,IAAIT,aAAa,CAACP,QAAQ,CAAC,WAAW,CAAC,IAAIM,YAAY,CAACxB,SAAS,KAAKwB,YAAY,CAACxB,SAAS,CAACF,EAAE,IAAI4B,WAAW,CAAC,KAAKG,aAAa,CAAC7B,SAAS,IAAI6B,aAAa,CAAC7B,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,IAAI,CAACA,SAAS,CAACF,EAAE,EAAE;IAC/MqC,iBAAiB,GAAG,IAAI;EAC1B;EACA,IAAIV,aAAa,CAACP,QAAQ,CAAC,YAAY,CAAC,IAAIM,YAAY,CAAC/B,UAAU,KAAK+B,YAAY,CAAC/B,UAAU,CAACE,MAAM,IAAIA,MAAM,CAAC,KAAK6B,YAAY,CAAC/B,UAAU,CAACC,MAAM,IAAIA,MAAM,CAAC,KAAKmC,aAAa,CAACpC,UAAU,IAAIoC,aAAa,CAACpC,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,IAAI,CAACA,UAAU,CAACE,MAAM,IAAI,CAACF,UAAU,CAACC,MAAM,EAAE;IAC7R0C,kBAAkB,GAAG,IAAI;EAC3B;EACA,MAAMO,aAAa,GAAGC,GAAG,IAAI;IAC3B,IAAI,CAACtB,MAAM,CAACsB,GAAG,CAAC,EAAE;IAClBtB,MAAM,CAACsB,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC;IACrB,IAAID,GAAG,KAAK,YAAY,EAAE;MACxB,IAAItB,MAAM,CAACwB,SAAS,EAAE;QACpBxB,MAAM,CAACsB,GAAG,CAAC,CAACjD,MAAM,CAACoD,MAAM,CAAC,CAAC;QAC3BzB,MAAM,CAACsB,GAAG,CAAC,CAAClD,MAAM,CAACqD,MAAM,CAAC,CAAC;MAC7B;MACAlB,aAAa,CAACe,GAAG,CAAC,CAACjD,MAAM,GAAGqD,SAAS;MACrCnB,aAAa,CAACe,GAAG,CAAC,CAAClD,MAAM,GAAGsD,SAAS;MACrC1B,MAAM,CAACsB,GAAG,CAAC,CAACjD,MAAM,GAAGqD,SAAS;MAC9B1B,MAAM,CAACsB,GAAG,CAAC,CAAClD,MAAM,GAAGsD,SAAS;IAChC,CAAC,MAAM;MACL,IAAI1B,MAAM,CAACwB,SAAS,EAAE;QACpBxB,MAAM,CAACsB,GAAG,CAAC,CAAC9C,EAAE,CAACiD,MAAM,CAAC,CAAC;MACzB;MACAlB,aAAa,CAACe,GAAG,CAAC,CAAC9C,EAAE,GAAGkD,SAAS;MACjC1B,MAAM,CAACsB,GAAG,CAAC,CAAC9C,EAAE,GAAGkD,SAAS;IAC5B;EACF,CAAC;EACD,IAAIvB,aAAa,CAACP,QAAQ,CAAC,MAAM,CAAC,IAAII,MAAM,CAACwB,SAAS,EAAE;IACtD,IAAIjB,aAAa,CAACoB,IAAI,IAAI,CAACzB,YAAY,CAACyB,IAAI,EAAE;MAC5CZ,eAAe,GAAG,IAAI;IACxB,CAAC,MAAM,IAAI,CAACR,aAAa,CAACoB,IAAI,IAAIzB,YAAY,CAACyB,IAAI,EAAE;MACnDX,cAAc,GAAG,IAAI;IACvB,CAAC,MAAM;MACLC,cAAc,GAAG,IAAI;IACvB;EACF;EACAX,YAAY,CAACvC,OAAO,CAACF,GAAG,IAAI;IAC1B,IAAIf,QAAQ,CAACyD,aAAa,CAAC1C,GAAG,CAAC,CAAC,IAAIf,QAAQ,CAACoD,YAAY,CAACrC,GAAG,CAAC,CAAC,EAAE;MAC/DZ,MAAM,CAAC2E,MAAM,CAACrB,aAAa,CAAC1C,GAAG,CAAC,EAAEqC,YAAY,CAACrC,GAAG,CAAC,CAAC;MACpD,IAAI,CAACA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,KAAK,SAAS,IAAIqC,YAAY,CAACrC,GAAG,CAAC,IAAI,CAACqC,YAAY,CAACrC,GAAG,CAAC,CAACgE,OAAO,EAAE;QACzIR,aAAa,CAACxD,GAAG,CAAC;MACpB;IACF,CAAC,MAAM;MACL,MAAMiE,QAAQ,GAAG5B,YAAY,CAACrC,GAAG,CAAC;MAClC,IAAI,CAACiE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,MAAMjE,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,CAAC,EAAE;QACtH,IAAIiE,QAAQ,KAAK,KAAK,EAAE;UACtBT,aAAa,CAACxD,GAAG,CAAC;QACpB;MACF,CAAC,MAAM;QACL0C,aAAa,CAAC1C,GAAG,CAAC,GAAGqC,YAAY,CAACrC,GAAG,CAAC;MACxC;IACF;EACF,CAAC,CAAC;EACF,IAAIyC,YAAY,CAACV,QAAQ,CAAC,YAAY,CAAC,IAAI,CAACe,kBAAkB,IAAIX,MAAM,CAACmB,UAAU,IAAInB,MAAM,CAACmB,UAAU,CAACC,OAAO,IAAIb,aAAa,CAACY,UAAU,IAAIZ,aAAa,CAACY,UAAU,CAACC,OAAO,EAAE;IAChLpB,MAAM,CAACmB,UAAU,CAACC,OAAO,GAAGb,aAAa,CAACY,UAAU,CAACC,OAAO;EAC9D;EACA,IAAIjB,aAAa,CAACP,QAAQ,CAAC,UAAU,CAAC,IAAIK,MAAM,IAAIO,OAAO,IAAID,aAAa,CAACC,OAAO,CAACqB,OAAO,EAAE;IAC5FrB,OAAO,CAACP,MAAM,GAAGA,MAAM;IACvBO,OAAO,CAACuB,MAAM,CAAC,IAAI,CAAC;EACtB,CAAC,MAAM,IAAI5B,aAAa,CAACP,QAAQ,CAAC,SAAS,CAAC,IAAIY,OAAO,IAAID,aAAa,CAACC,OAAO,CAACqB,OAAO,EAAE;IACxF,IAAI5B,MAAM,EAAEO,OAAO,CAACP,MAAM,GAAGA,MAAM;IACnCO,OAAO,CAACuB,MAAM,CAAC,IAAI,CAAC;EACtB;EACA,IAAI5B,aAAa,CAACP,QAAQ,CAAC,UAAU,CAAC,IAAIK,MAAM,IAAIM,aAAa,CAACoB,IAAI,EAAE;IACtEV,cAAc,GAAG,IAAI;EACvB;EACA,IAAIP,cAAc,EAAE;IAClB,MAAMsB,WAAW,GAAGvB,MAAM,CAACwB,IAAI,CAAC,CAAC;IACjC,IAAID,WAAW,EAAEvB,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC;EACtC;EACA,IAAIpB,kBAAkB,EAAE;IACtBX,MAAM,CAACmB,UAAU,CAACC,OAAO,GAAGb,aAAa,CAACY,UAAU,CAACC,OAAO;EAC9D;EACA,IAAIR,kBAAkB,EAAE;IACtB,IAAIZ,MAAM,CAACwB,SAAS,KAAK,CAACnB,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,CAAC,EAAE;MAC3EA,YAAY,GAAG6B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC5C9B,YAAY,CAAC+B,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAC/ChC,YAAY,CAACiC,IAAI,CAACD,GAAG,CAAC,YAAY,CAAC;MACnCrC,MAAM,CAACxB,EAAE,CAAC+D,WAAW,CAAClC,YAAY,CAAC;IACrC;IACA,IAAIA,YAAY,EAAEE,aAAa,CAAChC,UAAU,CAACC,EAAE,GAAG6B,YAAY;IAC5D9B,UAAU,CAAC0D,IAAI,CAAC,CAAC;IACjB1D,UAAU,CAACiE,MAAM,CAAC,CAAC;IACnBjE,UAAU,CAACwD,MAAM,CAAC,CAAC;EACrB;EACA,IAAIlB,iBAAiB,EAAE;IACrB,IAAIb,MAAM,CAACwB,SAAS,KAAK,CAACpB,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,CAAC,EAAE;MACzEA,WAAW,GAAG8B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3C/B,WAAW,CAACgC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC7CjC,WAAW,CAACkC,IAAI,CAACD,GAAG,CAAC,WAAW,CAAC;MACjCrC,MAAM,CAACxB,EAAE,CAAC+D,WAAW,CAACnC,WAAW,CAAC;IACpC;IACA,IAAIA,WAAW,EAAEG,aAAa,CAAC7B,SAAS,CAACF,EAAE,GAAG4B,WAAW;IACzD1B,SAAS,CAACuD,IAAI,CAAC,CAAC;IAChBvD,SAAS,CAAC+D,UAAU,CAAC,CAAC;IACtB/D,SAAS,CAACgE,YAAY,CAAC,CAAC;EAC1B;EACA,IAAI5B,kBAAkB,EAAE;IACtB,IAAId,MAAM,CAACwB,SAAS,EAAE;MACpB,IAAI,CAACpD,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACzCA,MAAM,GAAG8D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtC/D,MAAM,CAACgE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAC1CjE,MAAM,CAACuE,SAAS,GAAG3C,MAAM,CAAC4C,MAAM,CAAC5F,WAAW,CAAC6F,aAAa;QAC1DzE,MAAM,CAACkE,IAAI,CAACD,GAAG,CAAC,aAAa,CAAC;QAC9BrC,MAAM,CAACxB,EAAE,CAAC+D,WAAW,CAACnE,MAAM,CAAC;MAC/B;MACA,IAAI,CAACC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QACzCA,MAAM,GAAG6D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACtC9D,MAAM,CAAC+D,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAC1ChE,MAAM,CAACsE,SAAS,GAAG3C,MAAM,CAAC4C,MAAM,CAAC5F,WAAW,CAAC8F,aAAa;QAC1DzE,MAAM,CAACiE,IAAI,CAACD,GAAG,CAAC,aAAa,CAAC;QAC9BrC,MAAM,CAACxB,EAAE,CAAC+D,WAAW,CAAClE,MAAM,CAAC;MAC/B;IACF;IACA,IAAID,MAAM,EAAEmC,aAAa,CAACpC,UAAU,CAACC,MAAM,GAAGA,MAAM;IACpD,IAAIC,MAAM,EAAEkC,aAAa,CAACpC,UAAU,CAACE,MAAM,GAAGA,MAAM;IACpDF,UAAU,CAAC8D,IAAI,CAAC,CAAC;IACjB9D,UAAU,CAAC4D,MAAM,CAAC,CAAC;EACrB;EACA,IAAI5B,aAAa,CAACP,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IAC5CI,MAAM,CAAC+C,cAAc,GAAG7C,YAAY,CAAC6C,cAAc;EACrD;EACA,IAAI5C,aAAa,CAACP,QAAQ,CAAC,gBAAgB,CAAC,EAAE;IAC5CI,MAAM,CAACgD,cAAc,GAAG9C,YAAY,CAAC8C,cAAc;EACrD;EACA,IAAI7C,aAAa,CAACP,QAAQ,CAAC,WAAW,CAAC,EAAE;IACvCI,MAAM,CAACiD,eAAe,CAAC/C,YAAY,CAACgD,SAAS,EAAE,KAAK,CAAC;EACvD;EACA,IAAInC,eAAe,IAAIE,cAAc,EAAE;IACrCjB,MAAM,CAACmD,WAAW,CAAC,CAAC;EACtB;EACA,IAAInC,cAAc,IAAIC,cAAc,EAAE;IACpCjB,MAAM,CAACoD,UAAU,CAAC,CAAC;EACrB;EACApD,MAAM,CAAC+B,MAAM,CAAC,CAAC;AACjB;AAEA,SAASzD,eAAe,IAAI+E,CAAC,EAAE5E,cAAc,IAAI6E,CAAC,EAAEjE,UAAU,IAAIL,CAAC,EAAEL,aAAa,IAAI4E,CAAC,EAAEhG,MAAM,IAAIiG,CAAC,EAAE1G,QAAQ,IAAI2G,CAAC,EAAExF,eAAe,IAAIyF,CAAC,EAAE7G,UAAU,IAAI8G,CAAC,EAAE7D,YAAY,IAAI8D,CAAC,EAAElE,YAAY,IAAImE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}