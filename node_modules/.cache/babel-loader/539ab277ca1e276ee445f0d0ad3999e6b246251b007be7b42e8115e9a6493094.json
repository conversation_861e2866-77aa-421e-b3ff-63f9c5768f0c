{"ast": null, "code": "import { useConstant } from '../../utils/use-constant.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { createScopedWaapiAnimate } from '../animators/waapi/animate-style.mjs';\nfunction useAnimateMini() {\n  const scope = useConstant(() => ({\n    current: null,\n    // Will be hydrated by React\n    animations: []\n  }));\n  const animate = useConstant(() => createScopedWaapiAnimate(scope));\n  useUnmountEffect(() => {\n    scope.animations.forEach(animation => animation.stop());\n  });\n  return [scope, animate];\n}\nexport { useAnimateMini };", "map": {"version": 3, "names": ["useConstant", "useUnmountEffect", "createScopedWaapiAnimate", "useAnimateMini", "scope", "current", "animations", "animate", "for<PERSON>ach", "animation", "stop"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/hooks/use-animate-style.mjs"], "sourcesContent": ["import { useConstant } from '../../utils/use-constant.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { createScopedWaapiAnimate } from '../animators/waapi/animate-style.mjs';\n\nfunction useAnimateMini() {\n    const scope = useConstant(() => ({\n        current: null, // Will be hydrated by React\n        animations: [],\n    }));\n    const animate = useConstant(() => createScopedWaapiAnimate(scope));\n    useUnmountEffect(() => {\n        scope.animations.forEach((animation) => animation.stop());\n    });\n    return [scope, animate];\n}\n\nexport { useAnimateMini };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,wBAAwB,QAAQ,sCAAsC;AAE/E,SAASC,cAAcA,CAAA,EAAG;EACtB,MAAMC,KAAK,GAAGJ,WAAW,CAAC,OAAO;IAC7BK,OAAO,EAAE,IAAI;IAAE;IACfC,UAAU,EAAE;EAChB,CAAC,CAAC,CAAC;EACH,MAAMC,OAAO,GAAGP,WAAW,CAAC,MAAME,wBAAwB,CAACE,KAAK,CAAC,CAAC;EAClEH,gBAAgB,CAAC,MAAM;IACnBG,KAAK,CAACE,UAAU,CAACE,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACC,IAAI,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO,CAACN,KAAK,EAAEG,OAAO,CAAC;AAC3B;AAEA,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}