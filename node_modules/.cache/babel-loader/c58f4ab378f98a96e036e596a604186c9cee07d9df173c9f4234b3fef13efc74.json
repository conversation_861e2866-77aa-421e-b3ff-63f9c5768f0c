{"ast": null, "code": "import React from'react';// swiper\nimport{Swiper,SwiperSlide}from'swiper/react';// Import Swiper styles\nimport'swiper/css';import styles from'./filter.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterSwiper=_ref=>{let{activeModel,setActiveModel,cars}=_ref;const tabs=[...new Set(cars.map(car=>car.category))];return/*#__PURE__*/_jsx(\"div\",{className:styles.carsModelsList,children:/*#__PURE__*/_jsxs(Swiper,{breakpoints:{320:{slidesPerView:2.5,spaceBetween:0},580:{slidesPerView:3.5,spaceBetween:0},750:{slidesPerView:4.5,spaceBetween:0},860:{slidesPerView:4.5,spaceBetween:0},1160:{slidesPerView:5.5,spaceBetween:0},1460:{slidesPerView:6.5,spaceBetween:0}},spaceBetween:10,grabCursor:true,className:\"mySwiper\",children:[/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.btn,\" \").concat(activeModel==='Все модели'?styles.active:''),onClick:()=>setActiveModel('Все модели'),children:\"\\u0412\\u0441\\u0435 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438\"})}),tabs.map(item=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.btn,\" \").concat(activeModel===item?styles.active:''),onClick:()=>setActiveModel(item),children:item})},item)),/*#__PURE__*/_jsx(\"div\",{className:styles.activeBar})]})});};export default FilterSwiper;", "map": {"version": 3, "names": ["React", "Swiper", "SwiperSlide", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "FilterSwiper", "_ref", "activeModel", "setActiveModel", "cars", "tabs", "Set", "map", "car", "category", "className", "carsModelsList", "children", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "grabCursor", "concat", "btn", "active", "onClick", "item", "activeBar"], "sources": ["/var/www/html/gwm.tj/src/components/FilterSlide/FilterSwiper.jsx"], "sourcesContent": ["import React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\n\nconst FilterSwiper = ({ activeModel, setActiveModel, cars }) => {\n  const tabs = [...new Set(cars.map((car) => car.category))];\n\n  return (\n    <div className={styles.carsModelsList}>\n      <Swiper\n        breakpoints={{\n          320: {\n            slidesPerView: 2.5,\n            spaceBetween: 0,\n          },\n          580: {\n            slidesPerView: 3.5,\n            spaceBetween: 0,\n          },\n          750: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          860: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          1160: {\n            slidesPerView: 5.5,\n            spaceBetween: 0,\n          },\n\n          1460: {\n            slidesPerView: 6.5,\n            spaceBetween: 0,\n          },\n        }}\n        spaceBetween={10}\n        grabCursor={true}\n        className=\"mySwiper\"\n      >\n        <SwiperSlide>\n          <div\n            className={`${styles.btn} ${\n              activeModel === 'Все модели' ? styles.active : ''\n            }`}\n            onClick={() => setActiveModel('Все модели')}\n          >\n            Все модели\n          </div>\n        </SwiperSlide>\n        {tabs.map((item) => (\n          <SwiperSlide key={item}>\n            <div\n              className={`${styles.btn} ${\n                activeModel === item ? styles.active : ''\n              }`}\n              onClick={() => setActiveModel(item)}\n            >\n              {item}\n            </div>\n          </SwiperSlide>\n        ))}\n        <div className={styles.activeBar}></div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default FilterSwiper;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB;AACA,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD;AACA,MAAO,YAAY,CACnB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,WAAW,CAAEC,cAAc,CAAEC,IAAK,CAAC,CAAAH,IAAA,CACzD,KAAM,CAAAI,IAAI,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACF,IAAI,CAACG,GAAG,CAAEC,GAAG,EAAKA,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC,CAE1D,mBACEZ,IAAA,QAAKa,SAAS,CAAEf,MAAM,CAACgB,cAAe,CAAAC,QAAA,cACpCb,KAAA,CAACN,MAAM,EACLoB,WAAW,CAAE,CACX,GAAG,CAAE,CACHC,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CAED,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CACF,CAAE,CACFA,YAAY,CAAE,EAAG,CACjBC,UAAU,CAAE,IAAK,CACjBN,SAAS,CAAC,UAAU,CAAAE,QAAA,eAEpBf,IAAA,CAACH,WAAW,EAAAkB,QAAA,cACVf,IAAA,QACEa,SAAS,IAAAO,MAAA,CAAKtB,MAAM,CAACuB,GAAG,MAAAD,MAAA,CACtBf,WAAW,GAAK,YAAY,CAAGP,MAAM,CAACwB,MAAM,CAAG,EAAE,CAChD,CACHC,OAAO,CAAEA,CAAA,GAAMjB,cAAc,CAAC,YAAY,CAAE,CAAAS,QAAA,CAC7C,yDAED,CAAK,CAAC,CACK,CAAC,CACbP,IAAI,CAACE,GAAG,CAAEc,IAAI,eACbxB,IAAA,CAACH,WAAW,EAAAkB,QAAA,cACVf,IAAA,QACEa,SAAS,IAAAO,MAAA,CAAKtB,MAAM,CAACuB,GAAG,MAAAD,MAAA,CACtBf,WAAW,GAAKmB,IAAI,CAAG1B,MAAM,CAACwB,MAAM,CAAG,EAAE,CACxC,CACHC,OAAO,CAAEA,CAAA,GAAMjB,cAAc,CAACkB,IAAI,CAAE,CAAAT,QAAA,CAEnCS,IAAI,CACF,CAAC,EARUA,IASL,CACd,CAAC,cACFxB,IAAA,QAAKa,SAAS,CAAEf,MAAM,CAAC2B,SAAU,CAAM,CAAC,EAClC,CAAC,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}