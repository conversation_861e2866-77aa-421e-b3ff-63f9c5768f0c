{"ast": null, "code": "import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMotionComponent } from './create.mjs';\nconst motion = /*@__PURE__*/createDOMMotionComponentProxy(createMotionComponent);\nexport { motion };", "map": {"version": 3, "names": ["createDOMMotionComponentProxy", "createMotionComponent", "motion"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs"], "sourcesContent": ["import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMotionComponent } from './create.mjs';\n\nconst motion = /*@__PURE__*/ createDOMMotionComponentProxy(createMotionComponent);\n\nexport { motion };\n"], "mappings": "AAAA,SAASA,6BAA6B,QAAQ,qBAAqB;AACnE,SAASC,qBAAqB,QAAQ,cAAc;AAEpD,MAAMC,MAAM,GAAG,aAAcF,6BAA6B,CAACC,qBAAqB,CAAC;AAEjF,SAASC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}