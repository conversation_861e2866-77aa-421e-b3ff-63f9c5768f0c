{"ast": null, "code": "import { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, {\n  container = document.scrollingElement,\n  ...options\n} = {}) {\n  if (!container) return noop;\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) handler.measure();\n    };\n    const updateAll = () => {\n      for (const handler of containerHandlers) {\n        handler.update(frameData.timestamp);\n      }\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) handler.notify();\n    };\n    const listener = () => {\n      frame.read(measureAll);\n      frame.read(updateAll);\n      frame.preUpdate(notifyAll);\n    };\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n    listener();\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      resizeListeners.get(container)?.();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "noop", "resize", "createScrollInfo", "createOnScrollHandler", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "scrollingElement", "window", "scrollInfo", "onScroll", "container", "options", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "updateAll", "update", "timestamp", "notifyAll", "notify", "listener", "read", "preUpdate", "target", "addEventListener", "passive", "documentElement", "currentHandlers", "delete", "size", "scrollListener", "removeEventListener"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            frame.read(measureAll);\n            frame.read(updateAll);\n            frame.preUpdate(notifyAll);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AAC1D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,gBAAgB,GAAGC,MAAM,GAAGH,OAAO;AAC5F,SAASI,UAAUA,CAACC,QAAQ,EAAE;EAAEC,SAAS,GAAGL,QAAQ,CAACC,gBAAgB;EAAE,GAAGK;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EACtF,IAAI,CAACD,SAAS,EACV,OAAOf,IAAI;EACf,IAAIiB,iBAAiB,GAAGV,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACE,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7BZ,gBAAgB,CAACa,GAAG,CAACL,SAAS,EAAEE,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGnB,gBAAgB,CAAC,CAAC;EAC/B,MAAMoB,gBAAgB,GAAGnB,qBAAqB,CAACY,SAAS,EAAED,QAAQ,EAAEO,IAAI,EAAEL,OAAO,CAAC;EAClFC,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAAClB,eAAe,CAACoB,GAAG,CAACT,SAAS,CAAC,EAAE;IACjC,MAAMU,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACC,OAAO,CAAC,CAAC;IACzB,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMF,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACG,MAAM,CAAC9B,SAAS,CAAC+B,SAAS,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAML,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACM,MAAM,CAAC,CAAC;IACxB,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnBpC,KAAK,CAACqC,IAAI,CAACT,UAAU,CAAC;MACtB5B,KAAK,CAACqC,IAAI,CAACN,SAAS,CAAC;MACrB/B,KAAK,CAACsC,SAAS,CAACJ,SAAS,CAAC;IAC9B,CAAC;IACD3B,eAAe,CAACgB,GAAG,CAACL,SAAS,EAAEkB,QAAQ,CAAC;IACxC,MAAMG,MAAM,GAAG5B,cAAc,CAACO,SAAS,CAAC;IACxCH,MAAM,CAACyB,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIvB,SAAS,KAAKL,QAAQ,CAAC6B,eAAe,EAAE;MACxCjC,eAAe,CAACc,GAAG,CAACL,SAAS,EAAEd,MAAM,CAACc,SAAS,EAAEkB,QAAQ,CAAC,CAAC;IAC/D;IACAG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9DL,QAAQ,CAAC,CAAC;EACd;EACA,MAAMA,QAAQ,GAAG7B,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;EAC/ClB,KAAK,CAACqC,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACTnC,WAAW,CAACmC,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMO,eAAe,GAAGjC,gBAAgB,CAACW,GAAG,CAACH,SAAS,CAAC;IACvD,IAAI,CAACyB,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAACnB,gBAAgB,CAAC;IACxC,IAAIkB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAGvC,eAAe,CAACc,GAAG,CAACH,SAAS,CAAC;IACrDX,eAAe,CAACqC,MAAM,CAAC1B,SAAS,CAAC;IACjC,IAAI4B,cAAc,EAAE;MAChBnC,cAAc,CAACO,SAAS,CAAC,CAAC6B,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;MACvErC,eAAe,CAACY,GAAG,CAACH,SAAS,CAAC,GAAG,CAAC;MAClCH,MAAM,CAACgC,mBAAmB,CAAC,QAAQ,EAAED,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAAS9B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}