{"ast": null, "code": "import { wrap } from '../../wrap.mjs';\nimport { isEasingArray } from './is-easing-array.mjs';\nfunction getEasingForSegment(easing, i) {\n  return isEasingArray(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\nexport { getEasingForSegment };", "map": {"version": 3, "names": ["wrap", "isEasingArray", "getEasingForSegment", "easing", "i", "length"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-utils/dist/es/easing/utils/get-easing-for-segment.mjs"], "sourcesContent": ["import { wrap } from '../../wrap.mjs';\nimport { isEasingArray } from './is-easing-array.mjs';\n\nfunction getEasingForSegment(easing, i) {\n    return isEasingArray(easing) ? easing[wrap(0, easing.length, i)] : easing;\n}\n\nexport { getEasingForSegment };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,gBAAgB;AACrC,SAASC,aAAa,QAAQ,uBAAuB;AAErD,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,CAAC,EAAE;EACpC,OAAOH,aAAa,CAACE,MAAM,CAAC,GAAGA,MAAM,CAACH,IAAI,CAAC,CAAC,EAAEG,MAAM,CAACE,MAAM,EAAED,CAAC,CAAC,CAAC,GAAGD,MAAM;AAC7E;AAEA,SAASD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}