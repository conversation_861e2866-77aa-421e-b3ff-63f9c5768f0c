{"ast": null, "code": "const checkStringStartsWith = token => key => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = /*@__PURE__*/checkStringStartsWith(\"--\");\nconst startsAsVariableToken = /*@__PURE__*/checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = value => {\n  const startsWithToken = startsAsVariableToken(value);\n  if (!startsWithToken) return false;\n  // Ensure any comments are stripped from the value as this can harm performance of the regex.\n  return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\x2D0-9A-Z_a-z\\u017F\\u212A]+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*|[\\x2D0-9A-Z_a-z\\u017F\\u212A]+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*,(?:[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*(?:(?![\\t-\\r \\(\\)\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uD800-\\uDFFF\\uFEFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*\\((?:(?:(?![\\(\\)\\uD800-\\uDFFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|\\((?:(?![\\(\\)\\uD800-\\uDFFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])*\\))*\\))+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*)\\)$/i;\nexport { isCSSVariableName, isCSSVariableToken };", "map": {"version": 3, "names": ["checkStringStartsWith", "token", "key", "startsWith", "isCSSVariableName", "startsAsVariableToken", "isCSSVariableToken", "value", "startsWithToken", "singleCssVariableRegex", "test", "split", "trim"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs"], "sourcesContent": ["const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAIC,KAAK,IAAMC,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,UAAU,CAACF,KAAK,CAAC;AAClG,MAAMG,iBAAiB,GACvB,aAAcJ,qBAAqB,CAAC,IAAI,CAAC;AACzC,MAAMK,qBAAqB,GAC3B,aAAcL,qBAAqB,CAAC,QAAQ,CAAC;AAC7C,MAAMM,kBAAkB,GAAIC,KAAK,IAAK;EAClC,MAAMC,eAAe,GAAGH,qBAAqB,CAACE,KAAK,CAAC;EACpD,IAAI,CAACC,eAAe,EAChB,OAAO,KAAK;EAChB;EACA,OAAOC,sBAAsB,CAACC,IAAI,CAACH,KAAK,CAACI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;AACnE,CAAC;AACD,MAAMH,sBAAsB,GAAG,8qBAAqF;AAEpH,SAASL,iBAAiB,EAAEE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}