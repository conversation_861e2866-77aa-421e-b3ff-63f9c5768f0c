{"ast": null, "code": "import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nconst propEventHandlers = [\"AnimationStart\", \"AnimationComplete\", \"Update\", \"BeforeLayoutMeasure\", \"LayoutMeasure\", \"LayoutAnimationStart\", \"LayoutAnimationComplete\"];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n  /**\n   * This method takes React props and returns found MotionValues. For example, HTML\n   * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n   *\n   * This isn't an abstract method as it needs calling in the constructor, but it is\n   * intended to be one.\n   */\n  scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n    return {};\n  }\n  constructor({\n    parent,\n    props,\n    presenceContext,\n    reducedMotionConfig,\n    blockInitialAnimation,\n    visualState\n  }, options = {}) {\n    /**\n     * A reference to the current underlying Instance, e.g. a HTMLElement\n     * or Three.Mesh etc.\n     */\n    this.current = null;\n    /**\n     * A set containing references to this VisualElement's children.\n     */\n    this.children = new Set();\n    /**\n     * Determine what role this visual element should take in the variant tree.\n     */\n    this.isVariantNode = false;\n    this.isControllingVariants = false;\n    /**\n     * Decides whether this VisualElement should animate in reduced motion\n     * mode.\n     *\n     * TODO: This is currently set on every individual VisualElement but feels\n     * like it could be set globally.\n     */\n    this.shouldReduceMotion = null;\n    /**\n     * A map of all motion values attached to this visual element. Motion\n     * values are source of truth for any given animated value. A motion\n     * value might be provided externally by the component via props.\n     */\n    this.values = new Map();\n    this.KeyframeResolver = KeyframeResolver;\n    /**\n     * Cleanup functions for active features (hover/tap/exit etc)\n     */\n    this.features = {};\n    /**\n     * A map of every subscription that binds the provided or generated\n     * motion values onChange listeners to this visual element.\n     */\n    this.valueSubscriptions = new Map();\n    /**\n     * A reference to the previously-provided motion values as returned\n     * from scrapeMotionValuesFromProps. We use the keys in here to determine\n     * if any motion values need to be removed after props are updated.\n     */\n    this.prevMotionValues = {};\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    /**\n     * An object containing an unsubscribe function for each prop event subscription.\n     * For example, every \"Update\" event can have multiple subscribers via\n     * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n     */\n    this.propEventSubscriptions = {};\n    this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n    this.render = () => {\n      if (!this.current) return;\n      this.triggerBuild();\n      this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n    };\n    this.renderScheduledAt = 0.0;\n    this.scheduleRender = () => {\n      const now = time.now();\n      if (this.renderScheduledAt < now) {\n        this.renderScheduledAt = now;\n        frame.render(this.render, false, true);\n      }\n    };\n    const {\n      latestValues,\n      renderState\n    } = visualState;\n    this.latestValues = latestValues;\n    this.baseTarget = {\n      ...latestValues\n    };\n    this.initialValues = props.initial ? {\n      ...latestValues\n    } : {};\n    this.renderState = renderState;\n    this.parent = parent;\n    this.props = props;\n    this.presenceContext = presenceContext;\n    this.depth = parent ? parent.depth + 1 : 0;\n    this.reducedMotionConfig = reducedMotionConfig;\n    this.options = options;\n    this.blockInitialAnimation = Boolean(blockInitialAnimation);\n    this.isControllingVariants = isControllingVariants(props);\n    this.isVariantNode = isVariantNode(props);\n    if (this.isVariantNode) {\n      this.variantChildren = new Set();\n    }\n    this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n    /**\n     * Any motion values that are provided to the element when created\n     * aren't yet bound to the element, as this would technically be impure.\n     * However, we iterate through the motion values and set them to the\n     * initial values for this component.\n     *\n     * TODO: This is impure and we should look at changing this to run on mount.\n     * Doing so will break some tests but this isn't necessarily a breaking change,\n     * more a reflection of the test.\n     */\n    const {\n      willChange,\n      ...initialMotionValues\n    } = this.scrapeMotionValuesFromProps(props, {}, this);\n    for (const key in initialMotionValues) {\n      const value = initialMotionValues[key];\n      if (latestValues[key] !== undefined && isMotionValue(value)) {\n        value.set(latestValues[key], false);\n      }\n    }\n  }\n  mount(instance) {\n    this.current = instance;\n    visualElementStore.set(instance, this);\n    if (this.projection && !this.projection.instance) {\n      this.projection.mount(instance);\n    }\n    if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n      this.removeFromVariantTree = this.parent.addVariantChild(this);\n    }\n    this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n    if (!hasReducedMotionListener.current) {\n      initPrefersReducedMotion();\n    }\n    this.shouldReduceMotion = this.reducedMotionConfig === \"never\" ? false : this.reducedMotionConfig === \"always\" ? true : prefersReducedMotion.current;\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    if (this.parent) this.parent.children.add(this);\n    this.update(this.props, this.presenceContext);\n  }\n  unmount() {\n    this.projection && this.projection.unmount();\n    cancelFrame(this.notifyUpdate);\n    cancelFrame(this.render);\n    this.valueSubscriptions.forEach(remove => remove());\n    this.valueSubscriptions.clear();\n    this.removeFromVariantTree && this.removeFromVariantTree();\n    this.parent && this.parent.children.delete(this);\n    for (const key in this.events) {\n      this.events[key].clear();\n    }\n    for (const key in this.features) {\n      const feature = this.features[key];\n      if (feature) {\n        feature.unmount();\n        feature.isMounted = false;\n      }\n    }\n    this.current = null;\n  }\n  bindToMotionValue(key, value) {\n    if (this.valueSubscriptions.has(key)) {\n      this.valueSubscriptions.get(key)();\n    }\n    const valueIsTransform = transformProps.has(key);\n    if (valueIsTransform && this.onBindTransform) {\n      this.onBindTransform();\n    }\n    const removeOnChange = value.on(\"change\", latestValue => {\n      this.latestValues[key] = latestValue;\n      this.props.onUpdate && frame.preRender(this.notifyUpdate);\n      if (valueIsTransform && this.projection) {\n        this.projection.isTransformDirty = true;\n      }\n    });\n    const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n    let removeSyncCheck;\n    if (window.MotionCheckAppearSync) {\n      removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n    }\n    this.valueSubscriptions.set(key, () => {\n      removeOnChange();\n      removeOnRenderRequest();\n      if (removeSyncCheck) removeSyncCheck();\n      if (value.owner) value.stop();\n    });\n  }\n  sortNodePosition(other) {\n    /**\n     * If these nodes aren't even of the same type we can't compare their depth.\n     */\n    if (!this.current || !this.sortInstanceNodePosition || this.type !== other.type) {\n      return 0;\n    }\n    return this.sortInstanceNodePosition(this.current, other.current);\n  }\n  updateFeatures() {\n    let key = \"animation\";\n    for (key in featureDefinitions) {\n      const featureDefinition = featureDefinitions[key];\n      if (!featureDefinition) continue;\n      const {\n        isEnabled,\n        Feature: FeatureConstructor\n      } = featureDefinition;\n      /**\n       * If this feature is enabled but not active, make a new instance.\n       */\n      if (!this.features[key] && FeatureConstructor && isEnabled(this.props)) {\n        this.features[key] = new FeatureConstructor(this);\n      }\n      /**\n       * If we have a feature, mount or update it.\n       */\n      if (this.features[key]) {\n        const feature = this.features[key];\n        if (feature.isMounted) {\n          feature.update();\n        } else {\n          feature.mount();\n          feature.isMounted = true;\n        }\n      }\n    }\n  }\n  triggerBuild() {\n    this.build(this.renderState, this.latestValues, this.props);\n  }\n  /**\n   * Measure the current viewport box with or without transforms.\n   * Only measures axis-aligned boxes, rotate and skew must be manually\n   * removed with a re-render to work.\n   */\n  measureViewportBox() {\n    return this.current ? this.measureInstanceViewportBox(this.current, this.props) : createBox();\n  }\n  getStaticValue(key) {\n    return this.latestValues[key];\n  }\n  setStaticValue(key, value) {\n    this.latestValues[key] = value;\n  }\n  /**\n   * Update the provided props. Ensure any newly-added motion values are\n   * added to our map, old ones removed, and listeners updated.\n   */\n  update(props, presenceContext) {\n    if (props.transformTemplate || this.props.transformTemplate) {\n      this.scheduleRender();\n    }\n    this.prevProps = this.props;\n    this.props = props;\n    this.prevPresenceContext = this.presenceContext;\n    this.presenceContext = presenceContext;\n    /**\n     * Update prop event handlers ie onAnimationStart, onAnimationComplete\n     */\n    for (let i = 0; i < propEventHandlers.length; i++) {\n      const key = propEventHandlers[i];\n      if (this.propEventSubscriptions[key]) {\n        this.propEventSubscriptions[key]();\n        delete this.propEventSubscriptions[key];\n      }\n      const listenerName = \"on\" + key;\n      const listener = props[listenerName];\n      if (listener) {\n        this.propEventSubscriptions[key] = this.on(key, listener);\n      }\n    }\n    this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n    if (this.handleChildMotionValue) {\n      this.handleChildMotionValue();\n    }\n  }\n  getProps() {\n    return this.props;\n  }\n  /**\n   * Returns the variant definition with a given name.\n   */\n  getVariant(name) {\n    return this.props.variants ? this.props.variants[name] : undefined;\n  }\n  /**\n   * Returns the defined default transition on this component.\n   */\n  getDefaultTransition() {\n    return this.props.transition;\n  }\n  getTransformPagePoint() {\n    return this.props.transformPagePoint;\n  }\n  getClosestVariantNode() {\n    return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : undefined;\n  }\n  /**\n   * Add a child visual element to our set of children.\n   */\n  addVariantChild(child) {\n    const closestVariantNode = this.getClosestVariantNode();\n    if (closestVariantNode) {\n      closestVariantNode.variantChildren && closestVariantNode.variantChildren.add(child);\n      return () => closestVariantNode.variantChildren.delete(child);\n    }\n  }\n  /**\n   * Add a motion value and bind it to this visual element.\n   */\n  addValue(key, value) {\n    // Remove existing value if it exists\n    const existingValue = this.values.get(key);\n    if (value !== existingValue) {\n      if (existingValue) this.removeValue(key);\n      this.bindToMotionValue(key, value);\n      this.values.set(key, value);\n      this.latestValues[key] = value.get();\n    }\n  }\n  /**\n   * Remove a motion value and unbind any active subscriptions.\n   */\n  removeValue(key) {\n    this.values.delete(key);\n    const unsubscribe = this.valueSubscriptions.get(key);\n    if (unsubscribe) {\n      unsubscribe();\n      this.valueSubscriptions.delete(key);\n    }\n    delete this.latestValues[key];\n    this.removeValueFromRenderState(key, this.renderState);\n  }\n  /**\n   * Check whether we have a motion value for this key\n   */\n  hasValue(key) {\n    return this.values.has(key);\n  }\n  getValue(key, defaultValue) {\n    if (this.props.values && this.props.values[key]) {\n      return this.props.values[key];\n    }\n    let value = this.values.get(key);\n    if (value === undefined && defaultValue !== undefined) {\n      value = motionValue(defaultValue === null ? undefined : defaultValue, {\n        owner: this\n      });\n      this.addValue(key, value);\n    }\n    return value;\n  }\n  /**\n   * If we're trying to animate to a previously unencountered value,\n   * we need to check for it in our state and as a last resort read it\n   * directly from the instance (which might have performance implications).\n   */\n  readValue(key, target) {\n    let value = this.latestValues[key] !== undefined || !this.current ? this.latestValues[key] : this.getBaseTargetFromProps(this.props, key) ?? this.readValueFromInstance(this.current, key, this.options);\n    if (value !== undefined && value !== null) {\n      if (typeof value === \"string\" && (isNumericalString(value) || isZeroValueString(value))) {\n        // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n        value = parseFloat(value);\n      } else if (!findValueType(value) && complex.test(target)) {\n        value = getAnimatableNone(key, target);\n      }\n      this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n    }\n    return isMotionValue(value) ? value.get() : value;\n  }\n  /**\n   * Set the base target to later animate back to. This is currently\n   * only hydrated on creation and when we first read a value.\n   */\n  setBaseTarget(key, value) {\n    this.baseTarget[key] = value;\n  }\n  /**\n   * Find the base target for a value thats been removed from all animation\n   * props.\n   */\n  getBaseTarget(key) {\n    const {\n      initial\n    } = this.props;\n    let valueFromInitial;\n    if (typeof initial === \"string\" || typeof initial === \"object\") {\n      const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n      if (variant) {\n        valueFromInitial = variant[key];\n      }\n    }\n    /**\n     * If this value still exists in the current initial variant, read that.\n     */\n    if (initial && valueFromInitial !== undefined) {\n      return valueFromInitial;\n    }\n    /**\n     * Alternatively, if this VisualElement config has defined a getBaseTarget\n     * so we can read the value from an alternative source, try that.\n     */\n    const target = this.getBaseTargetFromProps(this.props, key);\n    if (target !== undefined && !isMotionValue(target)) return target;\n    /**\n     * If the value was initially defined on initial, but it doesn't any more,\n     * return undefined. Otherwise return the value as initially read from the DOM.\n     */\n    return this.initialValues[key] !== undefined && valueFromInitial === undefined ? undefined : this.baseTarget[key];\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  notify(eventName, ...args) {\n    if (this.events[eventName]) {\n      this.events[eventName].notify(...args);\n    }\n  }\n}\nexport { VisualElement };", "map": {"version": 3, "names": ["KeyframeResolver", "time", "frame", "isMotionValue", "cancelFrame", "transformProps", "motionValue", "findValueType", "complex", "getAnimatableNone", "warnOnce", "isNumericalString", "isZeroValueString", "SubscriptionManager", "featureDefinitions", "createBox", "initPrefersReducedMotion", "hasReducedMotionListener", "prefersReducedMotion", "visualElementStore", "isControllingVariants", "isVariantNode", "updateMotionValuesFromProps", "resolveVariantFromProps", "propEventHandlers", "VisualElement", "scrapeMotionValuesFromProps", "_props", "_prevProps", "_visualElement", "constructor", "parent", "props", "presenceContext", "reducedMotionConfig", "blockInitialAnimation", "visualState", "options", "current", "children", "Set", "shouldReduceMotion", "values", "Map", "features", "valueSubscriptions", "prevMotionValues", "events", "propEventSubscriptions", "notifyUpdate", "notify", "latestValues", "render", "triggerBuild", "renderInstance", "renderState", "style", "projection", "renderScheduledAt", "scheduleRender", "now", "baseTarget", "initialValues", "initial", "depth", "Boolean", "variant<PERSON><PERSON><PERSON>n", "manuallyAnimateOnMount", "<PERSON><PERSON><PERSON><PERSON>", "initialMotionValues", "key", "value", "undefined", "set", "mount", "instance", "removeFromVariantTree", "addVariant<PERSON>hild", "for<PERSON>ach", "bindToMotionValue", "process", "env", "NODE_ENV", "add", "update", "unmount", "remove", "clear", "delete", "feature", "isMounted", "has", "get", "valueIsTransform", "onBindTransform", "removeOnChange", "on", "latestValue", "onUpdate", "preRender", "isTransformDirty", "removeOnRenderRequest", "removeSyncCheck", "window", "MotionCheckAppearSync", "owner", "stop", "sortNodePosition", "other", "sortInstanceNodePosition", "type", "updateFeatures", "featureDefinition", "isEnabled", "Feature", "FeatureConstructor", "build", "measureViewportBox", "measureInstanceViewportBox", "getStaticValue", "setStaticValue", "transformTemplate", "prevProps", "prevPresenceContext", "i", "length", "listenerName", "listener", "handleChildMotionValue", "getProps", "getVariant", "name", "variants", "getDefaultTransition", "transition", "getTransformPagePoint", "transformPagePoint", "getClosestVariantNode", "child", "closestVariantNode", "addValue", "existingValue", "removeValue", "unsubscribe", "removeValueFromRenderState", "hasValue", "getValue", "defaultValue", "readValue", "target", "getBaseTargetFromProps", "readValueFromInstance", "parseFloat", "test", "set<PERSON><PERSON><PERSON><PERSON>get", "getBase<PERSON>arget", "valueFromInitial", "variant", "custom", "eventName", "callback", "args"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,IAAI,EAAEC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,EAAEC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAEC,iBAAiB,QAAQ,YAAY;AAC9J,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAQ,cAAc;AAClG,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAClG,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,qCAAqC;AAC1F,SAASC,2BAA2B,QAAQ,2BAA2B;AACvE,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE,MAAMC,iBAAiB,GAAG,CACtB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,EACrB,eAAe,EACf,sBAAsB,EACtB,yBAAyB,CAC5B;AACD;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,2BAA2BA,CAACC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAE;IAC5D,OAAO,CAAC,CAAC;EACb;EACAC,WAAWA,CAAC;IAAEC,MAAM;IAAEC,KAAK;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,qBAAqB;IAAEC;EAAa,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpH;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB;AACR;AACA;IACQ,IAAI,CAACnB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqB,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC3C,gBAAgB,GAAGA,gBAAgB;IACxC;AACR;AACA;IACQ,IAAI,CAAC4C,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;IAClE,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC,IAAI,CAACd,OAAO,EACb;MACJ,IAAI,CAACe,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACiB,WAAW,EAAE,IAAI,CAACvB,KAAK,CAACwB,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;IAC1F,CAAC;IACD,IAAI,CAACC,iBAAiB,GAAG,GAAG;IAC5B,IAAI,CAACC,cAAc,GAAG,MAAM;MACxB,MAAMC,GAAG,GAAG3D,IAAI,CAAC2D,GAAG,CAAC,CAAC;MACtB,IAAI,IAAI,CAACF,iBAAiB,GAAGE,GAAG,EAAE;QAC9B,IAAI,CAACF,iBAAiB,GAAGE,GAAG;QAC5B1D,KAAK,CAACkD,MAAM,CAAC,IAAI,CAACA,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;MAC1C;IACJ,CAAC;IACD,MAAM;MAAED,YAAY;MAAEI;IAAY,CAAC,GAAGnB,WAAW;IACjD,IAAI,CAACe,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACU,UAAU,GAAG;MAAE,GAAGV;IAAa,CAAC;IACrC,IAAI,CAACW,aAAa,GAAG9B,KAAK,CAAC+B,OAAO,GAAG;MAAE,GAAGZ;IAAa,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACxB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC+B,KAAK,GAAGjC,MAAM,GAAGA,MAAM,CAACiC,KAAK,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAAC9B,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,qBAAqB,GAAG8B,OAAO,CAAC9B,qBAAqB,CAAC;IAC3D,IAAI,CAACf,qBAAqB,GAAGA,qBAAqB,CAACY,KAAK,CAAC;IACzD,IAAI,CAACX,aAAa,GAAGA,aAAa,CAACW,KAAK,CAAC;IACzC,IAAI,IAAI,CAACX,aAAa,EAAE;MACpB,IAAI,CAAC6C,eAAe,GAAG,IAAI1B,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAAC2B,sBAAsB,GAAGF,OAAO,CAAClC,MAAM,IAAIA,MAAM,CAACO,OAAO,CAAC;IAC/D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM;MAAE8B,UAAU;MAAE,GAAGC;IAAoB,CAAC,GAAG,IAAI,CAAC3C,2BAA2B,CAACM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IAChG,KAAK,MAAMsC,GAAG,IAAID,mBAAmB,EAAE;MACnC,MAAME,KAAK,GAAGF,mBAAmB,CAACC,GAAG,CAAC;MACtC,IAAInB,YAAY,CAACmB,GAAG,CAAC,KAAKE,SAAS,IAAIrE,aAAa,CAACoE,KAAK,CAAC,EAAE;QACzDA,KAAK,CAACE,GAAG,CAACtB,YAAY,CAACmB,GAAG,CAAC,EAAE,KAAK,CAAC;MACvC;IACJ;EACJ;EACAI,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACrC,OAAO,GAAGqC,QAAQ;IACvBxD,kBAAkB,CAACsD,GAAG,CAACE,QAAQ,EAAE,IAAI,CAAC;IACtC,IAAI,IAAI,CAAClB,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACkB,QAAQ,EAAE;MAC9C,IAAI,CAAClB,UAAU,CAACiB,KAAK,CAACC,QAAQ,CAAC;IACnC;IACA,IAAI,IAAI,CAAC5C,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,CAAC,IAAI,CAACD,qBAAqB,EAAE;MAClE,IAAI,CAACwD,qBAAqB,GAAG,IAAI,CAAC7C,MAAM,CAAC8C,eAAe,CAAC,IAAI,CAAC;IAClE;IACA,IAAI,CAACnC,MAAM,CAACoC,OAAO,CAAC,CAACP,KAAK,EAAED,GAAG,KAAK,IAAI,CAACS,iBAAiB,CAACT,GAAG,EAAEC,KAAK,CAAC,CAAC;IACvE,IAAI,CAACtD,wBAAwB,CAACqB,OAAO,EAAE;MACnCtB,wBAAwB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACyB,kBAAkB,GACnB,IAAI,CAACP,mBAAmB,KAAK,OAAO,GAC9B,KAAK,GACL,IAAI,CAACA,mBAAmB,KAAK,QAAQ,GACjC,IAAI,GACJhB,oBAAoB,CAACoB,OAAO;IAC1C,IAAI0C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCxE,QAAQ,CAAC,IAAI,CAAC+B,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;IACxI;IACA,IAAI,IAAI,CAACV,MAAM,EACX,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAAC4C,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACpD,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC;EACjD;EACAoD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC5B,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4B,OAAO,CAAC,CAAC;IAC5CjF,WAAW,CAAC,IAAI,CAAC6C,YAAY,CAAC;IAC9B7C,WAAW,CAAC,IAAI,CAACgD,MAAM,CAAC;IACxB,IAAI,CAACP,kBAAkB,CAACiC,OAAO,CAAEQ,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;IACrD,IAAI,CAACzC,kBAAkB,CAAC0C,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACX,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAAC7C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAACiD,MAAM,CAAC,IAAI,CAAC;IAChD,KAAK,MAAMlB,GAAG,IAAI,IAAI,CAACvB,MAAM,EAAE;MAC3B,IAAI,CAACA,MAAM,CAACuB,GAAG,CAAC,CAACiB,KAAK,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMjB,GAAG,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MAC7B,MAAM6C,OAAO,GAAG,IAAI,CAAC7C,QAAQ,CAAC0B,GAAG,CAAC;MAClC,IAAImB,OAAO,EAAE;QACTA,OAAO,CAACJ,OAAO,CAAC,CAAC;QACjBI,OAAO,CAACC,SAAS,GAAG,KAAK;MAC7B;IACJ;IACA,IAAI,CAACpD,OAAO,GAAG,IAAI;EACvB;EACAyC,iBAAiBA,CAACT,GAAG,EAAEC,KAAK,EAAE;IAC1B,IAAI,IAAI,CAAC1B,kBAAkB,CAAC8C,GAAG,CAACrB,GAAG,CAAC,EAAE;MAClC,IAAI,CAACzB,kBAAkB,CAAC+C,GAAG,CAACtB,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,MAAMuB,gBAAgB,GAAGxF,cAAc,CAACsF,GAAG,CAACrB,GAAG,CAAC;IAChD,IAAIuB,gBAAgB,IAAI,IAAI,CAACC,eAAe,EAAE;MAC1C,IAAI,CAACA,eAAe,CAAC,CAAC;IAC1B;IACA,MAAMC,cAAc,GAAGxB,KAAK,CAACyB,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;MACvD,IAAI,CAAC9C,YAAY,CAACmB,GAAG,CAAC,GAAG2B,WAAW;MACpC,IAAI,CAACjE,KAAK,CAACkE,QAAQ,IAAIhG,KAAK,CAACiG,SAAS,CAAC,IAAI,CAAClD,YAAY,CAAC;MACzD,IAAI4C,gBAAgB,IAAI,IAAI,CAACpC,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAAC2C,gBAAgB,GAAG,IAAI;MAC3C;IACJ,CAAC,CAAC;IACF,MAAMC,qBAAqB,GAAG9B,KAAK,CAACyB,EAAE,CAAC,eAAe,EAAE,IAAI,CAACrC,cAAc,CAAC;IAC5E,IAAI2C,eAAe;IACnB,IAAIC,MAAM,CAACC,qBAAqB,EAAE;MAC9BF,eAAe,GAAGC,MAAM,CAACC,qBAAqB,CAAC,IAAI,EAAElC,GAAG,EAAEC,KAAK,CAAC;IACpE;IACA,IAAI,CAAC1B,kBAAkB,CAAC4B,GAAG,CAACH,GAAG,EAAE,MAAM;MACnCyB,cAAc,CAAC,CAAC;MAChBM,qBAAqB,CAAC,CAAC;MACvB,IAAIC,eAAe,EACfA,eAAe,CAAC,CAAC;MACrB,IAAI/B,KAAK,CAACkC,KAAK,EACXlC,KAAK,CAACmC,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC,IAAI,CAACtE,OAAO,IACb,CAAC,IAAI,CAACuE,wBAAwB,IAC9B,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAACvE,OAAO,EAAEsE,KAAK,CAACtE,OAAO,CAAC;EACrE;EACAyE,cAAcA,CAAA,EAAG;IACb,IAAIzC,GAAG,GAAG,WAAW;IACrB,KAAKA,GAAG,IAAIxD,kBAAkB,EAAE;MAC5B,MAAMkG,iBAAiB,GAAGlG,kBAAkB,CAACwD,GAAG,CAAC;MACjD,IAAI,CAAC0C,iBAAiB,EAClB;MACJ,MAAM;QAAEC,SAAS;QAAEC,OAAO,EAAEC;MAAmB,CAAC,GAAGH,iBAAiB;MACpE;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC0B,GAAG,CAAC,IACnB6C,kBAAkB,IAClBF,SAAS,CAAC,IAAI,CAACjF,KAAK,CAAC,EAAE;QACvB,IAAI,CAACY,QAAQ,CAAC0B,GAAG,CAAC,GAAG,IAAI6C,kBAAkB,CAAC,IAAI,CAAC;MACrD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACvE,QAAQ,CAAC0B,GAAG,CAAC,EAAE;QACpB,MAAMmB,OAAO,GAAG,IAAI,CAAC7C,QAAQ,CAAC0B,GAAG,CAAC;QAClC,IAAImB,OAAO,CAACC,SAAS,EAAE;UACnBD,OAAO,CAACL,MAAM,CAAC,CAAC;QACpB,CAAC,MACI;UACDK,OAAO,CAACf,KAAK,CAAC,CAAC;UACfe,OAAO,CAACC,SAAS,GAAG,IAAI;QAC5B;MACJ;IACJ;EACJ;EACArC,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC+D,KAAK,CAAC,IAAI,CAAC7D,WAAW,EAAE,IAAI,CAACJ,YAAY,EAAE,IAAI,CAACnB,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;EACIqF,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/E,OAAO,GACb,IAAI,CAACgF,0BAA0B,CAAC,IAAI,CAAChF,OAAO,EAAE,IAAI,CAACN,KAAK,CAAC,GACzDjB,SAAS,CAAC,CAAC;EACrB;EACAwG,cAAcA,CAACjD,GAAG,EAAE;IAChB,OAAO,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC;EACjC;EACAkD,cAAcA,CAAClD,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,GAAGC,KAAK;EAClC;EACA;AACJ;AACA;AACA;EACIa,MAAMA,CAACpD,KAAK,EAAEC,eAAe,EAAE;IAC3B,IAAID,KAAK,CAACyF,iBAAiB,IAAI,IAAI,CAACzF,KAAK,CAACyF,iBAAiB,EAAE;MACzD,IAAI,CAAC9D,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAAC+D,SAAS,GAAG,IAAI,CAAC1F,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC2F,mBAAmB,GAAG,IAAI,CAAC1F,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpG,iBAAiB,CAACqG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAMtD,GAAG,GAAG9C,iBAAiB,CAACoG,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC5E,sBAAsB,CAACsB,GAAG,CAAC,EAAE;QAClC,IAAI,CAACtB,sBAAsB,CAACsB,GAAG,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAACtB,sBAAsB,CAACsB,GAAG,CAAC;MAC3C;MACA,MAAMwD,YAAY,GAAI,IAAI,GAAGxD,GAAI;MACjC,MAAMyD,QAAQ,GAAG/F,KAAK,CAAC8F,YAAY,CAAC;MACpC,IAAIC,QAAQ,EAAE;QACV,IAAI,CAAC/E,sBAAsB,CAACsB,GAAG,CAAC,GAAG,IAAI,CAAC0B,EAAE,CAAC1B,GAAG,EAAEyD,QAAQ,CAAC;MAC7D;IACJ;IACA,IAAI,CAACjF,gBAAgB,GAAGxB,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACI,2BAA2B,CAACM,KAAK,EAAE,IAAI,CAAC0F,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC5E,gBAAgB,CAAC;IAC/I,IAAI,IAAI,CAACkF,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjG,KAAK;EACrB;EACA;AACJ;AACA;EACIkG,UAAUA,CAACC,IAAI,EAAE;IACb,OAAO,IAAI,CAACnG,KAAK,CAACoG,QAAQ,GAAG,IAAI,CAACpG,KAAK,CAACoG,QAAQ,CAACD,IAAI,CAAC,GAAG3D,SAAS;EACtE;EACA;AACJ;AACA;EACI6D,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACrG,KAAK,CAACsG,UAAU;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACvG,KAAK,CAACwG,kBAAkB;EACxC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpH,aAAa,GACnB,IAAI,GACJ,IAAI,CAACU,MAAM,GACP,IAAI,CAACA,MAAM,CAAC0G,qBAAqB,CAAC,CAAC,GACnCjE,SAAS;EACvB;EACA;AACJ;AACA;EACIK,eAAeA,CAAC6D,KAAK,EAAE;IACnB,MAAMC,kBAAkB,GAAG,IAAI,CAACF,qBAAqB,CAAC,CAAC;IACvD,IAAIE,kBAAkB,EAAE;MACpBA,kBAAkB,CAACzE,eAAe,IAC9ByE,kBAAkB,CAACzE,eAAe,CAACiB,GAAG,CAACuD,KAAK,CAAC;MACjD,OAAO,MAAMC,kBAAkB,CAACzE,eAAe,CAACsB,MAAM,CAACkD,KAAK,CAAC;IACjE;EACJ;EACA;AACJ;AACA;EACIE,QAAQA,CAACtE,GAAG,EAAEC,KAAK,EAAE;IACjB;IACA,MAAMsE,aAAa,GAAG,IAAI,CAACnG,MAAM,CAACkD,GAAG,CAACtB,GAAG,CAAC;IAC1C,IAAIC,KAAK,KAAKsE,aAAa,EAAE;MACzB,IAAIA,aAAa,EACb,IAAI,CAACC,WAAW,CAACxE,GAAG,CAAC;MACzB,IAAI,CAACS,iBAAiB,CAACT,GAAG,EAAEC,KAAK,CAAC;MAClC,IAAI,CAAC7B,MAAM,CAAC+B,GAAG,CAACH,GAAG,EAAEC,KAAK,CAAC;MAC3B,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,GAAGC,KAAK,CAACqB,GAAG,CAAC,CAAC;IACxC;EACJ;EACA;AACJ;AACA;EACIkD,WAAWA,CAACxE,GAAG,EAAE;IACb,IAAI,CAAC5B,MAAM,CAAC8C,MAAM,CAAClB,GAAG,CAAC;IACvB,MAAMyE,WAAW,GAAG,IAAI,CAAClG,kBAAkB,CAAC+C,GAAG,CAACtB,GAAG,CAAC;IACpD,IAAIyE,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;MACb,IAAI,CAAClG,kBAAkB,CAAC2C,MAAM,CAAClB,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAACnB,YAAY,CAACmB,GAAG,CAAC;IAC7B,IAAI,CAAC0E,0BAA0B,CAAC1E,GAAG,EAAE,IAAI,CAACf,WAAW,CAAC;EAC1D;EACA;AACJ;AACA;EACI0F,QAAQA,CAAC3E,GAAG,EAAE;IACV,OAAO,IAAI,CAAC5B,MAAM,CAACiD,GAAG,CAACrB,GAAG,CAAC;EAC/B;EACA4E,QAAQA,CAAC5E,GAAG,EAAE6E,YAAY,EAAE;IACxB,IAAI,IAAI,CAACnH,KAAK,CAACU,MAAM,IAAI,IAAI,CAACV,KAAK,CAACU,MAAM,CAAC4B,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAACtC,KAAK,CAACU,MAAM,CAAC4B,GAAG,CAAC;IACjC;IACA,IAAIC,KAAK,GAAG,IAAI,CAAC7B,MAAM,CAACkD,GAAG,CAACtB,GAAG,CAAC;IAChC,IAAIC,KAAK,KAAKC,SAAS,IAAI2E,YAAY,KAAK3E,SAAS,EAAE;MACnDD,KAAK,GAAGjE,WAAW,CAAC6I,YAAY,KAAK,IAAI,GAAG3E,SAAS,GAAG2E,YAAY,EAAE;QAAE1C,KAAK,EAAE;MAAK,CAAC,CAAC;MACtF,IAAI,CAACmC,QAAQ,CAACtE,GAAG,EAAEC,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI6E,SAASA,CAAC9E,GAAG,EAAE+E,MAAM,EAAE;IACnB,IAAI9E,KAAK,GAAG,IAAI,CAACpB,YAAY,CAACmB,GAAG,CAAC,KAAKE,SAAS,IAAI,CAAC,IAAI,CAAClC,OAAO,GAC3D,IAAI,CAACa,YAAY,CAACmB,GAAG,CAAC,GACtB,IAAI,CAACgF,sBAAsB,CAAC,IAAI,CAACtH,KAAK,EAAEsC,GAAG,CAAC,IAC1C,IAAI,CAACiF,qBAAqB,CAAC,IAAI,CAACjH,OAAO,EAAEgC,GAAG,EAAE,IAAI,CAACjC,OAAO,CAAC;IACnE,IAAIkC,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;MACvC,IAAI,OAAOA,KAAK,KAAK,QAAQ,KACxB5D,iBAAiB,CAAC4D,KAAK,CAAC,IAAI3D,iBAAiB,CAAC2D,KAAK,CAAC,CAAC,EAAE;QACxD;QACAA,KAAK,GAAGiF,UAAU,CAACjF,KAAK,CAAC;MAC7B,CAAC,MACI,IAAI,CAAChE,aAAa,CAACgE,KAAK,CAAC,IAAI/D,OAAO,CAACiJ,IAAI,CAACJ,MAAM,CAAC,EAAE;QACpD9E,KAAK,GAAG9D,iBAAiB,CAAC6D,GAAG,EAAE+E,MAAM,CAAC;MAC1C;MACA,IAAI,CAACK,aAAa,CAACpF,GAAG,EAAEnE,aAAa,CAACoE,KAAK,CAAC,GAAGA,KAAK,CAACqB,GAAG,CAAC,CAAC,GAAGrB,KAAK,CAAC;IACvE;IACA,OAAOpE,aAAa,CAACoE,KAAK,CAAC,GAAGA,KAAK,CAACqB,GAAG,CAAC,CAAC,GAAGrB,KAAK;EACrD;EACA;AACJ;AACA;AACA;EACImF,aAAaA,CAACpF,GAAG,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACV,UAAU,CAACS,GAAG,CAAC,GAAGC,KAAK;EAChC;EACA;AACJ;AACA;AACA;EACIoF,aAAaA,CAACrF,GAAG,EAAE;IACf,MAAM;MAAEP;IAAQ,CAAC,GAAG,IAAI,CAAC/B,KAAK;IAC9B,IAAI4H,gBAAgB;IACpB,IAAI,OAAO7F,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC5D,MAAM8F,OAAO,GAAGtI,uBAAuB,CAAC,IAAI,CAACS,KAAK,EAAE+B,OAAO,EAAE,IAAI,CAAC9B,eAAe,EAAE6H,MAAM,CAAC;MAC1F,IAAID,OAAO,EAAE;QACTD,gBAAgB,GAAGC,OAAO,CAACvF,GAAG,CAAC;MACnC;IACJ;IACA;AACR;AACA;IACQ,IAAIP,OAAO,IAAI6F,gBAAgB,KAAKpF,SAAS,EAAE;MAC3C,OAAOoF,gBAAgB;IAC3B;IACA;AACR;AACA;AACA;IACQ,MAAMP,MAAM,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACtH,KAAK,EAAEsC,GAAG,CAAC;IAC3D,IAAI+E,MAAM,KAAK7E,SAAS,IAAI,CAACrE,aAAa,CAACkJ,MAAM,CAAC,EAC9C,OAAOA,MAAM;IACjB;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACvF,aAAa,CAACQ,GAAG,CAAC,KAAKE,SAAS,IACxCoF,gBAAgB,KAAKpF,SAAS,GAC5BA,SAAS,GACT,IAAI,CAACX,UAAU,CAACS,GAAG,CAAC;EAC9B;EACA0B,EAAEA,CAAC+D,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAACjH,MAAM,CAACgH,SAAS,CAAC,EAAE;MACzB,IAAI,CAAChH,MAAM,CAACgH,SAAS,CAAC,GAAG,IAAIlJ,mBAAmB,CAAC,CAAC;IACtD;IACA,OAAO,IAAI,CAACkC,MAAM,CAACgH,SAAS,CAAC,CAAC5E,GAAG,CAAC6E,QAAQ,CAAC;EAC/C;EACA9G,MAAMA,CAAC6G,SAAS,EAAE,GAAGE,IAAI,EAAE;IACvB,IAAI,IAAI,CAAClH,MAAM,CAACgH,SAAS,CAAC,EAAE;MACxB,IAAI,CAAChH,MAAM,CAACgH,SAAS,CAAC,CAAC7G,MAAM,CAAC,GAAG+G,IAAI,CAAC;IAC1C;EACJ;AACJ;AAEA,SAASxI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}