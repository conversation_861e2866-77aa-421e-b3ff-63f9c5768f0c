{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/components/header-slider/SkeletonSlide.jsx\";\nimport React from 'react';\nimport styles from './header.module.css'; // Используем тот же CSS-модуль\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SkeletonSlide = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.slide} ${styles.skeletonSlide}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.slideContent,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.skeletonTag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.skeletonTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.skeletonDescription\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.skeletonButton\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = SkeletonSlide;\nexport default SkeletonSlide;\nvar _c;\n$RefreshReg$(_c, \"SkeletonSlide\");", "map": {"version": 3, "names": ["React", "styles", "jsxDEV", "_jsxDEV", "SkeletonSlide", "className", "slide", "skeletonSlide", "children", "slideContent", "skeletonTag", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "skeleton<PERSON>itle", "skeletonDescription", "skeleton<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/header-slider/SkeletonSlide.jsx"], "sourcesContent": ["import React from 'react';\nimport styles from './header.module.css'; // Используем тот же CSS-модуль\n\nconst SkeletonSlide = () => {\n  return (\n    <div className={`${styles.slide} ${styles.skeletonSlide}`}>\n      <div className=\"container\">\n        <div className={styles.slideContent}>\n          <div className={styles.skeletonTag} />\n          <div className={styles.skeletonTitle} />\n          <div className={styles.skeletonDescription} />\n          <div className={styles.skeletonButton} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SkeletonSlide;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,qBAAqB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA;IAAKE,SAAS,EAAE,GAAGJ,MAAM,CAACK,KAAK,IAAIL,MAAM,CAACM,aAAa,EAAG;IAAAC,QAAA,eACxDL,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAG,QAAA,eACxBL,OAAA;QAAKE,SAAS,EAAEJ,MAAM,CAACQ,YAAa;QAAAD,QAAA,gBAClCL,OAAA;UAAKE,SAAS,EAAEJ,MAAM,CAACS;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtCX,OAAA;UAAKE,SAAS,EAAEJ,MAAM,CAACc;QAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxCX,OAAA;UAAKE,SAAS,EAAEJ,MAAM,CAACe;QAAoB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CX,OAAA;UAAKE,SAAS,EAAEJ,MAAM,CAACgB;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GAbId,aAAa;AAenB,eAAeA,aAAa;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}