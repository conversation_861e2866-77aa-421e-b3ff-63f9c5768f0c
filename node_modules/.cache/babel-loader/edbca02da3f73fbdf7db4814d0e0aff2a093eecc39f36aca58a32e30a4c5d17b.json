{"ast": null, "code": "import _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"container\"];\nimport { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = element => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll) {\n  let _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    {\n      container = document.scrollingElement\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  if (!container) return noop;\n  let containerHandlers = onScrollHandlers.get(container);\n  /**\n   * Get the onScroll handlers for this container.\n   * If one isn't found, create a new one.\n   */\n  if (!containerHandlers) {\n    containerHandlers = new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  /**\n   * Create a new onScroll handler for the provided callback.\n   */\n  const info = createScrollInfo();\n  const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n  containerHandlers.add(containerHandler);\n  /**\n   * Check if there's a scroll event listener for this container.\n   * If not, create one.\n   */\n  if (!scrollListeners.has(container)) {\n    const measureAll = () => {\n      for (const handler of containerHandlers) handler.measure();\n    };\n    const updateAll = () => {\n      for (const handler of containerHandlers) {\n        handler.update(frameData.timestamp);\n      }\n    };\n    const notifyAll = () => {\n      for (const handler of containerHandlers) handler.notify();\n    };\n    const listener = () => {\n      frame.read(measureAll);\n      frame.read(updateAll);\n      frame.preUpdate(notifyAll);\n    };\n    scrollListeners.set(container, listener);\n    const target = getEventTarget(container);\n    window.addEventListener(\"resize\", listener, {\n      passive: true\n    });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, resize(container, listener));\n    }\n    target.addEventListener(\"scroll\", listener, {\n      passive: true\n    });\n    listener();\n  }\n  const listener = scrollListeners.get(container);\n  frame.read(listener, false, true);\n  return () => {\n    cancelFrame(listener);\n    /**\n     * Check if we even have any handlers for this container.\n     */\n    const currentHandlers = onScrollHandlers.get(container);\n    if (!currentHandlers) return;\n    currentHandlers.delete(containerHandler);\n    if (currentHandlers.size) return;\n    /**\n     * If no more handlers, remove the scroll listener too.\n     */\n    const scrollListener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (scrollListener) {\n      var _resizeListeners$get;\n      getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n      (_resizeListeners$get = resizeListeners.get(container)) === null || _resizeListeners$get === void 0 || _resizeListeners$get();\n      window.removeEventListener(\"resize\", scrollListener);\n    }\n  };\n}\nexport { scrollInfo };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "noop", "resize", "createScrollInfo", "createOnScrollHandler", "scrollListeners", "WeakMap", "resizeListeners", "onScrollHandlers", "getEventTarget", "element", "document", "scrollingElement", "window", "scrollInfo", "onScroll", "_ref", "arguments", "length", "undefined", "container", "options", "_objectWithoutProperties", "_excluded", "containerHandlers", "get", "Set", "set", "info", "containerHandler", "add", "has", "measureAll", "handler", "measure", "updateAll", "update", "timestamp", "notifyAll", "notify", "listener", "read", "preUpdate", "target", "addEventListener", "passive", "documentElement", "currentHandlers", "delete", "size", "scrollListener", "_resizeListeners$get", "removeEventListener"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { resize } from '../resize/index.mjs';\nimport { createScrollInfo } from './info.mjs';\nimport { createOnScrollHandler } from './on-scroll-handler.mjs';\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = createScrollInfo();\n    const containerHandler = createOnScrollHandler(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            frame.read(measureAll);\n            frame.read(updateAll);\n            frame.preUpdate(notifyAll);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, resize(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    frame.read(listener, false, true);\n    return () => {\n        cancelFrame(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\nexport { scrollInfo };\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AAC1D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,qBAAqB,QAAQ,yBAAyB;AAE/D,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,eAAe,GAAG,IAAID,OAAO,CAAC,CAAC;AACrC,MAAME,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;AACtC,MAAMG,cAAc,GAAIC,OAAO,IAAKA,OAAO,KAAKC,QAAQ,CAACC,gBAAgB,GAAGC,MAAM,GAAGH,OAAO;AAC5F,SAASI,UAAUA,CAACC,QAAQ,EAA8D;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;IAA1D;MAAEG,SAAS,GAAGT,QAAQ,CAACC;IAA6B,CAAC,GAAAI,IAAA;IAATK,OAAO,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAC7E,IAAI,CAACH,SAAS,EACV,OAAOnB,IAAI;EACf,IAAIuB,iBAAiB,GAAGhB,gBAAgB,CAACiB,GAAG,CAACL,SAAS,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAI,CAACI,iBAAiB,EAAE;IACpBA,iBAAiB,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC7BlB,gBAAgB,CAACmB,GAAG,CAACP,SAAS,EAAEI,iBAAiB,CAAC;EACtD;EACA;AACJ;AACA;EACI,MAAMI,IAAI,GAAGzB,gBAAgB,CAAC,CAAC;EAC/B,MAAM0B,gBAAgB,GAAGzB,qBAAqB,CAACgB,SAAS,EAAEL,QAAQ,EAAEa,IAAI,EAAEP,OAAO,CAAC;EAClFG,iBAAiB,CAACM,GAAG,CAACD,gBAAgB,CAAC;EACvC;AACJ;AACA;AACA;EACI,IAAI,CAACxB,eAAe,CAAC0B,GAAG,CAACX,SAAS,CAAC,EAAE;IACjC,MAAMY,UAAU,GAAGA,CAAA,KAAM;MACrB,KAAK,MAAMC,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACC,OAAO,CAAC,CAAC;IACzB,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAMF,OAAO,IAAIT,iBAAiB,EAAE;QACrCS,OAAO,CAACG,MAAM,CAACpC,SAAS,CAACqC,SAAS,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACpB,KAAK,MAAML,OAAO,IAAIT,iBAAiB,EACnCS,OAAO,CAACM,MAAM,CAAC,CAAC;IACxB,CAAC;IACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnB1C,KAAK,CAAC2C,IAAI,CAACT,UAAU,CAAC;MACtBlC,KAAK,CAAC2C,IAAI,CAACN,SAAS,CAAC;MACrBrC,KAAK,CAAC4C,SAAS,CAACJ,SAAS,CAAC;IAC9B,CAAC;IACDjC,eAAe,CAACsB,GAAG,CAACP,SAAS,EAAEoB,QAAQ,CAAC;IACxC,MAAMG,MAAM,GAAGlC,cAAc,CAACW,SAAS,CAAC;IACxCP,MAAM,CAAC+B,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAIzB,SAAS,KAAKT,QAAQ,CAACmC,eAAe,EAAE;MACxCvC,eAAe,CAACoB,GAAG,CAACP,SAAS,EAAElB,MAAM,CAACkB,SAAS,EAAEoB,QAAQ,CAAC,CAAC;IAC/D;IACAG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9DL,QAAQ,CAAC,CAAC;EACd;EACA,MAAMA,QAAQ,GAAGnC,eAAe,CAACoB,GAAG,CAACL,SAAS,CAAC;EAC/CtB,KAAK,CAAC2C,IAAI,CAACD,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;EACjC,OAAO,MAAM;IACTzC,WAAW,CAACyC,QAAQ,CAAC;IACrB;AACR;AACA;IACQ,MAAMO,eAAe,GAAGvC,gBAAgB,CAACiB,GAAG,CAACL,SAAS,CAAC;IACvD,IAAI,CAAC2B,eAAe,EAChB;IACJA,eAAe,CAACC,MAAM,CAACnB,gBAAgB,CAAC;IACxC,IAAIkB,eAAe,CAACE,IAAI,EACpB;IACJ;AACR;AACA;IACQ,MAAMC,cAAc,GAAG7C,eAAe,CAACoB,GAAG,CAACL,SAAS,CAAC;IACrDf,eAAe,CAAC2C,MAAM,CAAC5B,SAAS,CAAC;IACjC,IAAI8B,cAAc,EAAE;MAAA,IAAAC,oBAAA;MAChB1C,cAAc,CAACW,SAAS,CAAC,CAACgC,mBAAmB,CAAC,QAAQ,EAAEF,cAAc,CAAC;MACvE,CAAAC,oBAAA,GAAA5C,eAAe,CAACkB,GAAG,CAACL,SAAS,CAAC,cAAA+B,oBAAA,eAA9BA,oBAAA,CAAiC,CAAC;MAClCtC,MAAM,CAACuC,mBAAmB,CAAC,QAAQ,EAAEF,cAAc,CAAC;IACxD;EACJ,CAAC;AACL;AAEA,SAASpC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}