{"ast": null, "code": "function chooseLayerType(valueName) {\n  if (valueName === \"layout\") return \"group\";\n  if (valueName === \"enter\" || valueName === \"new\") return \"new\";\n  if (valueName === \"exit\" || valueName === \"old\") return \"old\";\n  return \"group\";\n}\nexport { chooseLayerType };", "map": {"version": 3, "names": ["chooseLayerType", "valueName"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs"], "sourcesContent": ["function chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\nexport { chooseLayerType };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,SAAS,EAAE;EAChC,IAAIA,SAAS,KAAK,QAAQ,EACtB,OAAO,OAAO;EAClB,IAAIA,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAC5C,OAAO,KAAK;EAChB,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAC3C,OAAO,KAAK;EAChB,OAAO,OAAO;AAClB;AAEA,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}