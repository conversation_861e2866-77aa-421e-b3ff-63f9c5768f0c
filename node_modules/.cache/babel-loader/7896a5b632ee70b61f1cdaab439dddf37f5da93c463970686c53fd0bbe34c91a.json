{"ast": null, "code": "import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\nfunction useInView(ref, {\n  root,\n  margin,\n  amount,\n  once = false,\n  initial = false\n} = {}) {\n  const [isInView, setInView] = useState(initial);\n  useEffect(() => {\n    if (!ref.current || once && isInView) return;\n    const onEnter = () => {\n      setInView(true);\n      return once ? undefined : () => setInView(false);\n    };\n    const options = {\n      root: root && root.current || undefined,\n      margin,\n      amount\n    };\n    return inView(ref.current, onEnter, options);\n  }, [root, ref, margin, once, amount]);\n  return isInView;\n}\nexport { useInView };", "map": {"version": 3, "names": ["useState", "useEffect", "inView", "useInView", "ref", "root", "margin", "amount", "once", "initial", "isInView", "setInView", "current", "onEnter", "undefined", "options"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/use-in-view.mjs"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { inView } from '../render/dom/viewport/index.mjs';\n\nfunction useInView(ref, { root, margin, amount, once = false, initial = false, } = {}) {\n    const [isInView, setInView] = useState(initial);\n    useEffect(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return inView(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\nexport { useInView };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,QAAQ,kCAAkC;AAEzD,SAASC,SAASA,CAACC,GAAG,EAAE;EAAEC,IAAI;EAAEC,MAAM;EAAEC,MAAM;EAAEC,IAAI,GAAG,KAAK;EAAEC,OAAO,GAAG;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnF,MAAM,CAACC,QAAQ,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAACS,OAAO,CAAC;EAC/CR,SAAS,CAAC,MAAM;IACZ,IAAI,CAACG,GAAG,CAACQ,OAAO,IAAKJ,IAAI,IAAIE,QAAS,EAClC;IACJ,MAAMG,OAAO,GAAGA,CAAA,KAAM;MAClBF,SAAS,CAAC,IAAI,CAAC;MACf,OAAOH,IAAI,GAAGM,SAAS,GAAG,MAAMH,SAAS,CAAC,KAAK,CAAC;IACpD,CAAC;IACD,MAAMI,OAAO,GAAG;MACZV,IAAI,EAAGA,IAAI,IAAIA,IAAI,CAACO,OAAO,IAAKE,SAAS;MACzCR,MAAM;MACNC;IACJ,CAAC;IACD,OAAOL,MAAM,CAACE,GAAG,CAACQ,OAAO,EAAEC,OAAO,EAAEE,OAAO,CAAC;EAChD,CAAC,EAAE,CAACV,IAAI,EAAED,GAAG,EAAEE,MAAM,EAAEE,IAAI,EAAED,MAAM,CAAC,CAAC;EACrC,OAAOG,QAAQ;AACnB;AAEA,SAASP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}