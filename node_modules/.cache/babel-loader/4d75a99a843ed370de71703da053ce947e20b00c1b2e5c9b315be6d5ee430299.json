{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/components/video-card/VideoCard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport styles from './videoCard.module.css';\nimport video from './Homepage-Multimedia-components-01-GWM-care.mp4';\nimport AOS from 'aos';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCard = () => {\n  _s();\n  const wrapperRef = useRef(null);\n  const [isAnimated, setIsAnimated] = useState(false);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    const observer = new IntersectionObserver(([entry]) => {\n      // Если секция в зоне видимости — запускаем анимацию\n      setIsAnimated(entry.isIntersecting);\n    }, {\n      threshold: 0.9 // 50% видимости\n    });\n    if (wrapperRef.current) {\n      observer.observe(wrapperRef.current);\n    }\n    return () => {\n      observer.disconnect();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    ref: wrapperRef,\n    className: `${styles.wrapper} ${isAnimated ? styles.wrapperActive : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${styles.container} ${isAnimated ? styles.containerActive : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${styles.videoWrapper} ${isAnimated ? styles.videoWrapperActive : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          src: video,\n          autoPlay: true,\n          muted: true,\n          loop: true,\n          playsInline: true,\n          className: `${styles.video} ${isAnimated ? styles.videoActive : ''}`,\n          \"aria-label\": \"\\u041F\\u0440\\u043E\\u043C\\u043E-\\u0432\\u0438\\u0434\\u0435\\u043E GWM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `${styles.text} `,\n          \"data-aos\": \"fade-up\",\n          \"data-aos-delay\": \"200\",\n          children: \"\\u041E\\u0442\\u043A\\u0440\\u043E\\u0439 \\u0434\\u043B\\u044F \\u0441\\u0435\\u0431\\u044F GWM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCard, \"DRATAmrqUKfNR3otr+uwq9PivzE=\");\n_c = VideoCard;\nexport default VideoCard;\nvar _c;\n$RefreshReg$(_c, \"VideoCard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "styles", "video", "AOS", "jsxDEV", "_jsxDEV", "VideoCard", "_s", "wrapperRef", "isAnimated", "setIsAnimated", "init", "duration", "once", "observer", "IntersectionObserver", "entry", "isIntersecting", "threshold", "current", "observe", "disconnect", "ref", "className", "wrapper", "wrapperActive", "children", "container", "containerActive", "videoWrapper", "videoWrapperActive", "src", "autoPlay", "muted", "loop", "playsInline", "videoActive", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/video-card/VideoCard.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport styles from './videoCard.module.css';\nimport video from './Homepage-Multimedia-components-01-GWM-care.mp4';\nimport AOS from 'aos';\n\nconst VideoCard = () => {\n  const wrapperRef = useRef(null);\n  const [isAnimated, setIsAnimated] = useState(false);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        // Если секция в зоне видимости — запускаем анимацию\n        setIsAnimated(entry.isIntersecting);\n      },\n      {\n        threshold: 0.9, // 50% видимости\n      }\n    );\n\n    if (wrapperRef.current) {\n      observer.observe(wrapperRef.current);\n    }\n\n    return () => {\n      observer.disconnect();\n    };\n  }, []);\n\n  return (\n    <section ref={wrapperRef}\n      className={`${styles.wrapper} ${isAnimated ? styles.wrapperActive : ''\n        }`}\n    >\n      <div\n        className={`${styles.container} ${isAnimated ? styles.containerActive : ''\n          }`}\n      >\n        <div\n          className={`${styles.videoWrapper} ${isAnimated ? styles.videoWrapperActive : ''\n            }`}\n        >\n          <video\n            src={video}\n            autoPlay\n            muted\n            loop\n            playsInline\n            className={`${styles.video} ${isAnimated ? styles.videoActive : ''\n              }`}\n            aria-label=\"Промо-видео GWM\"\n          />\n          <h1\n            className={`${styles.text} `}\n            data-aos=\"fade-up\"\n            data-aos-delay=\"200\"\n          >\n            Открой для себя GWM\n          </h1>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default VideoCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,OAAOC,KAAK,MAAM,kDAAkD;AACpE,OAAOC,GAAG,MAAM,KAAK;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,UAAU,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAEnDF,SAAS,CAAC,MAAM;IACdK,GAAG,CAACQ,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IACxC,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX;MACAN,aAAa,CAACM,KAAK,CAACC,cAAc,CAAC;IACrC,CAAC,EACD;MACEC,SAAS,EAAE,GAAG,CAAE;IAClB,CACF,CAAC;IAED,IAAIV,UAAU,CAACW,OAAO,EAAE;MACtBL,QAAQ,CAACM,OAAO,CAACZ,UAAU,CAACW,OAAO,CAAC;IACtC;IAEA,OAAO,MAAM;MACXL,QAAQ,CAACO,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhB,OAAA;IAASiB,GAAG,EAAEd,UAAW;IACvBe,SAAS,EAAE,GAAGtB,MAAM,CAACuB,OAAO,IAAIf,UAAU,GAAGR,MAAM,CAACwB,aAAa,GAAG,EAAE,EACjE;IAAAC,QAAA,eAELrB,OAAA;MACEkB,SAAS,EAAE,GAAGtB,MAAM,CAAC0B,SAAS,IAAIlB,UAAU,GAAGR,MAAM,CAAC2B,eAAe,GAAG,EAAE,EACrE;MAAAF,QAAA,eAELrB,OAAA;QACEkB,SAAS,EAAE,GAAGtB,MAAM,CAAC4B,YAAY,IAAIpB,UAAU,GAAGR,MAAM,CAAC6B,kBAAkB,GAAG,EAAE,EAC3E;QAAAJ,QAAA,gBAELrB,OAAA;UACE0B,GAAG,EAAE7B,KAAM;UACX8B,QAAQ;UACRC,KAAK;UACLC,IAAI;UACJC,WAAW;UACXZ,SAAS,EAAE,GAAGtB,MAAM,CAACC,KAAK,IAAIO,UAAU,GAAGR,MAAM,CAACmC,WAAW,GAAG,EAAE,EAC7D;UACL,cAAW;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFnC,OAAA;UACEkB,SAAS,EAAE,GAAGtB,MAAM,CAACwC,IAAI,GAAI;UAC7B,YAAS,SAAS;UAClB,kBAAe,KAAK;UAAAf,QAAA,EACrB;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACjC,EAAA,CA3DID,SAAS;AAAAoC,EAAA,GAATpC,SAAS;AA6Df,eAAeA,SAAS;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}