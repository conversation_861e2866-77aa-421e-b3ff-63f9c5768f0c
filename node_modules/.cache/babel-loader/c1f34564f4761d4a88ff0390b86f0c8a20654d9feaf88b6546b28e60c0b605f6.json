{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transitionEnd\", \"transition\"];\nimport { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n  if (visualElement.hasValue(key)) {\n    visualElement.getValue(key).set(value);\n  } else {\n    visualElement.addValue(key, motionValue(value));\n  }\n}\nfunction resolveFinalValueInKeyframes(v) {\n  // TODO maybe throw if v.length - 1 is placeholder token?\n  return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n  const resolved = resolveVariant(visualElement, definition);\n  let _ref = resolved || {},\n    {\n      transitionEnd = {},\n      transition = {}\n    } = _ref,\n    target = _objectWithoutProperties(_ref, _excluded);\n  target = _objectSpread(_objectSpread({}, target), transitionEnd);\n  for (const key in target) {\n    const value = resolveFinalValueInKeyframes(target[key]);\n    setMotionValue(visualElement, key, value);\n  }\n}\nexport { setTarget };", "map": {"version": 3, "names": ["motionValue", "isKeyframesTarget", "resolveV<PERSON>t", "setMotionValue", "visualElement", "key", "value", "hasValue", "getValue", "set", "addValue", "resolveFinalValueInKeyframes", "v", "length", "<PERSON><PERSON><PERSON><PERSON>", "definition", "resolved", "_ref", "transitionEnd", "transition", "target", "_objectWithoutProperties", "_excluded", "_objectSpread"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/utils/setters.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC/C,IAAIF,aAAa,CAACG,QAAQ,CAACF,GAAG,CAAC,EAAE;IAC7BD,aAAa,CAACI,QAAQ,CAACH,GAAG,CAAC,CAACI,GAAG,CAACH,KAAK,CAAC;EAC1C,CAAC,MACI;IACDF,aAAa,CAACM,QAAQ,CAACL,GAAG,EAAEL,WAAW,CAACM,KAAK,CAAC,CAAC;EACnD;AACJ;AACA,SAASK,4BAA4BA,CAACC,CAAC,EAAE;EACrC;EACA,OAAOX,iBAAiB,CAACW,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC;AAC1D;AACA,SAASE,SAASA,CAACV,aAAa,EAAEW,UAAU,EAAE;EAC1C,MAAMC,QAAQ,GAAGd,cAAc,CAACE,aAAa,EAAEW,UAAU,CAAC;EAC1D,IAAAE,IAAA,GAAyDD,QAAQ,IAAI,CAAC,CAAC;IAAnE;MAAEE,aAAa,GAAG,CAAC,CAAC;MAAEC,UAAU,GAAG,CAAC;IAAa,CAAC,GAAAF,IAAA;IAARG,MAAM,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACpDF,MAAM,GAAAG,aAAA,CAAAA,aAAA,KAAQH,MAAM,GAAKF,aAAa,CAAE;EACxC,KAAK,MAAMb,GAAG,IAAIe,MAAM,EAAE;IACtB,MAAMd,KAAK,GAAGK,4BAA4B,CAACS,MAAM,CAACf,GAAG,CAAC,CAAC;IACvDF,cAAc,CAACC,aAAa,EAAEC,GAAG,EAAEC,KAAK,CAAC;EAC7C;AACJ;AAEA,SAASQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}