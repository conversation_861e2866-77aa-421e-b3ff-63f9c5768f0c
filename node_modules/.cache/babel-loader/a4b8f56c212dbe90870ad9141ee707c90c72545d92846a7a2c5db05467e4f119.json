{"ast": null, "code": "import React from'react';import{FiChevronLeft}from'react-icons/fi';import styles from'./primaryBar.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PrimaryBar=_ref=>{let{isVisible,onClick}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(styles.primaryBar,\" \").concat(isVisible?styles.show:''),onClick:onClick,children:[/*#__PURE__*/_jsx(FiChevronLeft,{className:styles.icon}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u041D\\u0430\\u0437\\u0430\\u0434\"})]});};export default PrimaryBar;", "map": {"version": 3, "names": ["React", "FiChevronLeft", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "PrimaryBar", "_ref", "isVisible", "onClick", "className", "concat", "primaryBar", "show", "children", "icon"], "sources": ["/var/www/html/gwm.tj/src/layout/Navbar/components/mobPrimaryBar/PrimaryBar.jsx"], "sourcesContent": ["import React from 'react';\nimport { FiChevronLeft } from 'react-icons/fi';\nimport styles from './primaryBar.module.css';\n\nconst PrimaryBar = ({ isVisible, onClick }) => {\n  return (\n    <div\n      className={`${styles.primaryBar} ${isVisible ? styles.show : ''}`}\n      onClick={onClick}\n    >\n      <FiChevronLeft className={styles.icon} />\n      <span>Назад</span>\n    </div>\n  );\n};\n\nexport default PrimaryBar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,KAAQ,gBAAgB,CAC9C,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE7C,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA4B,IAA3B,CAAEC,SAAS,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CACxC,mBACEF,KAAA,QACEK,SAAS,IAAAC,MAAA,CAAKV,MAAM,CAACW,UAAU,MAAAD,MAAA,CAAIH,SAAS,CAAGP,MAAM,CAACY,IAAI,CAAG,EAAE,CAAG,CAClEJ,OAAO,CAAEA,OAAQ,CAAAK,QAAA,eAEjBX,IAAA,CAACH,aAAa,EAACU,SAAS,CAAET,MAAM,CAACc,IAAK,CAAE,CAAC,cACzCZ,IAAA,SAAAW,QAAA,CAAM,gCAAK,CAAM,CAAC,EACf,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}