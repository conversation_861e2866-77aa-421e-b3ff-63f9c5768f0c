{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/layout/Footer/Footer.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiChevronDown, FiChevronUp } from 'react-icons/fi';\n// sotcial icons\nimport { PiFacebookLogo } from 'react-icons/pi';\nimport { PiInstagramLogo } from 'react-icons/pi';\nimport { PiYoutubeLogo } from 'react-icons/pi';\nimport { PiTelegramLogo } from 'react-icons/pi';\nimport logo from '../../asset/imgs/logo/PcLogo.svg';\nimport vector from '../../asset/imgs/logo/vector.webp';\nimport { btnListData, footerMenuData } from '../../asset/data/footerData';\nimport styles from './footer.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst useIsMobile = (breakpoint = 768) => {\n  _s();\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= breakpoint);\n  useEffect(() => {\n    const handleResize = () => setIsMobile(window.innerWidth <= breakpoint);\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [breakpoint]);\n  return isMobile;\n};\n_s(useIsMobile, \"+AQhboyuE1boVeYDyXxeEygxsuU=\");\nconst Footer = () => {\n  _s2();\n  const isMobile = useIsMobile();\n  const [openSections, setOpenSections] = useState({});\n  const sectionRefs = useRef({});\n  const [socialLinks, setSocialLinks] = useState([]);\n  const toggleSection = id => {\n    setOpenSections(prev => ({\n      [id]: !prev[id] // откроется/закроется только один в моб версии\n    }));\n  };\n  useEffect(() => {\n    fetch('https://api.gwm.tj/api/v1/contacts').then(res => res.json()).then(data => {\n      const contacts = data.contacts || {};\n      const links = [];\n      if (contacts.facebook) links.push({\n        id: 'facebook',\n        url: contacts.facebook,\n        icon: /*#__PURE__*/_jsxDEV(PiFacebookLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 19\n        }, this),\n        title: 'Facebook'\n      });\n      if (contacts.instagram) links.push({\n        id: 'instagram',\n        url: contacts.instagram,\n        icon: /*#__PURE__*/_jsxDEV(PiInstagramLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 19\n        }, this),\n        title: 'Instagram'\n      });\n      if (contacts.youtube) links.push({\n        id: 'youtube',\n        url: contacts.youtube,\n        icon: /*#__PURE__*/_jsxDEV(PiYoutubeLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 19\n        }, this),\n        title: 'YouTube'\n      });\n      if (contacts.telegram) links.push({\n        id: 'telegram',\n        url: contacts.telegram,\n        icon: /*#__PURE__*/_jsxDEV(PiTelegramLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 19\n        }, this),\n        title: 'Telegram'\n      });\n      setSocialLinks(links);\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.content,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.logo,\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            \"aria-label\": \"\\u041D\\u0430 \\u0433\\u043B\\u0430\\u0432\\u043D\\u0443\\u044E\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: logo,\n              alt: \"GWM \\u041B\\u043E\\u0433\\u043E\\u0442\\u0438\\u043F\",\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.btnsList,\n          children: btnListData.map(({\n            id,\n            url,\n            icon,\n            title\n          }) => /*#__PURE__*/_jsxDEV(Link, {\n            to: url,\n            className: styles.btnItem,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.item,\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: icon,\n                alt: title,\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this)\n          }, id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.menu,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.menuContainer,\n            children: footerMenuData.map(({\n              id,\n              title,\n              items\n            }) => {\n              var _sectionRefs$current$;\n              const isOpen = openSections[id];\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.menuItem,\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: styles.menuItemTitle,\n                  onClick: () => isMobile && toggleSection(id),\n                  \"aria-expanded\": isOpen,\n                  \"aria-controls\": `footer-menu-${id}`,\n                  style: {\n                    cursor: isMobile ? 'pointer' : 'default',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    background: 'none',\n                    border: 'none',\n                    color: '#fff',\n                    width: '100%'\n                  },\n                  children: [title, isMobile && (isOpen ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: `footer-menu-${id}`,\n                  className: `${styles.menuLinks} ${isMobile ? styles.collapsible : ''} ${isOpen ? styles.open : ''}`,\n                  ref: el => sectionRefs.current[id] = el,\n                  style: isMobile ? {\n                    maxHeight: isOpen ? `${(_sectionRefs$current$ = sectionRefs.current[id]) === null || _sectionRefs$current$ === void 0 ? void 0 : _sectionRefs$current$.scrollHeight}px` : '0px',\n                    overflow: 'hidden',\n                    transition: 'max-height 0.3s ease'\n                  } : {},\n                  children: items.map(({\n                    id: linkId,\n                    url,\n                    title\n                  }) => /*#__PURE__*/_jsxDEV(Link, {\n                    to: url,\n                    children: title\n                  }, linkId, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)]\n              }, id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.related,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.social,\n            children: socialLinks.map(({\n              id,\n              url,\n              icon,\n              title\n            }) => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              \"aria-label\": title,\n              children: typeof icon === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: icon,\n                alt: title,\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.iconWrapper,\n                children: icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this)\n            }, id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.vector,\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"https://vector.tj\",\n              target: \"_black\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: vector,\n                alt: \"GWM \\u041B\\u043E\\u0433\\u043E\\u0442\\u0438\\u043F\",\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.legacy,\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/privacy\",\n              children: \"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430 \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.copy,\n            children: \"\\xA9 2025 GWM \\u0422\\u0430\\u0434\\u0436\\u0438\\u043A\\u0438\\u0441\\u0442\\u0430\\u043D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.desc,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Great Wall Motor Co. Ltd. (\\u0434\\u0430\\u043B\\u0435\\u0435 \\u0438\\u043C\\u0435\\u043D\\u0443\\u0435\\u043C\\u0430\\u044F \\xABGWM\\xBB) \\u2014 \\u043C\\u0435\\u0436\\u0434\\u0443\\u043D\\u0430\\u0440\\u043E\\u0434\\u043D\\u044B\\u0439 \\u043C\\u0443\\u043B\\u044C\\u0442\\u0438\\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0441\\u043E \\u0448\\u0442\\u0430\\u0431-\\u043A\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u043E\\u0439 \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435. \\u042D\\u0442\\u043E \\u0432\\u0441\\u0435\\u043C\\u0438\\u0440\\u043D\\u043E \\u0438\\u0437\\u0432\\u0435\\u0441\\u0442\\u043D\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u044C \\u0432\\u043D\\u0435\\u0434\\u043E\\u0440\\u043E\\u0436\\u043D\\u0438\\u043A\\u043E\\u0432 \\u0438 \\u043F\\u0438\\u043A\\u0430\\u043F\\u043E\\u0432, \\u0432\\u043B\\u0430\\u0434\\u0435\\u044E\\u0449\\u0438\\u0439 \\u043F\\u044F\\u0442\\u044C\\u044E \\u043B\\u0438\\u043D\\u0435\\u0439\\u043A\\u0430\\u043C\\u0438 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438, \\u0430 \\u0438\\u043C\\u0435\\u043D\\u043D\\u043E GWM HAVAL, GWM WEY, GWM ORA, GWM TANK \\u0438 GWM Pickup, \\u043E\\u0445\\u0432\\u0430\\u0442\\u044B\\u0432\\u0430\\u044E\\u0449\\u0438\\u043C\\u0438 \\u043B\\u0435\\u0433\\u043A\\u043E\\u0432\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u0438 \\u043F\\u0438\\u043A\\u0430\\u043F\\u044B. GWM \\u0438\\u043C\\u0435\\u0435\\u0442 \\u0441\\u0430\\u043C\\u043E\\u0434\\u043E\\u0441\\u0442\\u0430\\u0442\\u043E\\u0447\\u043D\\u0443\\u044E \\u0446\\u0435\\u043F\\u043E\\u0447\\u043A\\u0443 \\u0434\\u0432\\u0438\\u0433\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439, \\u0442\\u0440\\u0430\\u043D\\u0441\\u043C\\u0438\\u0441\\u0441\\u0438\\u0439 \\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u043E\\u0441\\u043D\\u043E\\u0432\\u043D\\u044B\\u0445 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u043E\\u0432. \\u0412 2003 \\u0433\\u043E\\u0434\\u0443 \\u043E\\u043D\\u0430 \\u0431\\u044B\\u043B\\u0430 \\u0432\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0430 \\u0432 \\u0441\\u043F\\u0438\\u0441\\u043E\\u043A \\u0430\\u043A\\u0446\\u0438\\u0439 H \\u0432 \\u0413\\u043E\\u043D\\u043A\\u043E\\u043D\\u0433\\u0435, \\u0430 \\u0432 2011 \\u0433\\u043E\\u0434\\u0443 \\u2014 \\u0430\\u043A\\u0446\\u0438\\u0439 A \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435. \\u041A \\u043A\\u043E\\u043D\\u0446\\u0443 2023 \\u0433\\u043E\\u0434\\u0430 \\u0435\\u0435 \\u0430\\u043A\\u0442\\u0438\\u0432\\u044B \\u0441\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\\u043B\\u0438 201,93 \\u043C\\u043B\\u0440\\u0434 \\u044E\\u0430\\u043D\\u0435\\u0439, \\u0430 \\u043E\\u0431\\u044A\\u0435\\u043C \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436 \\u043F\\u0440\\u0435\\u0432\\u044B\\u0441\\u0438\\u043B \\u043E\\u0434\\u0438\\u043D \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D \\u0432 \\u0442\\u0435\\u0447\\u0435\\u043D\\u0438\\u0435 8 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0434\\u0440\\u044F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s2(Footer, \"NV1plDefRBcEijKG+vyPwCh6jPU=\", false, function () {\n  return [useIsMobile];\n});\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "Link", "FiChevronDown", "FiChevronUp", "PiFacebookLogo", "PiInstagramLogo", "PiYoutubeLogo", "PiTelegramLogo", "logo", "vector", "btnListData", "footerMenuData", "styles", "jsxDEV", "_jsxDEV", "useIsMobile", "breakpoint", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "Footer", "_s2", "openSections", "setOpenSections", "sectionRefs", "socialLinks", "setSocialLinks", "toggleSection", "id", "prev", "fetch", "then", "res", "json", "data", "contacts", "links", "facebook", "push", "url", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "instagram", "youtube", "telegram", "children", "className", "content", "to", "src", "alt", "loading", "btnsList", "map", "btnItem", "item", "menu", "menuContainer", "items", "_sectionRefs$current$", "isOpen", "menuItem", "menuItemTitle", "onClick", "style", "cursor", "display", "alignItems", "justifyContent", "background", "border", "color", "width", "size", "menuLinks", "collapsible", "open", "ref", "el", "current", "maxHeight", "scrollHeight", "overflow", "transition", "linkId", "related", "social", "href", "target", "rel", "iconWrapper", "legacy", "copy", "desc", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/layout/Footer/Footer.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiChevronDown, FiChevronUp } from 'react-icons/fi';\n// sotcial icons\nimport { PiFacebookLogo } from 'react-icons/pi'\nimport { <PERSON><PERSON>nstagramLogo } from 'react-icons/pi'\nimport { PiYoutubeLogo } from 'react-icons/pi'\nimport { PiTelegramLogo } from 'react-icons/pi';\n\nimport logo from '../../asset/imgs/logo/PcLogo.svg';\nimport vector from '../../asset/imgs/logo/vector.webp';\nimport { btnListData, footerMenuData } from '../../asset/data/footerData';\n\nimport styles from './footer.module.css';\n\nconst useIsMobile = (breakpoint = 768) => {\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= breakpoint);\n\n  useEffect(() => {\n    const handleResize = () => setIsMobile(window.innerWidth <= breakpoint);\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [breakpoint]);\n\n  return isMobile;\n};\n\nconst Footer = () => {\n  const isMobile = useIsMobile();\n  const [openSections, setOpenSections] = useState({});\n  const sectionRefs = useRef({});\n  const [socialLinks, setSocialLinks] = useState([]);\n\n  const toggleSection = (id) => {\n    setOpenSections((prev) => ({\n      [id]: !prev[id], // откроется/закроется только один в моб версии\n    }));\n  };\n  useEffect(() => {\n    fetch('https://api.gwm.tj/api/v1/contacts')\n      .then((res) => res.json())\n      .then((data) => {\n        const contacts = data.contacts || {};\n        const links = [];\n\n        if (contacts.facebook)\n          links.push({\n            id: 'facebook',\n            url: contacts.facebook,\n            icon: <PiFacebookLogo />,\n            title: 'Facebook',\n          });\n        if (contacts.instagram)\n          links.push({\n            id: 'instagram',\n            url: contacts.instagram,\n            icon: <PiInstagramLogo />,\n            title: 'Instagram',\n          });\n        if (contacts.youtube)\n          links.push({\n            id: 'youtube',\n            url: contacts.youtube,\n            icon: <PiYoutubeLogo />,\n            title: 'YouTube',\n          });\n        if (contacts.telegram)\n          links.push({\n            id: 'telegram',\n            url: contacts.telegram,\n            icon: <PiTelegramLogo />,\n            title: 'Telegram',\n          });\n\n        setSocialLinks(links);\n      });\n  }, []);\n\n  return (\n    <footer>\n      <div className=\"container\">\n        <div className={styles.content}>\n          {/* Логотип */}\n          <div className={styles.logo}>\n            <Link to=\"/\" aria-label=\"На главную\">\n              <img src={logo} alt=\"GWM Логотип\" loading=\"lazy\" />\n            </Link>\n          </div>\n\n          {/* Кнопки */}\n          <div className={styles.btnsList}>\n            {btnListData.map(({ id, url, icon, title }) => (\n              <Link to={url} key={id} className={styles.btnItem}>\n                <div className={styles.item}>\n                  <img src={icon} alt={title} loading=\"lazy\" />\n                  <span>{title}</span>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Меню */}\n          <div className={styles.menu}>\n            <div className={styles.menuContainer}>\n              {footerMenuData.map(({ id, title, items }) => {\n                const isOpen = openSections[id];\n\n                return (\n                  <div className={styles.menuItem} key={id}>\n                    <button\n                      className={styles.menuItemTitle}\n                      onClick={() => isMobile && toggleSection(id)}\n                      aria-expanded={isOpen}\n                      aria-controls={`footer-menu-${id}`}\n                      style={{\n                        cursor: isMobile ? 'pointer' : 'default',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        background: 'none',\n                        border: 'none',\n                        color: '#fff',\n                        width: '100%',\n                      }}\n                    >\n                      {title}\n                      {isMobile &&\n                        (isOpen ? (\n                          <FiChevronUp size={24} />\n                        ) : (\n                          <FiChevronDown size={24} />\n                        ))}\n                    </button>\n\n                    <div\n                      id={`footer-menu-${id}`}\n                      className={`${styles.menuLinks} ${isMobile ? styles.collapsible : ''\n                        } ${isOpen ? styles.open : ''}`}\n                      ref={(el) => (sectionRefs.current[id] = el)}\n                      style={\n                        isMobile\n                          ? {\n                            maxHeight: isOpen\n                              ? `${sectionRefs.current[id]?.scrollHeight}px`\n                              : '0px',\n                            overflow: 'hidden',\n                            transition: 'max-height 0.3s ease',\n                          }\n                          : {}\n                      }\n                    >\n                      {items.map(({ id: linkId, url, title }) => (\n                        <Link to={url} key={linkId}>\n                          {title}\n                        </Link>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Соцсети и копирайт */}\n          <div className={styles.related}>\n            <div className={styles.social}>\n              {socialLinks.map(({ id, url, icon, title }) => (\n                <a\n                  key={id}\n                  href={url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  aria-label={title}\n                >\n                  {typeof icon === 'string' ? (\n                    <img src={icon} alt={title} loading=\"lazy\" />\n                  ) : (\n                    <span className={styles.iconWrapper}>{icon}</span>\n                  )}\n                </a>\n              ))}\n            </div>\n\n            <div className={styles.vector}>\n              <a href=\"https://vector.tj\" target=\"_black\">\n                <img src={vector} alt=\"GWM Логотип\" loading=\"lazy\" />\n              </a>\n            </div>\n\n            <div className={styles.legacy}>\n              <Link to=\"/privacy\">Политика конфиденциальности</Link>\n            </div>\n\n            <div className={styles.copy}>© 2025 GWM Таджикистан</div>\n          </div>\n\n          {/* Описание */}\n          <div className={styles.desc}>\n            <span>\n              Great Wall Motor Co. Ltd. (далее именуемая «GWM») — международный\n              мультибрендовый производитель автомобилей со штаб-квартирой в\n              Китае. Это всемирно известный производитель внедорожников и\n              пикапов, владеющий пятью линейками продукции, а именно GWM HAVAL,\n              GWM WEY, GWM ORA, GWM TANK и GWM Pickup, охватывающими легковые\n              автомобили и пикапы. GWM имеет самодостаточную цепочку двигателей,\n              трансмиссий и других основных компонентов. В 2003 году она была\n              включена в список акций H в Гонконге, а в 2011 году — акций A в\n              Китае. К концу 2023 года ее активы составили 201,93 млрд юаней, а\n              объем продаж превысил один миллион в течение 8 лет подря\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC3D;AACA,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,cAAc,QAAQ,gBAAgB;AAE/C,OAAOC,IAAI,MAAM,kCAAkC;AACnD,OAAOC,MAAM,MAAM,mCAAmC;AACtD,SAASC,WAAW,EAAEC,cAAc,QAAQ,6BAA6B;AAEzE,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,GAAGA,CAACC,UAAU,GAAG,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAACsB,MAAM,CAACC,UAAU,IAAIL,UAAU,CAAC;EAEzEjB,SAAS,CAAC,MAAM;IACd,MAAMuB,YAAY,GAAGA,CAAA,KAAMH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAIL,UAAU,CAAC;IACvEI,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;EAEhB,OAAOE,QAAQ;AACjB,CAAC;AAACD,EAAA,CAVIF,WAAW;AAYjB,MAAMU,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB,MAAMR,QAAQ,GAAGH,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM+B,WAAW,GAAG7B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAMkC,aAAa,GAAIC,EAAE,IAAK;IAC5BL,eAAe,CAAEM,IAAI,KAAM;MACzB,CAACD,EAAE,GAAG,CAACC,IAAI,CAACD,EAAE,CAAC,CAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;EACDlC,SAAS,CAAC,MAAM;IACdoC,KAAK,CAAC,oCAAoC,CAAC,CACxCC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACd,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAI,CAAC,CAAC;MACpC,MAAMC,KAAK,GAAG,EAAE;MAEhB,IAAID,QAAQ,CAACE,QAAQ,EACnBD,KAAK,CAACE,IAAI,CAAC;QACTV,EAAE,EAAE,UAAU;QACdW,GAAG,EAAEJ,QAAQ,CAACE,QAAQ;QACtBG,IAAI,eAAE/B,OAAA,CAACV,cAAc;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxBC,KAAK,EAAE;MACT,CAAC,CAAC;MACJ,IAAIV,QAAQ,CAACW,SAAS,EACpBV,KAAK,CAACE,IAAI,CAAC;QACTV,EAAE,EAAE,WAAW;QACfW,GAAG,EAAEJ,QAAQ,CAACW,SAAS;QACvBN,IAAI,eAAE/B,OAAA,CAACT,eAAe;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACzBC,KAAK,EAAE;MACT,CAAC,CAAC;MACJ,IAAIV,QAAQ,CAACY,OAAO,EAClBX,KAAK,CAACE,IAAI,CAAC;QACTV,EAAE,EAAE,SAAS;QACbW,GAAG,EAAEJ,QAAQ,CAACY,OAAO;QACrBP,IAAI,eAAE/B,OAAA,CAACR,aAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACvBC,KAAK,EAAE;MACT,CAAC,CAAC;MACJ,IAAIV,QAAQ,CAACa,QAAQ,EACnBZ,KAAK,CAACE,IAAI,CAAC;QACTV,EAAE,EAAE,UAAU;QACdW,GAAG,EAAEJ,QAAQ,CAACa,QAAQ;QACtBR,IAAI,eAAE/B,OAAA,CAACP,cAAc;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxBC,KAAK,EAAE;MACT,CAAC,CAAC;MAEJnB,cAAc,CAACU,KAAK,CAAC;IACvB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE3B,OAAA;IAAAwC,QAAA,eACExC,OAAA;MAAKyC,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxBxC,OAAA;QAAKyC,SAAS,EAAE3C,MAAM,CAAC4C,OAAQ;QAAAF,QAAA,gBAE7BxC,OAAA;UAAKyC,SAAS,EAAE3C,MAAM,CAACJ,IAAK;UAAA8C,QAAA,eAC1BxC,OAAA,CAACb,IAAI;YAACwD,EAAE,EAAC,GAAG;YAAC,cAAW,yDAAY;YAAAH,QAAA,eAClCxC,OAAA;cAAK4C,GAAG,EAAElD,IAAK;cAACmD,GAAG,EAAC,gDAAa;cAACC,OAAO,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNnC,OAAA;UAAKyC,SAAS,EAAE3C,MAAM,CAACiD,QAAS;UAAAP,QAAA,EAC7B5C,WAAW,CAACoD,GAAG,CAAC,CAAC;YAAE7B,EAAE;YAAEW,GAAG;YAAEC,IAAI;YAAEK;UAAM,CAAC,kBACxCpC,OAAA,CAACb,IAAI;YAACwD,EAAE,EAAEb,GAAI;YAAUW,SAAS,EAAE3C,MAAM,CAACmD,OAAQ;YAAAT,QAAA,eAChDxC,OAAA;cAAKyC,SAAS,EAAE3C,MAAM,CAACoD,IAAK;cAAAV,QAAA,gBAC1BxC,OAAA;gBAAK4C,GAAG,EAAEb,IAAK;gBAACc,GAAG,EAAET,KAAM;gBAACU,OAAO,EAAC;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CnC,OAAA;gBAAAwC,QAAA,EAAOJ;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC,GAJYhB,EAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhB,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnC,OAAA;UAAKyC,SAAS,EAAE3C,MAAM,CAACqD,IAAK;UAAAX,QAAA,eAC1BxC,OAAA;YAAKyC,SAAS,EAAE3C,MAAM,CAACsD,aAAc;YAAAZ,QAAA,EAClC3C,cAAc,CAACmD,GAAG,CAAC,CAAC;cAAE7B,EAAE;cAAEiB,KAAK;cAAEiB;YAAM,CAAC,KAAK;cAAA,IAAAC,qBAAA;cAC5C,MAAMC,MAAM,GAAG1C,YAAY,CAACM,EAAE,CAAC;cAE/B,oBACEnB,OAAA;gBAAKyC,SAAS,EAAE3C,MAAM,CAAC0D,QAAS;gBAAAhB,QAAA,gBAC9BxC,OAAA;kBACEyC,SAAS,EAAE3C,MAAM,CAAC2D,aAAc;kBAChCC,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,IAAIc,aAAa,CAACC,EAAE,CAAE;kBAC7C,iBAAeoC,MAAO;kBACtB,iBAAe,eAAepC,EAAE,EAAG;kBACnCwC,KAAK,EAAE;oBACLC,MAAM,EAAExD,QAAQ,GAAG,SAAS,GAAG,SAAS;oBACxCyD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/BC,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdC,KAAK,EAAE,MAAM;oBACbC,KAAK,EAAE;kBACT,CAAE;kBAAA3B,QAAA,GAEDJ,KAAK,EACLhC,QAAQ,KACNmD,MAAM,gBACLvD,OAAA,CAACX,WAAW;oBAAC+E,IAAI,EAAE;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEzBnC,OAAA,CAACZ,aAAa;oBAACgF,IAAI,EAAE;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAETnC,OAAA;kBACEmB,EAAE,EAAE,eAAeA,EAAE,EAAG;kBACxBsB,SAAS,EAAE,GAAG3C,MAAM,CAACuE,SAAS,IAAIjE,QAAQ,GAAGN,MAAM,CAACwE,WAAW,GAAG,EAAE,IAC9Df,MAAM,GAAGzD,MAAM,CAACyE,IAAI,GAAG,EAAE,EAAG;kBAClCC,GAAG,EAAGC,EAAE,IAAM1D,WAAW,CAAC2D,OAAO,CAACvD,EAAE,CAAC,GAAGsD,EAAI;kBAC5Cd,KAAK,EACHvD,QAAQ,GACJ;oBACAuE,SAAS,EAAEpB,MAAM,GACb,IAAAD,qBAAA,GAAGvC,WAAW,CAAC2D,OAAO,CAACvD,EAAE,CAAC,cAAAmC,qBAAA,uBAAvBA,qBAAA,CAAyBsB,YAAY,IAAI,GAC5C,KAAK;oBACTC,QAAQ,EAAE,QAAQ;oBAClBC,UAAU,EAAE;kBACd,CAAC,GACC,CAAC,CACN;kBAAAtC,QAAA,EAEAa,KAAK,CAACL,GAAG,CAAC,CAAC;oBAAE7B,EAAE,EAAE4D,MAAM;oBAAEjD,GAAG;oBAAEM;kBAAM,CAAC,kBACpCpC,OAAA,CAACb,IAAI;oBAACwD,EAAE,EAAEb,GAAI;oBAAAU,QAAA,EACXJ;kBAAK,GADY2C,MAAM;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GAhD8BhB,EAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDnC,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAKyC,SAAS,EAAE3C,MAAM,CAACkF,OAAQ;UAAAxC,QAAA,gBAC7BxC,OAAA;YAAKyC,SAAS,EAAE3C,MAAM,CAACmF,MAAO;YAAAzC,QAAA,EAC3BxB,WAAW,CAACgC,GAAG,CAAC,CAAC;cAAE7B,EAAE;cAAEW,GAAG;cAAEC,IAAI;cAAEK;YAAM,CAAC,kBACxCpC,OAAA;cAEEkF,IAAI,EAAEpD,GAAI;cACVqD,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB,cAAYhD,KAAM;cAAAI,QAAA,EAEjB,OAAOT,IAAI,KAAK,QAAQ,gBACvB/B,OAAA;gBAAK4C,GAAG,EAAEb,IAAK;gBAACc,GAAG,EAAET,KAAM;gBAACU,OAAO,EAAC;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE7CnC,OAAA;gBAAMyC,SAAS,EAAE3C,MAAM,CAACuF,WAAY;gBAAA7C,QAAA,EAAET;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAClD,GAVIhB,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWN,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnC,OAAA;YAAKyC,SAAS,EAAE3C,MAAM,CAACH,MAAO;YAAA6C,QAAA,eAC5BxC,OAAA;cAAGkF,IAAI,EAAC,mBAAmB;cAACC,MAAM,EAAC,QAAQ;cAAA3C,QAAA,eACzCxC,OAAA;gBAAK4C,GAAG,EAAEjD,MAAO;gBAACkD,GAAG,EAAC,gDAAa;gBAACC,OAAO,EAAC;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENnC,OAAA;YAAKyC,SAAS,EAAE3C,MAAM,CAACwF,MAAO;YAAA9C,QAAA,eAC5BxC,OAAA,CAACb,IAAI;cAACwD,EAAE,EAAC,UAAU;cAAAH,QAAA,EAAC;YAA2B;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENnC,OAAA;YAAKyC,SAAS,EAAE3C,MAAM,CAACyF,IAAK;YAAA/C,QAAA,EAAC;UAAsB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAGNnC,OAAA;UAAKyC,SAAS,EAAE3C,MAAM,CAAC0F,IAAK;UAAAhD,QAAA,eAC1BxC,OAAA;YAAAwC,QAAA,EAAM;UAWN;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACvB,GAAA,CA5LID,MAAM;EAAA,QACOV,WAAW;AAAA;AAAAwF,EAAA,GADxB9E,MAAM;AA8LZ,eAAeA,MAAM;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}