{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/components/SimpleCard/SimpleCard.jsx\",\n  _s = $RefreshSig$();\nimport styles from './simpleCard.module.css';\nimport img_1 from '../../asset/imgs/home/<USER>';\nimport img_2 from '../../asset/imgs/home/<USER>';\nimport { Link } from 'react-router-dom';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useEffect } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst data = [{\n  id: 1,\n  title: 'Специальные предложения',\n  text: 'Воспользуйтесь эксклюзивными предложениями на автомобили GWM.',\n  img: img_1,\n  url: '/offers'\n}, {\n  id: 2,\n  title: 'Флот GWM',\n  text: 'Расширьте возможности своего бизнеса с помощью гибких и надежных решений GWM для автопарков, отвечающих вашим эксплуатационным потребностям.',\n  img: img_2,\n  url: '/models'\n}];\nconst SimpleCard = () => {\n  _s();\n  useEffect(() => {\n    AOS.init({\n      duration: 1000,\n      once: false\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: styles.section,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.cards,\n        children: data.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.card,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.left,\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: item.url,\n              className: \"link\",\n              children: [\"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u0435\\u0435\", /*#__PURE__*/_jsxDEV(\"img\", {\n                src: arrowIcon,\n                alt: \"\",\n                className: \"linkIcon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.right,\n            \"data-aos\": \"fade-left\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.img,\n              alt: \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleCard, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = SimpleCard;\nexport default SimpleCard;\nvar _c;\n$RefreshReg$(_c, \"SimpleCard\");", "map": {"version": 3, "names": ["styles", "img_1", "img_2", "Link", "arrowIcon", "useEffect", "AOS", "jsxDEV", "_jsxDEV", "data", "id", "title", "text", "img", "url", "SimpleCard", "_s", "init", "duration", "once", "className", "section", "children", "cards", "map", "item", "card", "left", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "right", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/components/SimpleCard/SimpleCard.jsx"], "sourcesContent": ["import styles from './simpleCard.module.css';\nimport img_1 from '../../asset/imgs/home/<USER>';\nimport img_2 from '../../asset/imgs/home/<USER>';\nimport { Link } from 'react-router-dom';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useEffect } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst data = [\n  {\n    id: 1,\n    title: 'Специальные предложения',\n    text: 'Воспользуйтесь эксклюзивными предложениями на автомобили GWM.',\n    img: img_1,\n    url: '/offers',\n  },\n  {\n    id: 2,\n    title: 'Флот GWM',\n    text: 'Расширьте возможности своего бизнеса с помощью гибких и надежных решений GWM для автопарков, отвечающих вашим эксплуатационным потребностям.',\n    img: img_2,\n    url: '/models',\n  },\n];\n\nconst SimpleCard = () => {\n  useEffect(() => {\n    AOS.init({\n      duration: 1000,\n      once: false,\n    });\n  }, []);\n\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.cards}>\n          {data.map((item) => (\n            <div className={styles.card} key={item.id}>\n              <div className={styles.left}>\n                <h3>{item.title}</h3>\n                <p>{item.text}</p>\n                <Link to={item.url} className=\"link\">\n                  Подробнее\n                  <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                </Link>\n              </div>\n              <div className={styles.right} data-aos=\"fade-left\">\n                <img src={item.img} alt=\"\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SimpleCard;\n"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,0DAA0D;AAC5E,OAAOC,KAAK,MAAM,8CAA8C;AAChE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,SAAS,MAAM,kCAAkC;AACxD,SAASC,SAAS,QAAQ,OAAO;AACjC,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAG,CACX;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yBAAyB;EAChCC,IAAI,EAAE,+DAA+D;EACrEC,GAAG,EAAEZ,KAAK;EACVa,GAAG,EAAE;AACP,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,8IAA8I;EACpJC,GAAG,EAAEX,KAAK;EACVY,GAAG,EAAE;AACP,CAAC,CACF;AAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvBX,SAAS,CAAC,MAAM;IACdC,GAAG,CAACW,IAAI,CAAC;MACPC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEX,OAAA;IAASY,SAAS,EAAEpB,MAAM,CAACqB,OAAQ;IAAAC,QAAA,eACjCd,OAAA;MAAKY,SAAS,EAAC,WAAW;MAAAE,QAAA,eACxBd,OAAA;QAAKY,SAAS,EAAEpB,MAAM,CAACuB,KAAM;QAAAD,QAAA,EAC1Bb,IAAI,CAACe,GAAG,CAAEC,IAAI,iBACbjB,OAAA;UAAKY,SAAS,EAAEpB,MAAM,CAAC0B,IAAK;UAAAJ,QAAA,gBAC1Bd,OAAA;YAAKY,SAAS,EAAEpB,MAAM,CAAC2B,IAAK;YAAAL,QAAA,gBAC1Bd,OAAA;cAAAc,QAAA,EAAKG,IAAI,CAACd;YAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBvB,OAAA;cAAAc,QAAA,EAAIG,IAAI,CAACb;YAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBvB,OAAA,CAACL,IAAI;cAAC6B,EAAE,EAAEP,IAAI,CAACX,GAAI;cAACM,SAAS,EAAC,MAAM;cAAAE,QAAA,GAAC,wDAEnC,eAAAd,OAAA;gBAAKyB,GAAG,EAAE7B,SAAU;gBAAC8B,GAAG,EAAC,EAAE;gBAACd,SAAS,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvB,OAAA;YAAKY,SAAS,EAAEpB,MAAM,CAACmC,KAAM;YAAC,YAAS,WAAW;YAAAb,QAAA,eAChDd,OAAA;cAAKyB,GAAG,EAAER,IAAI,CAACZ,GAAI;cAACqB,GAAG,EAAC;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA,GAX0BN,IAAI,CAACf,EAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYpC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACf,EAAA,CA/BID,UAAU;AAAAqB,EAAA,GAAVrB,UAAU;AAiChB,eAAeA,UAAU;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}