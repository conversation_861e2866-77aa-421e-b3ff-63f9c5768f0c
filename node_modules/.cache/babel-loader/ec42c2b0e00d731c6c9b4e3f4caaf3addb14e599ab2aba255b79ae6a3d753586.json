{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { animateTarget } from '../interfaces/visual-element-target.mjs';\nimport { createDOMVisualElement, createObjectVisualElement } from '../utils/create-visual-element.mjs';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\nimport { resolveSubjects } from './resolve-subjects.mjs';\nimport { animateSingleValue } from './single-value.mjs';\nfunction isSingleValue(subject, keyframes) {\n  return isMotionValue(subject) || typeof subject === \"number\" || typeof subject === \"string\" && !isDOMKeyframes(keyframes);\n}\n/**\n * Implementation\n */\nfunction animateSubject(subject, keyframes, options, scope) {\n  const animations = [];\n  if (isSingleValue(subject, keyframes)) {\n    animations.push(animateSingleValue(subject, isDOMKeyframes(keyframes) ? keyframes.default || keyframes : keyframes, options ? options.default || options : options));\n  } else {\n    const subjects = resolveSubjects(subject, keyframes, scope);\n    const numSubjects = subjects.length;\n    invariant(Boolean(numSubjects), \"No valid elements provided.\");\n    for (let i = 0; i < numSubjects; i++) {\n      const thisSubject = subjects[i];\n      const createVisualElement = thisSubject instanceof Element ? createDOMVisualElement : createObjectVisualElement;\n      if (!visualElementStore.has(thisSubject)) {\n        createVisualElement(thisSubject);\n      }\n      const visualElement = visualElementStore.get(thisSubject);\n      const transition = _objectSpread({}, options);\n      /**\n       * Resolve stagger function if provided.\n       */\n      if (\"delay\" in transition && typeof transition.delay === \"function\") {\n        transition.delay = transition.delay(i, numSubjects);\n      }\n      animations.push(...animateTarget(visualElement, _objectSpread(_objectSpread({}, keyframes), {}, {\n        transition\n      }), {}));\n    }\n  }\n  return animations;\n}\nexport { animateSubject };", "map": {"version": 3, "names": ["isMotionValue", "invariant", "visualElementStore", "animate<PERSON>arget", "createDOMVisualElement", "createObjectVisualElement", "isDOMKeyframes", "resolveSubjects", "animateSingleValue", "isSingleValue", "subject", "keyframes", "animateSubject", "options", "scope", "animations", "push", "default", "subjects", "numSubjects", "length", "Boolean", "i", "thisSubject", "createVisualElement", "Element", "has", "visualElement", "get", "transition", "_objectSpread", "delay"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/animate/subject.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { animateTarget } from '../interfaces/visual-element-target.mjs';\nimport { createDOMVisualElement, createObjectVisualElement } from '../utils/create-visual-element.mjs';\nimport { isDOMKeyframes } from '../utils/is-dom-keyframes.mjs';\nimport { resolveSubjects } from './resolve-subjects.mjs';\nimport { animateSingleValue } from './single-value.mjs';\n\nfunction isSingleValue(subject, keyframes) {\n    return (isMotionValue(subject) ||\n        typeof subject === \"number\" ||\n        (typeof subject === \"string\" && !isDOMKeyframes(keyframes)));\n}\n/**\n * Implementation\n */\nfunction animateSubject(subject, keyframes, options, scope) {\n    const animations = [];\n    if (isSingleValue(subject, keyframes)) {\n        animations.push(animateSingleValue(subject, isDOMKeyframes(keyframes)\n            ? keyframes.default || keyframes\n            : keyframes, options ? options.default || options : options));\n    }\n    else {\n        const subjects = resolveSubjects(subject, keyframes, scope);\n        const numSubjects = subjects.length;\n        invariant(Boolean(numSubjects), \"No valid elements provided.\");\n        for (let i = 0; i < numSubjects; i++) {\n            const thisSubject = subjects[i];\n            const createVisualElement = thisSubject instanceof Element\n                ? createDOMVisualElement\n                : createObjectVisualElement;\n            if (!visualElementStore.has(thisSubject)) {\n                createVisualElement(thisSubject);\n            }\n            const visualElement = visualElementStore.get(thisSubject);\n            const transition = { ...options };\n            /**\n             * Resolve stagger function if provided.\n             */\n            if (\"delay\" in transition &&\n                typeof transition.delay === \"function\") {\n                transition.delay = transition.delay(i, numSubjects);\n            }\n            animations.push(...animateTarget(visualElement, { ...keyframes, transition }, {}));\n        }\n    }\n    return animations;\n}\n\nexport { animateSubject };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,aAAa,QAAQ,yCAAyC;AACvE,SAASC,sBAAsB,EAAEC,yBAAyB,QAAQ,oCAAoC;AACtG,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,kBAAkB,QAAQ,oBAAoB;AAEvD,SAASC,aAAaA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACvC,OAAQX,aAAa,CAACU,OAAO,CAAC,IAC1B,OAAOA,OAAO,KAAK,QAAQ,IAC1B,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACJ,cAAc,CAACK,SAAS,CAAE;AACnE;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACF,OAAO,EAAEC,SAAS,EAAEE,OAAO,EAAEC,KAAK,EAAE;EACxD,MAAMC,UAAU,GAAG,EAAE;EACrB,IAAIN,aAAa,CAACC,OAAO,EAAEC,SAAS,CAAC,EAAE;IACnCI,UAAU,CAACC,IAAI,CAACR,kBAAkB,CAACE,OAAO,EAAEJ,cAAc,CAACK,SAAS,CAAC,GAC/DA,SAAS,CAACM,OAAO,IAAIN,SAAS,GAC9BA,SAAS,EAAEE,OAAO,GAAGA,OAAO,CAACI,OAAO,IAAIJ,OAAO,GAAGA,OAAO,CAAC,CAAC;EACrE,CAAC,MACI;IACD,MAAMK,QAAQ,GAAGX,eAAe,CAACG,OAAO,EAAEC,SAAS,EAAEG,KAAK,CAAC;IAC3D,MAAMK,WAAW,GAAGD,QAAQ,CAACE,MAAM;IACnCnB,SAAS,CAACoB,OAAO,CAACF,WAAW,CAAC,EAAE,6BAA6B,CAAC;IAC9D,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;MAClC,MAAMC,WAAW,GAAGL,QAAQ,CAACI,CAAC,CAAC;MAC/B,MAAME,mBAAmB,GAAGD,WAAW,YAAYE,OAAO,GACpDrB,sBAAsB,GACtBC,yBAAyB;MAC/B,IAAI,CAACH,kBAAkB,CAACwB,GAAG,CAACH,WAAW,CAAC,EAAE;QACtCC,mBAAmB,CAACD,WAAW,CAAC;MACpC;MACA,MAAMI,aAAa,GAAGzB,kBAAkB,CAAC0B,GAAG,CAACL,WAAW,CAAC;MACzD,MAAMM,UAAU,GAAAC,aAAA,KAAQjB,OAAO,CAAE;MACjC;AACZ;AACA;MACY,IAAI,OAAO,IAAIgB,UAAU,IACrB,OAAOA,UAAU,CAACE,KAAK,KAAK,UAAU,EAAE;QACxCF,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACE,KAAK,CAACT,CAAC,EAAEH,WAAW,CAAC;MACvD;MACAJ,UAAU,CAACC,IAAI,CAAC,GAAGb,aAAa,CAACwB,aAAa,EAAAG,aAAA,CAAAA,aAAA,KAAOnB,SAAS;QAAEkB;MAAU,IAAI,CAAC,CAAC,CAAC,CAAC;IACtF;EACJ;EACA,OAAOd,UAAU;AACrB;AAEA,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}