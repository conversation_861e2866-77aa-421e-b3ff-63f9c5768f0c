{"ast": null, "code": "import { Fragment } from 'react';\nimport { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nconst createDomVisualElement = (Component, options) => {\n  return isSVGComponent(Component) ? new SVGVisualElement(options) : new HTMLVisualElement(options, {\n    allowProjection: Component !== Fragment\n  });\n};\nexport { createDomVisualElement };", "map": {"version": 3, "names": ["Fragment", "HTMLVisualElement", "SVGVisualElement", "isSVGComponent", "createDomVisualElement", "Component", "options", "allowProjection"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs"], "sourcesContent": ["import { Fragment } from 'react';\nimport { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nconst createDomVisualElement = (Component, options) => {\n    return isSVGComponent(Component)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options, {\n            allowProjection: Component !== Fragment,\n        });\n};\n\nexport { createDomVisualElement };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,MAAMC,sBAAsB,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;EACnD,OAAOH,cAAc,CAACE,SAAS,CAAC,GAC1B,IAAIH,gBAAgB,CAACI,OAAO,CAAC,GAC7B,IAAIL,iBAAiB,CAACK,OAAO,EAAE;IAC7BC,eAAe,EAAEF,SAAS,KAAKL;EACnC,CAAC,CAAC;AACV,CAAC;AAED,SAASI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}