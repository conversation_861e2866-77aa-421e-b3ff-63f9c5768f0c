{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import arrowIcon from'../../../../asset/imgs/icons/arrow.svg';import styles from'./ourModels.module.css';import{Link}from'react-router-dom';import FilterSwiper from'./FilterSlide/FilterSwiper';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MAX_DESC_LENGTH=100;const OurModels=()=>{const[cars,setCars]=useState([]);const[activeModel,setActiveModel]=useState(null);const[isExpanded,setIsExpanded]=useState(false);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchCars=async()=>{try{const response=await fetch('https://api.gwm.tj/api/v1/models');const data=await response.json();setCars(data.models);if(data.models.length>0){setActiveModel(data.models[0].id);}}catch(error){}finally{setLoading(false);}};fetchCars();},[]);const car=cars.find(item=>item.id===activeModel);const toggleExpand=()=>setIsExpanded(prev=>!prev);const renderDescription=desc=>{if(!desc)return'Описание появится скоро.';if(desc.length<=MAX_DESC_LENGTH||isExpanded)return desc;return desc.slice(0,MAX_DESC_LENGTH)+'...';};return/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.title,children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u0438\"}),/*#__PURE__*/_jsxs(Link,{to:\"/models\",className:\"link\",children:[\"\\u0412\\u0441\\u0435 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \",/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]})]}),loading?/*#__PURE__*/_jsxs(\"div\",{className:styles.item,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.box,children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonTitle)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonText)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonText)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonText)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonButton)})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.img,children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.skeletonImg)})})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:car&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},transition:{duration:0.4},className:styles.context,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.item,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.box,children:[/*#__PURE__*/_jsx(\"h3\",{children:car.title}),/*#__PURE__*/_jsxs(\"p\",{children:[renderDescription(car.description),car.description&&car.description.length>MAX_DESC_LENGTH&&/*#__PURE__*/_jsx(\"button\",{onClick:toggleExpand,className:styles.showMoreBtn,children:isExpanded?'Скрыть':'Показать ещё'})]}),/*#__PURE__*/_jsx(Link,{to:\"models/\".concat(car.slug||'#'),children:/*#__PURE__*/_jsx(\"button\",{className:\"button-black\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0423\\u0437\\u043D\\u0430\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435\"})})})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.img,children:/*#__PURE__*/_jsx(\"img\",{src:car.preview_show,alt:car.title})})]})},car.id)}),/*#__PURE__*/_jsx(FilterSwiper,{activeModel:activeModel,setActiveModel:id=>{setActiveModel(id);setIsExpanded(false);},cars:cars})]})]})});};export default OurModels;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "arrowIcon", "styles", "Link", "FilterSwiper", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MAX_DESC_LENGTH", "OurModels", "cars", "setCars", "activeModel", "setActiveModel", "isExpanded", "setIsExpanded", "loading", "setLoading", "fetchCars", "response", "fetch", "data", "json", "models", "length", "id", "error", "car", "find", "item", "toggleExpand", "prev", "renderDescription", "desc", "slice", "className", "section", "children", "title", "to", "src", "alt", "box", "concat", "skeleton", "skeleton<PERSON>itle", "skeletonText", "skeleton<PERSON><PERSON><PERSON>", "img", "skeletonImg", "mode", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "context", "description", "onClick", "showMoreBtn", "slug", "preview_show"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/Models/OurModels.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport arrowIcon from '../../../../asset/imgs/icons/arrow.svg';\n\nimport styles from './ourModels.module.css';\nimport { Link } from 'react-router-dom';\nimport FilterSwiper from './FilterSlide/FilterSwiper';\n\nconst MAX_DESC_LENGTH = 100;\n\nconst OurModels = () => {\n  const [cars, setCars] = useState([]);\n  const [activeModel, setActiveModel] = useState(null);\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchCars = async () => {\n      try {\n        const response = await fetch('https://api.gwm.tj/api/v1/models');\n        const data = await response.json();\n        setCars(data.models);\n        if (data.models.length > 0) {\n          setActiveModel(data.models[0].id);\n        }\n      } catch (error) {\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCars();\n  }, []);\n\n  const car = cars.find((item) => item.id === activeModel);\n\n  const toggleExpand = () => setIsExpanded((prev) => !prev);\n\n  const renderDescription = (desc) => {\n    if (!desc) return 'Описание появится скоро.';\n    if (desc.length <= MAX_DESC_LENGTH || isExpanded) return desc;\n    return desc.slice(0, MAX_DESC_LENGTH) + '...';\n  };\n\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.title}>\n          <h2>Модели</h2>\n\n          <Link to=\"/models\" className=\"link\">\n            Все модели <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n          </Link>\n        </div>\n\n        {loading ? (\n          <div className={styles.item}>\n            <div className={styles.box}>\n              <div\n                className={`${styles.skeleton} ${styles.skeletonTitle}`}\n              ></div>\n              <div\n                className={`${styles.skeleton} ${styles.skeletonText}`}\n              ></div>\n              <div\n                className={`${styles.skeleton} ${styles.skeletonText}`}\n              ></div>\n              <div\n                className={`${styles.skeleton} ${styles.skeletonText}`}\n              ></div>\n              <div\n                className={`${styles.skeleton} ${styles.skeletonButton}`}\n              ></div>\n            </div>\n            <div className={styles.img}>\n              <div className={`${styles.skeleton} ${styles.skeletonImg}`}></div>\n            </div>\n          </div>\n        ) : (\n          <>\n            <AnimatePresence mode=\"wait\">\n              {car && (\n                <motion.div\n                  key={car.id}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -30 }}\n                  transition={{ duration: 0.4 }}\n                  className={styles.context}\n                >\n                  <div className={styles.item}>\n                    <div className={styles.box}>\n                      <h3>{car.title}</h3>\n                      <p>\n                        {renderDescription(car.description)}\n                        {car.description &&\n                          car.description.length > MAX_DESC_LENGTH && (\n                            <button\n                              onClick={toggleExpand}\n                              className={styles.showMoreBtn}\n                            >\n                              {isExpanded ? 'Скрыть' : 'Показать ещё'}\n                            </button>\n                          )}\n                      </p>\n                      <Link to={`models/${car.slug || '#'}`}>\n                        <button className=\"button-black\">\n                          <span>Узнать больше</span>\n                        </button>\n                      </Link>\n                    </div>\n                    <div className={styles.img}>\n                      <img src={car.preview_show} alt={car.title} />\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            <FilterSwiper\n              activeModel={activeModel}\n              setActiveModel={(id) => {\n                setActiveModel(id);\n                setIsExpanded(false);\n              }}\n              cars={cars}\n            />\n          </>\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default OurModels;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,SAAS,KAAM,wCAAwC,CAE9D,MAAO,CAAAC,MAAM,KAAM,wBAAwB,CAC3C,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAE3B,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACsB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,kCAAkC,CAAC,CAChE,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClCX,OAAO,CAACU,IAAI,CAACE,MAAM,CAAC,CACpB,GAAIF,IAAI,CAACE,MAAM,CAACC,MAAM,CAAG,CAAC,CAAE,CAC1BX,cAAc,CAACQ,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC,CACnC,CACF,CAAE,MAAOC,KAAK,CAAE,CAChB,CAAC,OAAS,CACRT,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDC,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,GAAG,CAAGjB,IAAI,CAACkB,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACJ,EAAE,GAAKb,WAAW,CAAC,CAExD,KAAM,CAAAkB,YAAY,CAAGA,CAAA,GAAMf,aAAa,CAAEgB,IAAI,EAAK,CAACA,IAAI,CAAC,CAEzD,KAAM,CAAAC,iBAAiB,CAAIC,IAAI,EAAK,CAClC,GAAI,CAACA,IAAI,CAAE,MAAO,0BAA0B,CAC5C,GAAIA,IAAI,CAACT,MAAM,EAAIhB,eAAe,EAAIM,UAAU,CAAE,MAAO,CAAAmB,IAAI,CAC7D,MAAO,CAAAA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAE1B,eAAe,CAAC,CAAG,KAAK,CAC/C,CAAC,CAED,mBACEL,IAAA,YAASgC,SAAS,CAAEpC,MAAM,CAACqC,OAAQ,CAAAC,QAAA,cACjChC,KAAA,QAAK8B,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxBhC,KAAA,QAAK8B,SAAS,CAAEpC,MAAM,CAACuC,KAAM,CAAAD,QAAA,eAC3BlC,IAAA,OAAAkC,QAAA,CAAI,sCAAM,CAAI,CAAC,cAEfhC,KAAA,CAACL,IAAI,EAACuC,EAAE,CAAC,SAAS,CAACJ,SAAS,CAAC,MAAM,CAAAE,QAAA,EAAC,0DACvB,cAAAlC,IAAA,QAAKqC,GAAG,CAAE1C,SAAU,CAAC2C,GAAG,CAAC,EAAE,CAACN,SAAS,CAAC,UAAU,CAAE,CAAC,EAC1D,CAAC,EACJ,CAAC,CAELnB,OAAO,cACNX,KAAA,QAAK8B,SAAS,CAAEpC,MAAM,CAAC8B,IAAK,CAAAQ,QAAA,eAC1BhC,KAAA,QAAK8B,SAAS,CAAEpC,MAAM,CAAC2C,GAAI,CAAAL,QAAA,eACzBlC,IAAA,QACEgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAAC8C,aAAa,CAAG,CACpD,CAAC,cACP1C,IAAA,QACEgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAAC+C,YAAY,CAAG,CACnD,CAAC,cACP3C,IAAA,QACEgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAAC+C,YAAY,CAAG,CACnD,CAAC,cACP3C,IAAA,QACEgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAAC+C,YAAY,CAAG,CACnD,CAAC,cACP3C,IAAA,QACEgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAACgD,cAAc,CAAG,CACrD,CAAC,EACJ,CAAC,cACN5C,IAAA,QAAKgC,SAAS,CAAEpC,MAAM,CAACiD,GAAI,CAAAX,QAAA,cACzBlC,IAAA,QAAKgC,SAAS,IAAAQ,MAAA,CAAK5C,MAAM,CAAC6C,QAAQ,MAAAD,MAAA,CAAI5C,MAAM,CAACkD,WAAW,CAAG,CAAM,CAAC,CAC/D,CAAC,EACH,CAAC,cAEN5C,KAAA,CAAAE,SAAA,EAAA8B,QAAA,eACElC,IAAA,CAACN,eAAe,EAACqD,IAAI,CAAC,MAAM,CAAAb,QAAA,CACzBV,GAAG,eACFxB,IAAA,CAACP,MAAM,CAACuD,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BvB,SAAS,CAAEpC,MAAM,CAAC4D,OAAQ,CAAAtB,QAAA,cAE1BhC,KAAA,QAAK8B,SAAS,CAAEpC,MAAM,CAAC8B,IAAK,CAAAQ,QAAA,eAC1BhC,KAAA,QAAK8B,SAAS,CAAEpC,MAAM,CAAC2C,GAAI,CAAAL,QAAA,eACzBlC,IAAA,OAAAkC,QAAA,CAAKV,GAAG,CAACW,KAAK,CAAK,CAAC,cACpBjC,KAAA,MAAAgC,QAAA,EACGL,iBAAiB,CAACL,GAAG,CAACiC,WAAW,CAAC,CAClCjC,GAAG,CAACiC,WAAW,EACdjC,GAAG,CAACiC,WAAW,CAACpC,MAAM,CAAGhB,eAAe,eACtCL,IAAA,WACE0D,OAAO,CAAE/B,YAAa,CACtBK,SAAS,CAAEpC,MAAM,CAAC+D,WAAY,CAAAzB,QAAA,CAE7BvB,UAAU,CAAG,QAAQ,CAAG,cAAc,CACjC,CACT,EACF,CAAC,cACJX,IAAA,CAACH,IAAI,EAACuC,EAAE,WAAAI,MAAA,CAAYhB,GAAG,CAACoC,IAAI,EAAI,GAAG,CAAG,CAAA1B,QAAA,cACpClC,IAAA,WAAQgC,SAAS,CAAC,cAAc,CAAAE,QAAA,cAC9BlC,IAAA,SAAAkC,QAAA,CAAM,2EAAa,CAAM,CAAC,CACpB,CAAC,CACL,CAAC,EACJ,CAAC,cACNlC,IAAA,QAAKgC,SAAS,CAAEpC,MAAM,CAACiD,GAAI,CAAAX,QAAA,cACzBlC,IAAA,QAAKqC,GAAG,CAAEb,GAAG,CAACqC,YAAa,CAACvB,GAAG,CAAEd,GAAG,CAACW,KAAM,CAAE,CAAC,CAC3C,CAAC,EACH,CAAC,EA/BDX,GAAG,CAACF,EAgCC,CACb,CACc,CAAC,cAElBtB,IAAA,CAACF,YAAY,EACXW,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAGY,EAAE,EAAK,CACtBZ,cAAc,CAACY,EAAE,CAAC,CAClBV,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACFL,IAAI,CAAEA,IAAK,CACZ,CAAC,EACF,CACH,EACE,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}