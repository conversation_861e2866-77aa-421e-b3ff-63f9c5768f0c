{"ast": null, "code": "import { createRenderBatcher } from './batcher.mjs';\nconst {\n  schedule: microtask,\n  cancel: cancelMicrotask\n} = /* @__PURE__ */createRenderBatcher(queueMicrotask, false);\nexport { cancelMicrotask, microtask };", "map": {"version": 3, "names": ["createRenderBatcher", "schedule", "microtask", "cancel", "cancelMicrotask", "queueMicrotask"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/frameloop/microtask.mjs"], "sourcesContent": ["import { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ createRenderBatcher(queueMicrotask, false);\n\nexport { cancelMicrotask, microtask };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,eAAe;AAEnD,MAAM;EAAEC,QAAQ,EAAEC,SAAS;EAAEC,MAAM,EAAEC;AAAgB,CAAC,GACtD,eAAgBJ,mBAAmB,CAACK,cAAc,EAAE,KAAK,CAAC;AAE1D,SAASD,eAAe,EAAEF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}