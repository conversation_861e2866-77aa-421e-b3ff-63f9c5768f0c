{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Offer/Offers.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from 'react';\nimport styles from './offers.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useNavigate } from 'react-router-dom';\nimport Notification from '../../components/Notification/Notification';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SkeletonCard = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `${styles.card} ${styles.skeletonCard}`,\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.img} ${styles.skeleton_img}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.title,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${styles.skeleton} ${styles.textLine}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${styles.skeleton} ${styles.textLine}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.skeleton} ${styles.linkLine}`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this);\n_c = SkeletonCard;\nconst Offers = () => {\n  _s();\n  const [pageLoading, setPageLoading] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [news, setNews] = useState([]);\n  const navigate = useNavigate();\n  const [notification, setNotification] = useState({\n    message: '',\n    type: ''\n  });\n  const showNotification = (message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification({\n      message: '',\n      type: ''\n    }), 3000);\n  };\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    setPageLoading(false);\n    setLoading(true);\n    fetch('https://api.gwm.tj/api/v1/promo').then(res => res.json()).then(json => {\n      // Исправляем — теперь берем promo, а не news\n      setNews(json.promo || []);\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n      AOS.refresh();\n    }).catch(err => {\n      setLoading(false);\n      showNotification('Не удалось загрузить список. Возможно идет тех.работа', 'error');\n      document.body.style.overflow = 'visible';\n    });\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  const handleNavigation = useCallback(slug => {\n    navigate(`/offers/${slug}`);\n  }, [navigate]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Notification, {\n      message: notification.message,\n      type: notification.type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), pageLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"50\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u041E\\u0422\\u041A\\u0420\\u041E\\u0419\\u0422\\u0415 \\u0414\\u041B\\u042F \\u0421\\u0415\\u0411\\u042F \\u0423\\u041D\\u0418\\u041A\\u0410\\u041B\\u042C\\u041D\\u042B\\u0415 \\u0412\\u041E\\u0417\\u041C\\u041E\\u0416\\u041D\\u041E\\u0421\\u0422\\u0418\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"redLine\",\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"100\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"150\",\n            children: \"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\\u043C\\u0438, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u044B \\u043F\\u043E\\u0434\\u0433\\u043E\\u0442\\u043E\\u0432\\u0438\\u043B\\u0438 \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441 \\u0432 \\u044D\\u0442\\u043E\\u043C \\u043C\\u0435\\u0441\\u044F\\u0446\\u0435.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flexContent,\n            children: loading ? Array.from({\n              length: 4\n            }).map((_, index) => /*#__PURE__*/_jsxDEV(SkeletonCard, {}, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 21\n            }, this)) : Array.isArray(news) && news.length > 0 ? news.map(item => {\n              var _item$title;\n              const title = (item === null || item === void 0 ? void 0 : (_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.length) > 60 ? item.title.slice(0, 60).trim() + '...' : item.title;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.card,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.img,\n                  children: (item === null || item === void 0 ? void 0 : item.preview) && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.preview,\n                    alt: item.title || 'Image'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.title,\n                  children: [(item === null || item === void 0 ? void 0 : item.created_at) && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: new Date(item.created_at).toLocaleDateString('ru-RU')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 29\n                  }, this), title && /*#__PURE__*/_jsxDEV(\"h2\", {\n                    children: title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"link\",\n                  onClick: () => handleNavigation(item.slug),\n                  children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                    children: \"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u0435\\u0435\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: arrowIcon,\n                    alt: \"\",\n                    className: \"linkIcon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 25\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 23\n              }, this);\n            }) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u041D\\u0435\\u0442 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0438.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Offers, \"ltEKbR/9y5Gj+YvrBRvPsDWGOgs=\", false, function () {\n  return [useNavigate];\n});\n_c2 = Offers;\nexport default Offers;\nvar _c, _c2;\n$RefreshReg$(_c, \"SkeletonCard\");\n$RefreshReg$(_c2, \"Offers\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "styles", "AOS", "arrowIcon", "useNavigate", "Notification", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SkeletonCard", "className", "card", "skeletonCard", "children", "img", "skeleton_img", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "skeleton", "textLine", "linkLine", "_c", "Offers", "_s", "pageLoading", "setPageLoading", "loading", "setLoading", "news", "setNews", "navigate", "notification", "setNotification", "message", "type", "showNotification", "setTimeout", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "fetch", "then", "res", "json", "promo", "refresh", "catch", "err", "handleNavigation", "slug", "flexContent", "Array", "from", "length", "map", "_", "index", "isArray", "item", "_item$title", "slice", "trim", "preview", "src", "alt", "created_at", "Date", "toLocaleDateString", "onClick", "id", "_c2", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Offer/Offers.jsx"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from 'react';\nimport styles from './offers.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useNavigate } from 'react-router-dom';\nimport Notification from '../../components/Notification/Notification';\n\nconst SkeletonCard = () => (\n  <div className={`${styles.card} ${styles.skeletonCard}`}>\n    <div className={`${styles.img} ${styles.skeleton_img}`}></div>\n    <div className={styles.title}>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n    </div>\n    <div className={`${styles.skeleton} ${styles.linkLine}`}></div>\n  </div>\n);\n\nconst Offers = () => {\n  const [pageLoading, setPageLoading] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [news, setNews] = useState([]);\n  const navigate = useNavigate();\n  const [notification, setNotification] = useState({ message: '', type: '' });\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  };\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    setPageLoading(false);\n    setLoading(true);\n\n    fetch('https://api.gwm.tj/api/v1/promo')\n      .then((res) => res.json())\n      .then((json) => {\n        // Исправляем — теперь берем promo, а не news\n        setNews(json.promo || []);\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n        AOS.refresh();\n      })\n      .catch((err) => {\n        setLoading(false);\n        showNotification(\n          'Не удалось загрузить список. Возможно идет тех.работа',\n          'error'\n        );\n        document.body.style.overflow = 'visible';\n      });\n\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  const handleNavigation = useCallback(\n    (slug) => {\n      navigate(`/offers/${slug}`);\n    },\n    [navigate]\n  );\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {pageLoading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"50\">\n                <strong>ОТКРОЙТЕ ДЛЯ СЕБЯ УНИКАЛЬНЫЕ ВОЗМОЖНОСТИ</strong>\n              </h1>\n              <i\n                className=\"redLine\"\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n              ></i>\n              <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Ознакомьтесь с предложениями, которые мы подготовили для вас в\n                этом месяце.\n              </p>\n            </div>\n            <section>\n              <div className={styles.flexContent}>\n                {loading ? (\n                  Array.from({ length: 4 }).map((_, index) => (\n                    <SkeletonCard key={index} />\n                  ))\n                ) : Array.isArray(news) && news.length > 0 ? (\n                  news.map((item) => {\n                    const title =\n                      item?.title?.length > 60\n                        ? item.title.slice(0, 60).trim() + '...'\n                        : item.title;\n                    return (\n                      <div className={styles.card} key={item.id}>\n                        <div className={styles.img}>\n                          {item?.preview && (\n                            <img\n                              src={item.preview}\n                              alt={item.title || 'Image'}\n                            />\n                          )}\n                        </div>\n                        <div className={styles.title}>\n                          {item?.created_at && (\n                            <span>\n                              {new Date(item.created_at).toLocaleDateString(\n                                'ru-RU'\n                              )}\n                            </span>\n                          )}\n                          {title && <h2>{title}</h2>}\n                          {/*  sub_title */}\n                          {/* <p>{item.type}</p> */}\n                        </div>\n\n                        <div\n                          className=\"link\"\n                          onClick={() => handleNavigation(item.slug)}\n                        >\n                          <b>Подробнее</b>\n                          <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                        </div>\n                      </div>\n                    );\n                  })\n                ) : (\n                  <p>Нет предложении.</p>\n                )}\n              </div>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Offers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AACzB,OAAOC,SAAS,MAAM,kCAAkC;AACxD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,YAAY,GAAGA,CAAA,kBACnBH,OAAA;EAAKI,SAAS,EAAE,GAAGV,MAAM,CAACW,IAAI,IAAIX,MAAM,CAACY,YAAY,EAAG;EAAAC,QAAA,gBACtDP,OAAA;IAAKI,SAAS,EAAE,GAAGV,MAAM,CAACc,GAAG,IAAId,MAAM,CAACe,YAAY;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eAC9Db,OAAA;IAAKI,SAAS,EAAEV,MAAM,CAACoB,KAAM;IAAAP,QAAA,gBAC3BP,OAAA;MAAKI,SAAS,EAAE,GAAGV,MAAM,CAACqB,QAAQ,IAAIrB,MAAM,CAACsB,QAAQ;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/Db,OAAA;MAAKI,SAAS,EAAE,GAAGV,MAAM,CAACqB,QAAQ,IAAIrB,MAAM,CAACsB,QAAQ;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5D,CAAC,eACNb,OAAA;IAAKI,SAAS,EAAE,GAAGV,MAAM,CAACqB,QAAQ,IAAIrB,MAAM,CAACuB,QAAQ;EAAG;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5D,CACN;AAACK,EAAA,GATIf,YAAY;AAWlB,MAAMgB,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAMmC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC;IAAEsC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE3E,MAAMC,gBAAgB,GAAGA,CAACF,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IACtDF,eAAe,CAAC;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCE,UAAU,CAAC,MAAMJ,eAAe,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC;EAEDxC,SAAS,CAAC,MAAM;IACdI,GAAG,CAACuC,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvCpB,cAAc,CAAC,KAAK,CAAC;IACrBE,UAAU,CAAC,IAAI,CAAC;IAEhBmB,KAAK,CAAC,iCAAiC,CAAC,CACrCC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEE,IAAI,IAAK;MACd;MACApB,OAAO,CAACoB,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;MACzBvB,UAAU,CAAC,KAAK,CAAC;MACjBe,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;MACxC/C,GAAG,CAACqD,OAAO,CAAC,CAAC;IACf,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,IAAK;MACd1B,UAAU,CAAC,KAAK,CAAC;MACjBQ,gBAAgB,CACd,uDAAuD,EACvD,OACF,CAAC;MACDO,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,CAAC;IAEJ,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,gBAAgB,GAAG1D,WAAW,CACjC2D,IAAI,IAAK;IACRzB,QAAQ,CAAC,WAAWyB,IAAI,EAAE,CAAC;EAC7B,CAAC,EACD,CAACzB,QAAQ,CACX,CAAC;EAED,oBACE3B,OAAA,CAAAE,SAAA;IAAAK,QAAA,gBACEP,OAAA,CAACF,YAAY;MAACgC,OAAO,EAAEF,YAAY,CAACE,OAAQ;MAACC,IAAI,EAAEH,YAAY,CAACG;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvEQ,WAAW,gBACVrB,OAAA;MAAKI,SAAS,EAAC,eAAe;MAAAG,QAAA,eAC5BP,OAAA;QAAKI,SAAS,EAAC;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENb,OAAA;MAAKI,SAAS,EAAC,SAAS;MAAAG,QAAA,eACtBP,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAG,QAAA,gBACxBP,OAAA;UAAKI,SAAS,EAAC,SAAS;UAAAG,QAAA,gBACtBP,OAAA;YAAII,SAAS,EAAC,OAAO;YAAC,YAAS,SAAS;YAAC,kBAAe,IAAI;YAAAG,QAAA,eAC1DP,OAAA;cAAAO,QAAA,EAAQ;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACLb,OAAA;YACEI,SAAS,EAAC,SAAS;YACnB,YAAS,SAAS;YAClB,kBAAe;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACLb,OAAA;YAAG,YAAS,SAAS;YAAC,kBAAe,KAAK;YAAAO,QAAA,EAAC;UAG3C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNb,OAAA;UAAAO,QAAA,eACEP,OAAA;YAAKI,SAAS,EAAEV,MAAM,CAAC2D,WAAY;YAAA9C,QAAA,EAChCgB,OAAO,GACN+B,KAAK,CAACC,IAAI,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACrC3D,OAAA,CAACG,YAAY,MAAMwD,KAAK;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC5B,CAAC,GACAyC,KAAK,CAACM,OAAO,CAACnC,IAAI,CAAC,IAAIA,IAAI,CAAC+B,MAAM,GAAG,CAAC,GACxC/B,IAAI,CAACgC,GAAG,CAAEI,IAAI,IAAK;cAAA,IAAAC,WAAA;cACjB,MAAMhD,KAAK,GACT,CAAA+C,IAAI,aAAJA,IAAI,wBAAAC,WAAA,GAAJD,IAAI,CAAE/C,KAAK,cAAAgD,WAAA,uBAAXA,WAAA,CAAaN,MAAM,IAAG,EAAE,GACpBK,IAAI,CAAC/C,KAAK,CAACiD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,GAAG,KAAK,GACtCH,IAAI,CAAC/C,KAAK;cAChB,oBACEd,OAAA;gBAAKI,SAAS,EAAEV,MAAM,CAACW,IAAK;gBAAAE,QAAA,gBAC1BP,OAAA;kBAAKI,SAAS,EAAEV,MAAM,CAACc,GAAI;kBAAAD,QAAA,EACxB,CAAAsD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,OAAO,kBACZjE,OAAA;oBACEkE,GAAG,EAAEL,IAAI,CAACI,OAAQ;oBAClBE,GAAG,EAAEN,IAAI,CAAC/C,KAAK,IAAI;kBAAQ;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNb,OAAA;kBAAKI,SAAS,EAAEV,MAAM,CAACoB,KAAM;kBAAAP,QAAA,GAC1B,CAAAsD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,UAAU,kBACfpE,OAAA;oBAAAO,QAAA,EACG,IAAI8D,IAAI,CAACR,IAAI,CAACO,UAAU,CAAC,CAACE,kBAAkB,CAC3C,OACF;kBAAC;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CACP,EACAC,KAAK,iBAAId,OAAA;oBAAAO,QAAA,EAAKO;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGvB,CAAC,eAENb,OAAA;kBACEI,SAAS,EAAC,MAAM;kBAChBmE,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACU,IAAI,CAACT,IAAI,CAAE;kBAAA7C,QAAA,gBAE3CP,OAAA;oBAAAO,QAAA,EAAG;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChBb,OAAA;oBAAKkE,GAAG,EAAEtE,SAAU;oBAACuE,GAAG,EAAC,EAAE;oBAAC/D,SAAS,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA,GA5B0BgD,IAAI,CAACW,EAAE;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BpC,CAAC;YAEV,CAAC,CAAC,gBAEFb,OAAA;cAAAO,QAAA,EAAG;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACvB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACO,EAAA,CAjIID,MAAM;EAAA,QAIOtB,WAAW;AAAA;AAAA4E,GAAA,GAJxBtD,MAAM;AAmIZ,eAAeA,MAAM;AAAC,IAAAD,EAAA,EAAAuD,GAAA;AAAAC,YAAA,CAAAxD,EAAA;AAAAwD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}