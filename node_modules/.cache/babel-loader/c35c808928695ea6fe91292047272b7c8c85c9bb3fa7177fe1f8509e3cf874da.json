{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { a as elementParents, p as elementStyle, e as elementChildren, s as setCSSProperty, f as elementOuterSize, q as elementNextAll, r as elementPrevAll, j as getTranslate, t as animateCSSModeScroll, n as nextTick, u as showWarning, c as createElement, v as elementIsChildOf, d as now, w as extend, h as elementIndex, x as deleteProps } from './utils.mjs';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(\"\".concat(screenWidth, \"x\").concat(screenHeight)) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, \".\".concat(swiper.params.slideClass, \", swiper-slide\"));\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = \"\";\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = \"\".concat(slideSize, \"px\");\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = \"\".concat(swiper.virtualSize + spaceBetween, \"px\");\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = \"\".concat(swiper.virtualSize + spaceBetween, \"px\");\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = \"\".concat(spaceBetween, \"px\");\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', \"\".concat(-snapGrid[0], \"px\"));\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', \"\".concat(swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2, \"px\"));\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = \"\".concat(params.containerModifierClass, \"backface-hidden\");\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = \"\".concat(newHeight, \"px\");\n}\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, \".\".concat(params.slideClass).concat(selector, \", swiper-slide\").concat(selector))[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(\"[data-swiper-slide-index=\\\"\".concat(slideIndex, \"\\\"]\"));\n    } else {\n      activeSlide = getFilteredSlide(\"[data-swiper-slide-index=\\\"\".concat(activeIndex, \"\\\"]\"));\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, \".\".concat(params.slideClass, \", swiper-slide\"))[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, \".\".concat(params.slideClass, \", swiper-slide\"))[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? \"swiper-slide\" : \".\".concat(swiper.params.slideClass);\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(\".\".concat(swiper.params.lazyPreloaderClass));\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(\".\".concat(swiper.params.lazyPreloaderClass));\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(\".\".concat(swiper.params.lazyPreloaderClass));\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(\".\".concat(params.slideClass, \", swiper-slide\"));\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(\".\".concat(params.slideClass, \", swiper-slide\"))) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = \"translate3d(\".concat(x, \"px, \").concat(y, \"px, \").concat(z, \"px)\");\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = \"\".concat(duration, \"ms\");\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? \"0ms\" : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(\"transition\".concat(step));\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(\"slideResetTransition\".concat(step));\n      return;\n    }\n    swiper.emit(\"slideChangeTransition\".concat(step));\n    if (dir === 'next') {\n      swiper.emit(\"slideNextTransition\".concat(step));\n    } else {\n      swiper.emit(\"slidePrevTransition\".concat(step));\n    }\n  }\n}\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? \"swiper-slide\" : \".\".concat(params.slideClass);\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, \"\".concat(slideSelector, \"[data-swiper-slide-index=\\\"\").concat(realIndex, \"\\\"]\"))[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, \"\".concat(slideSelector, \"[data-swiper-slide-index=\\\"\").concat(realIndex, \"\\\"]\"))[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, \".\".concat(params.slideClass, \", swiper-slide\"));\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix(_objectSpread(_objectSpread({}, loopParams), {}, {\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        }));\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix(_objectSpread(_objectSpread({}, loopParams), {}, {\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      }));\n    }\n  }\n  swiper.emit('loopFix');\n}\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : \".\".concat(params.noSwipingClass);\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(\"\".concat(params.containerModifierClass, \"grid\"), \"\".concat(params.containerModifierClass, \"grid-column\"));\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(\"\".concat(params.containerModifierClass, \"grid\"));\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(\"\".concat(params.containerModifierClass, \"grid-column\"));\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(\"(min-width: \".concat(value, \"px)\")).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\nvar classes = {\n  addClasses,\n  removeClasses\n};\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, \".\".concat(params.slideClass, \", swiper-slide\"));\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, \".\".concat(params.slideClass, \", swiper-slide\"));\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(\"\".concat(swiper.params.containerModifierClass).concat(currentDirection));\n    swiper.el.classList.add(\"\".concat(swiper.params.containerModifierClass).concat(newDirection));\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(\"\".concat(swiper.params.containerModifierClass, \"rtl\"));\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(\"\".concat(swiper.params.containerModifierClass, \"rtl\"));\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return \".\".concat((swiper.params.wrapperClass || '').trim().split(' ').join('.'));\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, \".\".concat(swiper.params.slideClass)).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport { Swiper as S, defaults as d };", "map": {"version": 3, "names": ["a", "getWindow", "g", "getDocument", "elementParents", "p", "elementStyle", "e", "elementChildren", "s", "setCSSProperty", "f", "elementOuterSize", "q", "elementNextAll", "r", "elementPrevAll", "j", "getTranslate", "t", "animateCSSModeScroll", "n", "nextTick", "u", "showWarning", "c", "createElement", "v", "elementIsChildOf", "d", "now", "w", "extend", "h", "elementIndex", "x", "deleteProps", "support", "calcSupport", "window", "document", "smoothScroll", "documentElement", "style", "touch", "DocumentTouch", "getSupport", "deviceCached", "calcDevice", "_temp", "userAgent", "platform", "navigator", "ua", "device", "ios", "android", "screenWidth", "screen", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "indexOf", "concat", "os", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "split", "map", "num", "Number", "isWebView", "test", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "_ref", "swiper", "on", "emit", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "destroyed", "initialized", "createObserver", "ResizeObserver", "entries", "requestAnimationFrame", "newWidth", "newHeight", "for<PERSON>ach", "_ref2", "contentBoxSize", "contentRect", "target", "el", "inlineSize", "blockSize", "observe", "removeObserver", "cancelAnimationFrame", "unobserve", "orientationChangeHandler", "params", "resizeObserver", "addEventListener", "removeEventListener", "Observer", "extendParams", "observers", "attach", "options", "ObserverFunc", "MutationObserver", "WebkitMutationObserver", "mutations", "__preventObserver__", "length", "observerUpdate", "setTimeout", "attributes", "childList", "isElement", "characterData", "push", "init", "observeParents", "containerParents", "hostEl", "i", "observeSlideChildren", "wrapperEl", "destroy", "disconnect", "splice", "eventsEmitter", "events", "handler", "priority", "self", "eventsListeners", "method", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "arguments", "args", "Array", "_key", "apply", "onAny", "eventsAnyListeners", "offAny", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "context", "_len2", "_key2", "isArray", "slice", "unshift", "eventsArray", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "isNaN", "Object", "assign", "size", "updateSlides", "getDirectionPropertyValue", "node", "label", "parseFloat", "getPropertyValue", "getDirectionLabel", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "slideClass", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "call", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "replace", "virtualSize", "slideEl", "marginLeft", "marginRight", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "grid", "rows", "initSlides", "unsetSlides", "slideSize", "shouldResetSlideSize", "<PERSON><PERSON><PERSON><PERSON>iew", "breakpoints", "keys", "filter", "key", "slide", "updateSlide", "slideStyles", "getComputedStyle", "currentTransform", "transform", "currentWebKitTransform", "webkitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "offsetWidth", "Math", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "min", "slidesPerGroupSkip", "max", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "loop", "groups", "ceil", "slidesBefore", "slidesAfter", "groupSize", "_", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "classList", "contains", "maxBackfaceHiddenSlides", "add", "remove", "updateAutoHeight", "speed", "activeSlides", "setTransition", "getSlideByIndex", "getSlideIndexByData", "visibleSlides", "activeIndex", "offsetHeight", "minusOffset", "offsetLeft", "offsetTop", "swiperSlideOffset", "cssOverflowAdjustment", "toggleSlideClasses$1", "condition", "className", "updateSlidesProgress", "translate", "offsetCenter", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "slideVisibleClass", "slideFullyVisibleClass", "progress", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "toggleSlideClasses", "updateSlidesClasses", "getFilteredSlide", "selector", "activeSlide", "prevSlide", "nextSlide", "find", "column", "slideActiveClass", "slideNextClass", "slidePrevClass", "emitSlidesClasses", "processLazyPreloader", "imageEl", "slideSelector", "closest", "lazyEl", "querySelector", "lazyPreloaderClass", "shadowRoot", "unlazy", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "len", "slidesPerViewDynamic", "activeColumn", "preloadColumns", "from", "slideIndexLastInView", "rewind", "realIndex", "getActiveIndexByTranslate", "normalizeSlideIndex", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "getAttribute", "runCallbacksOnInit", "updateClickedSlide", "path", "pathEl", "matches", "slideFound", "clickedSlide", "clickedIndex", "undefined", "slideToClickedSlide", "update", "getSwiperTranslate", "axis", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "y", "z", "previousTranslate", "newProgress", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "targetPosition", "side", "scrollTo", "behavior", "onTranslateToWrapperTransitionEnd", "transitionEnd", "duration", "transitionDuration", "transitionDelay", "transitionEmit", "direction", "step", "dir", "transitionStart", "transition", "slideTo", "initial", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "isInitialVirtual", "scrollSnapType", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "onSlideToWrapperTransitionEnd", "slideToLoop", "indexAsNumber", "newIndex", "targetSlideIndex", "cols", "needLoopFix", "loopFix", "slideRealIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "increment", "loopPreventsSliding", "_clientLeft", "clientLeft", "slidePrev", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "freeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToIndex", "loopedSlides", "getSlideIndex", "loopCreate", "setAttribute", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slideBlankClass", "append", "loopAddBlankSlides", "slidesToAdd", "recalcSlides", "byMousewheel", "loopAdditionalSlides", "fill", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndex", "activeColIndexWithShift", "colIndexToPrepend", "swiperLoopMoveDOM", "prepend", "currentSlideTranslate", "newSlideTranslate", "diff", "touchEventsData", "startTranslate", "shift", "controller", "control", "loopParams", "_objectSpread", "constructor", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "touchEventsTarget", "cursor", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "host", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "innerWidth", "preventDefault", "onTouchStart", "originalEvent", "type", "pointerId", "targetTouches", "touchId", "identifier", "pageX", "touches", "pointerType", "targetEl", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "noSwipingSelector", "isTargetShadow", "noSwiping", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "currentY", "pageY", "startY", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "nodeName", "activeElement", "blur", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "onTouchMove", "id", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "previousX", "previousY", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "PI", "preventTouchMoveFromPointerMove", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "allowLoopFix", "evt", "CustomEvent", "bubbles", "detail", "bySwiperTouchMove", "dispatchEvent", "allowMomentumBounce", "loopFixed", "Date", "getTime", "_loopSwapReset", "loopSwapReset", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "isTouchEvent", "proceed", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "isNavButtonTarget", "navigation", "nextEl", "prevEl", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "clearTimeout", "resizeTimeout", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "scrollLeft", "scrollTop", "onLoad", "onDocumentTouchStart", "documentTouchHandlerProceeded", "touchAction", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "attachEvents", "bind", "detachEvents", "events$1", "isGridEnabled", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "emitContainerClasses", "prop", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "changeDirection", "isEnabled", "<PERSON><PERSON><PERSON>", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "value", "sort", "b", "matchMedia", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "classes", "wasLocked", "lastSlideRightEdge", "checkOverflow$1", "defaults", "swiperElementNodeName", "createElements", "eventsPrefix", "url", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "obj", "moduleParamName", "moduleParams", "auto", "prototypes", "extendedDefaults", "Swiper", "prototype", "toString", "querySelectorAll", "swipers", "newParams", "__swiper__", "modules", "__modules__", "mod", "swiperParams", "passedParams", "eventName", "velocity", "trunc", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "property", "setProgress", "current", "cls", "join", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "slideInView", "complete", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "element", "mounted", "parentNode", "toUpperCase", "getWrapperSelector", "trim", "getWrapper", "res", "slideSlots", "lazyElements", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "installModule", "use", "module", "m", "prototypeGroup", "protoMethod", "S"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/shared/swiper-core.mjs"], "sourcesContent": ["import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { a as elementParents, p as elementStyle, e as elementChildren, s as setCSSProperty, f as elementOuterSize, q as elementNextAll, r as elementPrevAll, j as getTranslate, t as animateCSSModeScroll, n as nextTick, u as showWarning, c as createElement, v as elementIsChildOf, d as now, w as extend, h as elementIndex, x as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n"], "mappings": ";AAAA,SAASA,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,WAAW,QAAQ,sBAAsB;AACvE,SAASH,CAAC,IAAII,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,WAAW,QAAQ,aAAa;AAEtW,IAAIC,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMuC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,OAAO;IACLsC,YAAY,EAAED,QAAQ,CAACE,eAAe,IAAIF,QAAQ,CAACE,eAAe,CAACC,KAAK,IAAI,gBAAgB,IAAIH,QAAQ,CAACE,eAAe,CAACC,KAAK;IAC9HC,KAAK,EAAE,CAAC,EAAE,cAAc,IAAIL,MAAM,IAAIA,MAAM,CAACM,aAAa,IAAIL,QAAQ,YAAYD,MAAM,CAACM,aAAa;EACxG,CAAC;AACH;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACT,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,CAAC,CAAC;EACzB;EACA,OAAOD,OAAO;AAChB;AAEA,IAAIU,YAAY;AAChB,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAMZ,OAAO,GAAGS,UAAU,CAAC,CAAC;EAC5B,MAAMP,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMkD,QAAQ,GAAGZ,MAAM,CAACa,SAAS,CAACD,QAAQ;EAC1C,MAAME,EAAE,GAAGH,SAAS,IAAIX,MAAM,CAACa,SAAS,CAACF,SAAS;EAClD,MAAMI,MAAM,GAAG;IACbC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,WAAW,GAAGlB,MAAM,CAACmB,MAAM,CAACC,KAAK;EACvC,MAAMC,YAAY,GAAGrB,MAAM,CAACmB,MAAM,CAACG,MAAM;EACzC,MAAML,OAAO,GAAGH,EAAE,CAACS,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;EACzD,IAAIC,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,sBAAsB,CAAC;EAC3C,MAAME,IAAI,GAAGX,EAAE,CAACS,KAAK,CAAC,yBAAyB,CAAC;EAChD,MAAMG,MAAM,GAAG,CAACF,IAAI,IAAIV,EAAE,CAACS,KAAK,CAAC,4BAA4B,CAAC;EAC9D,MAAMI,OAAO,GAAGf,QAAQ,KAAK,OAAO;EACpC,IAAIgB,KAAK,GAAGhB,QAAQ,KAAK,UAAU;;EAEnC;EACA,MAAMiB,WAAW,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACtK,IAAI,CAACL,IAAI,IAAII,KAAK,IAAI9B,OAAO,CAACO,KAAK,IAAIwB,WAAW,CAACC,OAAO,IAAAC,MAAA,CAAIb,WAAW,OAAAa,MAAA,CAAIV,YAAY,CAAE,CAAC,IAAI,CAAC,EAAE;IACjGG,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,qBAAqB,CAAC;IACtC,IAAI,CAACC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;IAClCI,KAAK,GAAG,KAAK;EACf;;EAEA;EACA,IAAIX,OAAO,IAAI,CAACU,OAAO,EAAE;IACvBZ,MAAM,CAACiB,EAAE,GAAG,SAAS;IACrBjB,MAAM,CAACE,OAAO,GAAG,IAAI;EACvB;EACA,IAAIO,IAAI,IAAIE,MAAM,IAAID,IAAI,EAAE;IAC1BV,MAAM,CAACiB,EAAE,GAAG,KAAK;IACjBjB,MAAM,CAACC,GAAG,GAAG,IAAI;EACnB;;EAEA;EACA,OAAOD,MAAM;AACf;AACA,SAASkB,SAASA,CAACC,SAAS,EAAE;EAC5B,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC,CAAC;EAChB;EACA,IAAI,CAAC1B,YAAY,EAAE;IACjBA,YAAY,GAAGC,UAAU,CAACyB,SAAS,CAAC;EACtC;EACA,OAAO1B,YAAY;AACrB;AAEA,IAAI2B,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMpC,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMqD,MAAM,GAAGkB,SAAS,CAAC,CAAC;EAC1B,IAAII,kBAAkB,GAAG,KAAK;EAC9B,SAASC,QAAQA,CAAA,EAAG;IAClB,MAAMxB,EAAE,GAAGd,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC4B,WAAW,CAAC,CAAC;IACnD,OAAOzB,EAAE,CAACgB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAIhB,EAAE,CAACgB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAIhB,EAAE,CAACgB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;EAC3F;EACA,IAAIQ,QAAQ,CAAC,CAAC,EAAE;IACd,MAAMxB,EAAE,GAAG0B,MAAM,CAACxC,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC;IAC7C,IAAIG,EAAE,CAAC2B,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC3B,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAG7B,EAAE,CAAC8B,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIC,MAAM,CAACD,GAAG,CAAC,CAAC;MAC/FT,kBAAkB,GAAGK,KAAK,GAAG,EAAE,IAAIA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAC;IAC9D;EACF;EACA,MAAMK,SAAS,GAAG,8CAA8C,CAACC,IAAI,CAACjD,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC;EACjG,MAAMuC,eAAe,GAAGZ,QAAQ,CAAC,CAAC;EAClC,MAAMa,SAAS,GAAGD,eAAe,IAAIF,SAAS,IAAIjC,MAAM,CAACC,GAAG;EAC5D,OAAO;IACLsB,QAAQ,EAAED,kBAAkB,IAAIa,eAAe;IAC/Cb,kBAAkB;IAClBc,SAAS;IACTH;EACF,CAAC;AACH;AACA,SAASI,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACjB,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,CAAC,CAAC;EACzB;EACA,OAAOD,OAAO;AAChB;AAEA,SAASkB,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI;IACFC,MAAM;IACNC,EAAE;IACFC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMtD,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,IAAIgG,QAAQ,GAAG,IAAI;EACnB,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACL,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDL,IAAI,CAAC,cAAc,CAAC;IACpBA,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC;EACD,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACR,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDJ,QAAQ,GAAG,IAAIM,cAAc,CAACC,OAAO,IAAI;MACvCN,cAAc,GAAG3D,MAAM,CAACkE,qBAAqB,CAAC,MAAM;QAClD,MAAM;UACJ9C,KAAK;UACLE;QACF,CAAC,GAAGiC,MAAM;QACV,IAAIY,QAAQ,GAAG/C,KAAK;QACpB,IAAIgD,SAAS,GAAG9C,MAAM;QACtB2C,OAAO,CAACI,OAAO,CAACC,KAAK,IAAI;UACvB,IAAI;YACFC,cAAc;YACdC,WAAW;YACXC;UACF,CAAC,GAAGH,KAAK;UACT,IAAIG,MAAM,IAAIA,MAAM,KAAKlB,MAAM,CAACmB,EAAE,EAAE;UACpCP,QAAQ,GAAGK,WAAW,GAAGA,WAAW,CAACpD,KAAK,GAAG,CAACmD,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEI,UAAU;UAC7FP,SAAS,GAAGI,WAAW,GAAGA,WAAW,CAAClD,MAAM,GAAG,CAACiD,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEK,SAAS;QAChG,CAAC,CAAC;QACF,IAAIT,QAAQ,KAAK/C,KAAK,IAAIgD,SAAS,KAAK9C,MAAM,EAAE;UAC9CsC,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,QAAQ,CAACmB,OAAO,CAACtB,MAAM,CAACmB,EAAE,CAAC;EAC7B,CAAC;EACD,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAInB,cAAc,EAAE;MAClB3D,MAAM,CAAC+E,oBAAoB,CAACpB,cAAc,CAAC;IAC7C;IACA,IAAID,QAAQ,IAAIA,QAAQ,CAACsB,SAAS,IAAIzB,MAAM,CAACmB,EAAE,EAAE;MAC/ChB,QAAQ,CAACsB,SAAS,CAACzB,MAAM,CAACmB,EAAE,CAAC;MAC7BhB,QAAQ,GAAG,IAAI;IACjB;EACF,CAAC;EACD,MAAMuB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC1B,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDL,IAAI,CAAC,mBAAmB,CAAC;EAC3B,CAAC;EACDD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAID,MAAM,CAAC2B,MAAM,CAACC,cAAc,IAAI,OAAOnF,MAAM,CAACgE,cAAc,KAAK,WAAW,EAAE;MAChFD,cAAc,CAAC,CAAC;MAChB;IACF;IACA/D,MAAM,CAACoF,gBAAgB,CAAC,QAAQ,EAAExB,aAAa,CAAC;IAChD5D,MAAM,CAACoF,gBAAgB,CAAC,mBAAmB,EAAEH,wBAAwB,CAAC;EACxE,CAAC,CAAC;EACFzB,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBsB,cAAc,CAAC,CAAC;IAChB9E,MAAM,CAACqF,mBAAmB,CAAC,QAAQ,EAAEzB,aAAa,CAAC;IACnD5D,MAAM,CAACqF,mBAAmB,CAAC,mBAAmB,EAAEJ,wBAAwB,CAAC;EAC3E,CAAC,CAAC;AACJ;AAEA,SAASK,QAAQA,CAAChC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNgC,YAAY;IACZ/B,EAAE;IACFC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMkC,SAAS,GAAG,EAAE;EACpB,MAAMxF,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAM+H,MAAM,GAAG,SAAAA,CAAUhB,MAAM,EAAEiB,OAAO,EAAE;IACxC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,MAAMC,YAAY,GAAG3F,MAAM,CAAC4F,gBAAgB,IAAI5F,MAAM,CAAC6F,sBAAsB;IAC7E,MAAMnC,QAAQ,GAAG,IAAIiC,YAAY,CAACG,SAAS,IAAI;MAC7C;MACA;MACA;MACA,IAAIvC,MAAM,CAACwC,mBAAmB,EAAE;MAChC,IAAID,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;QAC1BvC,IAAI,CAAC,gBAAgB,EAAEqC,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;MACA,MAAMG,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC/CxC,IAAI,CAAC,gBAAgB,EAAEqC,SAAS,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,IAAI9F,MAAM,CAACkE,qBAAqB,EAAE;QAChClE,MAAM,CAACkE,qBAAqB,CAAC+B,cAAc,CAAC;MAC9C,CAAC,MAAM;QACLjG,MAAM,CAACkG,UAAU,CAACD,cAAc,EAAE,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACFvC,QAAQ,CAACmB,OAAO,CAACJ,MAAM,EAAE;MACvB0B,UAAU,EAAE,OAAOT,OAAO,CAACS,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGT,OAAO,CAACS,UAAU;MACjFC,SAAS,EAAE7C,MAAM,CAAC8C,SAAS,IAAI,CAAC,OAAOX,OAAO,CAACU,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGV,OAAO,EAAEU,SAAS;MACpGE,aAAa,EAAE,OAAOZ,OAAO,CAACY,aAAa,KAAK,WAAW,GAAG,IAAI,GAAGZ,OAAO,CAACY;IAC/E,CAAC,CAAC;IACFd,SAAS,CAACe,IAAI,CAAC7C,QAAQ,CAAC;EAC1B,CAAC;EACD,MAAM8C,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAI,CAACjD,MAAM,CAAC2B,MAAM,CAACxB,QAAQ,EAAE;IAC7B,IAAIH,MAAM,CAAC2B,MAAM,CAACuB,cAAc,EAAE;MAChC,MAAMC,gBAAgB,GAAG7I,cAAc,CAAC0F,MAAM,CAACoD,MAAM,CAAC;MACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,CAACV,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;QACnDnB,MAAM,CAACiB,gBAAgB,CAACE,CAAC,CAAC,CAAC;MAC7B;IACF;IACA;IACAnB,MAAM,CAAClC,MAAM,CAACoD,MAAM,EAAE;MACpBP,SAAS,EAAE7C,MAAM,CAAC2B,MAAM,CAAC2B;IAC3B,CAAC,CAAC;;IAEF;IACApB,MAAM,CAAClC,MAAM,CAACuD,SAAS,EAAE;MACvBX,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EACD,MAAMY,OAAO,GAAGA,CAAA,KAAM;IACpBvB,SAAS,CAACnB,OAAO,CAACX,QAAQ,IAAI;MAC5BA,QAAQ,CAACsD,UAAU,CAAC,CAAC;IACvB,CAAC,CAAC;IACFxB,SAAS,CAACyB,MAAM,CAAC,CAAC,EAAEzB,SAAS,CAACQ,MAAM,CAAC;EACvC,CAAC;EACDT,YAAY,CAAC;IACX7B,QAAQ,EAAE,KAAK;IACf+C,cAAc,EAAE,KAAK;IACrBI,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACFrD,EAAE,CAAC,MAAM,EAAEgD,IAAI,CAAC;EAChBhD,EAAE,CAAC,SAAS,EAAEuD,OAAO,CAAC;AACxB;;AAEA;;AAEA,IAAIG,aAAa,GAAG;EAClB1D,EAAEA,CAAC2D,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC5B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAME,MAAM,GAAGH,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5CF,MAAM,CAACvE,KAAK,CAAC,GAAG,CAAC,CAACyB,OAAO,CAACoD,KAAK,IAAI;MACjC,IAAI,CAACH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAEH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,GAAG,EAAE;MAClEH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACD,MAAM,CAAC,CAACJ,OAAO,CAAC;IAC9C,CAAC,CAAC;IACF,OAAOE,IAAI;EACb,CAAC;EACDI,IAAIA,CAACP,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC9B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,SAASK,WAAWA,CAAA,EAAG;MACrBL,IAAI,CAACM,GAAG,CAACT,MAAM,EAAEQ,WAAW,CAAC;MAC7B,IAAIA,WAAW,CAACE,cAAc,EAAE;QAC9B,OAAOF,WAAW,CAACE,cAAc;MACnC;MACA,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAAC/B,MAAM,EAAEgC,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MACAd,OAAO,CAACe,KAAK,CAACb,IAAI,EAAEU,IAAI,CAAC;IAC3B;IACAL,WAAW,CAACE,cAAc,GAAGT,OAAO;IACpC,OAAOE,IAAI,CAAC9D,EAAE,CAAC2D,MAAM,EAAEQ,WAAW,EAAEN,QAAQ,CAAC;EAC/C,CAAC;EACDe,KAAKA,CAAChB,OAAO,EAAEC,QAAQ,EAAE;IACvB,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAME,MAAM,GAAGH,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5C,IAAIC,IAAI,CAACe,kBAAkB,CAACvG,OAAO,CAACsF,OAAO,CAAC,GAAG,CAAC,EAAE;MAChDE,IAAI,CAACe,kBAAkB,CAACb,MAAM,CAAC,CAACJ,OAAO,CAAC;IAC1C;IACA,OAAOE,IAAI;EACb,CAAC;EACDgB,MAAMA,CAAClB,OAAO,EAAE;IACd,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACe,kBAAkB,EAAE,OAAOf,IAAI;IACzC,MAAMiB,KAAK,GAAGjB,IAAI,CAACe,kBAAkB,CAACvG,OAAO,CAACsF,OAAO,CAAC;IACtD,IAAImB,KAAK,IAAI,CAAC,EAAE;MACdjB,IAAI,CAACe,kBAAkB,CAACpB,MAAM,CAACsB,KAAK,EAAE,CAAC,CAAC;IAC1C;IACA,OAAOjB,IAAI;EACb,CAAC;EACDM,GAAGA,CAACT,MAAM,EAAEC,OAAO,EAAE;IACnB,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtCH,MAAM,CAACvE,KAAK,CAAC,GAAG,CAAC,CAACyB,OAAO,CAACoD,KAAK,IAAI;MACjC,IAAI,OAAOL,OAAO,KAAK,WAAW,EAAE;QAClCE,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,GAAG,EAAE;MAClC,CAAC,MAAM,IAAIH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAE;QACtCH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACpD,OAAO,CAAC,CAACmE,YAAY,EAAED,KAAK,KAAK;UAC3D,IAAIC,YAAY,KAAKpB,OAAO,IAAIoB,YAAY,CAACX,cAAc,IAAIW,YAAY,CAACX,cAAc,KAAKT,OAAO,EAAE;YACtGE,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACR,MAAM,CAACsB,KAAK,EAAE,CAAC,CAAC;UAC9C;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOjB,IAAI;EACb,CAAC;EACD7D,IAAIA,CAAA,EAAG;IACL,MAAM6D,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACzD,SAAS,EAAE,OAAOyD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtC,IAAIH,MAAM;IACV,IAAIsB,IAAI;IACR,IAAIC,OAAO;IACX,KAAK,IAAIC,KAAK,GAAGZ,SAAS,CAAC/B,MAAM,EAAEgC,IAAI,GAAG,IAAIC,KAAK,CAACU,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FZ,IAAI,CAACY,KAAK,CAAC,GAAGb,SAAS,CAACa,KAAK,CAAC;IAChC;IACA,IAAI,OAAOZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIC,KAAK,CAACY,OAAO,CAACb,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACzDb,MAAM,GAAGa,IAAI,CAAC,CAAC,CAAC;MAChBS,IAAI,GAAGT,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEd,IAAI,CAAChC,MAAM,CAAC;MACjC0C,OAAO,GAAGpB,IAAI;IAChB,CAAC,MAAM;MACLH,MAAM,GAAGa,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM;MACvBsB,IAAI,GAAGT,IAAI,CAAC,CAAC,CAAC,CAACS,IAAI;MACnBC,OAAO,GAAGV,IAAI,CAAC,CAAC,CAAC,CAACU,OAAO,IAAIpB,IAAI;IACnC;IACAmB,IAAI,CAACM,OAAO,CAACL,OAAO,CAAC;IACrB,MAAMM,WAAW,GAAGf,KAAK,CAACY,OAAO,CAAC1B,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACvE,KAAK,CAAC,GAAG,CAAC;IACtEoG,WAAW,CAAC3E,OAAO,CAACoD,KAAK,IAAI;MAC3B,IAAIH,IAAI,CAACe,kBAAkB,IAAIf,IAAI,CAACe,kBAAkB,CAACrC,MAAM,EAAE;QAC7DsB,IAAI,CAACe,kBAAkB,CAAChE,OAAO,CAACmE,YAAY,IAAI;UAC9CA,YAAY,CAACL,KAAK,CAACO,OAAO,EAAE,CAACjB,KAAK,EAAE,GAAGgB,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC;MACJ;MACA,IAAInB,IAAI,CAACC,eAAe,IAAID,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAE;QACvDH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACpD,OAAO,CAACmE,YAAY,IAAI;UAClDA,YAAY,CAACL,KAAK,CAACO,OAAO,EAAED,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOnB,IAAI;EACb;AACF,CAAC;AAED,SAAS2B,UAAUA,CAAA,EAAG;EACpB,MAAM1F,MAAM,GAAG,IAAI;EACnB,IAAInC,KAAK;EACT,IAAIE,MAAM;EACV,MAAMoD,EAAE,GAAGnB,MAAM,CAACmB,EAAE;EACpB,IAAI,OAAOnB,MAAM,CAAC2B,MAAM,CAAC9D,KAAK,KAAK,WAAW,IAAImC,MAAM,CAAC2B,MAAM,CAAC9D,KAAK,KAAK,IAAI,EAAE;IAC9EA,KAAK,GAAGmC,MAAM,CAAC2B,MAAM,CAAC9D,KAAK;EAC7B,CAAC,MAAM;IACLA,KAAK,GAAGsD,EAAE,CAACwE,WAAW;EACxB;EACA,IAAI,OAAO3F,MAAM,CAAC2B,MAAM,CAAC5D,MAAM,KAAK,WAAW,IAAIiC,MAAM,CAAC2B,MAAM,CAAC5D,MAAM,KAAK,IAAI,EAAE;IAChFA,MAAM,GAAGiC,MAAM,CAAC2B,MAAM,CAAC5D,MAAM;EAC/B,CAAC,MAAM;IACLA,MAAM,GAAGoD,EAAE,CAACyE,YAAY;EAC1B;EACA,IAAI/H,KAAK,KAAK,CAAC,IAAImC,MAAM,CAAC6F,YAAY,CAAC,CAAC,IAAI9H,MAAM,KAAK,CAAC,IAAIiC,MAAM,CAAC8F,UAAU,CAAC,CAAC,EAAE;IAC/E;EACF;;EAEA;EACAjI,KAAK,GAAGA,KAAK,GAAGkI,QAAQ,CAACvL,YAAY,CAAC2G,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG4E,QAAQ,CAACvL,YAAY,CAAC2G,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC1HpD,MAAM,GAAGA,MAAM,GAAGgI,QAAQ,CAACvL,YAAY,CAAC2G,EAAE,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG4E,QAAQ,CAACvL,YAAY,CAAC2G,EAAE,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC5H,IAAI3B,MAAM,CAACwG,KAAK,CAACnI,KAAK,CAAC,EAAEA,KAAK,GAAG,CAAC;EAClC,IAAI2B,MAAM,CAACwG,KAAK,CAACjI,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC;EACpCkI,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;IACpBnC,KAAK;IACLE,MAAM;IACNoI,IAAI,EAAEnG,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGhI,KAAK,GAAGE;EACxC,CAAC,CAAC;AACJ;AAEA,SAASqI,YAAYA,CAAA,EAAG;EACtB,MAAMpG,MAAM,GAAG,IAAI;EACnB,SAASqG,yBAAyBA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOC,UAAU,CAACF,IAAI,CAACG,gBAAgB,CAACzG,MAAM,CAAC0G,iBAAiB,CAACH,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;EAChF;EACA,MAAM5E,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAM;IACJ4B,SAAS;IACToD,QAAQ;IACRR,IAAI,EAAES,UAAU;IAChBC,YAAY,EAAEC,GAAG;IACjBC;EACF,CAAC,GAAG/G,MAAM;EACV,MAAMgH,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO;EAC1D,MAAMC,oBAAoB,GAAGH,SAAS,GAAGhH,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAGzC,MAAM,CAACoH,MAAM,CAAC3E,MAAM;EAC5F,MAAM2E,MAAM,GAAG1M,eAAe,CAACiM,QAAQ,MAAAnI,MAAA,CAAMwB,MAAM,CAAC2B,MAAM,CAAC0F,UAAU,mBAAgB,CAAC;EACtF,MAAMC,YAAY,GAAGN,SAAS,GAAGhH,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAG2E,MAAM,CAAC3E,MAAM;EAC7E,IAAI8E,QAAQ,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIC,YAAY,GAAG/F,MAAM,CAACgG,kBAAkB;EAC5C,IAAI,OAAOD,YAAY,KAAK,UAAU,EAAE;IACtCA,YAAY,GAAG/F,MAAM,CAACgG,kBAAkB,CAACC,IAAI,CAAC5H,MAAM,CAAC;EACvD;EACA,IAAI6H,WAAW,GAAGlG,MAAM,CAACmG,iBAAiB;EAC1C,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;IACrCA,WAAW,GAAGlG,MAAM,CAACmG,iBAAiB,CAACF,IAAI,CAAC5H,MAAM,CAAC;EACrD;EACA,MAAM+H,sBAAsB,GAAG/H,MAAM,CAACuH,QAAQ,CAAC9E,MAAM;EACrD,MAAMuF,wBAAwB,GAAGhI,MAAM,CAACwH,UAAU,CAAC/E,MAAM;EACzD,IAAIwF,YAAY,GAAGtG,MAAM,CAACsG,YAAY;EACtC,IAAIC,aAAa,GAAG,CAACR,YAAY;EACjC,IAAIS,aAAa,GAAG,CAAC;EACrB,IAAInD,KAAK,GAAG,CAAC;EACb,IAAI,OAAO4B,UAAU,KAAK,WAAW,EAAE;IACrC;EACF;EACA,IAAI,OAAOqB,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAAC1J,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtE0J,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGxB,UAAU;EAC7E,CAAC,MAAM,IAAI,OAAOqB,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAAC;EACzC;EACAjI,MAAM,CAACqI,WAAW,GAAG,CAACJ,YAAY;;EAElC;EACAb,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;IACxB,IAAIxB,GAAG,EAAE;MACPwB,OAAO,CAACzL,KAAK,CAAC0L,UAAU,GAAG,EAAE;IAC/B,CAAC,MAAM;MACLD,OAAO,CAACzL,KAAK,CAAC2L,WAAW,GAAG,EAAE;IAChC;IACAF,OAAO,CAACzL,KAAK,CAAC4L,YAAY,GAAG,EAAE;IAC/BH,OAAO,CAACzL,KAAK,CAAC6L,SAAS,GAAG,EAAE;EAC9B,CAAC,CAAC;;EAEF;EACA,IAAI/G,MAAM,CAACgH,cAAc,IAAIhH,MAAM,CAACiH,OAAO,EAAE;IAC3ChO,cAAc,CAAC2I,SAAS,EAAE,iCAAiC,EAAE,EAAE,CAAC;IAChE3I,cAAc,CAAC2I,SAAS,EAAE,gCAAgC,EAAE,EAAE,CAAC;EACjE;EACA,MAAMsF,WAAW,GAAGlH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC,IAAI/I,MAAM,CAAC8I,IAAI;EACtE,IAAID,WAAW,EAAE;IACf7I,MAAM,CAAC8I,IAAI,CAACE,UAAU,CAAC5B,MAAM,CAAC;EAChC,CAAC,MAAM,IAAIpH,MAAM,CAAC8I,IAAI,EAAE;IACtB9I,MAAM,CAAC8I,IAAI,CAACG,WAAW,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAIC,SAAS;EACb,MAAMC,oBAAoB,GAAGxH,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIzH,MAAM,CAAC0H,WAAW,IAAIpD,MAAM,CAACqD,IAAI,CAAC3H,MAAM,CAAC0H,WAAW,CAAC,CAACE,MAAM,CAACC,GAAG,IAAI;IAClI,OAAO,OAAO7H,MAAM,CAAC0H,WAAW,CAACG,GAAG,CAAC,CAACJ,aAAa,KAAK,WAAW;EACrE,CAAC,CAAC,CAAC3G,MAAM,GAAG,CAAC;EACb,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,YAAY,EAAEjE,CAAC,IAAI,CAAC,EAAE;IACxC6F,SAAS,GAAG,CAAC;IACb,IAAIO,KAAK;IACT,IAAIrC,MAAM,CAAC/D,CAAC,CAAC,EAAEoG,KAAK,GAAGrC,MAAM,CAAC/D,CAAC,CAAC;IAChC,IAAIwF,WAAW,EAAE;MACf7I,MAAM,CAAC8I,IAAI,CAACY,WAAW,CAACrG,CAAC,EAAEoG,KAAK,EAAErC,MAAM,CAAC;IAC3C;IACA,IAAIA,MAAM,CAAC/D,CAAC,CAAC,IAAI7I,YAAY,CAACiP,KAAK,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE,SAAS,CAAC;;IAEtE,IAAI9H,MAAM,CAACyH,aAAa,KAAK,MAAM,EAAE;MACnC,IAAID,oBAAoB,EAAE;QACxB/B,MAAM,CAAC/D,CAAC,CAAC,CAACxG,KAAK,CAACmD,MAAM,CAAC0G,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK;MACzD;MACA,MAAMiD,WAAW,GAAGC,gBAAgB,CAACH,KAAK,CAAC;MAC3C,MAAMI,gBAAgB,GAAGJ,KAAK,CAAC5M,KAAK,CAACiN,SAAS;MAC9C,MAAMC,sBAAsB,GAAGN,KAAK,CAAC5M,KAAK,CAACmN,eAAe;MAC1D,IAAIH,gBAAgB,EAAE;QACpBJ,KAAK,CAAC5M,KAAK,CAACiN,SAAS,GAAG,MAAM;MAChC;MACA,IAAIC,sBAAsB,EAAE;QAC1BN,KAAK,CAAC5M,KAAK,CAACmN,eAAe,GAAG,MAAM;MACtC;MACA,IAAIrI,MAAM,CAACsI,YAAY,EAAE;QACvBf,SAAS,GAAGlJ,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAG/K,gBAAgB,CAAC2O,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG3O,gBAAgB,CAAC2O,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;MACtH,CAAC,MAAM;QACL;QACA,MAAM5L,KAAK,GAAGwI,yBAAyB,CAACsD,WAAW,EAAE,OAAO,CAAC;QAC7D,MAAMO,WAAW,GAAG7D,yBAAyB,CAACsD,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMQ,YAAY,GAAG9D,yBAAyB,CAACsD,WAAW,EAAE,eAAe,CAAC;QAC5E,MAAMpB,UAAU,GAAGlC,yBAAyB,CAACsD,WAAW,EAAE,aAAa,CAAC;QACxE,MAAMnB,WAAW,GAAGnC,yBAAyB,CAACsD,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMS,SAAS,GAAGT,WAAW,CAAClD,gBAAgB,CAAC,YAAY,CAAC;QAC5D,IAAI2D,SAAS,IAAIA,SAAS,KAAK,YAAY,EAAE;UAC3ClB,SAAS,GAAGrL,KAAK,GAAG0K,UAAU,GAAGC,WAAW;QAC9C,CAAC,MAAM;UACL,MAAM;YACJ7C,WAAW;YACX0E;UACF,CAAC,GAAGZ,KAAK;UACTP,SAAS,GAAGrL,KAAK,GAAGqM,WAAW,GAAGC,YAAY,GAAG5B,UAAU,GAAGC,WAAW,IAAI6B,WAAW,GAAG1E,WAAW,CAAC;QACzG;MACF;MACA,IAAIkE,gBAAgB,EAAE;QACpBJ,KAAK,CAAC5M,KAAK,CAACiN,SAAS,GAAGD,gBAAgB;MAC1C;MACA,IAAIE,sBAAsB,EAAE;QAC1BN,KAAK,CAAC5M,KAAK,CAACmN,eAAe,GAAGD,sBAAsB;MACtD;MACA,IAAIpI,MAAM,CAACsI,YAAY,EAAEf,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAACrB,SAAS,CAAC;IAC5D,CAAC,MAAM;MACLA,SAAS,GAAG,CAACtC,UAAU,GAAG,CAACjF,MAAM,CAACyH,aAAa,GAAG,CAAC,IAAInB,YAAY,IAAItG,MAAM,CAACyH,aAAa;MAC3F,IAAIzH,MAAM,CAACsI,YAAY,EAAEf,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAACrB,SAAS,CAAC;MAC1D,IAAI9B,MAAM,CAAC/D,CAAC,CAAC,EAAE;QACb+D,MAAM,CAAC/D,CAAC,CAAC,CAACxG,KAAK,CAACmD,MAAM,CAAC0G,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAAlI,MAAA,CAAM0K,SAAS,OAAI;MACvE;IACF;IACA,IAAI9B,MAAM,CAAC/D,CAAC,CAAC,EAAE;MACb+D,MAAM,CAAC/D,CAAC,CAAC,CAACmH,eAAe,GAAGtB,SAAS;IACvC;IACAzB,eAAe,CAACzE,IAAI,CAACkG,SAAS,CAAC;IAC/B,IAAIvH,MAAM,CAACgH,cAAc,EAAE;MACzBT,aAAa,GAAGA,aAAa,GAAGgB,SAAS,GAAG,CAAC,GAAGf,aAAa,GAAG,CAAC,GAAGF,YAAY;MAChF,IAAIE,aAAa,KAAK,CAAC,IAAI9E,CAAC,KAAK,CAAC,EAAE6E,aAAa,GAAGA,aAAa,GAAGtB,UAAU,GAAG,CAAC,GAAGqB,YAAY;MACjG,IAAI5E,CAAC,KAAK,CAAC,EAAE6E,aAAa,GAAGA,aAAa,GAAGtB,UAAU,GAAG,CAAC,GAAGqB,YAAY;MAC1E,IAAIqC,IAAI,CAACG,GAAG,CAACvC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,EAAEA,aAAa,GAAG,CAAC;MACzD,IAAIvG,MAAM,CAACsI,YAAY,EAAE/B,aAAa,GAAGoC,IAAI,CAACC,KAAK,CAACrC,aAAa,CAAC;MAClE,IAAIlD,KAAK,GAAGrD,MAAM,CAAC+I,cAAc,KAAK,CAAC,EAAEnD,QAAQ,CAACvE,IAAI,CAACkF,aAAa,CAAC;MACrEV,UAAU,CAACxE,IAAI,CAACkF,aAAa,CAAC;IAChC,CAAC,MAAM;MACL,IAAIvG,MAAM,CAACsI,YAAY,EAAE/B,aAAa,GAAGoC,IAAI,CAACC,KAAK,CAACrC,aAAa,CAAC;MAClE,IAAI,CAAClD,KAAK,GAAGsF,IAAI,CAACK,GAAG,CAAC3K,MAAM,CAAC2B,MAAM,CAACiJ,kBAAkB,EAAE5F,KAAK,CAAC,IAAIhF,MAAM,CAAC2B,MAAM,CAAC+I,cAAc,KAAK,CAAC,EAAEnD,QAAQ,CAACvE,IAAI,CAACkF,aAAa,CAAC;MAClIV,UAAU,CAACxE,IAAI,CAACkF,aAAa,CAAC;MAC9BA,aAAa,GAAGA,aAAa,GAAGgB,SAAS,GAAGjB,YAAY;IAC1D;IACAjI,MAAM,CAACqI,WAAW,IAAIa,SAAS,GAAGjB,YAAY;IAC9CE,aAAa,GAAGe,SAAS;IACzBlE,KAAK,IAAI,CAAC;EACZ;EACAhF,MAAM,CAACqI,WAAW,GAAGiC,IAAI,CAACO,GAAG,CAAC7K,MAAM,CAACqI,WAAW,EAAEzB,UAAU,CAAC,GAAGiB,WAAW;EAC3E,IAAIf,GAAG,IAAIC,QAAQ,KAAKpF,MAAM,CAACmJ,MAAM,KAAK,OAAO,IAAInJ,MAAM,CAACmJ,MAAM,KAAK,WAAW,CAAC,EAAE;IACnFvH,SAAS,CAAC1G,KAAK,CAACgB,KAAK,MAAAW,MAAA,CAAMwB,MAAM,CAACqI,WAAW,GAAGJ,YAAY,OAAI;EAClE;EACA,IAAItG,MAAM,CAACoJ,cAAc,EAAE;IACzBxH,SAAS,CAAC1G,KAAK,CAACmD,MAAM,CAAC0G,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAAlI,MAAA,CAAMwB,MAAM,CAACqI,WAAW,GAAGJ,YAAY,OAAI;EAC/F;EACA,IAAIY,WAAW,EAAE;IACf7I,MAAM,CAAC8I,IAAI,CAACkC,iBAAiB,CAAC9B,SAAS,EAAE3B,QAAQ,CAAC;EACpD;;EAEA;EACA,IAAI,CAAC5F,MAAM,CAACgH,cAAc,EAAE;IAC1B,MAAMsC,aAAa,GAAG,EAAE;IACxB,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,QAAQ,CAAC9E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;MAC3C,IAAI6H,cAAc,GAAG3D,QAAQ,CAAClE,CAAC,CAAC;MAChC,IAAI1B,MAAM,CAACsI,YAAY,EAAEiB,cAAc,GAAGZ,IAAI,CAACC,KAAK,CAACW,cAAc,CAAC;MACpE,IAAI3D,QAAQ,CAAClE,CAAC,CAAC,IAAIrD,MAAM,CAACqI,WAAW,GAAGzB,UAAU,EAAE;QAClDqE,aAAa,CAACjI,IAAI,CAACkI,cAAc,CAAC;MACpC;IACF;IACA3D,QAAQ,GAAG0D,aAAa;IACxB,IAAIX,IAAI,CAACC,KAAK,CAACvK,MAAM,CAACqI,WAAW,GAAGzB,UAAU,CAAC,GAAG0D,IAAI,CAACC,KAAK,CAAChD,QAAQ,CAACA,QAAQ,CAAC9E,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC/F8E,QAAQ,CAACvE,IAAI,CAAChD,MAAM,CAACqI,WAAW,GAAGzB,UAAU,CAAC;IAChD;EACF;EACA,IAAII,SAAS,IAAIrF,MAAM,CAACwJ,IAAI,EAAE;IAC5B,MAAMhF,IAAI,GAAGsB,eAAe,CAAC,CAAC,CAAC,GAAGQ,YAAY;IAC9C,IAAItG,MAAM,CAAC+I,cAAc,GAAG,CAAC,EAAE;MAC7B,MAAMU,MAAM,GAAGd,IAAI,CAACe,IAAI,CAAC,CAACrL,MAAM,CAACiH,OAAO,CAACqE,YAAY,GAAGtL,MAAM,CAACiH,OAAO,CAACsE,WAAW,IAAI5J,MAAM,CAAC+I,cAAc,CAAC;MAC5G,MAAMc,SAAS,GAAGrF,IAAI,GAAGxE,MAAM,CAAC+I,cAAc;MAC9C,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+H,MAAM,EAAE/H,CAAC,IAAI,CAAC,EAAE;QAClCkE,QAAQ,CAACvE,IAAI,CAACuE,QAAQ,CAACA,QAAQ,CAAC9E,MAAM,GAAG,CAAC,CAAC,GAAG+I,SAAS,CAAC;MAC1D;IACF;IACA,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,MAAM,CAACiH,OAAO,CAACqE,YAAY,GAAGtL,MAAM,CAACiH,OAAO,CAACsE,WAAW,EAAElI,CAAC,IAAI,CAAC,EAAE;MACpF,IAAI1B,MAAM,CAAC+I,cAAc,KAAK,CAAC,EAAE;QAC/BnD,QAAQ,CAACvE,IAAI,CAACuE,QAAQ,CAACA,QAAQ,CAAC9E,MAAM,GAAG,CAAC,CAAC,GAAG0D,IAAI,CAAC;MACrD;MACAqB,UAAU,CAACxE,IAAI,CAACwE,UAAU,CAACA,UAAU,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAG0D,IAAI,CAAC;MACzDnG,MAAM,CAACqI,WAAW,IAAIlC,IAAI;IAC5B;EACF;EACA,IAAIoB,QAAQ,CAAC9E,MAAM,KAAK,CAAC,EAAE8E,QAAQ,GAAG,CAAC,CAAC,CAAC;EACzC,IAAIU,YAAY,KAAK,CAAC,EAAE;IACtB,MAAMuB,GAAG,GAAGxJ,MAAM,CAAC6F,YAAY,CAAC,CAAC,IAAIiB,GAAG,GAAG,YAAY,GAAG9G,MAAM,CAAC0G,iBAAiB,CAAC,aAAa,CAAC;IACjGU,MAAM,CAACmC,MAAM,CAAC,CAACkC,CAAC,EAAEC,UAAU,KAAK;MAC/B,IAAI,CAAC/J,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAACwJ,IAAI,EAAE,OAAO,IAAI;MAC/C,IAAIO,UAAU,KAAKtE,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC3B,OAAO,CAACwH,OAAO,IAAI;MACpBA,OAAO,CAACzL,KAAK,CAAC2M,GAAG,CAAC,MAAAhL,MAAA,CAAMyJ,YAAY,OAAI;IAC1C,CAAC,CAAC;EACJ;EACA,IAAItG,MAAM,CAACgH,cAAc,IAAIhH,MAAM,CAACgK,oBAAoB,EAAE;IACxD,IAAIC,aAAa,GAAG,CAAC;IACrBnE,eAAe,CAAC3G,OAAO,CAAC+K,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAI5D,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACF2D,aAAa,IAAI3D,YAAY;IAC7B,MAAM6D,OAAO,GAAGF,aAAa,GAAGhF,UAAU,GAAGgF,aAAa,GAAGhF,UAAU,GAAG,CAAC;IAC3EW,QAAQ,GAAGA,QAAQ,CAACjI,GAAG,CAACyM,IAAI,IAAI;MAC9B,IAAIA,IAAI,IAAI,CAAC,EAAE,OAAO,CAACrE,YAAY;MACnC,IAAIqE,IAAI,GAAGD,OAAO,EAAE,OAAOA,OAAO,GAAGjE,WAAW;MAChD,OAAOkE,IAAI;IACb,CAAC,CAAC;EACJ;EACA,IAAIpK,MAAM,CAACqK,wBAAwB,EAAE;IACnC,IAAIJ,aAAa,GAAG,CAAC;IACrBnE,eAAe,CAAC3G,OAAO,CAAC+K,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAI5D,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACF2D,aAAa,IAAI3D,YAAY;IAC7B,MAAMgE,UAAU,GAAG,CAACtK,MAAM,CAACgG,kBAAkB,IAAI,CAAC,KAAKhG,MAAM,CAACmG,iBAAiB,IAAI,CAAC,CAAC;IACrF,IAAI8D,aAAa,GAAGK,UAAU,GAAGrF,UAAU,EAAE;MAC3C,MAAMsF,eAAe,GAAG,CAACtF,UAAU,GAAGgF,aAAa,GAAGK,UAAU,IAAI,CAAC;MACrE1E,QAAQ,CAACzG,OAAO,CAAC,CAACiL,IAAI,EAAEI,SAAS,KAAK;QACpC5E,QAAQ,CAAC4E,SAAS,CAAC,GAAGJ,IAAI,GAAGG,eAAe;MAC9C,CAAC,CAAC;MACF1E,UAAU,CAAC1G,OAAO,CAAC,CAACiL,IAAI,EAAEI,SAAS,KAAK;QACtC3E,UAAU,CAAC2E,SAAS,CAAC,GAAGJ,IAAI,GAAGG,eAAe;MAChD,CAAC,CAAC;IACJ;EACF;EACAjG,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;IACpBoH,MAAM;IACNG,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI9F,MAAM,CAACgH,cAAc,IAAIhH,MAAM,CAACiH,OAAO,IAAI,CAACjH,MAAM,CAACgK,oBAAoB,EAAE;IAC3E/Q,cAAc,CAAC2I,SAAS,EAAE,iCAAiC,KAAA/E,MAAA,CAAK,CAAC+I,QAAQ,CAAC,CAAC,CAAC,OAAI,CAAC;IACjF3M,cAAc,CAAC2I,SAAS,EAAE,gCAAgC,KAAA/E,MAAA,CAAKwB,MAAM,CAACmG,IAAI,GAAG,CAAC,GAAGsB,eAAe,CAACA,eAAe,CAAChF,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,OAAI,CAAC;IACrI,MAAM2J,aAAa,GAAG,CAACpM,MAAM,CAACuH,QAAQ,CAAC,CAAC,CAAC;IACzC,MAAM8E,eAAe,GAAG,CAACrM,MAAM,CAACwH,UAAU,CAAC,CAAC,CAAC;IAC7CxH,MAAM,CAACuH,QAAQ,GAAGvH,MAAM,CAACuH,QAAQ,CAACjI,GAAG,CAACzD,CAAC,IAAIA,CAAC,GAAGuQ,aAAa,CAAC;IAC7DpM,MAAM,CAACwH,UAAU,GAAGxH,MAAM,CAACwH,UAAU,CAAClI,GAAG,CAACzD,CAAC,IAAIA,CAAC,GAAGwQ,eAAe,CAAC;EACrE;EACA,IAAI/E,YAAY,KAAKH,oBAAoB,EAAE;IACzCnH,MAAM,CAACE,IAAI,CAAC,oBAAoB,CAAC;EACnC;EACA,IAAIqH,QAAQ,CAAC9E,MAAM,KAAKsF,sBAAsB,EAAE;IAC9C,IAAI/H,MAAM,CAAC2B,MAAM,CAAC2K,aAAa,EAAEtM,MAAM,CAACuM,aAAa,CAAC,CAAC;IACvDvM,MAAM,CAACE,IAAI,CAAC,sBAAsB,CAAC;EACrC;EACA,IAAIsH,UAAU,CAAC/E,MAAM,KAAKuF,wBAAwB,EAAE;IAClDhI,MAAM,CAACE,IAAI,CAAC,wBAAwB,CAAC;EACvC;EACA,IAAIyB,MAAM,CAAC6K,mBAAmB,EAAE;IAC9BxM,MAAM,CAACyM,kBAAkB,CAAC,CAAC;EAC7B;EACAzM,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;EAC5B,IAAI,CAAC8G,SAAS,IAAI,CAACrF,MAAM,CAACiH,OAAO,KAAKjH,MAAM,CAACmJ,MAAM,KAAK,OAAO,IAAInJ,MAAM,CAACmJ,MAAM,KAAK,MAAM,CAAC,EAAE;IAC5F,MAAM4B,mBAAmB,MAAAlO,MAAA,CAAMmD,MAAM,CAACgL,sBAAsB,oBAAiB;IAC7E,MAAMC,0BAA0B,GAAG5M,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACC,QAAQ,CAACJ,mBAAmB,CAAC;IACpF,IAAIpF,YAAY,IAAI3F,MAAM,CAACoL,uBAAuB,EAAE;MAClD,IAAI,CAACH,0BAA0B,EAAE5M,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACG,GAAG,CAACN,mBAAmB,CAAC;IAC/E,CAAC,MAAM,IAAIE,0BAA0B,EAAE;MACrC5M,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACI,MAAM,CAACP,mBAAmB,CAAC;IACjD;EACF;AACF;AAEA,SAASQ,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMnN,MAAM,GAAG,IAAI;EACnB,MAAMoN,YAAY,GAAG,EAAE;EACvB,MAAMpG,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO;EACjE,IAAIrG,SAAS,GAAG,CAAC;EACjB,IAAIwC,CAAC;EACL,IAAI,OAAO8J,KAAK,KAAK,QAAQ,EAAE;IAC7BnN,MAAM,CAACqN,aAAa,CAACF,KAAK,CAAC;EAC7B,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,EAAE;IACzBnN,MAAM,CAACqN,aAAa,CAACrN,MAAM,CAAC2B,MAAM,CAACwL,KAAK,CAAC;EAC3C;EACA,MAAMG,eAAe,GAAGtI,KAAK,IAAI;IAC/B,IAAIgC,SAAS,EAAE;MACb,OAAOhH,MAAM,CAACoH,MAAM,CAACpH,MAAM,CAACuN,mBAAmB,CAACvI,KAAK,CAAC,CAAC;IACzD;IACA,OAAOhF,MAAM,CAACoH,MAAM,CAACpC,KAAK,CAAC;EAC7B,CAAC;EACD;EACA,IAAIhF,MAAM,CAAC2B,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIpJ,MAAM,CAAC2B,MAAM,CAACyH,aAAa,GAAG,CAAC,EAAE;IAC7E,IAAIpJ,MAAM,CAAC2B,MAAM,CAACgH,cAAc,EAAE;MAChC,CAAC3I,MAAM,CAACwN,aAAa,IAAI,EAAE,EAAE1M,OAAO,CAAC2I,KAAK,IAAI;QAC5C2D,YAAY,CAACpK,IAAI,CAACyG,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAKpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiH,IAAI,CAACe,IAAI,CAACrL,MAAM,CAAC2B,MAAM,CAACyH,aAAa,CAAC,EAAE/F,CAAC,IAAI,CAAC,EAAE;QAC9D,MAAM2B,KAAK,GAAGhF,MAAM,CAACyN,WAAW,GAAGpK,CAAC;QACpC,IAAI2B,KAAK,GAAGhF,MAAM,CAACoH,MAAM,CAAC3E,MAAM,IAAI,CAACuE,SAAS,EAAE;QAChDoG,YAAY,CAACpK,IAAI,CAACsK,eAAe,CAACtI,KAAK,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,MAAM;IACLoI,YAAY,CAACpK,IAAI,CAACsK,eAAe,CAACtN,MAAM,CAACyN,WAAW,CAAC,CAAC;EACxD;;EAEA;EACA,KAAKpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+J,YAAY,CAAC3K,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAI,OAAO+J,YAAY,CAAC/J,CAAC,CAAC,KAAK,WAAW,EAAE;MAC1C,MAAMtF,MAAM,GAAGqP,YAAY,CAAC/J,CAAC,CAAC,CAACqK,YAAY;MAC3C7M,SAAS,GAAG9C,MAAM,GAAG8C,SAAS,GAAG9C,MAAM,GAAG8C,SAAS;IACrD;EACF;;EAEA;EACA,IAAIA,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAEb,MAAM,CAACuD,SAAS,CAAC1G,KAAK,CAACkB,MAAM,MAAAS,MAAA,CAAMqC,SAAS,OAAI;AACpF;AAEA,SAAS4L,kBAAkBA,CAAA,EAAG;EAC5B,MAAMzM,MAAM,GAAG,IAAI;EACnB,MAAMoH,MAAM,GAAGpH,MAAM,CAACoH,MAAM;EAC5B;EACA,MAAMuG,WAAW,GAAG3N,MAAM,CAAC8C,SAAS,GAAG9C,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAG7F,MAAM,CAACuD,SAAS,CAACqK,UAAU,GAAG5N,MAAM,CAACuD,SAAS,CAACsK,SAAS,GAAG,CAAC;EAC3H,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,MAAM,CAAC3E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IACzC+D,MAAM,CAAC/D,CAAC,CAAC,CAACyK,iBAAiB,GAAG,CAAC9N,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGuB,MAAM,CAAC/D,CAAC,CAAC,CAACuK,UAAU,GAAGxG,MAAM,CAAC/D,CAAC,CAAC,CAACwK,SAAS,IAAIF,WAAW,GAAG3N,MAAM,CAAC+N,qBAAqB,CAAC,CAAC;EACnJ;AACF;AAEA,MAAMC,oBAAoB,GAAGA,CAAC1F,OAAO,EAAE2F,SAAS,EAAEC,SAAS,KAAK;EAC9D,IAAID,SAAS,IAAI,CAAC3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IACvD5F,OAAO,CAACuE,SAAS,CAACG,GAAG,CAACkB,SAAS,CAAC;EAClC,CAAC,MAAM,IAAI,CAACD,SAAS,IAAI3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IAC9D5F,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACiB,SAAS,CAAC;EACrC;AACF,CAAC;AACD,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EACvC,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,IAAI,IAAI,IAAI,CAACA,SAAS,IAAI,CAAC;EACzC;EACA,MAAMpO,MAAM,GAAG,IAAI;EACnB,MAAM2B,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAM;IACJyF,MAAM;IACNP,YAAY,EAAEC,GAAG;IACjBS;EACF,CAAC,GAAGvH,MAAM;EACV,IAAIoH,MAAM,CAAC3E,MAAM,KAAK,CAAC,EAAE;EACzB,IAAI,OAAO2E,MAAM,CAAC,CAAC,CAAC,CAAC0G,iBAAiB,KAAK,WAAW,EAAE9N,MAAM,CAACyM,kBAAkB,CAAC,CAAC;EACnF,IAAI4B,YAAY,GAAG,CAACD,SAAS;EAC7B,IAAItH,GAAG,EAAEuH,YAAY,GAAGD,SAAS;EACjCpO,MAAM,CAACsO,oBAAoB,GAAG,EAAE;EAChCtO,MAAM,CAACwN,aAAa,GAAG,EAAE;EACzB,IAAIvF,YAAY,GAAGtG,MAAM,CAACsG,YAAY;EACtC,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAAC1J,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtE0J,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGpI,MAAM,CAACmG,IAAI;EAC9E,CAAC,MAAM,IAAI,OAAO8B,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAAC;EACzC;EACA,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,MAAM,CAAC3E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IACzC,MAAMoG,KAAK,GAAGrC,MAAM,CAAC/D,CAAC,CAAC;IACvB,IAAIkL,WAAW,GAAG9E,KAAK,CAACqE,iBAAiB;IACzC,IAAInM,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAACgH,cAAc,EAAE;MAC3C4F,WAAW,IAAInH,MAAM,CAAC,CAAC,CAAC,CAAC0G,iBAAiB;IAC5C;IACA,MAAMU,aAAa,GAAG,CAACH,YAAY,IAAI1M,MAAM,CAACgH,cAAc,GAAG3I,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGF,WAAW,KAAK9E,KAAK,CAACe,eAAe,GAAGvC,YAAY,CAAC;IACjJ,MAAMyG,qBAAqB,GAAG,CAACL,YAAY,GAAG9G,QAAQ,CAAC,CAAC,CAAC,IAAI5F,MAAM,CAACgH,cAAc,GAAG3I,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGF,WAAW,KAAK9E,KAAK,CAACe,eAAe,GAAGvC,YAAY,CAAC;IACvK,MAAM0G,WAAW,GAAG,EAAEN,YAAY,GAAGE,WAAW,CAAC;IACjD,MAAMK,UAAU,GAAGD,WAAW,GAAG3O,MAAM,CAACyH,eAAe,CAACpE,CAAC,CAAC;IAC1D,MAAMwL,cAAc,GAAGF,WAAW,IAAI,CAAC,IAAIA,WAAW,IAAI3O,MAAM,CAACmG,IAAI,GAAGnG,MAAM,CAACyH,eAAe,CAACpE,CAAC,CAAC;IACjG,MAAMyL,SAAS,GAAGH,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAG3O,MAAM,CAACmG,IAAI,GAAG,CAAC,IAAIyI,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI5O,MAAM,CAACmG,IAAI,IAAIwI,WAAW,IAAI,CAAC,IAAIC,UAAU,IAAI5O,MAAM,CAACmG,IAAI;IACnK,IAAI2I,SAAS,EAAE;MACb9O,MAAM,CAACwN,aAAa,CAACxK,IAAI,CAACyG,KAAK,CAAC;MAChCzJ,MAAM,CAACsO,oBAAoB,CAACtL,IAAI,CAACK,CAAC,CAAC;IACrC;IACA2K,oBAAoB,CAACvE,KAAK,EAAEqF,SAAS,EAAEnN,MAAM,CAACoN,iBAAiB,CAAC;IAChEf,oBAAoB,CAACvE,KAAK,EAAEoF,cAAc,EAAElN,MAAM,CAACqN,sBAAsB,CAAC;IAC1EvF,KAAK,CAACwF,QAAQ,GAAGnI,GAAG,GAAG,CAAC0H,aAAa,GAAGA,aAAa;IACrD/E,KAAK,CAACyF,gBAAgB,GAAGpI,GAAG,GAAG,CAAC4H,qBAAqB,GAAGA,qBAAqB;EAC/E;AACF;AAEA,SAASS,cAAcA,CAACf,SAAS,EAAE;EACjC,MAAMpO,MAAM,GAAG,IAAI;EACnB,IAAI,OAAOoO,SAAS,KAAK,WAAW,EAAE;IACpC,MAAMgB,UAAU,GAAGpP,MAAM,CAAC6G,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C;IACAuH,SAAS,GAAGpO,MAAM,IAAIA,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACoO,SAAS,GAAGgB,UAAU,IAAI,CAAC;EAC9E;EACA,MAAMzN,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAM0N,cAAc,GAAGrP,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAGtP,MAAM,CAACyO,YAAY,CAAC,CAAC;EACpE,IAAI;IACFQ,QAAQ;IACRM,WAAW;IACXC,KAAK;IACLC;EACF,CAAC,GAAGzP,MAAM;EACV,MAAM0P,YAAY,GAAGH,WAAW;EAChC,MAAMI,MAAM,GAAGH,KAAK;EACpB,IAAIH,cAAc,KAAK,CAAC,EAAE;IACxBJ,QAAQ,GAAG,CAAC;IACZM,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAG,IAAI;EACd,CAAC,MAAM;IACLP,QAAQ,GAAG,CAACb,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,IAAIY,cAAc;IAC/D,MAAMO,kBAAkB,GAAGtF,IAAI,CAACG,GAAG,CAAC2D,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1E,MAAMoB,YAAY,GAAGvF,IAAI,CAACG,GAAG,CAAC2D,SAAS,GAAGpO,MAAM,CAACsP,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IACpEC,WAAW,GAAGK,kBAAkB,IAAIX,QAAQ,IAAI,CAAC;IACjDO,KAAK,GAAGK,YAAY,IAAIZ,QAAQ,IAAI,CAAC;IACrC,IAAIW,kBAAkB,EAAEX,QAAQ,GAAG,CAAC;IACpC,IAAIY,YAAY,EAAEZ,QAAQ,GAAG,CAAC;EAChC;EACA,IAAItN,MAAM,CAACwJ,IAAI,EAAE;IACf,MAAM2E,eAAe,GAAG9P,MAAM,CAACuN,mBAAmB,CAAC,CAAC,CAAC;IACrD,MAAMwC,cAAc,GAAG/P,MAAM,CAACuN,mBAAmB,CAACvN,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC,CAAC;IAC3E,MAAMuN,mBAAmB,GAAGhQ,MAAM,CAACwH,UAAU,CAACsI,eAAe,CAAC;IAC9D,MAAMG,kBAAkB,GAAGjQ,MAAM,CAACwH,UAAU,CAACuI,cAAc,CAAC;IAC5D,MAAMG,YAAY,GAAGlQ,MAAM,CAACwH,UAAU,CAACxH,MAAM,CAACwH,UAAU,CAAC/E,MAAM,GAAG,CAAC,CAAC;IACpE,MAAM0N,YAAY,GAAG7F,IAAI,CAACG,GAAG,CAAC2D,SAAS,CAAC;IACxC,IAAI+B,YAAY,IAAIH,mBAAmB,EAAE;MACvCP,YAAY,GAAG,CAACU,YAAY,GAAGH,mBAAmB,IAAIE,YAAY;IACpE,CAAC,MAAM;MACLT,YAAY,GAAG,CAACU,YAAY,GAAGD,YAAY,GAAGD,kBAAkB,IAAIC,YAAY;IAClF;IACA,IAAIT,YAAY,GAAG,CAAC,EAAEA,YAAY,IAAI,CAAC;EACzC;EACAxJ,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;IACpBiP,QAAQ;IACRQ,YAAY;IACZF,WAAW;IACXC;EACF,CAAC,CAAC;EACF,IAAI7N,MAAM,CAAC6K,mBAAmB,IAAI7K,MAAM,CAACgH,cAAc,IAAIhH,MAAM,CAACyO,UAAU,EAAEpQ,MAAM,CAACmO,oBAAoB,CAACC,SAAS,CAAC;EACpH,IAAImB,WAAW,IAAI,CAACG,YAAY,EAAE;IAChC1P,MAAM,CAACE,IAAI,CAAC,uBAAuB,CAAC;EACtC;EACA,IAAIsP,KAAK,IAAI,CAACG,MAAM,EAAE;IACpB3P,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;EAChC;EACA,IAAIwP,YAAY,IAAI,CAACH,WAAW,IAAII,MAAM,IAAI,CAACH,KAAK,EAAE;IACpDxP,MAAM,CAACE,IAAI,CAAC,UAAU,CAAC;EACzB;EACAF,MAAM,CAACE,IAAI,CAAC,UAAU,EAAE+O,QAAQ,CAAC;AACnC;AAEA,MAAMoB,kBAAkB,GAAGA,CAAC/H,OAAO,EAAE2F,SAAS,EAAEC,SAAS,KAAK;EAC5D,IAAID,SAAS,IAAI,CAAC3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IACvD5F,OAAO,CAACuE,SAAS,CAACG,GAAG,CAACkB,SAAS,CAAC;EAClC,CAAC,MAAM,IAAI,CAACD,SAAS,IAAI3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IAC9D5F,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACiB,SAAS,CAAC;EACrC;AACF,CAAC;AACD,SAASoC,mBAAmBA,CAAA,EAAG;EAC7B,MAAMtQ,MAAM,GAAG,IAAI;EACnB,MAAM;IACJoH,MAAM;IACNzF,MAAM;IACNgF,QAAQ;IACR8G;EACF,CAAC,GAAGzN,MAAM;EACV,MAAMgH,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO;EAC1D,MAAM2B,WAAW,GAAG7I,MAAM,CAAC8I,IAAI,IAAInH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,MAAMwH,gBAAgB,GAAGC,QAAQ,IAAI;IACnC,OAAO9V,eAAe,CAACiM,QAAQ,MAAAnI,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,EAAA7I,MAAA,CAAGgS,QAAQ,oBAAAhS,MAAA,CAAiBgS,QAAQ,CAAE,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC;EACD,IAAIC,WAAW;EACf,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAI3J,SAAS,EAAE;IACb,IAAIrF,MAAM,CAACwJ,IAAI,EAAE;MACf,IAAIO,UAAU,GAAG+B,WAAW,GAAGzN,MAAM,CAACiH,OAAO,CAACqE,YAAY;MAC1D,IAAII,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG1L,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAGiJ,UAAU;MAC1E,IAAIA,UAAU,IAAI1L,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,EAAEiJ,UAAU,IAAI1L,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM;MAC1FgO,WAAW,GAAGF,gBAAgB,+BAAA/R,MAAA,CAA8BkN,UAAU,QAAI,CAAC;IAC7E,CAAC,MAAM;MACL+E,WAAW,GAAGF,gBAAgB,+BAAA/R,MAAA,CAA8BiP,WAAW,QAAI,CAAC;IAC9E;EACF,CAAC,MAAM;IACL,IAAI5E,WAAW,EAAE;MACf4H,WAAW,GAAGrJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,CAAC;MACpEkD,SAAS,GAAGvJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,GAAG,CAAC,CAAC;MACtEiD,SAAS,GAAGtJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,GAAG,CAAC,CAAC;IACxE,CAAC,MAAM;MACLgD,WAAW,GAAGrJ,MAAM,CAACqG,WAAW,CAAC;IACnC;EACF;EACA,IAAIgD,WAAW,EAAE;IACf,IAAI,CAAC5H,WAAW,EAAE;MAChB;MACA8H,SAAS,GAAG3V,cAAc,CAACyV,WAAW,MAAAjS,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC,CAAC,CAAC,CAAC;MACjF,IAAI1F,MAAM,CAACwJ,IAAI,IAAI,CAACwF,SAAS,EAAE;QAC7BA,SAAS,GAAGvJ,MAAM,CAAC,CAAC,CAAC;MACvB;;MAEA;MACAsJ,SAAS,GAAGxV,cAAc,CAACuV,WAAW,MAAAjS,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC,CAAC,CAAC,CAAC;MACjF,IAAI1F,MAAM,CAACwJ,IAAI,IAAI,CAACuF,SAAS,KAAK,CAAC,EAAE;QACnCA,SAAS,GAAGtJ,MAAM,CAACA,MAAM,CAAC3E,MAAM,GAAG,CAAC,CAAC;MACvC;IACF;EACF;EACA2E,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;IACxB+H,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKmI,WAAW,EAAE9O,MAAM,CAACmP,gBAAgB,CAAC;IAC7ET,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKqI,SAAS,EAAEhP,MAAM,CAACoP,cAAc,CAAC;IACzEV,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKoI,SAAS,EAAE/O,MAAM,CAACqP,cAAc,CAAC;EAC3E,CAAC,CAAC;EACFhR,MAAM,CAACiR,iBAAiB,CAAC,CAAC;AAC5B;AAEA,MAAMC,oBAAoB,GAAGA,CAAClR,MAAM,EAAEmR,OAAO,KAAK;EAChD,IAAI,CAACnR,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAAC2B,MAAM,EAAE;EACnD,MAAMyP,aAAa,GAAGA,CAAA,KAAMpR,MAAM,CAAC8C,SAAS,wBAAAtE,MAAA,CAAwBwB,MAAM,CAAC2B,MAAM,CAAC0F,UAAU,CAAE;EAC9F,MAAMiB,OAAO,GAAG6I,OAAO,CAACE,OAAO,CAACD,aAAa,CAAC,CAAC,CAAC;EAChD,IAAI9I,OAAO,EAAE;IACX,IAAIgJ,MAAM,GAAGhJ,OAAO,CAACiJ,aAAa,KAAA/S,MAAA,CAAKwB,MAAM,CAAC2B,MAAM,CAAC6P,kBAAkB,CAAE,CAAC;IAC1E,IAAI,CAACF,MAAM,IAAItR,MAAM,CAAC8C,SAAS,EAAE;MAC/B,IAAIwF,OAAO,CAACmJ,UAAU,EAAE;QACtBH,MAAM,GAAGhJ,OAAO,CAACmJ,UAAU,CAACF,aAAa,KAAA/S,MAAA,CAAKwB,MAAM,CAAC2B,MAAM,CAAC6P,kBAAkB,CAAE,CAAC;MACnF,CAAC,MAAM;QACL;QACA7Q,qBAAqB,CAAC,MAAM;UAC1B,IAAI2H,OAAO,CAACmJ,UAAU,EAAE;YACtBH,MAAM,GAAGhJ,OAAO,CAACmJ,UAAU,CAACF,aAAa,KAAA/S,MAAA,CAAKwB,MAAM,CAAC2B,MAAM,CAAC6P,kBAAkB,CAAE,CAAC;YACjF,IAAIF,MAAM,EAAEA,MAAM,CAACrE,MAAM,CAAC,CAAC;UAC7B;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAIqE,MAAM,EAAEA,MAAM,CAACrE,MAAM,CAAC,CAAC;EAC7B;AACF,CAAC;AACD,MAAMyE,MAAM,GAAGA,CAAC1R,MAAM,EAAEgF,KAAK,KAAK;EAChC,IAAI,CAAChF,MAAM,CAACoH,MAAM,CAACpC,KAAK,CAAC,EAAE;EAC3B,MAAMmM,OAAO,GAAGnR,MAAM,CAACoH,MAAM,CAACpC,KAAK,CAAC,CAACuM,aAAa,CAAC,kBAAkB,CAAC;EACtE,IAAIJ,OAAO,EAAEA,OAAO,CAACQ,eAAe,CAAC,SAAS,CAAC;AACjD,CAAC;AACD,MAAMC,OAAO,GAAG5R,MAAM,IAAI;EACxB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAAC2B,MAAM,EAAE;EACnD,IAAIkQ,MAAM,GAAG7R,MAAM,CAAC2B,MAAM,CAACmQ,mBAAmB;EAC9C,MAAMC,GAAG,GAAG/R,MAAM,CAACoH,MAAM,CAAC3E,MAAM;EAChC,IAAI,CAACsP,GAAG,IAAI,CAACF,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;EACnCA,MAAM,GAAGvH,IAAI,CAACK,GAAG,CAACkH,MAAM,EAAEE,GAAG,CAAC;EAC9B,MAAM3I,aAAa,GAAGpJ,MAAM,CAAC2B,MAAM,CAACyH,aAAa,KAAK,MAAM,GAAGpJ,MAAM,CAACgS,oBAAoB,CAAC,CAAC,GAAG1H,IAAI,CAACe,IAAI,CAACrL,MAAM,CAAC2B,MAAM,CAACyH,aAAa,CAAC;EACrI,MAAMqE,WAAW,GAAGzN,MAAM,CAACyN,WAAW;EACtC,IAAIzN,MAAM,CAAC2B,MAAM,CAACmH,IAAI,IAAI9I,MAAM,CAAC2B,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;IACrD,MAAMkJ,YAAY,GAAGxE,WAAW;IAChC,MAAMyE,cAAc,GAAG,CAACD,YAAY,GAAGJ,MAAM,CAAC;IAC9CK,cAAc,CAAClP,IAAI,CAAC,GAAG0B,KAAK,CAACyN,IAAI,CAAC;MAChC1P,MAAM,EAAEoP;IACV,CAAC,CAAC,CAACvS,GAAG,CAAC,CAACmM,CAAC,EAAEpI,CAAC,KAAK;MACf,OAAO4O,YAAY,GAAG7I,aAAa,GAAG/F,CAAC;IACzC,CAAC,CAAC,CAAC;IACHrD,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAAC,CAACwH,OAAO,EAAEjF,CAAC,KAAK;MACpC,IAAI6O,cAAc,CAAChT,QAAQ,CAACoJ,OAAO,CAACuI,MAAM,CAAC,EAAEa,MAAM,CAAC1R,MAAM,EAAEqD,CAAC,CAAC;IAChE,CAAC,CAAC;IACF;EACF;EACA,MAAM+O,oBAAoB,GAAG3E,WAAW,GAAGrE,aAAa,GAAG,CAAC;EAC5D,IAAIpJ,MAAM,CAAC2B,MAAM,CAAC0Q,MAAM,IAAIrS,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,EAAE;IAC9C,KAAK,IAAI9H,CAAC,GAAGoK,WAAW,GAAGoE,MAAM,EAAExO,CAAC,IAAI+O,oBAAoB,GAAGP,MAAM,EAAExO,CAAC,IAAI,CAAC,EAAE;MAC7E,MAAMiP,SAAS,GAAG,CAACjP,CAAC,GAAG0O,GAAG,GAAGA,GAAG,IAAIA,GAAG;MACvC,IAAIO,SAAS,GAAG7E,WAAW,IAAI6E,SAAS,GAAGF,oBAAoB,EAAEV,MAAM,CAAC1R,MAAM,EAAEsS,SAAS,CAAC;IAC5F;EACF,CAAC,MAAM;IACL,KAAK,IAAIjP,CAAC,GAAGiH,IAAI,CAACO,GAAG,CAAC4C,WAAW,GAAGoE,MAAM,EAAE,CAAC,CAAC,EAAExO,CAAC,IAAIiH,IAAI,CAACK,GAAG,CAACyH,oBAAoB,GAAGP,MAAM,EAAEE,GAAG,GAAG,CAAC,CAAC,EAAE1O,CAAC,IAAI,CAAC,EAAE;MAC7G,IAAIA,CAAC,KAAKoK,WAAW,KAAKpK,CAAC,GAAG+O,oBAAoB,IAAI/O,CAAC,GAAGoK,WAAW,CAAC,EAAE;QACtEiE,MAAM,CAAC1R,MAAM,EAAEqD,CAAC,CAAC;MACnB;IACF;EACF;AACF,CAAC;AAED,SAASkP,yBAAyBA,CAACvS,MAAM,EAAE;EACzC,MAAM;IACJwH,UAAU;IACV7F;EACF,CAAC,GAAG3B,MAAM;EACV,MAAMoO,SAAS,GAAGpO,MAAM,CAAC6G,YAAY,GAAG7G,MAAM,CAACoO,SAAS,GAAG,CAACpO,MAAM,CAACoO,SAAS;EAC5E,IAAIX,WAAW;EACf,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,UAAU,CAAC/E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAI,OAAOmE,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;MAC5C,IAAI+K,SAAS,IAAI5G,UAAU,CAACnE,CAAC,CAAC,IAAI+K,SAAS,GAAG5G,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,GAAG,CAACmE,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,GAAGmE,UAAU,CAACnE,CAAC,CAAC,IAAI,CAAC,EAAE;QACzGoK,WAAW,GAAGpK,CAAC;MACjB,CAAC,MAAM,IAAI+K,SAAS,IAAI5G,UAAU,CAACnE,CAAC,CAAC,IAAI+K,SAAS,GAAG5G,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,EAAE;QACtEoK,WAAW,GAAGpK,CAAC,GAAG,CAAC;MACrB;IACF,CAAC,MAAM,IAAI+K,SAAS,IAAI5G,UAAU,CAACnE,CAAC,CAAC,EAAE;MACrCoK,WAAW,GAAGpK,CAAC;IACjB;EACF;EACA;EACA,IAAI1B,MAAM,CAAC6Q,mBAAmB,EAAE;IAC9B,IAAI/E,WAAW,GAAG,CAAC,IAAI,OAAOA,WAAW,KAAK,WAAW,EAAEA,WAAW,GAAG,CAAC;EAC5E;EACA,OAAOA,WAAW;AACpB;AACA,SAASgF,iBAAiBA,CAACC,cAAc,EAAE;EACzC,MAAM1S,MAAM,GAAG,IAAI;EACnB,MAAMoO,SAAS,GAAGpO,MAAM,CAAC6G,YAAY,GAAG7G,MAAM,CAACoO,SAAS,GAAG,CAACpO,MAAM,CAACoO,SAAS;EAC5E,MAAM;IACJ7G,QAAQ;IACR5F,MAAM;IACN8L,WAAW,EAAEkF,aAAa;IAC1BL,SAAS,EAAEM,iBAAiB;IAC5BzG,SAAS,EAAE0G;EACb,CAAC,GAAG7S,MAAM;EACV,IAAIyN,WAAW,GAAGiF,cAAc;EAChC,IAAIvG,SAAS;EACb,MAAM2G,mBAAmB,GAAGC,MAAM,IAAI;IACpC,IAAIT,SAAS,GAAGS,MAAM,GAAG/S,MAAM,CAACiH,OAAO,CAACqE,YAAY;IACpD,IAAIgH,SAAS,GAAG,CAAC,EAAE;MACjBA,SAAS,GAAGtS,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAG6P,SAAS;IACtD;IACA,IAAIA,SAAS,IAAItS,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,EAAE;MAC7C6P,SAAS,IAAItS,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM;IAC3C;IACA,OAAO6P,SAAS;EAClB,CAAC;EACD,IAAI,OAAO7E,WAAW,KAAK,WAAW,EAAE;IACtCA,WAAW,GAAG8E,yBAAyB,CAACvS,MAAM,CAAC;EACjD;EACA,IAAIuH,QAAQ,CAAChJ,OAAO,CAAC6P,SAAS,CAAC,IAAI,CAAC,EAAE;IACpCjC,SAAS,GAAG5E,QAAQ,CAAChJ,OAAO,CAAC6P,SAAS,CAAC;EACzC,CAAC,MAAM;IACL,MAAM4E,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAChJ,MAAM,CAACiJ,kBAAkB,EAAE6C,WAAW,CAAC;IAC7DtB,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACkD,WAAW,GAAGuF,IAAI,IAAIrR,MAAM,CAAC+I,cAAc,CAAC;EAC7E;EACA,IAAIyB,SAAS,IAAI5E,QAAQ,CAAC9E,MAAM,EAAE0J,SAAS,GAAG5E,QAAQ,CAAC9E,MAAM,GAAG,CAAC;EACjE,IAAIgL,WAAW,KAAKkF,aAAa,IAAI,CAAC3S,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,EAAE;IACxD,IAAIgB,SAAS,KAAK0G,iBAAiB,EAAE;MACnC7S,MAAM,CAACmM,SAAS,GAAGA,SAAS;MAC5BnM,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA;EACF;EACA,IAAIuN,WAAW,KAAKkF,aAAa,IAAI3S,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,IAAInL,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;IAC1GlH,MAAM,CAACsS,SAAS,GAAGQ,mBAAmB,CAACrF,WAAW,CAAC;IACnD;EACF;EACA,MAAM5E,WAAW,GAAG7I,MAAM,CAAC8I,IAAI,IAAInH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;;EAEtE;EACA,IAAIuJ,SAAS;EACb,IAAItS,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO,IAAIvF,MAAM,CAACwJ,IAAI,EAAE;IAC3DmH,SAAS,GAAGQ,mBAAmB,CAACrF,WAAW,CAAC;EAC9C,CAAC,MAAM,IAAI5E,WAAW,EAAE;IACtB,MAAMoK,kBAAkB,GAAGjT,MAAM,CAACoH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,CAAC;IACxF,IAAIyF,gBAAgB,GAAGnN,QAAQ,CAACkN,kBAAkB,CAACE,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IAC/F,IAAI3T,MAAM,CAACwG,KAAK,CAACkN,gBAAgB,CAAC,EAAE;MAClCA,gBAAgB,GAAG5I,IAAI,CAACO,GAAG,CAAC7K,MAAM,CAACoH,MAAM,CAAC7I,OAAO,CAAC0U,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC3E;IACAX,SAAS,GAAGhI,IAAI,CAACC,KAAK,CAAC2I,gBAAgB,GAAGvR,MAAM,CAACmH,IAAI,CAACC,IAAI,CAAC;EAC7D,CAAC,MAAM,IAAI/I,MAAM,CAACoH,MAAM,CAACqG,WAAW,CAAC,EAAE;IACrC,MAAM/B,UAAU,GAAG1L,MAAM,CAACoH,MAAM,CAACqG,WAAW,CAAC,CAAC0F,YAAY,CAAC,yBAAyB,CAAC;IACrF,IAAIzH,UAAU,EAAE;MACd4G,SAAS,GAAGvM,QAAQ,CAAC2F,UAAU,EAAE,EAAE,CAAC;IACtC,CAAC,MAAM;MACL4G,SAAS,GAAG7E,WAAW;IACzB;EACF,CAAC,MAAM;IACL6E,SAAS,GAAG7E,WAAW;EACzB;EACAxH,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;IACpB6S,iBAAiB;IACjB1G,SAAS;IACTyG,iBAAiB;IACjBN,SAAS;IACTK,aAAa;IACblF;EACF,CAAC,CAAC;EACF,IAAIzN,MAAM,CAACO,WAAW,EAAE;IACtBqR,OAAO,CAAC5R,MAAM,CAAC;EACjB;EACAA,MAAM,CAACE,IAAI,CAAC,mBAAmB,CAAC;EAChCF,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;EAC9B,IAAIF,MAAM,CAACO,WAAW,IAAIP,MAAM,CAAC2B,MAAM,CAACyR,kBAAkB,EAAE;IAC1D,IAAIR,iBAAiB,KAAKN,SAAS,EAAE;MACnCtS,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACAF,MAAM,CAACE,IAAI,CAAC,aAAa,CAAC;EAC5B;AACF;AAEA,SAASmT,kBAAkBA,CAAClS,EAAE,EAAEmS,IAAI,EAAE;EACpC,MAAMtT,MAAM,GAAG,IAAI;EACnB,MAAM2B,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,IAAI8H,KAAK,GAAGtI,EAAE,CAACkQ,OAAO,KAAA7S,MAAA,CAAKmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC;EAC7D,IAAI,CAACoC,KAAK,IAAIzJ,MAAM,CAAC8C,SAAS,IAAIwQ,IAAI,IAAIA,IAAI,CAAC7Q,MAAM,GAAG,CAAC,IAAI6Q,IAAI,CAACpU,QAAQ,CAACiC,EAAE,CAAC,EAAE;IAC9E,CAAC,GAAGmS,IAAI,CAAC/N,KAAK,CAAC+N,IAAI,CAAC/U,OAAO,CAAC4C,EAAE,CAAC,GAAG,CAAC,EAAEmS,IAAI,CAAC7Q,MAAM,CAAC,CAAC,CAAC3B,OAAO,CAACyS,MAAM,IAAI;MACnE,IAAI,CAAC9J,KAAK,IAAI8J,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,KAAAhV,MAAA,CAAKmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC,EAAE;QACrFoC,KAAK,GAAG8J,MAAM;MAChB;IACF,CAAC,CAAC;EACJ;EACA,IAAIE,UAAU,GAAG,KAAK;EACtB,IAAI/H,UAAU;EACd,IAAIjC,KAAK,EAAE;IACT,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,MAAM,CAACoH,MAAM,CAAC3E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;MAChD,IAAIrD,MAAM,CAACoH,MAAM,CAAC/D,CAAC,CAAC,KAAKoG,KAAK,EAAE;QAC9BgK,UAAU,GAAG,IAAI;QACjB/H,UAAU,GAAGrI,CAAC;QACd;MACF;IACF;EACF;EACA,IAAIoG,KAAK,IAAIgK,UAAU,EAAE;IACvBzT,MAAM,CAAC0T,YAAY,GAAGjK,KAAK;IAC3B,IAAIzJ,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;MACnDlH,MAAM,CAAC2T,YAAY,GAAG5N,QAAQ,CAAC0D,KAAK,CAAC0J,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACnF,CAAC,MAAM;MACLnT,MAAM,CAAC2T,YAAY,GAAGjI,UAAU;IAClC;EACF,CAAC,MAAM;IACL1L,MAAM,CAAC0T,YAAY,GAAGE,SAAS;IAC/B5T,MAAM,CAAC2T,YAAY,GAAGC,SAAS;IAC/B;EACF;EACA,IAAIjS,MAAM,CAACkS,mBAAmB,IAAI7T,MAAM,CAAC2T,YAAY,KAAKC,SAAS,IAAI5T,MAAM,CAAC2T,YAAY,KAAK3T,MAAM,CAACyN,WAAW,EAAE;IACjHzN,MAAM,CAAC6T,mBAAmB,CAAC,CAAC;EAC9B;AACF;AAEA,IAAIC,MAAM,GAAG;EACXpO,UAAU;EACVU,YAAY;EACZ8G,gBAAgB;EAChBT,kBAAkB;EAClB0B,oBAAoB;EACpBgB,cAAc;EACdmB,mBAAmB;EACnBmC,iBAAiB;EACjBY;AACF,CAAC;AAED,SAASU,kBAAkBA,CAACC,IAAI,EAAE;EAChC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,IAAI,CAACnO,YAAY,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;EACxC;EACA,MAAM7F,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNkF,YAAY,EAAEC,GAAG;IACjBsH,SAAS;IACT7K;EACF,CAAC,GAAGvD,MAAM;EACV,IAAI2B,MAAM,CAACsS,gBAAgB,EAAE;IAC3B,OAAOnN,GAAG,GAAG,CAACsH,SAAS,GAAGA,SAAS;EACrC;EACA,IAAIzM,MAAM,CAACiH,OAAO,EAAE;IAClB,OAAOwF,SAAS;EAClB;EACA,IAAI8F,gBAAgB,GAAG9Y,YAAY,CAACmI,SAAS,EAAEyQ,IAAI,CAAC;EACpDE,gBAAgB,IAAIlU,MAAM,CAAC+N,qBAAqB,CAAC,CAAC;EAClD,IAAIjH,GAAG,EAAEoN,gBAAgB,GAAG,CAACA,gBAAgB;EAC7C,OAAOA,gBAAgB,IAAI,CAAC;AAC9B;AAEA,SAASC,YAAYA,CAAC/F,SAAS,EAAEgG,YAAY,EAAE;EAC7C,MAAMpU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ6G,YAAY,EAAEC,GAAG;IACjBnF,MAAM;IACN4B,SAAS;IACT0L;EACF,CAAC,GAAGjP,MAAM;EACV,IAAI3D,CAAC,GAAG,CAAC;EACT,IAAIgY,CAAC,GAAG,CAAC;EACT,MAAMC,CAAC,GAAG,CAAC;EACX,IAAItU,MAAM,CAAC6F,YAAY,CAAC,CAAC,EAAE;IACzBxJ,CAAC,GAAGyK,GAAG,GAAG,CAACsH,SAAS,GAAGA,SAAS;EAClC,CAAC,MAAM;IACLiG,CAAC,GAAGjG,SAAS;EACf;EACA,IAAIzM,MAAM,CAACsI,YAAY,EAAE;IACvB5N,CAAC,GAAGiO,IAAI,CAACC,KAAK,CAAClO,CAAC,CAAC;IACjBgY,CAAC,GAAG/J,IAAI,CAACC,KAAK,CAAC8J,CAAC,CAAC;EACnB;EACArU,MAAM,CAACuU,iBAAiB,GAAGvU,MAAM,CAACoO,SAAS;EAC3CpO,MAAM,CAACoO,SAAS,GAAGpO,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGxJ,CAAC,GAAGgY,CAAC;EAChD,IAAI1S,MAAM,CAACiH,OAAO,EAAE;IAClBrF,SAAS,CAACvD,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG7F,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAG,CAACxJ,CAAC,GAAG,CAACgY,CAAC;EACjG,CAAC,MAAM,IAAI,CAAC1S,MAAM,CAACsS,gBAAgB,EAAE;IACnC,IAAIjU,MAAM,CAAC6F,YAAY,CAAC,CAAC,EAAE;MACzBxJ,CAAC,IAAI2D,MAAM,CAAC+N,qBAAqB,CAAC,CAAC;IACrC,CAAC,MAAM;MACLsG,CAAC,IAAIrU,MAAM,CAAC+N,qBAAqB,CAAC,CAAC;IACrC;IACAxK,SAAS,CAAC1G,KAAK,CAACiN,SAAS,kBAAAtL,MAAA,CAAkBnC,CAAC,UAAAmC,MAAA,CAAO6V,CAAC,UAAA7V,MAAA,CAAO8V,CAAC,QAAK;EACnE;;EAEA;EACA,IAAIE,WAAW;EACf,MAAMnF,cAAc,GAAGrP,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAGtP,MAAM,CAACyO,YAAY,CAAC,CAAC;EACpE,IAAIY,cAAc,KAAK,CAAC,EAAE;IACxBmF,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACpG,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,IAAIY,cAAc;EACpE;EACA,IAAImF,WAAW,KAAKvF,QAAQ,EAAE;IAC5BjP,MAAM,CAACmP,cAAc,CAACf,SAAS,CAAC;EAClC;EACApO,MAAM,CAACE,IAAI,CAAC,cAAc,EAAEF,MAAM,CAACoO,SAAS,EAAEgG,YAAY,CAAC;AAC7D;AAEA,SAAS3F,YAAYA,CAAA,EAAG;EACtB,OAAO,CAAC,IAAI,CAAClH,QAAQ,CAAC,CAAC,CAAC;AAC1B;AAEA,SAAS+H,YAAYA,CAAA,EAAG;EACtB,OAAO,CAAC,IAAI,CAAC/H,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC9E,MAAM,GAAG,CAAC,CAAC;AACjD;AAEA,SAASgS,WAAWA,CAACrG,SAAS,EAAEjB,KAAK,EAAEuH,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAE;EAC9E,IAAIxG,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC;EACf;EACA,IAAIjB,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,IAAI,CAACxL,MAAM,CAACwL,KAAK;EAC3B;EACA,IAAIuH,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,MAAM3U,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN4B;EACF,CAAC,GAAGvD,MAAM;EACV,IAAIA,MAAM,CAAC6U,SAAS,IAAIlT,MAAM,CAACmT,8BAA8B,EAAE;IAC7D,OAAO,KAAK;EACd;EACA,MAAMrG,YAAY,GAAGzO,MAAM,CAACyO,YAAY,CAAC,CAAC;EAC1C,MAAMa,YAAY,GAAGtP,MAAM,CAACsP,YAAY,CAAC,CAAC;EAC1C,IAAIyF,YAAY;EAChB,IAAIJ,eAAe,IAAIvG,SAAS,GAAGK,YAAY,EAAEsG,YAAY,GAAGtG,YAAY,CAAC,KAAK,IAAIkG,eAAe,IAAIvG,SAAS,GAAGkB,YAAY,EAAEyF,YAAY,GAAGzF,YAAY,CAAC,KAAKyF,YAAY,GAAG3G,SAAS;;EAE5L;EACApO,MAAM,CAACmP,cAAc,CAAC4F,YAAY,CAAC;EACnC,IAAIpT,MAAM,CAACiH,OAAO,EAAE;IAClB,MAAMoM,GAAG,GAAGhV,MAAM,CAAC6F,YAAY,CAAC,CAAC;IACjC,IAAIsH,KAAK,KAAK,CAAC,EAAE;MACf5J,SAAS,CAACyR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG,CAACD,YAAY;IAC7D,CAAC,MAAM;MACL,IAAI,CAAC/U,MAAM,CAACzD,OAAO,CAACI,YAAY,EAAE;QAChCrB,oBAAoB,CAAC;UACnB0E,MAAM;UACNiV,cAAc,EAAE,CAACF,YAAY;UAC7BG,IAAI,EAAEF,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAzR,SAAS,CAAC4R,QAAQ,CAAC;QACjB,CAACH,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,CAACD,YAAY;QACrCK,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA,IAAIjI,KAAK,KAAK,CAAC,EAAE;IACfnN,MAAM,CAACqN,aAAa,CAAC,CAAC,CAAC;IACvBrN,MAAM,CAACmU,YAAY,CAACY,YAAY,CAAC;IACjC,IAAIL,YAAY,EAAE;MAChB1U,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEiN,KAAK,EAAEyH,QAAQ,CAAC;MACrD5U,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;IAC9B;EACF,CAAC,MAAM;IACLF,MAAM,CAACqN,aAAa,CAACF,KAAK,CAAC;IAC3BnN,MAAM,CAACmU,YAAY,CAACY,YAAY,CAAC;IACjC,IAAIL,YAAY,EAAE;MAChB1U,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEiN,KAAK,EAAEyH,QAAQ,CAAC;MACrD5U,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA,IAAI,CAACF,MAAM,CAAC6U,SAAS,EAAE;MACrB7U,MAAM,CAAC6U,SAAS,GAAG,IAAI;MACvB,IAAI,CAAC7U,MAAM,CAACqV,iCAAiC,EAAE;QAC7CrV,MAAM,CAACqV,iCAAiC,GAAG,SAASC,aAAaA,CAAC7a,CAAC,EAAE;UACnE,IAAI,CAACuF,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;UACjC,IAAI7F,CAAC,CAACyG,MAAM,KAAK,IAAI,EAAE;UACvBlB,MAAM,CAACuD,SAAS,CAACzB,mBAAmB,CAAC,eAAe,EAAE9B,MAAM,CAACqV,iCAAiC,CAAC;UAC/FrV,MAAM,CAACqV,iCAAiC,GAAG,IAAI;UAC/C,OAAOrV,MAAM,CAACqV,iCAAiC;UAC/CrV,MAAM,CAAC6U,SAAS,GAAG,KAAK;UACxB,IAAIH,YAAY,EAAE;YAChB1U,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;UAC9B;QACF,CAAC;MACH;MACAF,MAAM,CAACuD,SAAS,CAAC1B,gBAAgB,CAAC,eAAe,EAAE7B,MAAM,CAACqV,iCAAiC,CAAC;IAC9F;EACF;EACA,OAAO,IAAI;AACb;AAEA,IAAIjH,SAAS,GAAG;EACdhT,YAAY,EAAE2Y,kBAAkB;EAChCI,YAAY;EACZ1F,YAAY;EACZa,YAAY;EACZmF;AACF,CAAC;AAED,SAASpH,aAAaA,CAACkI,QAAQ,EAAEnB,YAAY,EAAE;EAC7C,MAAMpU,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACiH,OAAO,EAAE;IAC1B5I,MAAM,CAACuD,SAAS,CAAC1G,KAAK,CAAC2Y,kBAAkB,MAAAhX,MAAA,CAAM+W,QAAQ,OAAI;IAC3DvV,MAAM,CAACuD,SAAS,CAAC1G,KAAK,CAAC4Y,eAAe,GAAGF,QAAQ,KAAK,CAAC,WAAW,EAAE;EACtE;EACAvV,MAAM,CAACE,IAAI,CAAC,eAAe,EAAEqV,QAAQ,EAAEnB,YAAY,CAAC;AACtD;AAEA,SAASsB,cAAcA,CAAC3V,IAAI,EAAE;EAC5B,IAAI;IACFC,MAAM;IACN0U,YAAY;IACZiB,SAAS;IACTC;EACF,CAAC,GAAG7V,IAAI;EACR,MAAM;IACJ0N,WAAW;IACXkF;EACF,CAAC,GAAG3S,MAAM;EACV,IAAI6V,GAAG,GAAGF,SAAS;EACnB,IAAI,CAACE,GAAG,EAAE;IACR,IAAIpI,WAAW,GAAGkF,aAAa,EAAEkD,GAAG,GAAG,MAAM,CAAC,KAAK,IAAIpI,WAAW,GAAGkF,aAAa,EAAEkD,GAAG,GAAG,MAAM,CAAC,KAAKA,GAAG,GAAG,OAAO;EACrH;EACA7V,MAAM,CAACE,IAAI,cAAA1B,MAAA,CAAcoX,IAAI,CAAE,CAAC;EAChC,IAAIlB,YAAY,IAAIjH,WAAW,KAAKkF,aAAa,EAAE;IACjD,IAAIkD,GAAG,KAAK,OAAO,EAAE;MACnB7V,MAAM,CAACE,IAAI,wBAAA1B,MAAA,CAAwBoX,IAAI,CAAE,CAAC;MAC1C;IACF;IACA5V,MAAM,CAACE,IAAI,yBAAA1B,MAAA,CAAyBoX,IAAI,CAAE,CAAC;IAC3C,IAAIC,GAAG,KAAK,MAAM,EAAE;MAClB7V,MAAM,CAACE,IAAI,uBAAA1B,MAAA,CAAuBoX,IAAI,CAAE,CAAC;IAC3C,CAAC,MAAM;MACL5V,MAAM,CAACE,IAAI,uBAAA1B,MAAA,CAAuBoX,IAAI,CAAE,CAAC;IAC3C;EACF;AACF;AAEA,SAASE,eAAeA,CAACpB,YAAY,EAAEiB,SAAS,EAAE;EAChD,IAAIjB,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAM1U,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACV,IAAI2B,MAAM,CAACiH,OAAO,EAAE;EACpB,IAAIjH,MAAM,CAACyO,UAAU,EAAE;IACrBpQ,MAAM,CAACkN,gBAAgB,CAAC,CAAC;EAC3B;EACAwI,cAAc,CAAC;IACb1V,MAAM;IACN0U,YAAY;IACZiB,SAAS;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEA,SAASN,aAAaA,CAACZ,YAAY,EAAEiB,SAAS,EAAE;EAC9C,IAAIjB,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAM1U,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACVA,MAAM,CAAC6U,SAAS,GAAG,KAAK;EACxB,IAAIlT,MAAM,CAACiH,OAAO,EAAE;EACpB5I,MAAM,CAACqN,aAAa,CAAC,CAAC,CAAC;EACvBqI,cAAc,CAAC;IACb1V,MAAM;IACN0U,YAAY;IACZiB,SAAS;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEA,IAAIG,UAAU,GAAG;EACf1I,aAAa;EACbyI,eAAe;EACfR;AACF,CAAC;AAED,SAASU,OAAOA,CAAChR,KAAK,EAAEmI,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAEqB,OAAO,EAAE;EAC9D,IAAIjR,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAI0P,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI,OAAO1P,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGe,QAAQ,CAACf,KAAK,EAAE,EAAE,CAAC;EAC7B;EACA,MAAMhF,MAAM,GAAG,IAAI;EACnB,IAAI0L,UAAU,GAAG1G,KAAK;EACtB,IAAI0G,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC;EAClC,MAAM;IACJ/J,MAAM;IACN4F,QAAQ;IACRC,UAAU;IACVmL,aAAa;IACblF,WAAW;IACX5G,YAAY,EAAEC,GAAG;IACjBvD,SAAS;IACT2D;EACF,CAAC,GAAGlH,MAAM;EACV,IAAI,CAACkH,OAAO,IAAI,CAAC0N,QAAQ,IAAI,CAACqB,OAAO,IAAIjW,MAAM,CAACM,SAAS,IAAIN,MAAM,CAAC6U,SAAS,IAAIlT,MAAM,CAACmT,8BAA8B,EAAE;IACtH,OAAO,KAAK;EACd;EACA,IAAI,OAAO3H,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,MAAM6F,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAC3K,MAAM,CAAC2B,MAAM,CAACiJ,kBAAkB,EAAEc,UAAU,CAAC;EACnE,IAAIS,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACmB,UAAU,GAAGsH,IAAI,IAAIhT,MAAM,CAAC2B,MAAM,CAAC+I,cAAc,CAAC;EACrF,IAAIyB,SAAS,IAAI5E,QAAQ,CAAC9E,MAAM,EAAE0J,SAAS,GAAG5E,QAAQ,CAAC9E,MAAM,GAAG,CAAC;EACjE,MAAM2L,SAAS,GAAG,CAAC7G,QAAQ,CAAC4E,SAAS,CAAC;EACtC;EACA,IAAIxK,MAAM,CAAC6Q,mBAAmB,EAAE;IAC9B,KAAK,IAAInP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,UAAU,CAAC/E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;MAC7C,MAAM6S,mBAAmB,GAAG,CAAC5L,IAAI,CAACC,KAAK,CAAC6D,SAAS,GAAG,GAAG,CAAC;MACxD,MAAM+H,cAAc,GAAG7L,IAAI,CAACC,KAAK,CAAC/C,UAAU,CAACnE,CAAC,CAAC,GAAG,GAAG,CAAC;MACtD,MAAM+S,kBAAkB,GAAG9L,IAAI,CAACC,KAAK,CAAC/C,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;MAC9D,IAAI,OAAOmE,UAAU,CAACnE,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;QAC5C,IAAI6S,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,GAAG,CAACA,kBAAkB,GAAGD,cAAc,IAAI,CAAC,EAAE;UACjIzK,UAAU,GAAGrI,CAAC;QAChB,CAAC,MAAM,IAAI6S,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,EAAE;UAC5F1K,UAAU,GAAGrI,CAAC,GAAG,CAAC;QACpB;MACF,CAAC,MAAM,IAAI6S,mBAAmB,IAAIC,cAAc,EAAE;QAChDzK,UAAU,GAAGrI,CAAC;MAChB;IACF;EACF;EACA;EACA,IAAIrD,MAAM,CAACO,WAAW,IAAImL,UAAU,KAAK+B,WAAW,EAAE;IACpD,IAAI,CAACzN,MAAM,CAACqW,cAAc,KAAKvP,GAAG,GAAGsH,SAAS,GAAGpO,MAAM,CAACoO,SAAS,IAAIA,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAGL,SAAS,GAAGpO,MAAM,CAACoO,SAAS,IAAIA,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC3K,OAAO,KAAK;IACd;IACA,IAAI,CAACzO,MAAM,CAACsW,cAAc,IAAIlI,SAAS,GAAGpO,MAAM,CAACoO,SAAS,IAAIA,SAAS,GAAGpO,MAAM,CAACsP,YAAY,CAAC,CAAC,EAAE;MAC/F,IAAI,CAAC7B,WAAW,IAAI,CAAC,MAAM/B,UAAU,EAAE;QACrC,OAAO,KAAK;MACd;IACF;EACF;EACA,IAAIA,UAAU,MAAMiH,aAAa,IAAI,CAAC,CAAC,IAAI+B,YAAY,EAAE;IACvD1U,MAAM,CAACE,IAAI,CAAC,wBAAwB,CAAC;EACvC;;EAEA;EACAF,MAAM,CAACmP,cAAc,CAACf,SAAS,CAAC;EAChC,IAAIuH,SAAS;EACb,IAAIjK,UAAU,GAAG+B,WAAW,EAAEkI,SAAS,GAAG,MAAM,CAAC,KAAK,IAAIjK,UAAU,GAAG+B,WAAW,EAAEkI,SAAS,GAAG,MAAM,CAAC,KAAKA,SAAS,GAAG,OAAO;;EAE/H;EACA,MAAM3O,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO;EACjE,MAAMqP,gBAAgB,GAAGvP,SAAS,IAAIiP,OAAO;EAC7C;EACA,IAAI,CAACM,gBAAgB,KAAKzP,GAAG,IAAI,CAACsH,SAAS,KAAKpO,MAAM,CAACoO,SAAS,IAAI,CAACtH,GAAG,IAAIsH,SAAS,KAAKpO,MAAM,CAACoO,SAAS,CAAC,EAAE;IAC3GpO,MAAM,CAACyS,iBAAiB,CAAC/G,UAAU,CAAC;IACpC;IACA,IAAI/J,MAAM,CAACyO,UAAU,EAAE;MACrBpQ,MAAM,CAACkN,gBAAgB,CAAC,CAAC;IAC3B;IACAlN,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;IAC5B,IAAI3O,MAAM,CAACmJ,MAAM,KAAK,OAAO,EAAE;MAC7B9K,MAAM,CAACmU,YAAY,CAAC/F,SAAS,CAAC;IAChC;IACA,IAAIuH,SAAS,KAAK,OAAO,EAAE;MACzB3V,MAAM,CAAC8V,eAAe,CAACpB,YAAY,EAAEiB,SAAS,CAAC;MAC/C3V,MAAM,CAACsV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;IAC/C;IACA,OAAO,KAAK;EACd;EACA,IAAIhU,MAAM,CAACiH,OAAO,EAAE;IAClB,MAAMoM,GAAG,GAAGhV,MAAM,CAAC6F,YAAY,CAAC,CAAC;IACjC,MAAMxK,CAAC,GAAGyL,GAAG,GAAGsH,SAAS,GAAG,CAACA,SAAS;IACtC,IAAIjB,KAAK,KAAK,CAAC,EAAE;MACf,IAAInG,SAAS,EAAE;QACbhH,MAAM,CAACuD,SAAS,CAAC1G,KAAK,CAAC2Z,cAAc,GAAG,MAAM;QAC9CxW,MAAM,CAACyW,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAIzP,SAAS,IAAI,CAAChH,MAAM,CAAC0W,yBAAyB,IAAI1W,MAAM,CAAC2B,MAAM,CAACgV,YAAY,GAAG,CAAC,EAAE;QACpF3W,MAAM,CAAC0W,yBAAyB,GAAG,IAAI;QACvC/V,qBAAqB,CAAC,MAAM;UAC1B4C,SAAS,CAACyR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG3Z,CAAC;QACjD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLkI,SAAS,CAACyR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG3Z,CAAC;MACjD;MACA,IAAI2L,SAAS,EAAE;QACbrG,qBAAqB,CAAC,MAAM;UAC1BX,MAAM,CAACuD,SAAS,CAAC1G,KAAK,CAAC2Z,cAAc,GAAG,EAAE;UAC1CxW,MAAM,CAACyW,iBAAiB,GAAG,KAAK;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACzW,MAAM,CAACzD,OAAO,CAACI,YAAY,EAAE;QAChCrB,oBAAoB,CAAC;UACnB0E,MAAM;UACNiV,cAAc,EAAE5Z,CAAC;UACjB6Z,IAAI,EAAEF,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAzR,SAAS,CAAC4R,QAAQ,CAAC;QACjB,CAACH,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG3Z,CAAC;QACzB+Z,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA,MAAMxW,OAAO,GAAGiB,UAAU,CAAC,CAAC;EAC5B,MAAMd,QAAQ,GAAGH,OAAO,CAACG,QAAQ;EACjC,IAAIiI,SAAS,IAAI,CAACiP,OAAO,IAAIlX,QAAQ,IAAIiB,MAAM,CAAC8C,SAAS,EAAE;IACzD9C,MAAM,CAACiH,OAAO,CAAC6M,MAAM,CAAC,KAAK,EAAE,KAAK,EAAEpI,UAAU,CAAC;EACjD;EACA1L,MAAM,CAACqN,aAAa,CAACF,KAAK,CAAC;EAC3BnN,MAAM,CAACmU,YAAY,CAAC/F,SAAS,CAAC;EAC9BpO,MAAM,CAACyS,iBAAiB,CAAC/G,UAAU,CAAC;EACpC1L,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;EAC5BtQ,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEiN,KAAK,EAAEyH,QAAQ,CAAC;EACrD5U,MAAM,CAAC8V,eAAe,CAACpB,YAAY,EAAEiB,SAAS,CAAC;EAC/C,IAAIxI,KAAK,KAAK,CAAC,EAAE;IACfnN,MAAM,CAACsV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;EAC/C,CAAC,MAAM,IAAI,CAAC3V,MAAM,CAAC6U,SAAS,EAAE;IAC5B7U,MAAM,CAAC6U,SAAS,GAAG,IAAI;IACvB,IAAI,CAAC7U,MAAM,CAAC4W,6BAA6B,EAAE;MACzC5W,MAAM,CAAC4W,6BAA6B,GAAG,SAAStB,aAAaA,CAAC7a,CAAC,EAAE;QAC/D,IAAI,CAACuF,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;QACjC,IAAI7F,CAAC,CAACyG,MAAM,KAAK,IAAI,EAAE;QACvBlB,MAAM,CAACuD,SAAS,CAACzB,mBAAmB,CAAC,eAAe,EAAE9B,MAAM,CAAC4W,6BAA6B,CAAC;QAC3F5W,MAAM,CAAC4W,6BAA6B,GAAG,IAAI;QAC3C,OAAO5W,MAAM,CAAC4W,6BAA6B;QAC3C5W,MAAM,CAACsV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;MAC/C,CAAC;IACH;IACA3V,MAAM,CAACuD,SAAS,CAAC1B,gBAAgB,CAAC,eAAe,EAAE7B,MAAM,CAAC4W,6BAA6B,CAAC;EAC1F;EACA,OAAO,IAAI;AACb;AAEA,SAASC,WAAWA,CAAC7R,KAAK,EAAEmI,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAE;EACzD,IAAI5P,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAI0P,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI,OAAO1P,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM8R,aAAa,GAAG/Q,QAAQ,CAACf,KAAK,EAAE,EAAE,CAAC;IACzCA,KAAK,GAAG8R,aAAa;EACvB;EACA,MAAM9W,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO6M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,MAAMtE,WAAW,GAAG7I,MAAM,CAAC8I,IAAI,IAAI9I,MAAM,CAAC2B,MAAM,CAACmH,IAAI,IAAI9I,MAAM,CAAC2B,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;EACpF,IAAIgO,QAAQ,GAAG/R,KAAK;EACpB,IAAIhF,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,EAAE;IACtB,IAAInL,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;MACnD;MACA6P,QAAQ,GAAGA,QAAQ,GAAG/W,MAAM,CAACiH,OAAO,CAACqE,YAAY;IACnD,CAAC,MAAM;MACL,IAAI0L,gBAAgB;MACpB,IAAInO,WAAW,EAAE;QACf,MAAM6C,UAAU,GAAGqL,QAAQ,GAAG/W,MAAM,CAAC2B,MAAM,CAACmH,IAAI,CAACC,IAAI;QACrDiO,gBAAgB,GAAGhX,MAAM,CAACoH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKzH,UAAU,CAAC,CAACmF,MAAM;MAC7H,CAAC,MAAM;QACLmG,gBAAgB,GAAGhX,MAAM,CAACuN,mBAAmB,CAACwJ,QAAQ,CAAC;MACzD;MACA,MAAME,IAAI,GAAGpO,WAAW,GAAGyB,IAAI,CAACe,IAAI,CAACrL,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGzC,MAAM,CAAC2B,MAAM,CAACmH,IAAI,CAACC,IAAI,CAAC,GAAG/I,MAAM,CAACoH,MAAM,CAAC3E,MAAM;MAC3G,MAAM;QACJkG;MACF,CAAC,GAAG3I,MAAM,CAAC2B,MAAM;MACjB,IAAIyH,aAAa,GAAGpJ,MAAM,CAAC2B,MAAM,CAACyH,aAAa;MAC/C,IAAIA,aAAa,KAAK,MAAM,EAAE;QAC5BA,aAAa,GAAGpJ,MAAM,CAACgS,oBAAoB,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL5I,aAAa,GAAGkB,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAACxG,MAAM,CAAC2B,MAAM,CAACyH,aAAa,EAAE,EAAE,CAAC,CAAC;QACtE,IAAIT,cAAc,IAAIS,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;UAC7CA,aAAa,GAAGA,aAAa,GAAG,CAAC;QACnC;MACF;MACA,IAAI8N,WAAW,GAAGD,IAAI,GAAGD,gBAAgB,GAAG5N,aAAa;MACzD,IAAIT,cAAc,EAAE;QAClBuO,WAAW,GAAGA,WAAW,IAAIF,gBAAgB,GAAG1M,IAAI,CAACe,IAAI,CAACjC,aAAa,GAAG,CAAC,CAAC;MAC9E;MACA,IAAIwL,QAAQ,IAAIjM,cAAc,IAAI3I,MAAM,CAAC2B,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAI,CAACP,WAAW,EAAE;QACxFqO,WAAW,GAAG,KAAK;MACrB;MACA,IAAIA,WAAW,EAAE;QACf,MAAMvB,SAAS,GAAGhN,cAAc,GAAGqO,gBAAgB,GAAGhX,MAAM,CAACyN,WAAW,GAAG,MAAM,GAAG,MAAM,GAAGuJ,gBAAgB,GAAGhX,MAAM,CAACyN,WAAW,GAAG,CAAC,GAAGzN,MAAM,CAAC2B,MAAM,CAACyH,aAAa,GAAG,MAAM,GAAG,MAAM;QACtLpJ,MAAM,CAACmX,OAAO,CAAC;UACbxB,SAAS;UACTK,OAAO,EAAE,IAAI;UACb9C,gBAAgB,EAAEyC,SAAS,KAAK,MAAM,GAAGqB,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGC,IAAI,GAAG,CAAC;UAC3FG,cAAc,EAAEzB,SAAS,KAAK,MAAM,GAAG3V,MAAM,CAACsS,SAAS,GAAGsB;QAC5D,CAAC,CAAC;MACJ;MACA,IAAI/K,WAAW,EAAE;QACf,MAAM6C,UAAU,GAAGqL,QAAQ,GAAG/W,MAAM,CAAC2B,MAAM,CAACmH,IAAI,CAACC,IAAI;QACrDgO,QAAQ,GAAG/W,MAAM,CAACoH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKzH,UAAU,CAAC,CAACmF,MAAM;MACrH,CAAC,MAAM;QACLkG,QAAQ,GAAG/W,MAAM,CAACuN,mBAAmB,CAACwJ,QAAQ,CAAC;MACjD;IACF;EACF;EACApW,qBAAqB,CAAC,MAAM;IAC1BX,MAAM,CAACgW,OAAO,CAACe,QAAQ,EAAE5J,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;EACzD,CAAC,CAAC;EACF,OAAO5U,MAAM;AACf;;AAEA;AACA,SAASqX,SAASA,CAAClK,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAE;EAChD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAM1U,MAAM,GAAG,IAAI;EACnB,MAAM;IACJkH,OAAO;IACPvF,MAAM;IACNkT;EACF,CAAC,GAAG7U,MAAM;EACV,IAAI,CAACkH,OAAO,IAAIlH,MAAM,CAACM,SAAS,EAAE,OAAON,MAAM;EAC/C,IAAI,OAAOmN,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,IAAImK,QAAQ,GAAG3V,MAAM,CAAC+I,cAAc;EACpC,IAAI/I,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIzH,MAAM,CAAC+I,cAAc,KAAK,CAAC,IAAI/I,MAAM,CAAC4V,kBAAkB,EAAE;IAC/FD,QAAQ,GAAGhN,IAAI,CAACO,GAAG,CAAC7K,MAAM,CAACgS,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;EACtE;EACA,MAAMwF,SAAS,GAAGxX,MAAM,CAACyN,WAAW,GAAG9L,MAAM,CAACiJ,kBAAkB,GAAG,CAAC,GAAG0M,QAAQ;EAC/E,MAAMtQ,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO;EAC1D,IAAIvF,MAAM,CAACwJ,IAAI,EAAE;IACf,IAAI0J,SAAS,IAAI,CAAC7N,SAAS,IAAIrF,MAAM,CAAC8V,mBAAmB,EAAE,OAAO,KAAK;IACvEzX,MAAM,CAACmX,OAAO,CAAC;MACbxB,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACA3V,MAAM,CAAC0X,WAAW,GAAG1X,MAAM,CAACuD,SAAS,CAACoU,UAAU;IAChD,IAAI3X,MAAM,CAACyN,WAAW,KAAKzN,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC,IAAId,MAAM,CAACiH,OAAO,EAAE;MACrEjI,qBAAqB,CAAC,MAAM;QAC1BX,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,GAAG+J,SAAS,EAAErK,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;MAC/E,CAAC,CAAC;MACF,OAAO,IAAI;IACb;EACF;EACA,IAAIjT,MAAM,CAAC0Q,MAAM,IAAIrS,MAAM,CAACwP,KAAK,EAAE;IACjC,OAAOxP,MAAM,CAACgW,OAAO,CAAC,CAAC,EAAE7I,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;EACzD;EACA,OAAO5U,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,GAAG+J,SAAS,EAAErK,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;AACtF;;AAEA;AACA,SAASgD,SAASA,CAACzK,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAE;EAChD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAM1U,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN4F,QAAQ;IACRC,UAAU;IACVX,YAAY;IACZK,OAAO;IACP2N;EACF,CAAC,GAAG7U,MAAM;EACV,IAAI,CAACkH,OAAO,IAAIlH,MAAM,CAACM,SAAS,EAAE,OAAON,MAAM;EAC/C,IAAI,OAAOmN,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,MAAMnG,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO;EAC1D,IAAIvF,MAAM,CAACwJ,IAAI,EAAE;IACf,IAAI0J,SAAS,IAAI,CAAC7N,SAAS,IAAIrF,MAAM,CAAC8V,mBAAmB,EAAE,OAAO,KAAK;IACvEzX,MAAM,CAACmX,OAAO,CAAC;MACbxB,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACA3V,MAAM,CAAC0X,WAAW,GAAG1X,MAAM,CAACuD,SAAS,CAACoU,UAAU;EAClD;EACA,MAAMvJ,SAAS,GAAGvH,YAAY,GAAG7G,MAAM,CAACoO,SAAS,GAAG,CAACpO,MAAM,CAACoO,SAAS;EACrE,SAASyJ,SAASA,CAACC,GAAG,EAAE;IACtB,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAO,CAACxN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAACqN,GAAG,CAAC,CAAC;IAC9C,OAAOxN,IAAI,CAACC,KAAK,CAACuN,GAAG,CAAC;EACxB;EACA,MAAM5B,mBAAmB,GAAG2B,SAAS,CAACzJ,SAAS,CAAC;EAChD,MAAM2J,kBAAkB,GAAGxQ,QAAQ,CAACjI,GAAG,CAACwY,GAAG,IAAID,SAAS,CAACC,GAAG,CAAC,CAAC;EAC9D,MAAME,UAAU,GAAGrW,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO;EAC7D,IAAIgR,QAAQ,GAAG3Q,QAAQ,CAACwQ,kBAAkB,CAACxZ,OAAO,CAAC2X,mBAAmB,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,OAAOgC,QAAQ,KAAK,WAAW,KAAKvW,MAAM,CAACiH,OAAO,IAAIoP,UAAU,CAAC,EAAE;IACrE,IAAIG,aAAa;IACjB5Q,QAAQ,CAACzG,OAAO,CAAC,CAACiL,IAAI,EAAEI,SAAS,KAAK;MACpC,IAAI+J,mBAAmB,IAAInK,IAAI,EAAE;QAC/B;QACAoM,aAAa,GAAGhM,SAAS;MAC3B;IACF,CAAC,CAAC;IACF,IAAI,OAAOgM,aAAa,KAAK,WAAW,EAAE;MACxCD,QAAQ,GAAGF,UAAU,GAAGzQ,QAAQ,CAAC4Q,aAAa,CAAC,GAAG5Q,QAAQ,CAAC4Q,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAGA,aAAa,CAAC;IACnH;EACF;EACA,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAI,OAAOF,QAAQ,KAAK,WAAW,EAAE;IACnCE,SAAS,GAAG5Q,UAAU,CAACjJ,OAAO,CAAC2Z,QAAQ,CAAC;IACxC,IAAIE,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGpY,MAAM,CAACyN,WAAW,GAAG,CAAC;IACrD,IAAI9L,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIzH,MAAM,CAAC+I,cAAc,KAAK,CAAC,IAAI/I,MAAM,CAAC4V,kBAAkB,EAAE;MAC/Fa,SAAS,GAAGA,SAAS,GAAGpY,MAAM,CAACgS,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;MACzEoG,SAAS,GAAG9N,IAAI,CAACO,GAAG,CAACuN,SAAS,EAAE,CAAC,CAAC;IACpC;EACF;EACA,IAAIzW,MAAM,CAAC0Q,MAAM,IAAIrS,MAAM,CAACuP,WAAW,EAAE;IACvC,MAAM8I,SAAS,GAAGrY,MAAM,CAAC2B,MAAM,CAACsF,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,IAAIlH,MAAM,CAACiH,OAAO,GAAGjH,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAG,CAAC,GAAGzC,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC;IACxJ,OAAOzC,MAAM,CAACgW,OAAO,CAACqC,SAAS,EAAElL,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;EACjE,CAAC,MAAM,IAAIjT,MAAM,CAACwJ,IAAI,IAAInL,MAAM,CAACyN,WAAW,KAAK,CAAC,IAAI9L,MAAM,CAACiH,OAAO,EAAE;IACpEjI,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAACgW,OAAO,CAACoC,SAAS,EAAEjL,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;IAC1D,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACA,OAAO5U,MAAM,CAACgW,OAAO,CAACoC,SAAS,EAAEjL,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;AACjE;;AAEA;AACA,SAAS0D,UAAUA,CAACnL,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAE;EACjD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAM1U,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO6M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,OAAOnN,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,EAAEN,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;AAC1E;;AAEA;AACA,SAAS2D,cAAcA,CAACpL,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,EAAE4D,SAAS,EAAE;EAChE,IAAI9D,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI8D,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,GAAG;EACjB;EACA,MAAMxY,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO6M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGnN,MAAM,CAAC2B,MAAM,CAACwL,KAAK;EAC7B;EACA,IAAInI,KAAK,GAAGhF,MAAM,CAACyN,WAAW;EAC9B,MAAMuF,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAC3K,MAAM,CAAC2B,MAAM,CAACiJ,kBAAkB,EAAE5F,KAAK,CAAC;EAC9D,MAAMmH,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACvF,KAAK,GAAGgO,IAAI,IAAIhT,MAAM,CAAC2B,MAAM,CAAC+I,cAAc,CAAC;EAClF,MAAM0D,SAAS,GAAGpO,MAAM,CAAC6G,YAAY,GAAG7G,MAAM,CAACoO,SAAS,GAAG,CAACpO,MAAM,CAACoO,SAAS;EAC5E,IAAIA,SAAS,IAAIpO,MAAM,CAACuH,QAAQ,CAAC4E,SAAS,CAAC,EAAE;IAC3C;IACA;IACA,MAAMsM,WAAW,GAAGzY,MAAM,CAACuH,QAAQ,CAAC4E,SAAS,CAAC;IAC9C,MAAMuM,QAAQ,GAAG1Y,MAAM,CAACuH,QAAQ,CAAC4E,SAAS,GAAG,CAAC,CAAC;IAC/C,IAAIiC,SAAS,GAAGqK,WAAW,GAAG,CAACC,QAAQ,GAAGD,WAAW,IAAID,SAAS,EAAE;MAClExT,KAAK,IAAIhF,MAAM,CAAC2B,MAAM,CAAC+I,cAAc;IACvC;EACF,CAAC,MAAM;IACL;IACA;IACA,MAAMwN,QAAQ,GAAGlY,MAAM,CAACuH,QAAQ,CAAC4E,SAAS,GAAG,CAAC,CAAC;IAC/C,MAAMsM,WAAW,GAAGzY,MAAM,CAACuH,QAAQ,CAAC4E,SAAS,CAAC;IAC9C,IAAIiC,SAAS,GAAG8J,QAAQ,IAAI,CAACO,WAAW,GAAGP,QAAQ,IAAIM,SAAS,EAAE;MAChExT,KAAK,IAAIhF,MAAM,CAAC2B,MAAM,CAAC+I,cAAc;IACvC;EACF;EACA1F,KAAK,GAAGsF,IAAI,CAACO,GAAG,CAAC7F,KAAK,EAAE,CAAC,CAAC;EAC1BA,KAAK,GAAGsF,IAAI,CAACK,GAAG,CAAC3F,KAAK,EAAEhF,MAAM,CAACwH,UAAU,CAAC/E,MAAM,GAAG,CAAC,CAAC;EACrD,OAAOzC,MAAM,CAACgW,OAAO,CAAChR,KAAK,EAAEmI,KAAK,EAAEuH,YAAY,EAAEE,QAAQ,CAAC;AAC7D;AAEA,SAASf,mBAAmBA,CAAA,EAAG;EAC7B,MAAM7T,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,MAAM;IACJqB,MAAM;IACNgF;EACF,CAAC,GAAG3G,MAAM;EACV,MAAMoJ,aAAa,GAAGzH,MAAM,CAACyH,aAAa,KAAK,MAAM,GAAGpJ,MAAM,CAACgS,oBAAoB,CAAC,CAAC,GAAGrQ,MAAM,CAACyH,aAAa;EAC5G,IAAIuP,YAAY,GAAG3Y,MAAM,CAAC2T,YAAY;EACtC,IAAIrB,SAAS;EACb,MAAMlB,aAAa,GAAGpR,MAAM,CAAC8C,SAAS,wBAAAtE,MAAA,CAAwBmD,MAAM,CAAC0F,UAAU,CAAE;EACjF,IAAI1F,MAAM,CAACwJ,IAAI,EAAE;IACf,IAAInL,MAAM,CAAC6U,SAAS,EAAE;IACtBvC,SAAS,GAAGvM,QAAQ,CAAC/F,MAAM,CAAC0T,YAAY,CAACP,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACrF,IAAIxR,MAAM,CAACgH,cAAc,EAAE;MACzB,IAAIgQ,YAAY,GAAG3Y,MAAM,CAAC4Y,YAAY,GAAGxP,aAAa,GAAG,CAAC,IAAIuP,YAAY,GAAG3Y,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGzC,MAAM,CAAC4Y,YAAY,GAAGxP,aAAa,GAAG,CAAC,EAAE;QAC3IpJ,MAAM,CAACmX,OAAO,CAAC,CAAC;QAChBwB,YAAY,GAAG3Y,MAAM,CAAC6Y,aAAa,CAACne,eAAe,CAACiM,QAAQ,KAAAnI,MAAA,CAAK4S,aAAa,iCAAA5S,MAAA,CAA6B8T,SAAS,QAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7H9W,QAAQ,CAAC,MAAM;UACbwE,MAAM,CAACgW,OAAO,CAAC2C,YAAY,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL3Y,MAAM,CAACgW,OAAO,CAAC2C,YAAY,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIA,YAAY,GAAG3Y,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG2G,aAAa,EAAE;MAC9DpJ,MAAM,CAACmX,OAAO,CAAC,CAAC;MAChBwB,YAAY,GAAG3Y,MAAM,CAAC6Y,aAAa,CAACne,eAAe,CAACiM,QAAQ,KAAAnI,MAAA,CAAK4S,aAAa,iCAAA5S,MAAA,CAA6B8T,SAAS,QAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7H9W,QAAQ,CAAC,MAAM;QACbwE,MAAM,CAACgW,OAAO,CAAC2C,YAAY,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL3Y,MAAM,CAACgW,OAAO,CAAC2C,YAAY,CAAC;IAC9B;EACF,CAAC,MAAM;IACL3Y,MAAM,CAACgW,OAAO,CAAC2C,YAAY,CAAC;EAC9B;AACF;AAEA,IAAIlP,KAAK,GAAG;EACVuM,OAAO;EACPa,WAAW;EACXQ,SAAS;EACTO,SAAS;EACTU,UAAU;EACVC,cAAc;EACd1E;AACF,CAAC;AAED,SAASiF,UAAUA,CAAC1B,cAAc,EAAEnB,OAAO,EAAE;EAC3C,MAAMjW,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNgF;EACF,CAAC,GAAG3G,MAAM;EACV,IAAI,CAAC2B,MAAM,CAACwJ,IAAI,IAAInL,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;EACrE,MAAM8B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAM5B,MAAM,GAAG1M,eAAe,CAACiM,QAAQ,MAAAnI,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC;IAC/ED,MAAM,CAACtG,OAAO,CAAC,CAACK,EAAE,EAAE6D,KAAK,KAAK;MAC5B7D,EAAE,CAAC4X,YAAY,CAAC,yBAAyB,EAAE/T,KAAK,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EACD,MAAM6D,WAAW,GAAG7I,MAAM,CAAC8I,IAAI,IAAInH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,MAAM2B,cAAc,GAAG/I,MAAM,CAAC+I,cAAc,IAAI7B,WAAW,GAAGlH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EACnF,MAAMiQ,eAAe,GAAGhZ,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGiI,cAAc,KAAK,CAAC;EACnE,MAAMuO,cAAc,GAAGpQ,WAAW,IAAI7I,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGd,MAAM,CAACmH,IAAI,CAACC,IAAI,KAAK,CAAC;EACnF,MAAMmQ,cAAc,GAAGC,cAAc,IAAI;IACvC,KAAK,IAAI9V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8V,cAAc,EAAE9V,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMiF,OAAO,GAAGtI,MAAM,CAAC8C,SAAS,GAAGlH,aAAa,CAAC,cAAc,EAAE,CAAC+F,MAAM,CAACyX,eAAe,CAAC,CAAC,GAAGxd,aAAa,CAAC,KAAK,EAAE,CAAC+F,MAAM,CAAC0F,UAAU,EAAE1F,MAAM,CAACyX,eAAe,CAAC,CAAC;MAC9JpZ,MAAM,CAAC2G,QAAQ,CAAC0S,MAAM,CAAC/Q,OAAO,CAAC;IACjC;EACF,CAAC;EACD,IAAI0Q,eAAe,EAAE;IACnB,IAAIrX,MAAM,CAAC2X,kBAAkB,EAAE;MAC7B,MAAMC,WAAW,GAAG7O,cAAc,GAAG1K,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGiI,cAAc;MAC1EwO,cAAc,CAACK,WAAW,CAAC;MAC3BvZ,MAAM,CAACwZ,YAAY,CAAC,CAAC;MACrBxZ,MAAM,CAACoG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM;MACL1K,WAAW,CAAC,iLAAiL,CAAC;IAChM;IACAsN,UAAU,CAAC,CAAC;EACd,CAAC,MAAM,IAAIiQ,cAAc,EAAE;IACzB,IAAItX,MAAM,CAAC2X,kBAAkB,EAAE;MAC7B,MAAMC,WAAW,GAAG5X,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG/I,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGd,MAAM,CAACmH,IAAI,CAACC,IAAI;MAC9EmQ,cAAc,CAACK,WAAW,CAAC;MAC3BvZ,MAAM,CAACwZ,YAAY,CAAC,CAAC;MACrBxZ,MAAM,CAACoG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM;MACL1K,WAAW,CAAC,4KAA4K,CAAC;IAC3L;IACAsN,UAAU,CAAC,CAAC;EACd,CAAC,MAAM;IACLA,UAAU,CAAC,CAAC;EACd;EACAhJ,MAAM,CAACmX,OAAO,CAAC;IACbC,cAAc;IACdzB,SAAS,EAAEhU,MAAM,CAACgH,cAAc,GAAGiL,SAAS,GAAG,MAAM;IACrDqC;EACF,CAAC,CAAC;AACJ;AAEA,SAASkB,OAAOA,CAACha,KAAK,EAAE;EACtB,IAAI;IACFia,cAAc;IACdpB,OAAO,GAAG,IAAI;IACdL,SAAS;IACTxB,YAAY;IACZjB,gBAAgB;IAChB+C,OAAO;IACP7B,YAAY;IACZqF;EACF,CAAC,GAAGtc,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAM6C,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,EAAE;EACzBnL,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;EAC5B,MAAM;IACJkH,MAAM;IACNkP,cAAc;IACdD,cAAc;IACd1P,QAAQ;IACRhF;EACF,CAAC,GAAG3B,MAAM;EACV,MAAM;IACJ2I,cAAc;IACdgO;EACF,CAAC,GAAGhV,MAAM;EACV3B,MAAM,CAACsW,cAAc,GAAG,IAAI;EAC5BtW,MAAM,CAACqW,cAAc,GAAG,IAAI;EAC5B,IAAIrW,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;IAC5C,IAAI8O,OAAO,EAAE;MACX,IAAI,CAACrU,MAAM,CAACgH,cAAc,IAAI3I,MAAM,CAACmM,SAAS,KAAK,CAAC,EAAE;QACpDnM,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC9D,CAAC,MAAM,IAAId,MAAM,CAACgH,cAAc,IAAI3I,MAAM,CAACmM,SAAS,GAAGxK,MAAM,CAACyH,aAAa,EAAE;QAC3EpJ,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAGzC,MAAM,CAACmM,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjF,CAAC,MAAM,IAAInM,MAAM,CAACmM,SAAS,KAAKnM,MAAM,CAACuH,QAAQ,CAAC9E,MAAM,GAAG,CAAC,EAAE;QAC1DzC,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACiH,OAAO,CAACqE,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;IACF;IACAtL,MAAM,CAACsW,cAAc,GAAGA,cAAc;IACtCtW,MAAM,CAACqW,cAAc,GAAGA,cAAc;IACtCrW,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;IACtB;EACF;EACA,IAAIkJ,aAAa,GAAGzH,MAAM,CAACyH,aAAa;EACxC,IAAIA,aAAa,KAAK,MAAM,EAAE;IAC5BA,aAAa,GAAGpJ,MAAM,CAACgS,oBAAoB,CAAC,CAAC;EAC/C,CAAC,MAAM;IACL5I,aAAa,GAAGkB,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAAC7E,MAAM,CAACyH,aAAa,EAAE,EAAE,CAAC,CAAC;IAC/D,IAAIT,cAAc,IAAIS,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;MAC7CA,aAAa,GAAGA,aAAa,GAAG,CAAC;IACnC;EACF;EACA,MAAMsB,cAAc,GAAG/I,MAAM,CAAC4V,kBAAkB,GAAGnO,aAAa,GAAGzH,MAAM,CAAC+I,cAAc;EACxF,IAAIkO,YAAY,GAAGlO,cAAc;EACjC,IAAIkO,YAAY,GAAGlO,cAAc,KAAK,CAAC,EAAE;IACvCkO,YAAY,IAAIlO,cAAc,GAAGkO,YAAY,GAAGlO,cAAc;EAChE;EACAkO,YAAY,IAAIjX,MAAM,CAAC+X,oBAAoB;EAC3C1Z,MAAM,CAAC4Y,YAAY,GAAGA,YAAY;EAClC,MAAM/P,WAAW,GAAG7I,MAAM,CAAC8I,IAAI,IAAInH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,IAAI3B,MAAM,CAAC3E,MAAM,GAAG2G,aAAa,GAAGwP,YAAY,IAAI5Y,MAAM,CAAC2B,MAAM,CAACmJ,MAAM,KAAK,OAAO,IAAI1D,MAAM,CAAC3E,MAAM,GAAG2G,aAAa,GAAGwP,YAAY,GAAG,CAAC,EAAE;IACxIld,WAAW,CAAC,0OAA0O,CAAC;EACzP,CAAC,MAAM,IAAImN,WAAW,IAAIlH,MAAM,CAACmH,IAAI,CAAC6Q,IAAI,KAAK,KAAK,EAAE;IACpDje,WAAW,CAAC,yEAAyE,CAAC;EACxF;EACA,MAAMke,oBAAoB,GAAG,EAAE;EAC/B,MAAMC,mBAAmB,GAAG,EAAE;EAC9B,MAAM5C,IAAI,GAAGpO,WAAW,GAAGyB,IAAI,CAACe,IAAI,CAACjE,MAAM,CAAC3E,MAAM,GAAGd,MAAM,CAACmH,IAAI,CAACC,IAAI,CAAC,GAAG3B,MAAM,CAAC3E,MAAM;EACtF,MAAMqX,iBAAiB,GAAG7D,OAAO,IAAIgB,IAAI,GAAGN,YAAY,GAAGvN,aAAa,IAAI,CAACT,cAAc;EAC3F,IAAI8E,WAAW,GAAGqM,iBAAiB,GAAGnD,YAAY,GAAG3W,MAAM,CAACyN,WAAW;EACvE,IAAI,OAAOyF,gBAAgB,KAAK,WAAW,EAAE;IAC3CA,gBAAgB,GAAGlT,MAAM,CAAC6Y,aAAa,CAACzR,MAAM,CAACwJ,IAAI,CAACzP,EAAE,IAAIA,EAAE,CAAC0L,SAAS,CAACC,QAAQ,CAACnL,MAAM,CAACmP,gBAAgB,CAAC,CAAC,CAAC;EAC5G,CAAC,MAAM;IACLrD,WAAW,GAAGyF,gBAAgB;EAChC;EACA,MAAM6G,MAAM,GAAGpE,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,MAAMqE,MAAM,GAAGrE,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,IAAIsE,eAAe,GAAG,CAAC;EACvB,IAAIC,cAAc,GAAG,CAAC;EACtB,MAAMC,cAAc,GAAGtR,WAAW,GAAGzB,MAAM,CAAC8L,gBAAgB,CAAC,CAACrC,MAAM,GAAGqC,gBAAgB;EACvF,MAAMkH,uBAAuB,GAAGD,cAAc,IAAIxR,cAAc,IAAI,OAAOwL,YAAY,KAAK,WAAW,GAAG,CAAC/K,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;EACvI;EACA,IAAIgR,uBAAuB,GAAGxB,YAAY,EAAE;IAC1CqB,eAAe,GAAG3P,IAAI,CAACO,GAAG,CAAC+N,YAAY,GAAGwB,uBAAuB,EAAE1P,cAAc,CAAC;IAClF,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuV,YAAY,GAAGwB,uBAAuB,EAAE/W,CAAC,IAAI,CAAC,EAAE;MAClE,MAAM2B,KAAK,GAAG3B,CAAC,GAAGiH,IAAI,CAACC,KAAK,CAAClH,CAAC,GAAG4T,IAAI,CAAC,GAAGA,IAAI;MAC7C,IAAIpO,WAAW,EAAE;QACf,MAAMwR,iBAAiB,GAAGpD,IAAI,GAAGjS,KAAK,GAAG,CAAC;QAC1C,KAAK,IAAI3B,CAAC,GAAG+D,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAEY,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC9C,IAAI+D,MAAM,CAAC/D,CAAC,CAAC,CAACwN,MAAM,KAAKwJ,iBAAiB,EAAET,oBAAoB,CAAC5W,IAAI,CAACK,CAAC,CAAC;QAC1E;QACA;QACA;QACA;MACF,CAAC,MAAM;QACLuW,oBAAoB,CAAC5W,IAAI,CAACiU,IAAI,GAAGjS,KAAK,GAAG,CAAC,CAAC;MAC7C;IACF;EACF,CAAC,MAAM,IAAIoV,uBAAuB,GAAGhR,aAAa,GAAG6N,IAAI,GAAG2B,YAAY,EAAE;IACxEsB,cAAc,GAAG5P,IAAI,CAACO,GAAG,CAACuP,uBAAuB,IAAInD,IAAI,GAAG2B,YAAY,GAAG,CAAC,CAAC,EAAElO,cAAc,CAAC;IAC9F,IAAIoP,iBAAiB,EAAE;MACrBI,cAAc,GAAG5P,IAAI,CAACO,GAAG,CAACqP,cAAc,EAAE9Q,aAAa,GAAG6N,IAAI,GAAGN,YAAY,GAAG,CAAC,CAAC;IACpF;IACA,KAAK,IAAItT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6W,cAAc,EAAE7W,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAM2B,KAAK,GAAG3B,CAAC,GAAGiH,IAAI,CAACC,KAAK,CAAClH,CAAC,GAAG4T,IAAI,CAAC,GAAGA,IAAI;MAC7C,IAAIpO,WAAW,EAAE;QACfzB,MAAM,CAACtG,OAAO,CAAC,CAAC2I,KAAK,EAAEiC,UAAU,KAAK;UACpC,IAAIjC,KAAK,CAACoH,MAAM,KAAK7L,KAAK,EAAE6U,mBAAmB,CAAC7W,IAAI,CAAC0I,UAAU,CAAC;QAClE,CAAC,CAAC;MACJ,CAAC,MAAM;QACLmO,mBAAmB,CAAC7W,IAAI,CAACgC,KAAK,CAAC;MACjC;IACF;EACF;EACAhF,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACjC7B,qBAAqB,CAAC,MAAM;IAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;EACpC,CAAC,CAAC;EACF,IAAIxC,MAAM,CAAC2B,MAAM,CAACmJ,MAAM,KAAK,OAAO,IAAI1D,MAAM,CAAC3E,MAAM,GAAG2G,aAAa,GAAGwP,YAAY,GAAG,CAAC,EAAE;IACxF,IAAIiB,mBAAmB,CAAC3a,QAAQ,CAACgU,gBAAgB,CAAC,EAAE;MAClD2G,mBAAmB,CAACnW,MAAM,CAACmW,mBAAmB,CAACtb,OAAO,CAAC2U,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9E;IACA,IAAI0G,oBAAoB,CAAC1a,QAAQ,CAACgU,gBAAgB,CAAC,EAAE;MACnD0G,oBAAoB,CAAClW,MAAM,CAACkW,oBAAoB,CAACrb,OAAO,CAAC2U,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAChF;EACF;EACA,IAAI8G,MAAM,EAAE;IACVJ,oBAAoB,CAAC9Y,OAAO,CAACkE,KAAK,IAAI;MACpCoC,MAAM,CAACpC,KAAK,CAAC,CAACsV,iBAAiB,GAAG,IAAI;MACtC3T,QAAQ,CAAC4T,OAAO,CAACnT,MAAM,CAACpC,KAAK,CAAC,CAAC;MAC/BoC,MAAM,CAACpC,KAAK,CAAC,CAACsV,iBAAiB,GAAG,KAAK;IACzC,CAAC,CAAC;EACJ;EACA,IAAIP,MAAM,EAAE;IACVF,mBAAmB,CAAC/Y,OAAO,CAACkE,KAAK,IAAI;MACnCoC,MAAM,CAACpC,KAAK,CAAC,CAACsV,iBAAiB,GAAG,IAAI;MACtC3T,QAAQ,CAAC0S,MAAM,CAACjS,MAAM,CAACpC,KAAK,CAAC,CAAC;MAC9BoC,MAAM,CAACpC,KAAK,CAAC,CAACsV,iBAAiB,GAAG,KAAK;IACzC,CAAC,CAAC;EACJ;EACAta,MAAM,CAACwZ,YAAY,CAAC,CAAC;EACrB,IAAI7X,MAAM,CAACyH,aAAa,KAAK,MAAM,EAAE;IACnCpJ,MAAM,CAACoG,YAAY,CAAC,CAAC;EACvB,CAAC,MAAM,IAAIyC,WAAW,KAAK+Q,oBAAoB,CAACnX,MAAM,GAAG,CAAC,IAAIuX,MAAM,IAAIH,mBAAmB,CAACpX,MAAM,GAAG,CAAC,IAAIsX,MAAM,CAAC,EAAE;IACjH/Z,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAAC,CAAC2I,KAAK,EAAEiC,UAAU,KAAK;MAC3C1L,MAAM,CAAC8I,IAAI,CAACY,WAAW,CAACgC,UAAU,EAAEjC,KAAK,EAAEzJ,MAAM,CAACoH,MAAM,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,IAAIzF,MAAM,CAAC6K,mBAAmB,EAAE;IAC9BxM,MAAM,CAACyM,kBAAkB,CAAC,CAAC;EAC7B;EACA,IAAIuJ,OAAO,EAAE;IACX,IAAI4D,oBAAoB,CAACnX,MAAM,GAAG,CAAC,IAAIuX,MAAM,EAAE;MAC7C,IAAI,OAAO5C,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMoD,qBAAqB,GAAGxa,MAAM,CAACwH,UAAU,CAACiG,WAAW,CAAC;QAC5D,MAAMgN,iBAAiB,GAAGza,MAAM,CAACwH,UAAU,CAACiG,WAAW,GAAGwM,eAAe,CAAC;QAC1E,MAAMS,IAAI,GAAGD,iBAAiB,GAAGD,qBAAqB;QACtD,IAAIf,YAAY,EAAE;UAChBzZ,MAAM,CAACmU,YAAY,CAACnU,MAAM,CAACoO,SAAS,GAAGsM,IAAI,CAAC;QAC9C,CAAC,MAAM;UACL1a,MAAM,CAACgW,OAAO,CAACvI,WAAW,GAAGnD,IAAI,CAACe,IAAI,CAAC4O,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UACxE,IAAI9F,YAAY,EAAE;YAChBnU,MAAM,CAAC2a,eAAe,CAACC,cAAc,GAAG5a,MAAM,CAAC2a,eAAe,CAACC,cAAc,GAAGF,IAAI;YACpF1a,MAAM,CAAC2a,eAAe,CAACzG,gBAAgB,GAAGlU,MAAM,CAAC2a,eAAe,CAACzG,gBAAgB,GAAGwG,IAAI;UAC1F;QACF;MACF,CAAC,MAAM;QACL,IAAIvG,YAAY,EAAE;UAChB,MAAM0G,KAAK,GAAGhS,WAAW,GAAG+Q,oBAAoB,CAACnX,MAAM,GAAGd,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG6Q,oBAAoB,CAACnX,MAAM;UACxGzC,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,GAAGoN,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC1D7a,MAAM,CAAC2a,eAAe,CAACzG,gBAAgB,GAAGlU,MAAM,CAACoO,SAAS;QAC5D;MACF;IACF,CAAC,MAAM,IAAIyL,mBAAmB,CAACpX,MAAM,GAAG,CAAC,IAAIsX,MAAM,EAAE;MACnD,IAAI,OAAO3C,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMoD,qBAAqB,GAAGxa,MAAM,CAACwH,UAAU,CAACiG,WAAW,CAAC;QAC5D,MAAMgN,iBAAiB,GAAGza,MAAM,CAACwH,UAAU,CAACiG,WAAW,GAAGyM,cAAc,CAAC;QACzE,MAAMQ,IAAI,GAAGD,iBAAiB,GAAGD,qBAAqB;QACtD,IAAIf,YAAY,EAAE;UAChBzZ,MAAM,CAACmU,YAAY,CAACnU,MAAM,CAACoO,SAAS,GAAGsM,IAAI,CAAC;QAC9C,CAAC,MAAM;UACL1a,MAAM,CAACgW,OAAO,CAACvI,WAAW,GAAGyM,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC5D,IAAI/F,YAAY,EAAE;YAChBnU,MAAM,CAAC2a,eAAe,CAACC,cAAc,GAAG5a,MAAM,CAAC2a,eAAe,CAACC,cAAc,GAAGF,IAAI;YACpF1a,MAAM,CAAC2a,eAAe,CAACzG,gBAAgB,GAAGlU,MAAM,CAAC2a,eAAe,CAACzG,gBAAgB,GAAGwG,IAAI;UAC1F;QACF;MACF,CAAC,MAAM;QACL,MAAMG,KAAK,GAAGhS,WAAW,GAAGgR,mBAAmB,CAACpX,MAAM,GAAGd,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG8Q,mBAAmB,CAACpX,MAAM;QACtGzC,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,GAAGoN,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC5D;IACF;EACF;EACA7a,MAAM,CAACsW,cAAc,GAAGA,cAAc;EACtCtW,MAAM,CAACqW,cAAc,GAAGA,cAAc;EACtC,IAAIrW,MAAM,CAAC8a,UAAU,IAAI9a,MAAM,CAAC8a,UAAU,CAACC,OAAO,IAAI,CAAC3G,YAAY,EAAE;IACnE,MAAM4G,UAAU,GAAG;MACjB5D,cAAc;MACdzB,SAAS;MACTxB,YAAY;MACZjB,gBAAgB;MAChBkB,YAAY,EAAE;IAChB,CAAC;IACD,IAAI1P,KAAK,CAACY,OAAO,CAACtF,MAAM,CAAC8a,UAAU,CAACC,OAAO,CAAC,EAAE;MAC5C/a,MAAM,CAAC8a,UAAU,CAACC,OAAO,CAACja,OAAO,CAACnF,CAAC,IAAI;QACrC,IAAI,CAACA,CAAC,CAAC2E,SAAS,IAAI3E,CAAC,CAACgG,MAAM,CAACwJ,IAAI,EAAExP,CAAC,CAACwb,OAAO,CAAA8D,aAAA,CAAAA,aAAA,KACvCD,UAAU;UACbhF,OAAO,EAAEra,CAAC,CAACgG,MAAM,CAACyH,aAAa,KAAKzH,MAAM,CAACyH,aAAa,GAAG4M,OAAO,GAAG;QAAK,EAC3E,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhW,MAAM,CAAC8a,UAAU,CAACC,OAAO,YAAY/a,MAAM,CAACkb,WAAW,IAAIlb,MAAM,CAAC8a,UAAU,CAACC,OAAO,CAACpZ,MAAM,CAACwJ,IAAI,EAAE;MAC3GnL,MAAM,CAAC8a,UAAU,CAACC,OAAO,CAAC5D,OAAO,CAAA8D,aAAA,CAAAA,aAAA,KAC5BD,UAAU;QACbhF,OAAO,EAAEhW,MAAM,CAAC8a,UAAU,CAACC,OAAO,CAACpZ,MAAM,CAACyH,aAAa,KAAKzH,MAAM,CAACyH,aAAa,GAAG4M,OAAO,GAAG;MAAK,EACnG,CAAC;IACJ;EACF;EACAhW,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;AACxB;AAEA,SAASib,WAAWA,CAAA,EAAG;EACrB,MAAMnb,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNgF;EACF,CAAC,GAAG3G,MAAM;EACV,IAAI,CAAC2B,MAAM,CAACwJ,IAAI,IAAI,CAACxE,QAAQ,IAAI3G,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;EAClFlH,MAAM,CAACwZ,YAAY,CAAC,CAAC;EACrB,MAAM4B,cAAc,GAAG,EAAE;EACzBpb,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;IAC/B,MAAMtD,KAAK,GAAG,OAAOsD,OAAO,CAAC+S,gBAAgB,KAAK,WAAW,GAAG/S,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAG7K,OAAO,CAAC+S,gBAAgB;IAC9ID,cAAc,CAACpW,KAAK,CAAC,GAAGsD,OAAO;EACjC,CAAC,CAAC;EACFtI,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;IAC/BA,OAAO,CAACqJ,eAAe,CAAC,yBAAyB,CAAC;EACpD,CAAC,CAAC;EACFyJ,cAAc,CAACta,OAAO,CAACwH,OAAO,IAAI;IAChC3B,QAAQ,CAAC0S,MAAM,CAAC/Q,OAAO,CAAC;EAC1B,CAAC,CAAC;EACFtI,MAAM,CAACwZ,YAAY,CAAC,CAAC;EACrBxZ,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACsS,SAAS,EAAE,CAAC,CAAC;AACrC;AAEA,IAAInH,IAAI,GAAG;EACT2N,UAAU;EACV3B,OAAO;EACPgE;AACF,CAAC;AAED,SAASG,aAAaA,CAACC,MAAM,EAAE;EAC7B,MAAMvb,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAAC6Z,aAAa,IAAIxb,MAAM,CAAC2B,MAAM,CAAC2K,aAAa,IAAItM,MAAM,CAACyb,QAAQ,IAAIzb,MAAM,CAAC2B,MAAM,CAACiH,OAAO,EAAE;EAC7G,MAAMzH,EAAE,GAAGnB,MAAM,CAAC2B,MAAM,CAAC+Z,iBAAiB,KAAK,WAAW,GAAG1b,MAAM,CAACmB,EAAE,GAAGnB,MAAM,CAACuD,SAAS;EACzF,IAAIvD,MAAM,CAAC8C,SAAS,EAAE;IACpB9C,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACnC;EACArB,EAAE,CAACtE,KAAK,CAAC8e,MAAM,GAAG,MAAM;EACxBxa,EAAE,CAACtE,KAAK,CAAC8e,MAAM,GAAGJ,MAAM,GAAG,UAAU,GAAG,MAAM;EAC9C,IAAIvb,MAAM,CAAC8C,SAAS,EAAE;IACpBnC,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF;AAEA,SAASoZ,eAAeA,CAAA,EAAG;EACzB,MAAM5b,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAAC2B,MAAM,CAAC2K,aAAa,IAAItM,MAAM,CAACyb,QAAQ,IAAIzb,MAAM,CAAC2B,MAAM,CAACiH,OAAO,EAAE;IAC3E;EACF;EACA,IAAI5I,MAAM,CAAC8C,SAAS,EAAE;IACpB9C,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACnC;EACAxC,MAAM,CAACA,MAAM,CAAC2B,MAAM,CAAC+Z,iBAAiB,KAAK,WAAW,GAAG,IAAI,GAAG,WAAW,CAAC,CAAC7e,KAAK,CAAC8e,MAAM,GAAG,EAAE;EAC9F,IAAI3b,MAAM,CAAC8C,SAAS,EAAE;IACpBnC,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF;AAEA,IAAIqZ,UAAU,GAAG;EACfP,aAAa;EACbM;AACF,CAAC;;AAED;AACA,SAASE,cAAcA,CAACtL,QAAQ,EAAEuL,IAAI,EAAE;EACtC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,IAAI;EACb;EACA,SAASC,aAAaA,CAAC7a,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,IAAIA,EAAE,KAAK9G,WAAW,CAAC,CAAC,IAAI8G,EAAE,KAAKhH,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI;IAClE,IAAIgH,EAAE,CAAC8a,YAAY,EAAE9a,EAAE,GAAGA,EAAE,CAAC8a,YAAY;IACzC,MAAMC,KAAK,GAAG/a,EAAE,CAACkQ,OAAO,CAACb,QAAQ,CAAC;IAClC,IAAI,CAAC0L,KAAK,IAAI,CAAC/a,EAAE,CAACgb,WAAW,EAAE;MAC7B,OAAO,IAAI;IACb;IACA,OAAOD,KAAK,IAAIF,aAAa,CAAC7a,EAAE,CAACgb,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC;EACtD;EACA,OAAOJ,aAAa,CAACD,IAAI,CAAC;AAC5B;AACA,SAASM,gBAAgBA,CAACrc,MAAM,EAAEkE,KAAK,EAAEoY,MAAM,EAAE;EAC/C,MAAM7f,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAM;IACJwH;EACF,CAAC,GAAG3B,MAAM;EACV,MAAMuc,kBAAkB,GAAG5a,MAAM,CAAC4a,kBAAkB;EACpD,MAAMC,kBAAkB,GAAG7a,MAAM,CAAC6a,kBAAkB;EACpD,IAAID,kBAAkB,KAAKD,MAAM,IAAIE,kBAAkB,IAAIF,MAAM,IAAI7f,MAAM,CAACggB,UAAU,GAAGD,kBAAkB,CAAC,EAAE;IAC5G,IAAID,kBAAkB,KAAK,SAAS,EAAE;MACpCrY,KAAK,CAACwY,cAAc,CAAC,CAAC;MACtB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASC,YAAYA,CAACzY,KAAK,EAAE;EAC3B,MAAMlE,MAAM,GAAG,IAAI;EACnB,MAAMtD,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,IAAII,CAAC,GAAGyJ,KAAK;EACb,IAAIzJ,CAAC,CAACmiB,aAAa,EAAEniB,CAAC,GAAGA,CAAC,CAACmiB,aAAa;EACxC,MAAM1X,IAAI,GAAGlF,MAAM,CAAC2a,eAAe;EACnC,IAAIlgB,CAAC,CAACoiB,IAAI,KAAK,aAAa,EAAE;IAC5B,IAAI3X,IAAI,CAAC4X,SAAS,KAAK,IAAI,IAAI5X,IAAI,CAAC4X,SAAS,KAAKriB,CAAC,CAACqiB,SAAS,EAAE;MAC7D;IACF;IACA5X,IAAI,CAAC4X,SAAS,GAAGriB,CAAC,CAACqiB,SAAS;EAC9B,CAAC,MAAM,IAAIriB,CAAC,CAACoiB,IAAI,KAAK,YAAY,IAAIpiB,CAAC,CAACsiB,aAAa,CAACta,MAAM,KAAK,CAAC,EAAE;IAClEyC,IAAI,CAAC8X,OAAO,GAAGviB,CAAC,CAACsiB,aAAa,CAAC,CAAC,CAAC,CAACE,UAAU;EAC9C;EACA,IAAIxiB,CAAC,CAACoiB,IAAI,KAAK,YAAY,EAAE;IAC3B;IACAR,gBAAgB,CAACrc,MAAM,EAAEvF,CAAC,EAAEA,CAAC,CAACsiB,aAAa,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC;IACrD;EACF;EACA,MAAM;IACJvb,MAAM;IACNwb,OAAO;IACPjW;EACF,CAAC,GAAGlH,MAAM;EACV,IAAI,CAACkH,OAAO,EAAE;EACd,IAAI,CAACvF,MAAM,CAAC6Z,aAAa,IAAI/gB,CAAC,CAAC2iB,WAAW,KAAK,OAAO,EAAE;EACxD,IAAIpd,MAAM,CAAC6U,SAAS,IAAIlT,MAAM,CAACmT,8BAA8B,EAAE;IAC7D;EACF;EACA,IAAI,CAAC9U,MAAM,CAAC6U,SAAS,IAAIlT,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAACwJ,IAAI,EAAE;IACtDnL,MAAM,CAACmX,OAAO,CAAC,CAAC;EAClB;EACA,IAAIkG,QAAQ,GAAG5iB,CAAC,CAACyG,MAAM;EACvB,IAAIS,MAAM,CAAC+Z,iBAAiB,KAAK,SAAS,EAAE;IAC1C,IAAI,CAAC5f,gBAAgB,CAACuhB,QAAQ,EAAErd,MAAM,CAACuD,SAAS,CAAC,EAAE;EACrD;EACA,IAAI,OAAO,IAAI9I,CAAC,IAAIA,CAAC,CAAC6iB,KAAK,KAAK,CAAC,EAAE;EACnC,IAAI,QAAQ,IAAI7iB,CAAC,IAAIA,CAAC,CAAC8iB,MAAM,GAAG,CAAC,EAAE;EACnC,IAAIrY,IAAI,CAACsY,SAAS,IAAItY,IAAI,CAACuY,OAAO,EAAE;;EAEpC;EACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC/b,MAAM,CAACgc,cAAc,IAAIhc,MAAM,CAACgc,cAAc,KAAK,EAAE;EACpF;EACA,MAAMC,SAAS,GAAGnjB,CAAC,CAACojB,YAAY,GAAGpjB,CAAC,CAACojB,YAAY,CAAC,CAAC,GAAGpjB,CAAC,CAAC6Y,IAAI;EAC5D,IAAIoK,oBAAoB,IAAIjjB,CAAC,CAACyG,MAAM,IAAIzG,CAAC,CAACyG,MAAM,CAACuQ,UAAU,IAAImM,SAAS,EAAE;IACxEP,QAAQ,GAAGO,SAAS,CAAC,CAAC,CAAC;EACzB;EACA,MAAME,iBAAiB,GAAGnc,MAAM,CAACmc,iBAAiB,GAAGnc,MAAM,CAACmc,iBAAiB,OAAAtf,MAAA,CAAOmD,MAAM,CAACgc,cAAc,CAAE;EAC3G,MAAMI,cAAc,GAAG,CAAC,EAAEtjB,CAAC,CAACyG,MAAM,IAAIzG,CAAC,CAACyG,MAAM,CAACuQ,UAAU,CAAC;;EAE1D;EACA,IAAI9P,MAAM,CAACqc,SAAS,KAAKD,cAAc,GAAGjC,cAAc,CAACgC,iBAAiB,EAAET,QAAQ,CAAC,GAAGA,QAAQ,CAAChM,OAAO,CAACyM,iBAAiB,CAAC,CAAC,EAAE;IAC5H9d,MAAM,CAACie,UAAU,GAAG,IAAI;IACxB;EACF;EACA,IAAItc,MAAM,CAACuc,YAAY,EAAE;IACvB,IAAI,CAACb,QAAQ,CAAChM,OAAO,CAAC1P,MAAM,CAACuc,YAAY,CAAC,EAAE;EAC9C;EACAf,OAAO,CAACgB,QAAQ,GAAG1jB,CAAC,CAACyiB,KAAK;EAC1BC,OAAO,CAACiB,QAAQ,GAAG3jB,CAAC,CAAC4jB,KAAK;EAC1B,MAAM/B,MAAM,GAAGa,OAAO,CAACgB,QAAQ;EAC/B,MAAMG,MAAM,GAAGnB,OAAO,CAACiB,QAAQ;;EAE/B;;EAEA,IAAI,CAAC/B,gBAAgB,CAACrc,MAAM,EAAEvF,CAAC,EAAE6hB,MAAM,CAAC,EAAE;IACxC;EACF;EACArW,MAAM,CAACC,MAAM,CAAChB,IAAI,EAAE;IAClBsY,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,KAAK;IACdc,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAE5K,SAAS;IACtB6K,WAAW,EAAE7K;EACf,CAAC,CAAC;EACFuJ,OAAO,CAACb,MAAM,GAAGA,MAAM;EACvBa,OAAO,CAACmB,MAAM,GAAGA,MAAM;EACvBpZ,IAAI,CAACwZ,cAAc,GAAG1iB,GAAG,CAAC,CAAC;EAC3BgE,MAAM,CAACie,UAAU,GAAG,IAAI;EACxBje,MAAM,CAAC0F,UAAU,CAAC,CAAC;EACnB1F,MAAM,CAAC2e,cAAc,GAAG/K,SAAS;EACjC,IAAIjS,MAAM,CAAC6W,SAAS,GAAG,CAAC,EAAEtT,IAAI,CAAC0Z,kBAAkB,GAAG,KAAK;EACzD,IAAIlC,cAAc,GAAG,IAAI;EACzB,IAAIW,QAAQ,CAAC7J,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,EAAE;IAC5CnC,cAAc,GAAG,KAAK;IACtB,IAAIW,QAAQ,CAACyB,QAAQ,KAAK,QAAQ,EAAE;MAClC5Z,IAAI,CAACsY,SAAS,GAAG,KAAK;IACxB;EACF;EACA,IAAI9gB,QAAQ,CAACqiB,aAAa,IAAIriB,QAAQ,CAACqiB,aAAa,CAACvL,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,IAAIniB,QAAQ,CAACqiB,aAAa,KAAK1B,QAAQ,KAAK5iB,CAAC,CAAC2iB,WAAW,KAAK,OAAO,IAAI3iB,CAAC,CAAC2iB,WAAW,KAAK,OAAO,IAAI,CAACC,QAAQ,CAAC7J,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,CAAC,EAAE;IACpOniB,QAAQ,CAACqiB,aAAa,CAACC,IAAI,CAAC,CAAC;EAC/B;EACA,MAAMC,oBAAoB,GAAGvC,cAAc,IAAI1c,MAAM,CAACkf,cAAc,IAAIvd,MAAM,CAACwd,wBAAwB;EACvG,IAAI,CAACxd,MAAM,CAACyd,6BAA6B,IAAIH,oBAAoB,KAAK,CAAC5B,QAAQ,CAACgC,iBAAiB,EAAE;IACjG5kB,CAAC,CAACiiB,cAAc,CAAC,CAAC;EACpB;EACA,IAAI/a,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO,IAAIlH,MAAM,CAACiY,QAAQ,IAAIjY,MAAM,CAAC6U,SAAS,IAAI,CAAClT,MAAM,CAACiH,OAAO,EAAE;IACxG5I,MAAM,CAACiY,QAAQ,CAAC0E,YAAY,CAAC,CAAC;EAChC;EACA3c,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEzF,CAAC,CAAC;AAC9B;AAEA,SAAS6kB,WAAWA,CAACpb,KAAK,EAAE;EAC1B,MAAMxH,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM2F,MAAM,GAAG,IAAI;EACnB,MAAMkF,IAAI,GAAGlF,MAAM,CAAC2a,eAAe;EACnC,MAAM;IACJhZ,MAAM;IACNwb,OAAO;IACPtW,YAAY,EAAEC,GAAG;IACjBI;EACF,CAAC,GAAGlH,MAAM;EACV,IAAI,CAACkH,OAAO,EAAE;EACd,IAAI,CAACvF,MAAM,CAAC6Z,aAAa,IAAItX,KAAK,CAACkZ,WAAW,KAAK,OAAO,EAAE;EAC5D,IAAI3iB,CAAC,GAAGyJ,KAAK;EACb,IAAIzJ,CAAC,CAACmiB,aAAa,EAAEniB,CAAC,GAAGA,CAAC,CAACmiB,aAAa;EACxC,IAAIniB,CAAC,CAACoiB,IAAI,KAAK,aAAa,EAAE;IAC5B,IAAI3X,IAAI,CAAC8X,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;IACnC,MAAMuC,EAAE,GAAG9kB,CAAC,CAACqiB,SAAS;IACtB,IAAIyC,EAAE,KAAKra,IAAI,CAAC4X,SAAS,EAAE;EAC7B;EACA,IAAI0C,WAAW;EACf,IAAI/kB,CAAC,CAACoiB,IAAI,KAAK,WAAW,EAAE;IAC1B2C,WAAW,GAAG,CAAC,GAAG/kB,CAAC,CAACglB,cAAc,CAAC,CAAC7O,IAAI,CAACvV,CAAC,IAAIA,CAAC,CAAC4hB,UAAU,KAAK/X,IAAI,CAAC8X,OAAO,CAAC;IAC5E,IAAI,CAACwC,WAAW,IAAIA,WAAW,CAACvC,UAAU,KAAK/X,IAAI,CAAC8X,OAAO,EAAE;EAC/D,CAAC,MAAM;IACLwC,WAAW,GAAG/kB,CAAC;EACjB;EACA,IAAI,CAACyK,IAAI,CAACsY,SAAS,EAAE;IACnB,IAAItY,IAAI,CAACuZ,WAAW,IAAIvZ,IAAI,CAACsZ,WAAW,EAAE;MACxCxe,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEzF,CAAC,CAAC;IACrC;IACA;EACF;EACA,MAAMyiB,KAAK,GAAGsC,WAAW,CAACtC,KAAK;EAC/B,MAAMmB,KAAK,GAAGmB,WAAW,CAACnB,KAAK;EAC/B,IAAI5jB,CAAC,CAACilB,uBAAuB,EAAE;IAC7BvC,OAAO,CAACb,MAAM,GAAGY,KAAK;IACtBC,OAAO,CAACmB,MAAM,GAAGD,KAAK;IACtB;EACF;EACA,IAAI,CAACre,MAAM,CAACkf,cAAc,EAAE;IAC1B,IAAI,CAACzkB,CAAC,CAACyG,MAAM,CAACsS,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,EAAE;MAC7C7e,MAAM,CAACie,UAAU,GAAG,KAAK;IAC3B;IACA,IAAI/Y,IAAI,CAACsY,SAAS,EAAE;MAClBvX,MAAM,CAACC,MAAM,CAACiX,OAAO,EAAE;QACrBb,MAAM,EAAEY,KAAK;QACboB,MAAM,EAAED,KAAK;QACbF,QAAQ,EAAEjB,KAAK;QACfkB,QAAQ,EAAEC;MACZ,CAAC,CAAC;MACFnZ,IAAI,CAACwZ,cAAc,GAAG1iB,GAAG,CAAC,CAAC;IAC7B;IACA;EACF;EACA,IAAI2F,MAAM,CAACge,mBAAmB,IAAI,CAAChe,MAAM,CAACwJ,IAAI,EAAE;IAC9C,IAAInL,MAAM,CAAC8F,UAAU,CAAC,CAAC,EAAE;MACvB;MACA,IAAIuY,KAAK,GAAGlB,OAAO,CAACmB,MAAM,IAAIte,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACsP,YAAY,CAAC,CAAC,IAAI+O,KAAK,GAAGlB,OAAO,CAACmB,MAAM,IAAIte,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACyO,YAAY,CAAC,CAAC,EAAE;QAC9IvJ,IAAI,CAACsY,SAAS,GAAG,KAAK;QACtBtY,IAAI,CAACuY,OAAO,GAAG,KAAK;QACpB;MACF;IACF,CAAC,MAAM,IAAI3W,GAAG,KAAKoW,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAI,CAACtc,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACsP,YAAY,CAAC,CAAC,IAAI4N,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAI,CAACtc,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC,EAAE;MAChK;IACF,CAAC,MAAM,IAAI,CAAC3H,GAAG,KAAKoW,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAItc,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACsP,YAAY,CAAC,CAAC,IAAI4N,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAItc,MAAM,CAACoO,SAAS,IAAIpO,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC/J;IACF;EACF;EACA,IAAI/R,QAAQ,CAACqiB,aAAa,IAAIriB,QAAQ,CAACqiB,aAAa,CAACvL,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,IAAIniB,QAAQ,CAACqiB,aAAa,KAAKtkB,CAAC,CAACyG,MAAM,IAAIzG,CAAC,CAAC2iB,WAAW,KAAK,OAAO,EAAE;IACxJ1gB,QAAQ,CAACqiB,aAAa,CAACC,IAAI,CAAC,CAAC;EAC/B;EACA,IAAItiB,QAAQ,CAACqiB,aAAa,EAAE;IAC1B,IAAItkB,CAAC,CAACyG,MAAM,KAAKxE,QAAQ,CAACqiB,aAAa,IAAItkB,CAAC,CAACyG,MAAM,CAACsS,OAAO,CAACtO,IAAI,CAAC2Z,iBAAiB,CAAC,EAAE;MACnF3Z,IAAI,CAACuY,OAAO,GAAG,IAAI;MACnBzd,MAAM,CAACie,UAAU,GAAG,KAAK;MACzB;IACF;EACF;EACA,IAAI/Y,IAAI,CAACqZ,mBAAmB,EAAE;IAC5Bve,MAAM,CAACE,IAAI,CAAC,WAAW,EAAEzF,CAAC,CAAC;EAC7B;EACA0iB,OAAO,CAACyC,SAAS,GAAGzC,OAAO,CAACgB,QAAQ;EACpChB,OAAO,CAAC0C,SAAS,GAAG1C,OAAO,CAACiB,QAAQ;EACpCjB,OAAO,CAACgB,QAAQ,GAAGjB,KAAK;EACxBC,OAAO,CAACiB,QAAQ,GAAGC,KAAK;EACxB,MAAMyB,KAAK,GAAG3C,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACb,MAAM;EAC/C,MAAMyD,KAAK,GAAG5C,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAACmB,MAAM;EAC/C,IAAIte,MAAM,CAAC2B,MAAM,CAAC6W,SAAS,IAAIlO,IAAI,CAAC0V,IAAI,CAACF,KAAK,IAAI,CAAC,GAAGC,KAAK,IAAI,CAAC,CAAC,GAAG/f,MAAM,CAAC2B,MAAM,CAAC6W,SAAS,EAAE;EAC7F,IAAI,OAAOtT,IAAI,CAACsZ,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAIyB,UAAU;IACd,IAAIjgB,MAAM,CAAC6F,YAAY,CAAC,CAAC,IAAIsX,OAAO,CAACiB,QAAQ,KAAKjB,OAAO,CAACmB,MAAM,IAAIte,MAAM,CAAC8F,UAAU,CAAC,CAAC,IAAIqX,OAAO,CAACgB,QAAQ,KAAKhB,OAAO,CAACb,MAAM,EAAE;MAC9HpX,IAAI,CAACsZ,WAAW,GAAG,KAAK;IAC1B,CAAC,MAAM;MACL;MACA,IAAIsB,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,IAAI,EAAE,EAAE;QACvCE,UAAU,GAAG3V,IAAI,CAAC4V,KAAK,CAAC5V,IAAI,CAACG,GAAG,CAACsV,KAAK,CAAC,EAAEzV,IAAI,CAACG,GAAG,CAACqV,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGxV,IAAI,CAAC6V,EAAE;QACzEjb,IAAI,CAACsZ,WAAW,GAAGxe,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGoa,UAAU,GAAGte,MAAM,CAACse,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAGte,MAAM,CAACse,UAAU;MACjH;IACF;EACF;EACA,IAAI/a,IAAI,CAACsZ,WAAW,EAAE;IACpBxe,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEzF,CAAC,CAAC;EACrC;EACA,IAAI,OAAOyK,IAAI,CAACuZ,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAItB,OAAO,CAACgB,QAAQ,KAAKhB,OAAO,CAACb,MAAM,IAAIa,OAAO,CAACiB,QAAQ,KAAKjB,OAAO,CAACmB,MAAM,EAAE;MAC9EpZ,IAAI,CAACuZ,WAAW,GAAG,IAAI;IACzB;EACF;EACA,IAAIvZ,IAAI,CAACsZ,WAAW,IAAI/jB,CAAC,CAACoiB,IAAI,KAAK,WAAW,IAAI3X,IAAI,CAACkb,+BAA+B,EAAE;IACtFlb,IAAI,CAACsY,SAAS,GAAG,KAAK;IACtB;EACF;EACA,IAAI,CAACtY,IAAI,CAACuZ,WAAW,EAAE;IACrB;EACF;EACAze,MAAM,CAACie,UAAU,GAAG,KAAK;EACzB,IAAI,CAACtc,MAAM,CAACiH,OAAO,IAAInO,CAAC,CAAC4lB,UAAU,EAAE;IACnC5lB,CAAC,CAACiiB,cAAc,CAAC,CAAC;EACpB;EACA,IAAI/a,MAAM,CAAC2e,wBAAwB,IAAI,CAAC3e,MAAM,CAAC4e,MAAM,EAAE;IACrD9lB,CAAC,CAAC+lB,eAAe,CAAC,CAAC;EACrB;EACA,IAAI9F,IAAI,GAAG1a,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGia,KAAK,GAAGC,KAAK;EAChD,IAAIU,WAAW,GAAGzgB,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGsX,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACyC,SAAS,GAAGzC,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAAC0C,SAAS;EACrH,IAAIle,MAAM,CAAC+e,cAAc,EAAE;IACzBhG,IAAI,GAAGpQ,IAAI,CAACG,GAAG,CAACiQ,IAAI,CAAC,IAAI5T,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtC2Z,WAAW,GAAGnW,IAAI,CAACG,GAAG,CAACgW,WAAW,CAAC,IAAI3Z,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACtD;EACAqW,OAAO,CAACzC,IAAI,GAAGA,IAAI;EACnBA,IAAI,IAAI/Y,MAAM,CAACgf,UAAU;EACzB,IAAI7Z,GAAG,EAAE;IACP4T,IAAI,GAAG,CAACA,IAAI;IACZ+F,WAAW,GAAG,CAACA,WAAW;EAC5B;EACA,MAAMG,oBAAoB,GAAG5gB,MAAM,CAAC6gB,gBAAgB;EACpD7gB,MAAM,CAAC2e,cAAc,GAAGjE,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAClD1a,MAAM,CAAC6gB,gBAAgB,GAAGJ,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAC3D,MAAMK,MAAM,GAAG9gB,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,IAAI,CAACxJ,MAAM,CAACiH,OAAO;EACpD,MAAMmY,YAAY,GAAG/gB,MAAM,CAAC6gB,gBAAgB,KAAK,MAAM,IAAI7gB,MAAM,CAACqW,cAAc,IAAIrW,MAAM,CAAC6gB,gBAAgB,KAAK,MAAM,IAAI7gB,MAAM,CAACsW,cAAc;EAC/I,IAAI,CAACpR,IAAI,CAACuY,OAAO,EAAE;IACjB,IAAIqD,MAAM,IAAIC,YAAY,EAAE;MAC1B/gB,MAAM,CAACmX,OAAO,CAAC;QACbxB,SAAS,EAAE3V,MAAM,CAAC2e;MACpB,CAAC,CAAC;IACJ;IACAzZ,IAAI,CAAC0V,cAAc,GAAG5a,MAAM,CAAC5E,YAAY,CAAC,CAAC;IAC3C4E,MAAM,CAACqN,aAAa,CAAC,CAAC,CAAC;IACvB,IAAIrN,MAAM,CAAC6U,SAAS,EAAE;MACpB,MAAMmM,GAAG,GAAG,IAAIvkB,MAAM,CAACwkB,WAAW,CAAC,eAAe,EAAE;QAClDC,OAAO,EAAE,IAAI;QACbb,UAAU,EAAE,IAAI;QAChBc,MAAM,EAAE;UACNC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;MACFphB,MAAM,CAACuD,SAAS,CAAC8d,aAAa,CAACL,GAAG,CAAC;IACrC;IACA9b,IAAI,CAACoc,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI3f,MAAM,CAACka,UAAU,KAAK7b,MAAM,CAACqW,cAAc,KAAK,IAAI,IAAIrW,MAAM,CAACsW,cAAc,KAAK,IAAI,CAAC,EAAE;MAC3FtW,MAAM,CAACsb,aAAa,CAAC,IAAI,CAAC;IAC5B;IACAtb,MAAM,CAACE,IAAI,CAAC,iBAAiB,EAAEzF,CAAC,CAAC;EACnC;EACA,IAAI8mB,SAAS;EACb,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACpB,IAAI9f,MAAM,CAAC+f,cAAc,KAAK,KAAK,IAAIxc,IAAI,CAACuY,OAAO,IAAIvY,IAAI,CAAC0Z,kBAAkB,IAAIgC,oBAAoB,KAAK5gB,MAAM,CAAC6gB,gBAAgB,IAAIC,MAAM,IAAIC,YAAY,IAAIzW,IAAI,CAACG,GAAG,CAACiQ,IAAI,CAAC,IAAI,CAAC,EAAE;IACnLzU,MAAM,CAACC,MAAM,CAACiX,OAAO,EAAE;MACrBb,MAAM,EAAEY,KAAK;MACboB,MAAM,EAAED,KAAK;MACbF,QAAQ,EAAEjB,KAAK;MACfkB,QAAQ,EAAEC,KAAK;MACfzD,cAAc,EAAE1V,IAAI,CAACgP;IACvB,CAAC,CAAC;IACFhP,IAAI,CAACyc,aAAa,GAAG,IAAI;IACzBzc,IAAI,CAAC0V,cAAc,GAAG1V,IAAI,CAACgP,gBAAgB;IAC3C;EACF;EACAlU,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEzF,CAAC,CAAC;EAC5ByK,IAAI,CAACuY,OAAO,GAAG,IAAI;EACnBvY,IAAI,CAACgP,gBAAgB,GAAGwG,IAAI,GAAGxV,IAAI,CAAC0V,cAAc;EAClD,IAAIgH,mBAAmB,GAAG,IAAI;EAC9B,IAAIC,eAAe,GAAGlgB,MAAM,CAACkgB,eAAe;EAC5C,IAAIlgB,MAAM,CAACge,mBAAmB,EAAE;IAC9BkC,eAAe,GAAG,CAAC;EACrB;EACA,IAAInH,IAAI,GAAG,CAAC,EAAE;IACZ,IAAIoG,MAAM,IAAIC,YAAY,IAAI,CAACQ,SAAS,IAAIrc,IAAI,CAAC0Z,kBAAkB,IAAI1Z,IAAI,CAACgP,gBAAgB,IAAIvS,MAAM,CAACgH,cAAc,GAAG3I,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAGzO,MAAM,CAACyH,eAAe,CAACzH,MAAM,CAACyN,WAAW,GAAG,CAAC,CAAC,IAAI9L,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIpJ,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGd,MAAM,CAACyH,aAAa,IAAI,CAAC,GAAGpJ,MAAM,CAACyH,eAAe,CAACzH,MAAM,CAACyN,WAAW,GAAG,CAAC,CAAC,GAAGzN,MAAM,CAAC2B,MAAM,CAACsG,YAAY,GAAG,CAAC,CAAC,GAAGjI,MAAM,CAAC2B,MAAM,CAACsG,YAAY,GAAGjI,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC9ZzO,MAAM,CAACmX,OAAO,CAAC;QACbxB,SAAS,EAAE,MAAM;QACjBxB,YAAY,EAAE,IAAI;QAClBjB,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IACA,IAAIhO,IAAI,CAACgP,gBAAgB,GAAGlU,MAAM,CAACyO,YAAY,CAAC,CAAC,EAAE;MACjDmT,mBAAmB,GAAG,KAAK;MAC3B,IAAIjgB,MAAM,CAACmgB,UAAU,EAAE;QACrB5c,IAAI,CAACgP,gBAAgB,GAAGlU,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAACzO,MAAM,CAACyO,YAAY,CAAC,CAAC,GAAGvJ,IAAI,CAAC0V,cAAc,GAAGF,IAAI,KAAKmH,eAAe;MAC9H;IACF;EACF,CAAC,MAAM,IAAInH,IAAI,GAAG,CAAC,EAAE;IACnB,IAAIoG,MAAM,IAAIC,YAAY,IAAI,CAACQ,SAAS,IAAIrc,IAAI,CAAC0Z,kBAAkB,IAAI1Z,IAAI,CAACgP,gBAAgB,IAAIvS,MAAM,CAACgH,cAAc,GAAG3I,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAGtP,MAAM,CAACyH,eAAe,CAACzH,MAAM,CAACyH,eAAe,CAAChF,MAAM,GAAG,CAAC,CAAC,GAAGzC,MAAM,CAAC2B,MAAM,CAACsG,YAAY,IAAItG,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIpJ,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAGd,MAAM,CAACyH,aAAa,IAAI,CAAC,GAAGpJ,MAAM,CAACyH,eAAe,CAACzH,MAAM,CAACyH,eAAe,CAAChF,MAAM,GAAG,CAAC,CAAC,GAAGzC,MAAM,CAAC2B,MAAM,CAACsG,YAAY,GAAG,CAAC,CAAC,GAAGjI,MAAM,CAACsP,YAAY,CAAC,CAAC,CAAC,EAAE;MACpbtP,MAAM,CAACmX,OAAO,CAAC;QACbxB,SAAS,EAAE,MAAM;QACjBxB,YAAY,EAAE,IAAI;QAClBjB,gBAAgB,EAAElT,MAAM,CAACoH,MAAM,CAAC3E,MAAM,IAAId,MAAM,CAACyH,aAAa,KAAK,MAAM,GAAGpJ,MAAM,CAACgS,oBAAoB,CAAC,CAAC,GAAG1H,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAAC7E,MAAM,CAACyH,aAAa,EAAE,EAAE,CAAC,CAAC;MAC7J,CAAC,CAAC;IACJ;IACA,IAAIlE,IAAI,CAACgP,gBAAgB,GAAGlU,MAAM,CAACsP,YAAY,CAAC,CAAC,EAAE;MACjDsS,mBAAmB,GAAG,KAAK;MAC3B,IAAIjgB,MAAM,CAACmgB,UAAU,EAAE;QACrB5c,IAAI,CAACgP,gBAAgB,GAAGlU,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAACtP,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAGpK,IAAI,CAAC0V,cAAc,GAAGF,IAAI,KAAKmH,eAAe;MAC7H;IACF;EACF;EACA,IAAID,mBAAmB,EAAE;IACvBnnB,CAAC,CAACilB,uBAAuB,GAAG,IAAI;EAClC;;EAEA;EACA,IAAI,CAAC1f,MAAM,CAACqW,cAAc,IAAIrW,MAAM,CAAC2e,cAAc,KAAK,MAAM,IAAIzZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc,EAAE;IAC7G1V,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc;EAC7C;EACA,IAAI,CAAC5a,MAAM,CAACsW,cAAc,IAAItW,MAAM,CAAC2e,cAAc,KAAK,MAAM,IAAIzZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc,EAAE;IAC7G1V,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc;EAC7C;EACA,IAAI,CAAC5a,MAAM,CAACsW,cAAc,IAAI,CAACtW,MAAM,CAACqW,cAAc,EAAE;IACpDnR,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc;EAC7C;;EAEA;EACA,IAAIjZ,MAAM,CAAC6W,SAAS,GAAG,CAAC,EAAE;IACxB,IAAIlO,IAAI,CAACG,GAAG,CAACiQ,IAAI,CAAC,GAAG/Y,MAAM,CAAC6W,SAAS,IAAItT,IAAI,CAAC0Z,kBAAkB,EAAE;MAChE,IAAI,CAAC1Z,IAAI,CAAC0Z,kBAAkB,EAAE;QAC5B1Z,IAAI,CAAC0Z,kBAAkB,GAAG,IAAI;QAC9BzB,OAAO,CAACb,MAAM,GAAGa,OAAO,CAACgB,QAAQ;QACjChB,OAAO,CAACmB,MAAM,GAAGnB,OAAO,CAACiB,QAAQ;QACjClZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc;QAC3CuC,OAAO,CAACzC,IAAI,GAAG1a,MAAM,CAAC6F,YAAY,CAAC,CAAC,GAAGsX,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACb,MAAM,GAAGa,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAACmB,MAAM;QAC5G;MACF;IACF,CAAC,MAAM;MACLpZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAAC0V,cAAc;MAC3C;IACF;EACF;EACA,IAAI,CAACjZ,MAAM,CAACogB,YAAY,IAAIpgB,MAAM,CAACiH,OAAO,EAAE;;EAE5C;EACA,IAAIjH,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO,IAAIlH,MAAM,CAACiY,QAAQ,IAAItW,MAAM,CAAC6K,mBAAmB,EAAE;IAC/FxM,MAAM,CAACyS,iBAAiB,CAAC,CAAC;IAC1BzS,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;EAC9B;EACA,IAAI3O,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO,IAAIlH,MAAM,CAACiY,QAAQ,EAAE;IACjEjY,MAAM,CAACiY,QAAQ,CAACqH,WAAW,CAAC,CAAC;EAC/B;EACA;EACAtf,MAAM,CAACmP,cAAc,CAACjK,IAAI,CAACgP,gBAAgB,CAAC;EAC5C;EACAlU,MAAM,CAACmU,YAAY,CAACjP,IAAI,CAACgP,gBAAgB,CAAC;AAC5C;AAEA,SAAS8N,UAAUA,CAAC9d,KAAK,EAAE;EACzB,MAAMlE,MAAM,GAAG,IAAI;EACnB,MAAMkF,IAAI,GAAGlF,MAAM,CAAC2a,eAAe;EACnC,IAAIlgB,CAAC,GAAGyJ,KAAK;EACb,IAAIzJ,CAAC,CAACmiB,aAAa,EAAEniB,CAAC,GAAGA,CAAC,CAACmiB,aAAa;EACxC,IAAI4C,WAAW;EACf,MAAMyC,YAAY,GAAGxnB,CAAC,CAACoiB,IAAI,KAAK,UAAU,IAAIpiB,CAAC,CAACoiB,IAAI,KAAK,aAAa;EACtE,IAAI,CAACoF,YAAY,EAAE;IACjB,IAAI/c,IAAI,CAAC8X,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;IACnC,IAAIviB,CAAC,CAACqiB,SAAS,KAAK5X,IAAI,CAAC4X,SAAS,EAAE;IACpC0C,WAAW,GAAG/kB,CAAC;EACjB,CAAC,MAAM;IACL+kB,WAAW,GAAG,CAAC,GAAG/kB,CAAC,CAACglB,cAAc,CAAC,CAAC7O,IAAI,CAACvV,CAAC,IAAIA,CAAC,CAAC4hB,UAAU,KAAK/X,IAAI,CAAC8X,OAAO,CAAC;IAC5E,IAAI,CAACwC,WAAW,IAAIA,WAAW,CAACvC,UAAU,KAAK/X,IAAI,CAAC8X,OAAO,EAAE;EAC/D;EACA,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC9d,QAAQ,CAACzE,CAAC,CAACoiB,IAAI,CAAC,EAAE;IACnF,MAAMqF,OAAO,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAChjB,QAAQ,CAACzE,CAAC,CAACoiB,IAAI,CAAC,KAAK7c,MAAM,CAACpB,OAAO,CAACG,QAAQ,IAAIiB,MAAM,CAACpB,OAAO,CAACa,SAAS,CAAC;IAC1H,IAAI,CAACyiB,OAAO,EAAE;MACZ;IACF;EACF;EACAhd,IAAI,CAAC4X,SAAS,GAAG,IAAI;EACrB5X,IAAI,CAAC8X,OAAO,GAAG,IAAI;EACnB,MAAM;IACJrb,MAAM;IACNwb,OAAO;IACPtW,YAAY,EAAEC,GAAG;IACjBU,UAAU;IACVN;EACF,CAAC,GAAGlH,MAAM;EACV,IAAI,CAACkH,OAAO,EAAE;EACd,IAAI,CAACvF,MAAM,CAAC6Z,aAAa,IAAI/gB,CAAC,CAAC2iB,WAAW,KAAK,OAAO,EAAE;EACxD,IAAIlY,IAAI,CAACqZ,mBAAmB,EAAE;IAC5Bve,MAAM,CAACE,IAAI,CAAC,UAAU,EAAEzF,CAAC,CAAC;EAC5B;EACAyK,IAAI,CAACqZ,mBAAmB,GAAG,KAAK;EAChC,IAAI,CAACrZ,IAAI,CAACsY,SAAS,EAAE;IACnB,IAAItY,IAAI,CAACuY,OAAO,IAAI9b,MAAM,CAACka,UAAU,EAAE;MACrC7b,MAAM,CAACsb,aAAa,CAAC,KAAK,CAAC;IAC7B;IACApW,IAAI,CAACuY,OAAO,GAAG,KAAK;IACpBvY,IAAI,CAACuZ,WAAW,GAAG,KAAK;IACxB;EACF;;EAEA;EACA,IAAI9c,MAAM,CAACka,UAAU,IAAI3W,IAAI,CAACuY,OAAO,IAAIvY,IAAI,CAACsY,SAAS,KAAKxd,MAAM,CAACqW,cAAc,KAAK,IAAI,IAAIrW,MAAM,CAACsW,cAAc,KAAK,IAAI,CAAC,EAAE;IAC7HtW,MAAM,CAACsb,aAAa,CAAC,KAAK,CAAC;EAC7B;;EAEA;EACA,MAAM6G,YAAY,GAAGnmB,GAAG,CAAC,CAAC;EAC1B,MAAMomB,QAAQ,GAAGD,YAAY,GAAGjd,IAAI,CAACwZ,cAAc;;EAEnD;EACA,IAAI1e,MAAM,CAACie,UAAU,EAAE;IACrB,MAAMoE,QAAQ,GAAG5nB,CAAC,CAAC6Y,IAAI,IAAI7Y,CAAC,CAACojB,YAAY,IAAIpjB,CAAC,CAACojB,YAAY,CAAC,CAAC;IAC7D7d,MAAM,CAACqT,kBAAkB,CAACgP,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAI5nB,CAAC,CAACyG,MAAM,EAAEmhB,QAAQ,CAAC;IACxEriB,MAAM,CAACE,IAAI,CAAC,WAAW,EAAEzF,CAAC,CAAC;IAC3B,IAAI2nB,QAAQ,GAAG,GAAG,IAAID,YAAY,GAAGjd,IAAI,CAACod,aAAa,GAAG,GAAG,EAAE;MAC7DtiB,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEzF,CAAC,CAAC;IACzC;EACF;EACAyK,IAAI,CAACod,aAAa,GAAGtmB,GAAG,CAAC,CAAC;EAC1BR,QAAQ,CAAC,MAAM;IACb,IAAI,CAACwE,MAAM,CAACM,SAAS,EAAEN,MAAM,CAACie,UAAU,GAAG,IAAI;EACjD,CAAC,CAAC;EACF,IAAI,CAAC/Y,IAAI,CAACsY,SAAS,IAAI,CAACtY,IAAI,CAACuY,OAAO,IAAI,CAACzd,MAAM,CAAC2e,cAAc,IAAIxB,OAAO,CAACzC,IAAI,KAAK,CAAC,IAAI,CAACxV,IAAI,CAACyc,aAAa,IAAIzc,IAAI,CAACgP,gBAAgB,KAAKhP,IAAI,CAAC0V,cAAc,IAAI,CAAC1V,IAAI,CAACyc,aAAa,EAAE;IACnLzc,IAAI,CAACsY,SAAS,GAAG,KAAK;IACtBtY,IAAI,CAACuY,OAAO,GAAG,KAAK;IACpBvY,IAAI,CAACuZ,WAAW,GAAG,KAAK;IACxB;EACF;EACAvZ,IAAI,CAACsY,SAAS,GAAG,KAAK;EACtBtY,IAAI,CAACuY,OAAO,GAAG,KAAK;EACpBvY,IAAI,CAACuZ,WAAW,GAAG,KAAK;EACxB,IAAI8D,UAAU;EACd,IAAI5gB,MAAM,CAACogB,YAAY,EAAE;IACvBQ,UAAU,GAAGzb,GAAG,GAAG9G,MAAM,CAACoO,SAAS,GAAG,CAACpO,MAAM,CAACoO,SAAS;EACzD,CAAC,MAAM;IACLmU,UAAU,GAAG,CAACrd,IAAI,CAACgP,gBAAgB;EACrC;EACA,IAAIvS,MAAM,CAACiH,OAAO,EAAE;IAClB;EACF;EACA,IAAIjH,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO,EAAE;IAC9ClH,MAAM,CAACiY,QAAQ,CAAC+J,UAAU,CAAC;MACzBO;IACF,CAAC,CAAC;IACF;EACF;;EAEA;EACA,MAAMC,WAAW,GAAGD,UAAU,IAAI,CAACviB,MAAM,CAACsP,YAAY,CAAC,CAAC,IAAI,CAACtP,MAAM,CAAC2B,MAAM,CAACwJ,IAAI;EAC/E,IAAIsX,SAAS,GAAG,CAAC;EACjB,IAAIjX,SAAS,GAAGxL,MAAM,CAACyH,eAAe,CAAC,CAAC,CAAC;EACzC,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,UAAU,CAAC/E,MAAM,EAAEY,CAAC,IAAIA,CAAC,GAAG1B,MAAM,CAACiJ,kBAAkB,GAAG,CAAC,GAAGjJ,MAAM,CAAC+I,cAAc,EAAE;IACrG,MAAM8M,SAAS,GAAGnU,CAAC,GAAG1B,MAAM,CAACiJ,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAGjJ,MAAM,CAAC+I,cAAc;IAC/E,IAAI,OAAOlD,UAAU,CAACnE,CAAC,GAAGmU,SAAS,CAAC,KAAK,WAAW,EAAE;MACpD,IAAIgL,WAAW,IAAID,UAAU,IAAI/a,UAAU,CAACnE,CAAC,CAAC,IAAIkf,UAAU,GAAG/a,UAAU,CAACnE,CAAC,GAAGmU,SAAS,CAAC,EAAE;QACxFiL,SAAS,GAAGpf,CAAC;QACbmI,SAAS,GAAGhE,UAAU,CAACnE,CAAC,GAAGmU,SAAS,CAAC,GAAGhQ,UAAU,CAACnE,CAAC,CAAC;MACvD;IACF,CAAC,MAAM,IAAImf,WAAW,IAAID,UAAU,IAAI/a,UAAU,CAACnE,CAAC,CAAC,EAAE;MACrDof,SAAS,GAAGpf,CAAC;MACbmI,SAAS,GAAGhE,UAAU,CAACA,UAAU,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAG+E,UAAU,CAACA,UAAU,CAAC/E,MAAM,GAAG,CAAC,CAAC;IACnF;EACF;EACA,IAAIigB,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIhhB,MAAM,CAAC0Q,MAAM,EAAE;IACjB,IAAIrS,MAAM,CAACuP,WAAW,EAAE;MACtBoT,eAAe,GAAGhhB,MAAM,CAACsF,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO,IAAIlH,MAAM,CAACiH,OAAO,GAAGjH,MAAM,CAACiH,OAAO,CAACG,MAAM,CAAC3E,MAAM,GAAG,CAAC,GAAGzC,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC;IAC5I,CAAC,MAAM,IAAIzC,MAAM,CAACwP,KAAK,EAAE;MACvBkT,gBAAgB,GAAG,CAAC;IACtB;EACF;EACA;EACA,MAAME,KAAK,GAAG,CAACL,UAAU,GAAG/a,UAAU,CAACib,SAAS,CAAC,IAAIjX,SAAS;EAC9D,MAAMgM,SAAS,GAAGiL,SAAS,GAAG9gB,MAAM,CAACiJ,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAGjJ,MAAM,CAAC+I,cAAc;EACvF,IAAI0X,QAAQ,GAAGzgB,MAAM,CAACkhB,YAAY,EAAE;IAClC;IACA,IAAI,CAAClhB,MAAM,CAACmhB,UAAU,EAAE;MACtB9iB,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,CAAC;MAClC;IACF;IACA,IAAIzN,MAAM,CAAC2e,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIiE,KAAK,IAAIjhB,MAAM,CAACohB,eAAe,EAAE/iB,MAAM,CAACgW,OAAO,CAACrU,MAAM,CAAC0Q,MAAM,IAAIrS,MAAM,CAACwP,KAAK,GAAGkT,gBAAgB,GAAGD,SAAS,GAAGjL,SAAS,CAAC,CAAC,KAAKxX,MAAM,CAACgW,OAAO,CAACyM,SAAS,CAAC;IAC9J;IACA,IAAIziB,MAAM,CAAC2e,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIiE,KAAK,GAAG,CAAC,GAAGjhB,MAAM,CAACohB,eAAe,EAAE;QACtC/iB,MAAM,CAACgW,OAAO,CAACyM,SAAS,GAAGjL,SAAS,CAAC;MACvC,CAAC,MAAM,IAAImL,eAAe,KAAK,IAAI,IAAIC,KAAK,GAAG,CAAC,IAAItY,IAAI,CAACG,GAAG,CAACmY,KAAK,CAAC,GAAGjhB,MAAM,CAACohB,eAAe,EAAE;QAC5F/iB,MAAM,CAACgW,OAAO,CAAC2M,eAAe,CAAC;MACjC,CAAC,MAAM;QACL3iB,MAAM,CAACgW,OAAO,CAACyM,SAAS,CAAC;MAC3B;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAI,CAAC9gB,MAAM,CAACqhB,WAAW,EAAE;MACvBhjB,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,CAAC;MAClC;IACF;IACA,MAAMwV,iBAAiB,GAAGjjB,MAAM,CAACkjB,UAAU,KAAKzoB,CAAC,CAACyG,MAAM,KAAKlB,MAAM,CAACkjB,UAAU,CAACC,MAAM,IAAI1oB,CAAC,CAACyG,MAAM,KAAKlB,MAAM,CAACkjB,UAAU,CAACE,MAAM,CAAC;IAC/H,IAAI,CAACH,iBAAiB,EAAE;MACtB,IAAIjjB,MAAM,CAAC2e,cAAc,KAAK,MAAM,EAAE;QACpC3e,MAAM,CAACgW,OAAO,CAAC0M,gBAAgB,KAAK,IAAI,GAAGA,gBAAgB,GAAGD,SAAS,GAAGjL,SAAS,CAAC;MACtF;MACA,IAAIxX,MAAM,CAAC2e,cAAc,KAAK,MAAM,EAAE;QACpC3e,MAAM,CAACgW,OAAO,CAAC2M,eAAe,KAAK,IAAI,GAAGA,eAAe,GAAGF,SAAS,CAAC;MACxE;IACF,CAAC,MAAM,IAAIhoB,CAAC,CAACyG,MAAM,KAAKlB,MAAM,CAACkjB,UAAU,CAACC,MAAM,EAAE;MAChDnjB,MAAM,CAACgW,OAAO,CAACyM,SAAS,GAAGjL,SAAS,CAAC;IACvC,CAAC,MAAM;MACLxX,MAAM,CAACgW,OAAO,CAACyM,SAAS,CAAC;IAC3B;EACF;AACF;AAEA,SAASY,QAAQA,CAAA,EAAG;EAClB,MAAMrjB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNR;EACF,CAAC,GAAGnB,MAAM;EACV,IAAImB,EAAE,IAAIA,EAAE,CAACkJ,WAAW,KAAK,CAAC,EAAE;;EAEhC;EACA,IAAI1I,MAAM,CAAC0H,WAAW,EAAE;IACtBrJ,MAAM,CAACsjB,aAAa,CAAC,CAAC;EACxB;;EAEA;EACA,MAAM;IACJjN,cAAc;IACdC,cAAc;IACd/O;EACF,CAAC,GAAGvH,MAAM;EACV,MAAMgH,SAAS,GAAGhH,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO;;EAEjE;EACAlH,MAAM,CAACqW,cAAc,GAAG,IAAI;EAC5BrW,MAAM,CAACsW,cAAc,GAAG,IAAI;EAC5BtW,MAAM,CAAC0F,UAAU,CAAC,CAAC;EACnB1F,MAAM,CAACoG,YAAY,CAAC,CAAC;EACrBpG,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;EAC5B,MAAMiT,aAAa,GAAGvc,SAAS,IAAIrF,MAAM,CAACwJ,IAAI;EAC9C,IAAI,CAACxJ,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIzH,MAAM,CAACyH,aAAa,GAAG,CAAC,KAAKpJ,MAAM,CAACwP,KAAK,IAAI,CAACxP,MAAM,CAACuP,WAAW,IAAI,CAACvP,MAAM,CAAC2B,MAAM,CAACgH,cAAc,IAAI,CAAC4a,aAAa,EAAE;IAC3JvjB,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC1D,CAAC,MAAM;IACL,IAAIzC,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,IAAI,CAACnE,SAAS,EAAE;MACpChH,MAAM,CAAC6W,WAAW,CAAC7W,MAAM,CAACsS,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACtD,CAAC,MAAM;MACLtS,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACpD;EACF;EACA,IAAIzN,MAAM,CAACwjB,QAAQ,IAAIxjB,MAAM,CAACwjB,QAAQ,CAACC,OAAO,IAAIzjB,MAAM,CAACwjB,QAAQ,CAACE,MAAM,EAAE;IACxEC,YAAY,CAAC3jB,MAAM,CAACwjB,QAAQ,CAACI,aAAa,CAAC;IAC3C5jB,MAAM,CAACwjB,QAAQ,CAACI,aAAa,GAAGjhB,UAAU,CAAC,MAAM;MAC/C,IAAI3C,MAAM,CAACwjB,QAAQ,IAAIxjB,MAAM,CAACwjB,QAAQ,CAACC,OAAO,IAAIzjB,MAAM,CAACwjB,QAAQ,CAACE,MAAM,EAAE;QACxE1jB,MAAM,CAACwjB,QAAQ,CAACK,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EACA;EACA7jB,MAAM,CAACsW,cAAc,GAAGA,cAAc;EACtCtW,MAAM,CAACqW,cAAc,GAAGA,cAAc;EACtC,IAAIrW,MAAM,CAAC2B,MAAM,CAAC2K,aAAa,IAAI/E,QAAQ,KAAKvH,MAAM,CAACuH,QAAQ,EAAE;IAC/DvH,MAAM,CAACuM,aAAa,CAAC,CAAC;EACxB;AACF;AAEA,SAASuX,OAAOA,CAACrpB,CAAC,EAAE;EAClB,MAAMuF,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACkH,OAAO,EAAE;EACrB,IAAI,CAAClH,MAAM,CAACie,UAAU,EAAE;IACtB,IAAIje,MAAM,CAAC2B,MAAM,CAACoiB,aAAa,EAAEtpB,CAAC,CAACiiB,cAAc,CAAC,CAAC;IACnD,IAAI1c,MAAM,CAAC2B,MAAM,CAACqiB,wBAAwB,IAAIhkB,MAAM,CAAC6U,SAAS,EAAE;MAC9Dpa,CAAC,CAAC+lB,eAAe,CAAC,CAAC;MACnB/lB,CAAC,CAACwpB,wBAAwB,CAAC,CAAC;IAC9B;EACF;AACF;AAEA,SAASC,QAAQA,CAAA,EAAG;EAClB,MAAMlkB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJuD,SAAS;IACTsD,YAAY;IACZK;EACF,CAAC,GAAGlH,MAAM;EACV,IAAI,CAACkH,OAAO,EAAE;EACdlH,MAAM,CAACuU,iBAAiB,GAAGvU,MAAM,CAACoO,SAAS;EAC3C,IAAIpO,MAAM,CAAC6F,YAAY,CAAC,CAAC,EAAE;IACzB7F,MAAM,CAACoO,SAAS,GAAG,CAAC7K,SAAS,CAAC4gB,UAAU;EAC1C,CAAC,MAAM;IACLnkB,MAAM,CAACoO,SAAS,GAAG,CAAC7K,SAAS,CAAC6gB,SAAS;EACzC;EACA;EACA,IAAIpkB,MAAM,CAACoO,SAAS,KAAK,CAAC,EAAEpO,MAAM,CAACoO,SAAS,GAAG,CAAC;EAChDpO,MAAM,CAACyS,iBAAiB,CAAC,CAAC;EAC1BzS,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;EAC5B,IAAIkE,WAAW;EACf,MAAMnF,cAAc,GAAGrP,MAAM,CAACsP,YAAY,CAAC,CAAC,GAAGtP,MAAM,CAACyO,YAAY,CAAC,CAAC;EACpE,IAAIY,cAAc,KAAK,CAAC,EAAE;IACxBmF,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACxU,MAAM,CAACoO,SAAS,GAAGpO,MAAM,CAACyO,YAAY,CAAC,CAAC,IAAIY,cAAc;EAC3E;EACA,IAAImF,WAAW,KAAKxU,MAAM,CAACiP,QAAQ,EAAE;IACnCjP,MAAM,CAACmP,cAAc,CAACtI,YAAY,GAAG,CAAC7G,MAAM,CAACoO,SAAS,GAAGpO,MAAM,CAACoO,SAAS,CAAC;EAC5E;EACApO,MAAM,CAACE,IAAI,CAAC,cAAc,EAAEF,MAAM,CAACoO,SAAS,EAAE,KAAK,CAAC;AACtD;AAEA,SAASiW,MAAMA,CAAC5pB,CAAC,EAAE;EACjB,MAAMuF,MAAM,GAAG,IAAI;EACnBkR,oBAAoB,CAAClR,MAAM,EAAEvF,CAAC,CAACyG,MAAM,CAAC;EACtC,IAAIlB,MAAM,CAAC2B,MAAM,CAACiH,OAAO,IAAI5I,MAAM,CAAC2B,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAI,CAACpJ,MAAM,CAAC2B,MAAM,CAACyO,UAAU,EAAE;IAChG;EACF;EACApQ,MAAM,CAAC8T,MAAM,CAAC,CAAC;AACjB;AAEA,SAASwQ,oBAAoBA,CAAA,EAAG;EAC9B,MAAMtkB,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACukB,6BAA6B,EAAE;EAC1CvkB,MAAM,CAACukB,6BAA6B,GAAG,IAAI;EAC3C,IAAIvkB,MAAM,CAAC2B,MAAM,CAACge,mBAAmB,EAAE;IACrC3f,MAAM,CAACmB,EAAE,CAACtE,KAAK,CAAC2nB,WAAW,GAAG,MAAM;EACtC;AACF;AAEA,MAAM5gB,MAAM,GAAGA,CAAC5D,MAAM,EAAEiE,MAAM,KAAK;EACjC,MAAMvH,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJsH,MAAM;IACNR,EAAE;IACFoC,SAAS;IACT/F;EACF,CAAC,GAAGwC,MAAM;EACV,MAAMykB,OAAO,GAAG,CAAC,CAAC9iB,MAAM,CAAC4e,MAAM;EAC/B,MAAMmE,SAAS,GAAGzgB,MAAM,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB;EAC9E,MAAM0gB,YAAY,GAAG1gB,MAAM;EAC3B,IAAI,CAAC9C,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;;EAEnC;EACAzE,QAAQ,CAACgoB,SAAS,CAAC,CAAC,YAAY,EAAE1kB,MAAM,CAACskB,oBAAoB,EAAE;IAC7DM,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACFtjB,EAAE,CAACujB,SAAS,CAAC,CAAC,YAAY,EAAE1kB,MAAM,CAAC2c,YAAY,EAAE;IAC/CiI,OAAO,EAAE;EACX,CAAC,CAAC;EACFzjB,EAAE,CAACujB,SAAS,CAAC,CAAC,aAAa,EAAE1kB,MAAM,CAAC2c,YAAY,EAAE;IAChDiI,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,WAAW,EAAE1kB,MAAM,CAACsf,WAAW,EAAE;IACnDsF,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACF/nB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,aAAa,EAAE1kB,MAAM,CAACsf,WAAW,EAAE;IACrDsF,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACF/nB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,UAAU,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACjD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,WAAW,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IAClD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,eAAe,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACtD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,aAAa,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACpD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,YAAY,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACnD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,cAAc,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACrD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACFloB,QAAQ,CAACgoB,SAAS,CAAC,CAAC,aAAa,EAAE1kB,MAAM,CAACgiB,UAAU,EAAE;IACpD4C,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,IAAIjjB,MAAM,CAACoiB,aAAa,IAAIpiB,MAAM,CAACqiB,wBAAwB,EAAE;IAC3D7iB,EAAE,CAACujB,SAAS,CAAC,CAAC,OAAO,EAAE1kB,MAAM,CAAC8jB,OAAO,EAAE,IAAI,CAAC;EAC9C;EACA,IAAIniB,MAAM,CAACiH,OAAO,EAAE;IAClBrF,SAAS,CAACmhB,SAAS,CAAC,CAAC,QAAQ,EAAE1kB,MAAM,CAACkkB,QAAQ,CAAC;EACjD;;EAEA;EACA,IAAIviB,MAAM,CAACkjB,oBAAoB,EAAE;IAC/B7kB,MAAM,CAAC2kB,YAAY,CAAC,CAACnnB,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,OAAO,GAAG,yCAAyC,GAAG,uBAAuB,EAAE2lB,QAAQ,EAAE,IAAI,CAAC;EAC1I,CAAC,MAAM;IACLrjB,MAAM,CAAC2kB,YAAY,CAAC,CAAC,gBAAgB,EAAEtB,QAAQ,EAAE,IAAI,CAAC;EACxD;;EAEA;EACAliB,EAAE,CAACujB,SAAS,CAAC,CAAC,MAAM,EAAE1kB,MAAM,CAACqkB,MAAM,EAAE;IACnCI,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASK,YAAYA,CAAA,EAAG;EACtB,MAAM9kB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACVA,MAAM,CAAC2c,YAAY,GAAGA,YAAY,CAACoI,IAAI,CAAC/kB,MAAM,CAAC;EAC/CA,MAAM,CAACsf,WAAW,GAAGA,WAAW,CAACyF,IAAI,CAAC/kB,MAAM,CAAC;EAC7CA,MAAM,CAACgiB,UAAU,GAAGA,UAAU,CAAC+C,IAAI,CAAC/kB,MAAM,CAAC;EAC3CA,MAAM,CAACskB,oBAAoB,GAAGA,oBAAoB,CAACS,IAAI,CAAC/kB,MAAM,CAAC;EAC/D,IAAI2B,MAAM,CAACiH,OAAO,EAAE;IAClB5I,MAAM,CAACkkB,QAAQ,GAAGA,QAAQ,CAACa,IAAI,CAAC/kB,MAAM,CAAC;EACzC;EACAA,MAAM,CAAC8jB,OAAO,GAAGA,OAAO,CAACiB,IAAI,CAAC/kB,MAAM,CAAC;EACrCA,MAAM,CAACqkB,MAAM,GAAGA,MAAM,CAACU,IAAI,CAAC/kB,MAAM,CAAC;EACnC4D,MAAM,CAAC5D,MAAM,EAAE,IAAI,CAAC;AACtB;AACA,SAASglB,YAAYA,CAAA,EAAG;EACtB,MAAMhlB,MAAM,GAAG,IAAI;EACnB4D,MAAM,CAAC5D,MAAM,EAAE,KAAK,CAAC;AACvB;AACA,IAAIilB,QAAQ,GAAG;EACbH,YAAY;EACZE;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACllB,MAAM,EAAE2B,MAAM,KAAK;EACxC,OAAO3B,MAAM,CAAC8I,IAAI,IAAInH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC;AAC3D,CAAC;AACD,SAASua,aAAaA,CAAA,EAAG;EACvB,MAAMtjB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJsS,SAAS;IACT/R,WAAW;IACXoB,MAAM;IACNR;EACF,CAAC,GAAGnB,MAAM;EACV,MAAMqJ,WAAW,GAAG1H,MAAM,CAAC0H,WAAW;EACtC,IAAI,CAACA,WAAW,IAAIA,WAAW,IAAIpD,MAAM,CAACqD,IAAI,CAACD,WAAW,CAAC,CAAC5G,MAAM,KAAK,CAAC,EAAE;EAC1E,MAAM/F,QAAQ,GAAGrC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM8qB,eAAe,GAAGxjB,MAAM,CAACwjB,eAAe,KAAK,QAAQ,IAAI,CAACxjB,MAAM,CAACwjB,eAAe,GAAGxjB,MAAM,CAACwjB,eAAe,GAAG,WAAW;EAC7H,MAAMC,mBAAmB,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAClmB,QAAQ,CAACyC,MAAM,CAACwjB,eAAe,CAAC,IAAI,CAACxjB,MAAM,CAACwjB,eAAe,GAAGnlB,MAAM,CAACmB,EAAE,GAAGzE,QAAQ,CAAC6U,aAAa,CAAC5P,MAAM,CAACwjB,eAAe,CAAC;EAC5K,MAAME,UAAU,GAAGrlB,MAAM,CAACslB,aAAa,CAACjc,WAAW,EAAE8b,eAAe,EAAEC,mBAAmB,CAAC;EAC1F,IAAI,CAACC,UAAU,IAAIrlB,MAAM,CAACulB,iBAAiB,KAAKF,UAAU,EAAE;EAC5D,MAAMG,oBAAoB,GAAGH,UAAU,IAAIhc,WAAW,GAAGA,WAAW,CAACgc,UAAU,CAAC,GAAGzR,SAAS;EAC5F,MAAM6R,gBAAgB,GAAGD,oBAAoB,IAAIxlB,MAAM,CAAC0lB,cAAc;EACtE,MAAMC,WAAW,GAAGT,aAAa,CAACllB,MAAM,EAAE2B,MAAM,CAAC;EACjD,MAAMikB,UAAU,GAAGV,aAAa,CAACllB,MAAM,EAAEylB,gBAAgB,CAAC;EAC1D,MAAMI,aAAa,GAAG7lB,MAAM,CAAC2B,MAAM,CAACka,UAAU;EAC9C,MAAMiK,YAAY,GAAGL,gBAAgB,CAAC5J,UAAU;EAChD,MAAMkK,UAAU,GAAGpkB,MAAM,CAACuF,OAAO;EACjC,IAAIye,WAAW,IAAI,CAACC,UAAU,EAAE;IAC9BzkB,EAAE,CAAC0L,SAAS,CAACI,MAAM,IAAAzO,MAAA,CAAImD,MAAM,CAACgL,sBAAsB,cAAAnO,MAAA,CAAWmD,MAAM,CAACgL,sBAAsB,gBAAa,CAAC;IAC1G3M,MAAM,CAACgmB,oBAAoB,CAAC,CAAC;EAC/B,CAAC,MAAM,IAAI,CAACL,WAAW,IAAIC,UAAU,EAAE;IACrCzkB,EAAE,CAAC0L,SAAS,CAACG,GAAG,IAAAxO,MAAA,CAAImD,MAAM,CAACgL,sBAAsB,SAAM,CAAC;IACxD,IAAI8Y,gBAAgB,CAAC3c,IAAI,CAAC6Q,IAAI,IAAI8L,gBAAgB,CAAC3c,IAAI,CAAC6Q,IAAI,KAAK,QAAQ,IAAI,CAAC8L,gBAAgB,CAAC3c,IAAI,CAAC6Q,IAAI,IAAIhY,MAAM,CAACmH,IAAI,CAAC6Q,IAAI,KAAK,QAAQ,EAAE;MACzIxY,EAAE,CAAC0L,SAAS,CAACG,GAAG,IAAAxO,MAAA,CAAImD,MAAM,CAACgL,sBAAsB,gBAAa,CAAC;IACjE;IACA3M,MAAM,CAACgmB,oBAAoB,CAAC,CAAC;EAC/B;EACA,IAAIH,aAAa,IAAI,CAACC,YAAY,EAAE;IAClC9lB,MAAM,CAAC4b,eAAe,CAAC,CAAC;EAC1B,CAAC,MAAM,IAAI,CAACiK,aAAa,IAAIC,YAAY,EAAE;IACzC9lB,MAAM,CAACsb,aAAa,CAAC,CAAC;EACxB;;EAEA;EACA,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACxa,OAAO,CAACmlB,IAAI,IAAI;IACxD,IAAI,OAAOR,gBAAgB,CAACQ,IAAI,CAAC,KAAK,WAAW,EAAE;IACnD,MAAMC,gBAAgB,GAAGvkB,MAAM,CAACskB,IAAI,CAAC,IAAItkB,MAAM,CAACskB,IAAI,CAAC,CAAC/e,OAAO;IAC7D,MAAMif,eAAe,GAAGV,gBAAgB,CAACQ,IAAI,CAAC,IAAIR,gBAAgB,CAACQ,IAAI,CAAC,CAAC/e,OAAO;IAChF,IAAIgf,gBAAgB,IAAI,CAACC,eAAe,EAAE;MACxCnmB,MAAM,CAACimB,IAAI,CAAC,CAACG,OAAO,CAAC,CAAC;IACxB;IACA,IAAI,CAACF,gBAAgB,IAAIC,eAAe,EAAE;MACxCnmB,MAAM,CAACimB,IAAI,CAAC,CAACI,MAAM,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGb,gBAAgB,CAAC9P,SAAS,IAAI8P,gBAAgB,CAAC9P,SAAS,KAAKhU,MAAM,CAACgU,SAAS;EACtG,MAAM4Q,WAAW,GAAG5kB,MAAM,CAACwJ,IAAI,KAAKsa,gBAAgB,CAACrc,aAAa,KAAKzH,MAAM,CAACyH,aAAa,IAAIkd,gBAAgB,CAAC;EAChH,MAAME,OAAO,GAAG7kB,MAAM,CAACwJ,IAAI;EAC3B,IAAImb,gBAAgB,IAAI/lB,WAAW,EAAE;IACnCP,MAAM,CAACymB,eAAe,CAAC,CAAC;EAC1B;EACAvqB,MAAM,CAAC8D,MAAM,CAAC2B,MAAM,EAAE8jB,gBAAgB,CAAC;EACvC,MAAMiB,SAAS,GAAG1mB,MAAM,CAAC2B,MAAM,CAACuF,OAAO;EACvC,MAAMyf,OAAO,GAAG3mB,MAAM,CAAC2B,MAAM,CAACwJ,IAAI;EAClClF,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;IACpBkf,cAAc,EAAElf,MAAM,CAAC2B,MAAM,CAACud,cAAc;IAC5C7I,cAAc,EAAErW,MAAM,CAAC2B,MAAM,CAAC0U,cAAc;IAC5CC,cAAc,EAAEtW,MAAM,CAAC2B,MAAM,CAAC2U;EAChC,CAAC,CAAC;EACF,IAAIyP,UAAU,IAAI,CAACW,SAAS,EAAE;IAC5B1mB,MAAM,CAAComB,OAAO,CAAC,CAAC;EAClB,CAAC,MAAM,IAAI,CAACL,UAAU,IAAIW,SAAS,EAAE;IACnC1mB,MAAM,CAACqmB,MAAM,CAAC,CAAC;EACjB;EACArmB,MAAM,CAACulB,iBAAiB,GAAGF,UAAU;EACrCrlB,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEulB,gBAAgB,CAAC;EAClD,IAAIllB,WAAW,EAAE;IACf,IAAIgmB,WAAW,EAAE;MACfvmB,MAAM,CAACmb,WAAW,CAAC,CAAC;MACpBnb,MAAM,CAAC8Y,UAAU,CAACxG,SAAS,CAAC;MAC5BtS,MAAM,CAACoG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,CAACogB,OAAO,IAAIG,OAAO,EAAE;MAC9B3mB,MAAM,CAAC8Y,UAAU,CAACxG,SAAS,CAAC;MAC5BtS,MAAM,CAACoG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAIogB,OAAO,IAAI,CAACG,OAAO,EAAE;MAC9B3mB,MAAM,CAACmb,WAAW,CAAC,CAAC;IACtB;EACF;EACAnb,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEulB,gBAAgB,CAAC;AAC7C;AAEA,SAASH,aAAaA,CAACjc,WAAW,EAAE0S,IAAI,EAAE6K,WAAW,EAAE;EACrD,IAAI7K,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,QAAQ;EACjB;EACA,IAAI,CAAC1S,WAAW,IAAI0S,IAAI,KAAK,WAAW,IAAI,CAAC6K,WAAW,EAAE,OAAOhT,SAAS;EAC1E,IAAIyR,UAAU,GAAG,KAAK;EACtB,MAAM5oB,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAM0sB,aAAa,GAAG9K,IAAI,KAAK,QAAQ,GAAGtf,MAAM,CAACqqB,WAAW,GAAGF,WAAW,CAAChhB,YAAY;EACvF,MAAMmhB,MAAM,GAAG9gB,MAAM,CAACqD,IAAI,CAACD,WAAW,CAAC,CAAC/J,GAAG,CAAC0nB,KAAK,IAAI;IACnD,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACzoB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MACzD,MAAM0oB,QAAQ,GAAGzgB,UAAU,CAACwgB,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,MAAMC,KAAK,GAAGN,aAAa,GAAGI,QAAQ;MACtC,OAAO;QACLE,KAAK;QACLH;MACF,CAAC;IACH;IACA,OAAO;MACLG,KAAK,EAAEH,KAAK;MACZA;IACF,CAAC;EACH,CAAC,CAAC;EACFD,MAAM,CAACK,IAAI,CAAC,CAACltB,CAAC,EAAEmtB,CAAC,KAAKthB,QAAQ,CAAC7L,CAAC,CAACitB,KAAK,EAAE,EAAE,CAAC,GAAGphB,QAAQ,CAACshB,CAAC,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC;EACpE,KAAK,IAAI9jB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0jB,MAAM,CAACtkB,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;IACzC,MAAM;MACJ2jB,KAAK;MACLG;IACF,CAAC,GAAGJ,MAAM,CAAC1jB,CAAC,CAAC;IACb,IAAI0Y,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAItf,MAAM,CAAC6qB,UAAU,gBAAA9oB,MAAA,CAAgB2oB,KAAK,QAAK,CAAC,CAAC3T,OAAO,EAAE;QACxD6R,UAAU,GAAG2B,KAAK;MACpB;IACF,CAAC,MAAM,IAAIG,KAAK,IAAIP,WAAW,CAACjhB,WAAW,EAAE;MAC3C0f,UAAU,GAAG2B,KAAK;IACpB;EACF;EACA,OAAO3B,UAAU,IAAI,KAAK;AAC5B;AAEA,IAAIhc,WAAW,GAAG;EAChBia,aAAa;EACbgC;AACF,CAAC;AAED,SAASiC,cAAcA,CAAC7mB,OAAO,EAAE8mB,MAAM,EAAE;EACvC,MAAMC,aAAa,GAAG,EAAE;EACxB/mB,OAAO,CAACI,OAAO,CAAC4mB,IAAI,IAAI;IACtB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5BzhB,MAAM,CAACqD,IAAI,CAACoe,IAAI,CAAC,CAAC5mB,OAAO,CAAC6mB,UAAU,IAAI;QACtC,IAAID,IAAI,CAACC,UAAU,CAAC,EAAE;UACpBF,aAAa,CAACzkB,IAAI,CAACwkB,MAAM,GAAGG,UAAU,CAAC;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MACnCD,aAAa,CAACzkB,IAAI,CAACwkB,MAAM,GAAGE,IAAI,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAOD,aAAa;AACtB;AACA,SAASG,UAAUA,CAAA,EAAG;EACpB,MAAM5nB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2nB,UAAU;IACVhmB,MAAM;IACNmF,GAAG;IACH3F,EAAE;IACF3D;EACF,CAAC,GAAGwC,MAAM;EACV;EACA,MAAM6nB,QAAQ,GAAGN,cAAc,CAAC,CAAC,aAAa,EAAE5lB,MAAM,CAACgU,SAAS,EAAE;IAChE,WAAW,EAAE3V,MAAM,CAAC2B,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q;EACzD,CAAC,EAAE;IACD,YAAY,EAAEvF,MAAM,CAACyO;EACvB,CAAC,EAAE;IACD,KAAK,EAAEtJ;EACT,CAAC,EAAE;IACD,MAAM,EAAEnF,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG;EAC5C,CAAC,EAAE;IACD,aAAa,EAAEpH,MAAM,CAACmH,IAAI,IAAInH,MAAM,CAACmH,IAAI,CAACC,IAAI,GAAG,CAAC,IAAIpH,MAAM,CAACmH,IAAI,CAAC6Q,IAAI,KAAK;EAC7E,CAAC,EAAE;IACD,SAAS,EAAEnc,MAAM,CAACE;EACpB,CAAC,EAAE;IACD,KAAK,EAAEF,MAAM,CAACC;EAChB,CAAC,EAAE;IACD,UAAU,EAAEkE,MAAM,CAACiH;EACrB,CAAC,EAAE;IACD,UAAU,EAAEjH,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAACgH;EACvC,CAAC,EAAE;IACD,gBAAgB,EAAEhH,MAAM,CAAC6K;EAC3B,CAAC,CAAC,EAAE7K,MAAM,CAACgL,sBAAsB,CAAC;EAClCgb,UAAU,CAAC3kB,IAAI,CAAC,GAAG6kB,QAAQ,CAAC;EAC5B1mB,EAAE,CAAC0L,SAAS,CAACG,GAAG,CAAC,GAAG2a,UAAU,CAAC;EAC/B3nB,MAAM,CAACgmB,oBAAoB,CAAC,CAAC;AAC/B;AAEA,SAAS8B,aAAaA,CAAA,EAAG;EACvB,MAAM9nB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJmB,EAAE;IACFwmB;EACF,CAAC,GAAG3nB,MAAM;EACV,IAAI,CAACmB,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;EACnCA,EAAE,CAAC0L,SAAS,CAACI,MAAM,CAAC,GAAG0a,UAAU,CAAC;EAClC3nB,MAAM,CAACgmB,oBAAoB,CAAC,CAAC;AAC/B;AAEA,IAAI+B,OAAO,GAAG;EACZH,UAAU;EACVE;AACF,CAAC;AAED,SAASvb,aAAaA,CAAA,EAAG;EACvB,MAAMvM,MAAM,GAAG,IAAI;EACnB,MAAM;IACJyb,QAAQ,EAAEuM,SAAS;IACnBrmB;EACF,CAAC,GAAG3B,MAAM;EACV,MAAM;IACJ2H;EACF,CAAC,GAAGhG,MAAM;EACV,IAAIgG,kBAAkB,EAAE;IACtB,MAAMoI,cAAc,GAAG/P,MAAM,CAACoH,MAAM,CAAC3E,MAAM,GAAG,CAAC;IAC/C,MAAMwlB,kBAAkB,GAAGjoB,MAAM,CAACwH,UAAU,CAACuI,cAAc,CAAC,GAAG/P,MAAM,CAACyH,eAAe,CAACsI,cAAc,CAAC,GAAGpI,kBAAkB,GAAG,CAAC;IAC9H3H,MAAM,CAACyb,QAAQ,GAAGzb,MAAM,CAACmG,IAAI,GAAG8hB,kBAAkB;EACpD,CAAC,MAAM;IACLjoB,MAAM,CAACyb,QAAQ,GAAGzb,MAAM,CAACuH,QAAQ,CAAC9E,MAAM,KAAK,CAAC;EAChD;EACA,IAAId,MAAM,CAAC0U,cAAc,KAAK,IAAI,EAAE;IAClCrW,MAAM,CAACqW,cAAc,GAAG,CAACrW,MAAM,CAACyb,QAAQ;EAC1C;EACA,IAAI9Z,MAAM,CAAC2U,cAAc,KAAK,IAAI,EAAE;IAClCtW,MAAM,CAACsW,cAAc,GAAG,CAACtW,MAAM,CAACyb,QAAQ;EAC1C;EACA,IAAIuM,SAAS,IAAIA,SAAS,KAAKhoB,MAAM,CAACyb,QAAQ,EAAE;IAC9Czb,MAAM,CAACwP,KAAK,GAAG,KAAK;EACtB;EACA,IAAIwY,SAAS,KAAKhoB,MAAM,CAACyb,QAAQ,EAAE;IACjCzb,MAAM,CAACE,IAAI,CAACF,MAAM,CAACyb,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;EAClD;AACF;AACA,IAAIyM,eAAe,GAAG;EACpB3b;AACF,CAAC;AAED,IAAI4b,QAAQ,GAAG;EACbllB,IAAI,EAAE,IAAI;EACV0S,SAAS,EAAE,YAAY;EACvB+K,cAAc,EAAE,KAAK;EACrB0H,qBAAqB,EAAE,kBAAkB;EACzC1M,iBAAiB,EAAE,SAAS;EAC5B/E,YAAY,EAAE,CAAC;EACfxJ,KAAK,EAAE,GAAG;EACVvE,OAAO,EAAE,KAAK;EACdic,oBAAoB,EAAE,IAAI;EAC1BjjB,cAAc,EAAE,IAAI;EACpB2e,MAAM,EAAE,KAAK;EACb8H,cAAc,EAAE,KAAK;EACrBC,YAAY,EAAE,QAAQ;EACtBphB,OAAO,EAAE,IAAI;EACb2X,iBAAiB,EAAE,uDAAuD;EAC1E;EACAhhB,KAAK,EAAE,IAAI;EACXE,MAAM,EAAE,IAAI;EACZ;EACA+W,8BAA8B,EAAE,KAAK;EACrC;EACA1X,SAAS,EAAE,IAAI;EACfmrB,GAAG,EAAE,IAAI;EACT;EACAhM,kBAAkB,EAAE,KAAK;EACzBC,kBAAkB,EAAE,EAAE;EACtB;EACApM,UAAU,EAAE,KAAK;EACjB;EACArF,cAAc,EAAE,KAAK;EACrB;EACAkJ,gBAAgB,EAAE,KAAK;EACvB;EACAnJ,MAAM,EAAE,OAAO;EACf;;EAEA;EACAzB,WAAW,EAAEuK,SAAS;EACtBuR,eAAe,EAAE,QAAQ;EACzB;EACAld,YAAY,EAAE,CAAC;EACfmB,aAAa,EAAE,CAAC;EAChBsB,cAAc,EAAE,CAAC;EACjBE,kBAAkB,EAAE,CAAC;EACrB2M,kBAAkB,EAAE,KAAK;EACzB5O,cAAc,EAAE,KAAK;EACrBgD,oBAAoB,EAAE,KAAK;EAC3BhE,kBAAkB,EAAE,CAAC;EACrB;EACAG,iBAAiB,EAAE,CAAC;EACpB;EACA0K,mBAAmB,EAAE,IAAI;EACzBxG,wBAAwB,EAAE,KAAK;EAC/B;EACAM,aAAa,EAAE,IAAI;EACnB;EACArC,YAAY,EAAE,KAAK;EACnB;EACA0W,UAAU,EAAE,CAAC;EACbV,UAAU,EAAE,EAAE;EACdzE,aAAa,EAAE,IAAI;EACnBwH,WAAW,EAAE,IAAI;EACjBF,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,GAAG;EACpBF,YAAY,EAAE,GAAG;EACjBd,YAAY,EAAE,IAAI;EAClB7C,cAAc,EAAE,IAAI;EACpB1G,SAAS,EAAE,CAAC;EACZ8H,wBAAwB,EAAE,KAAK;EAC/BnB,wBAAwB,EAAE,IAAI;EAC9BC,6BAA6B,EAAE,KAAK;EACpCO,mBAAmB,EAAE,KAAK;EAC1B;EACA6I,iBAAiB,EAAE,IAAI;EACvB;EACA1G,UAAU,EAAE,IAAI;EAChBD,eAAe,EAAE,IAAI;EACrB;EACArV,mBAAmB,EAAE,KAAK;EAC1B;EACAqP,UAAU,EAAE,KAAK;EACjB;EACAkI,aAAa,EAAE,IAAI;EACnBC,wBAAwB,EAAE,IAAI;EAC9BnQ,mBAAmB,EAAE,KAAK;EAC1B;EACA1I,IAAI,EAAE,KAAK;EACXmO,kBAAkB,EAAE,IAAI;EACxBI,oBAAoB,EAAE,CAAC;EACvBjC,mBAAmB,EAAE,IAAI;EACzB;EACApF,MAAM,EAAE,KAAK;EACb;EACAiE,cAAc,EAAE,IAAI;EACpBD,cAAc,EAAE,IAAI;EACpB6H,YAAY,EAAE,IAAI;EAClB;EACAF,SAAS,EAAE,IAAI;EACfL,cAAc,EAAE,mBAAmB;EACnCG,iBAAiB,EAAE,IAAI;EACvB;EACA2K,gBAAgB,EAAE,IAAI;EACtB1b,uBAAuB,EAAE,EAAE;EAC3B;EACAJ,sBAAsB,EAAE,SAAS;EACjC;EACAtF,UAAU,EAAE,cAAc;EAC1B+R,eAAe,EAAE,oBAAoB;EACrCtI,gBAAgB,EAAE,qBAAqB;EACvC/B,iBAAiB,EAAE,sBAAsB;EACzCC,sBAAsB,EAAE,4BAA4B;EACpD+B,cAAc,EAAE,mBAAmB;EACnCC,cAAc,EAAE,mBAAmB;EACnC0X,YAAY,EAAE,gBAAgB;EAC9BlX,kBAAkB,EAAE,uBAAuB;EAC3CM,mBAAmB,EAAE,CAAC;EACtB;EACAsB,kBAAkB,EAAE,IAAI;EACxB;EACAuV,YAAY,EAAE;AAChB,CAAC;AAED,SAASC,kBAAkBA,CAACjnB,MAAM,EAAEknB,gBAAgB,EAAE;EACpD,OAAO,SAAS7mB,YAAYA,CAAC8mB,GAAG,EAAE;IAChC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAClBA,GAAG,GAAG,CAAC,CAAC;IACV;IACA,MAAMC,eAAe,GAAG9iB,MAAM,CAACqD,IAAI,CAACwf,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAME,YAAY,GAAGF,GAAG,CAACC,eAAe,CAAC;IACzC,IAAI,OAAOC,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;MAC7D9sB,MAAM,CAAC2sB,gBAAgB,EAAEC,GAAG,CAAC;MAC7B;IACF;IACA,IAAInnB,MAAM,CAAConB,eAAe,CAAC,KAAK,IAAI,EAAE;MACpCpnB,MAAM,CAAConB,eAAe,CAAC,GAAG;QACxB7hB,OAAO,EAAE;MACX,CAAC;IACH;IACA,IAAI6hB,eAAe,KAAK,YAAY,IAAIpnB,MAAM,CAAConB,eAAe,CAAC,IAAIpnB,MAAM,CAAConB,eAAe,CAAC,CAAC7hB,OAAO,IAAI,CAACvF,MAAM,CAAConB,eAAe,CAAC,CAAC3F,MAAM,IAAI,CAACzhB,MAAM,CAAConB,eAAe,CAAC,CAAC5F,MAAM,EAAE;MACxKxhB,MAAM,CAAConB,eAAe,CAAC,CAACE,IAAI,GAAG,IAAI;IACrC;IACA,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC1qB,OAAO,CAACwqB,eAAe,CAAC,IAAI,CAAC,IAAIpnB,MAAM,CAAConB,eAAe,CAAC,IAAIpnB,MAAM,CAAConB,eAAe,CAAC,CAAC7hB,OAAO,IAAI,CAACvF,MAAM,CAAConB,eAAe,CAAC,CAAC5nB,EAAE,EAAE;MAC1JQ,MAAM,CAAConB,eAAe,CAAC,CAACE,IAAI,GAAG,IAAI;IACrC;IACA,IAAI,EAAEF,eAAe,IAAIpnB,MAAM,IAAI,SAAS,IAAIqnB,YAAY,CAAC,EAAE;MAC7D9sB,MAAM,CAAC2sB,gBAAgB,EAAEC,GAAG,CAAC;MAC7B;IACF;IACA,IAAI,OAAOnnB,MAAM,CAAConB,eAAe,CAAC,KAAK,QAAQ,IAAI,EAAE,SAAS,IAAIpnB,MAAM,CAAConB,eAAe,CAAC,CAAC,EAAE;MAC1FpnB,MAAM,CAAConB,eAAe,CAAC,CAAC7hB,OAAO,GAAG,IAAI;IACxC;IACA,IAAI,CAACvF,MAAM,CAAConB,eAAe,CAAC,EAAEpnB,MAAM,CAAConB,eAAe,CAAC,GAAG;MACtD7hB,OAAO,EAAE;IACX,CAAC;IACDhL,MAAM,CAAC2sB,gBAAgB,EAAEC,GAAG,CAAC;EAC/B,CAAC;AACH;;AAEA;AACA,MAAMI,UAAU,GAAG;EACjBvlB,aAAa;EACbmQ,MAAM;EACN1F,SAAS;EACT2H,UAAU;EACVtM,KAAK;EACL0B,IAAI;EACJ0Q,UAAU;EACVjY,MAAM,EAAEqhB,QAAQ;EAChB5b,WAAW;EACXkD,aAAa,EAAE2b,eAAe;EAC9BH;AACF,CAAC;AACD,MAAMoB,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,MAAM,CAAC;EACXlO,WAAWA,CAAA,EAAG;IACZ,IAAI/Z,EAAE;IACN,IAAIQ,MAAM;IACV,KAAK,IAAI4C,IAAI,GAAGC,SAAS,CAAC/B,MAAM,EAAEgC,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAC9B;IACA,IAAIF,IAAI,CAAChC,MAAM,KAAK,CAAC,IAAIgC,IAAI,CAAC,CAAC,CAAC,CAACyW,WAAW,IAAIjV,MAAM,CAACojB,SAAS,CAACC,QAAQ,CAAC1hB,IAAI,CAACnD,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjH5D,MAAM,GAAG8C,IAAI,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,CAACtD,EAAE,EAAEQ,MAAM,CAAC,GAAG8C,IAAI;IACrB;IACA,IAAI,CAAC9C,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;IACxBA,MAAM,GAAGzF,MAAM,CAAC,CAAC,CAAC,EAAEyF,MAAM,CAAC;IAC3B,IAAIR,EAAE,IAAI,CAACQ,MAAM,CAACR,EAAE,EAAEQ,MAAM,CAACR,EAAE,GAAGA,EAAE;IACpC,MAAMzE,QAAQ,GAAGrC,WAAW,CAAC,CAAC;IAC9B,IAAIsH,MAAM,CAACR,EAAE,IAAI,OAAOQ,MAAM,CAACR,EAAE,KAAK,QAAQ,IAAIzE,QAAQ,CAAC6sB,gBAAgB,CAAC5nB,MAAM,CAACR,EAAE,CAAC,CAACsB,MAAM,GAAG,CAAC,EAAE;MACjG,MAAM+mB,OAAO,GAAG,EAAE;MAClB9sB,QAAQ,CAAC6sB,gBAAgB,CAAC5nB,MAAM,CAACR,EAAE,CAAC,CAACL,OAAO,CAAC8lB,WAAW,IAAI;QAC1D,MAAM6C,SAAS,GAAGvtB,MAAM,CAAC,CAAC,CAAC,EAAEyF,MAAM,EAAE;UACnCR,EAAE,EAAEylB;QACN,CAAC,CAAC;QACF4C,OAAO,CAACxmB,IAAI,CAAC,IAAIomB,MAAM,CAACK,SAAS,CAAC,CAAC;MACrC,CAAC,CAAC;MACF;MACA,OAAOD,OAAO;IAChB;;IAEA;IACA,MAAMxpB,MAAM,GAAG,IAAI;IACnBA,MAAM,CAAC0pB,UAAU,GAAG,IAAI;IACxB1pB,MAAM,CAACzD,OAAO,GAAGS,UAAU,CAAC,CAAC;IAC7BgD,MAAM,CAACxC,MAAM,GAAGkB,SAAS,CAAC;MACxBtB,SAAS,EAAEuE,MAAM,CAACvE;IACpB,CAAC,CAAC;IACF4C,MAAM,CAACpB,OAAO,GAAGiB,UAAU,CAAC,CAAC;IAC7BG,MAAM,CAACgE,eAAe,GAAG,CAAC,CAAC;IAC3BhE,MAAM,CAAC8E,kBAAkB,GAAG,EAAE;IAC9B9E,MAAM,CAAC2pB,OAAO,GAAG,CAAC,GAAG3pB,MAAM,CAAC4pB,WAAW,CAAC;IACxC,IAAIjoB,MAAM,CAACgoB,OAAO,IAAIjlB,KAAK,CAACY,OAAO,CAAC3D,MAAM,CAACgoB,OAAO,CAAC,EAAE;MACnD3pB,MAAM,CAAC2pB,OAAO,CAAC3mB,IAAI,CAAC,GAAGrB,MAAM,CAACgoB,OAAO,CAAC;IACxC;IACA,MAAMd,gBAAgB,GAAG,CAAC,CAAC;IAC3B7oB,MAAM,CAAC2pB,OAAO,CAAC7oB,OAAO,CAAC+oB,GAAG,IAAI;MAC5BA,GAAG,CAAC;QACFloB,MAAM;QACN3B,MAAM;QACNgC,YAAY,EAAE4mB,kBAAkB,CAACjnB,MAAM,EAAEknB,gBAAgB,CAAC;QAC1D5oB,EAAE,EAAED,MAAM,CAACC,EAAE,CAAC8kB,IAAI,CAAC/kB,MAAM,CAAC;QAC1BmE,IAAI,EAAEnE,MAAM,CAACmE,IAAI,CAAC4gB,IAAI,CAAC/kB,MAAM,CAAC;QAC9BqE,GAAG,EAAErE,MAAM,CAACqE,GAAG,CAAC0gB,IAAI,CAAC/kB,MAAM,CAAC;QAC5BE,IAAI,EAAEF,MAAM,CAACE,IAAI,CAAC6kB,IAAI,CAAC/kB,MAAM;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAM8pB,YAAY,GAAG5tB,MAAM,CAAC,CAAC,CAAC,EAAEisB,QAAQ,EAAEU,gBAAgB,CAAC;;IAE3D;IACA7oB,MAAM,CAAC2B,MAAM,GAAGzF,MAAM,CAAC,CAAC,CAAC,EAAE4tB,YAAY,EAAEX,gBAAgB,EAAExnB,MAAM,CAAC;IAClE3B,MAAM,CAAC0lB,cAAc,GAAGxpB,MAAM,CAAC,CAAC,CAAC,EAAE8D,MAAM,CAAC2B,MAAM,CAAC;IACjD3B,MAAM,CAAC+pB,YAAY,GAAG7tB,MAAM,CAAC,CAAC,CAAC,EAAEyF,MAAM,CAAC;;IAExC;IACA,IAAI3B,MAAM,CAAC2B,MAAM,IAAI3B,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,EAAE;MACrCgG,MAAM,CAACqD,IAAI,CAACtJ,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,CAAC,CAACa,OAAO,CAACkpB,SAAS,IAAI;QACjDhqB,MAAM,CAACC,EAAE,CAAC+pB,SAAS,EAAEhqB,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,CAAC+pB,SAAS,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;IACA,IAAIhqB,MAAM,CAAC2B,MAAM,IAAI3B,MAAM,CAAC2B,MAAM,CAACkD,KAAK,EAAE;MACxC7E,MAAM,CAAC6E,KAAK,CAAC7E,MAAM,CAAC2B,MAAM,CAACkD,KAAK,CAAC;IACnC;;IAEA;IACAoB,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;MACpBkH,OAAO,EAAElH,MAAM,CAAC2B,MAAM,CAACuF,OAAO;MAC9B/F,EAAE;MACF;MACAwmB,UAAU,EAAE,EAAE;MACd;MACAvgB,MAAM,EAAE,EAAE;MACVI,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAE,EAAE;MACZE,eAAe,EAAE,EAAE;MACnB;MACA5B,YAAYA,CAAA,EAAG;QACb,OAAO7F,MAAM,CAAC2B,MAAM,CAACgU,SAAS,KAAK,YAAY;MACjD,CAAC;MACD7P,UAAUA,CAAA,EAAG;QACX,OAAO9F,MAAM,CAAC2B,MAAM,CAACgU,SAAS,KAAK,UAAU;MAC/C,CAAC;MACD;MACAlI,WAAW,EAAE,CAAC;MACd6E,SAAS,EAAE,CAAC;MACZ;MACA/C,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,KAAK;MACZ;MACApB,SAAS,EAAE,CAAC;MACZmG,iBAAiB,EAAE,CAAC;MACpBtF,QAAQ,EAAE,CAAC;MACXgb,QAAQ,EAAE,CAAC;MACXpV,SAAS,EAAE,KAAK;MAChB9G,qBAAqBA,CAAA,EAAG;QACtB;QACA;QACA,OAAOzD,IAAI,CAAC4f,KAAK,CAAC,IAAI,CAAC9b,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE;MACvD,CAAC;MACD;MACAiI,cAAc,EAAErW,MAAM,CAAC2B,MAAM,CAAC0U,cAAc;MAC5CC,cAAc,EAAEtW,MAAM,CAAC2B,MAAM,CAAC2U,cAAc;MAC5C;MACAqE,eAAe,EAAE;QACf6C,SAAS,EAAE5J,SAAS;QACpB6J,OAAO,EAAE7J,SAAS;QAClB2K,mBAAmB,EAAE3K,SAAS;QAC9B8K,cAAc,EAAE9K,SAAS;QACzB4K,WAAW,EAAE5K,SAAS;QACtBM,gBAAgB,EAAEN,SAAS;QAC3BgH,cAAc,EAAEhH,SAAS;QACzBgL,kBAAkB,EAAEhL,SAAS;QAC7B;QACAiL,iBAAiB,EAAE7e,MAAM,CAAC2B,MAAM,CAACkd,iBAAiB;QAClD;QACAyD,aAAa,EAAE,CAAC;QAChB6H,YAAY,EAAEvW,SAAS;QACvB;QACAwW,UAAU,EAAE,EAAE;QACd9I,mBAAmB,EAAE1N,SAAS;QAC9B6K,WAAW,EAAE7K,SAAS;QACtBkJ,SAAS,EAAE,IAAI;QACfE,OAAO,EAAE;MACX,CAAC;MACD;MACAiB,UAAU,EAAE,IAAI;MAChB;MACAiB,cAAc,EAAElf,MAAM,CAAC2B,MAAM,CAACud,cAAc;MAC5C/B,OAAO,EAAE;QACPb,MAAM,EAAE,CAAC;QACTgC,MAAM,EAAE,CAAC;QACTH,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACX1D,IAAI,EAAE;MACR,CAAC;MACD;MACA2P,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;IAChB,CAAC,CAAC;IACFtqB,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA,IAAIF,MAAM,CAAC2B,MAAM,CAACsB,IAAI,EAAE;MACtBjD,MAAM,CAACiD,IAAI,CAAC,CAAC;IACf;;IAEA;IACA;IACA,OAAOjD,MAAM;EACf;EACA0G,iBAAiBA,CAAC6jB,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAAC1kB,YAAY,CAAC,CAAC,EAAE;MACvB,OAAO0kB,QAAQ;IACjB;IACA;IACA,OAAO;MACL,OAAO,EAAE,QAAQ;MACjB,YAAY,EAAE,aAAa;MAC3B,gBAAgB,EAAE,cAAc;MAChC,aAAa,EAAE,YAAY;MAC3B,cAAc,EAAE,eAAe;MAC/B,cAAc,EAAE,aAAa;MAC7B,eAAe,EAAE,gBAAgB;MACjC,aAAa,EAAE;IACjB,CAAC,CAACA,QAAQ,CAAC;EACb;EACA1R,aAAaA,CAACvQ,OAAO,EAAE;IACrB,MAAM;MACJ3B,QAAQ;MACRhF;IACF,CAAC,GAAG,IAAI;IACR,MAAMyF,MAAM,GAAG1M,eAAe,CAACiM,QAAQ,MAAAnI,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC;IAC/E,MAAMyI,eAAe,GAAG1T,YAAY,CAACgL,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAOhL,YAAY,CAACkM,OAAO,CAAC,GAAGwH,eAAe;EAChD;EACAvC,mBAAmBA,CAACvI,KAAK,EAAE;IACzB,OAAO,IAAI,CAAC6T,aAAa,CAAC,IAAI,CAACzR,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKnO,KAAK,CAAC,CAAC;EACvH;EACAwU,YAAYA,CAAA,EAAG;IACb,MAAMxZ,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ2G,QAAQ;MACRhF;IACF,CAAC,GAAG3B,MAAM;IACVA,MAAM,CAACoH,MAAM,GAAG1M,eAAe,CAACiM,QAAQ,MAAAnI,MAAA,CAAMmD,MAAM,CAAC0F,UAAU,mBAAgB,CAAC;EAClF;EACAgf,MAAMA,CAAA,EAAG;IACP,MAAMrmB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACkH,OAAO,EAAE;IACpBlH,MAAM,CAACkH,OAAO,GAAG,IAAI;IACrB,IAAIlH,MAAM,CAAC2B,MAAM,CAACka,UAAU,EAAE;MAC5B7b,MAAM,CAACsb,aAAa,CAAC,CAAC;IACxB;IACAtb,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;EACvB;EACAkmB,OAAOA,CAAA,EAAG;IACR,MAAMpmB,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAACkH,OAAO,EAAE;IACrBlH,MAAM,CAACkH,OAAO,GAAG,KAAK;IACtB,IAAIlH,MAAM,CAAC2B,MAAM,CAACka,UAAU,EAAE;MAC5B7b,MAAM,CAAC4b,eAAe,CAAC,CAAC;IAC1B;IACA5b,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;EACxB;EACAsqB,WAAWA,CAACvb,QAAQ,EAAE9B,KAAK,EAAE;IAC3B,MAAMnN,MAAM,GAAG,IAAI;IACnBiP,QAAQ,GAAG3E,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,GAAG,CAACoE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAMtE,GAAG,GAAG3K,MAAM,CAACyO,YAAY,CAAC,CAAC;IACjC,MAAM5D,GAAG,GAAG7K,MAAM,CAACsP,YAAY,CAAC,CAAC;IACjC,MAAMmb,OAAO,GAAG,CAAC5f,GAAG,GAAGF,GAAG,IAAIsE,QAAQ,GAAGtE,GAAG;IAC5C3K,MAAM,CAACyU,WAAW,CAACgW,OAAO,EAAE,OAAOtd,KAAK,KAAK,WAAW,GAAG,CAAC,GAAGA,KAAK,CAAC;IACrEnN,MAAM,CAACyS,iBAAiB,CAAC,CAAC;IAC1BzS,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;EAC9B;EACA0V,oBAAoBA,CAAA,EAAG;IACrB,MAAMhmB,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACgnB,YAAY,IAAI,CAAC3oB,MAAM,CAACmB,EAAE,EAAE;IAC/C,MAAMupB,GAAG,GAAG1qB,MAAM,CAACmB,EAAE,CAAC+M,SAAS,CAAC7O,KAAK,CAAC,GAAG,CAAC,CAACkK,MAAM,CAAC2E,SAAS,IAAI;MAC7D,OAAOA,SAAS,CAAC3P,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI2P,SAAS,CAAC3P,OAAO,CAACyB,MAAM,CAAC2B,MAAM,CAACgL,sBAAsB,CAAC,KAAK,CAAC;IAC3G,CAAC,CAAC;IACF3M,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEwqB,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;EACjD;EACAC,eAAeA,CAACtiB,OAAO,EAAE;IACvB,MAAMtI,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACM,SAAS,EAAE,OAAO,EAAE;IAC/B,OAAOgI,OAAO,CAAC4F,SAAS,CAAC7O,KAAK,CAAC,GAAG,CAAC,CAACkK,MAAM,CAAC2E,SAAS,IAAI;MACtD,OAAOA,SAAS,CAAC3P,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI2P,SAAS,CAAC3P,OAAO,CAACyB,MAAM,CAAC2B,MAAM,CAAC0F,UAAU,CAAC,KAAK,CAAC;IACrG,CAAC,CAAC,CAACsjB,IAAI,CAAC,GAAG,CAAC;EACd;EACA1Z,iBAAiBA,CAAA,EAAG;IAClB,MAAMjR,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACgnB,YAAY,IAAI,CAAC3oB,MAAM,CAACmB,EAAE,EAAE;IAC/C,MAAM0pB,OAAO,GAAG,EAAE;IAClB7qB,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;MAC/B,MAAMqf,UAAU,GAAG3nB,MAAM,CAAC4qB,eAAe,CAACtiB,OAAO,CAAC;MAClDuiB,OAAO,CAAC7nB,IAAI,CAAC;QACXsF,OAAO;QACPqf;MACF,CAAC,CAAC;MACF3nB,MAAM,CAACE,IAAI,CAAC,aAAa,EAAEoI,OAAO,EAAEqf,UAAU,CAAC;IACjD,CAAC,CAAC;IACF3nB,MAAM,CAACE,IAAI,CAAC,eAAe,EAAE2qB,OAAO,CAAC;EACvC;EACA7Y,oBAAoBA,CAAC8Y,IAAI,EAAEC,KAAK,EAAE;IAChC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,SAAS;IAClB;IACA,IAAIC,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,KAAK;IACf;IACA,MAAM/qB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ2B,MAAM;MACNyF,MAAM;MACNI,UAAU;MACVC,eAAe;MACftB,IAAI,EAAES,UAAU;MAChB6G;IACF,CAAC,GAAGzN,MAAM;IACV,IAAIgrB,GAAG,GAAG,CAAC;IACX,IAAI,OAAOrpB,MAAM,CAACyH,aAAa,KAAK,QAAQ,EAAE,OAAOzH,MAAM,CAACyH,aAAa;IACzE,IAAIzH,MAAM,CAACgH,cAAc,EAAE;MACzB,IAAIO,SAAS,GAAG9B,MAAM,CAACqG,WAAW,CAAC,GAAGnD,IAAI,CAACe,IAAI,CAACjE,MAAM,CAACqG,WAAW,CAAC,CAACjD,eAAe,CAAC,GAAG,CAAC;MACxF,IAAIygB,SAAS;MACb,KAAK,IAAI5nB,CAAC,GAAGoK,WAAW,GAAG,CAAC,EAAEpK,CAAC,GAAG+D,MAAM,CAAC3E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;QACvD,IAAI+D,MAAM,CAAC/D,CAAC,CAAC,IAAI,CAAC4nB,SAAS,EAAE;UAC3B/hB,SAAS,IAAIoB,IAAI,CAACe,IAAI,CAACjE,MAAM,CAAC/D,CAAC,CAAC,CAACmH,eAAe,CAAC;UACjDwgB,GAAG,IAAI,CAAC;UACR,IAAI9hB,SAAS,GAAGtC,UAAU,EAAEqkB,SAAS,GAAG,IAAI;QAC9C;MACF;MACA,KAAK,IAAI5nB,CAAC,GAAGoK,WAAW,GAAG,CAAC,EAAEpK,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI+D,MAAM,CAAC/D,CAAC,CAAC,IAAI,CAAC4nB,SAAS,EAAE;UAC3B/hB,SAAS,IAAI9B,MAAM,CAAC/D,CAAC,CAAC,CAACmH,eAAe;UACtCwgB,GAAG,IAAI,CAAC;UACR,IAAI9hB,SAAS,GAAGtC,UAAU,EAAEqkB,SAAS,GAAG,IAAI;QAC9C;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtB,KAAK,IAAIznB,CAAC,GAAGoK,WAAW,GAAG,CAAC,EAAEpK,CAAC,GAAG+D,MAAM,CAAC3E,MAAM,EAAEY,CAAC,IAAI,CAAC,EAAE;UACvD,MAAM6nB,WAAW,GAAGH,KAAK,GAAGvjB,UAAU,CAACnE,CAAC,CAAC,GAAGoE,eAAe,CAACpE,CAAC,CAAC,GAAGmE,UAAU,CAACiG,WAAW,CAAC,GAAG7G,UAAU,GAAGY,UAAU,CAACnE,CAAC,CAAC,GAAGmE,UAAU,CAACiG,WAAW,CAAC,GAAG7G,UAAU;UAC5J,IAAIskB,WAAW,EAAE;YACfF,GAAG,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA,KAAK,IAAI3nB,CAAC,GAAGoK,WAAW,GAAG,CAAC,EAAEpK,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC5C,MAAM6nB,WAAW,GAAG1jB,UAAU,CAACiG,WAAW,CAAC,GAAGjG,UAAU,CAACnE,CAAC,CAAC,GAAGuD,UAAU;UACxE,IAAIskB,WAAW,EAAE;YACfF,GAAG,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA,OAAOA,GAAG;EACZ;EACAlX,MAAMA,CAAA,EAAG;IACP,MAAM9T,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;IACjC,MAAM;MACJiH,QAAQ;MACR5F;IACF,CAAC,GAAG3B,MAAM;IACV;IACA,IAAI2B,MAAM,CAAC0H,WAAW,EAAE;MACtBrJ,MAAM,CAACsjB,aAAa,CAAC,CAAC;IACxB;IACA,CAAC,GAAGtjB,MAAM,CAACmB,EAAE,CAACooB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAACzoB,OAAO,CAACqQ,OAAO,IAAI;MACrE,IAAIA,OAAO,CAACga,QAAQ,EAAE;QACpBja,oBAAoB,CAAClR,MAAM,EAAEmR,OAAO,CAAC;MACvC;IACF,CAAC,CAAC;IACFnR,MAAM,CAAC0F,UAAU,CAAC,CAAC;IACnB1F,MAAM,CAACoG,YAAY,CAAC,CAAC;IACrBpG,MAAM,CAACmP,cAAc,CAAC,CAAC;IACvBnP,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;IAC5B,SAAS6D,YAAYA,CAAA,EAAG;MACtB,MAAMiX,cAAc,GAAGprB,MAAM,CAAC6G,YAAY,GAAG7G,MAAM,CAACoO,SAAS,GAAG,CAAC,CAAC,GAAGpO,MAAM,CAACoO,SAAS;MACrF,MAAM2G,YAAY,GAAGzK,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,GAAG,CAACugB,cAAc,EAAEprB,MAAM,CAACsP,YAAY,CAAC,CAAC,CAAC,EAAEtP,MAAM,CAACyO,YAAY,CAAC,CAAC,CAAC;MACrGzO,MAAM,CAACmU,YAAY,CAACY,YAAY,CAAC;MACjC/U,MAAM,CAACyS,iBAAiB,CAAC,CAAC;MAC1BzS,MAAM,CAACsQ,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI+a,UAAU;IACd,IAAI1pB,MAAM,CAACsW,QAAQ,IAAItW,MAAM,CAACsW,QAAQ,CAAC/Q,OAAO,IAAI,CAACvF,MAAM,CAACiH,OAAO,EAAE;MACjEuL,YAAY,CAAC,CAAC;MACd,IAAIxS,MAAM,CAACyO,UAAU,EAAE;QACrBpQ,MAAM,CAACkN,gBAAgB,CAAC,CAAC;MAC3B;IACF,CAAC,MAAM;MACL,IAAI,CAACvL,MAAM,CAACyH,aAAa,KAAK,MAAM,IAAIzH,MAAM,CAACyH,aAAa,GAAG,CAAC,KAAKpJ,MAAM,CAACwP,KAAK,IAAI,CAAC7N,MAAM,CAACgH,cAAc,EAAE;QAC3G,MAAMvB,MAAM,GAAGpH,MAAM,CAACiH,OAAO,IAAItF,MAAM,CAACsF,OAAO,CAACC,OAAO,GAAGlH,MAAM,CAACiH,OAAO,CAACG,MAAM,GAAGpH,MAAM,CAACoH,MAAM;QAC/FikB,UAAU,GAAGrrB,MAAM,CAACgW,OAAO,CAAC5O,MAAM,CAAC3E,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAChE,CAAC,MAAM;QACL4oB,UAAU,GAAGrrB,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAACyN,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjE;MACA,IAAI,CAAC4d,UAAU,EAAE;QACflX,YAAY,CAAC,CAAC;MAChB;IACF;IACA,IAAIxS,MAAM,CAAC2K,aAAa,IAAI/E,QAAQ,KAAKvH,MAAM,CAACuH,QAAQ,EAAE;MACxDvH,MAAM,CAACuM,aAAa,CAAC,CAAC;IACxB;IACAvM,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;EACvB;EACAumB,eAAeA,CAAC6E,YAAY,EAAEC,UAAU,EAAE;IACxC,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAG,IAAI;IACnB;IACA,MAAMvrB,MAAM,GAAG,IAAI;IACnB,MAAMwrB,gBAAgB,GAAGxrB,MAAM,CAAC2B,MAAM,CAACgU,SAAS;IAChD,IAAI,CAAC2V,YAAY,EAAE;MACjB;MACAA,YAAY,GAAGE,gBAAgB,KAAK,YAAY,GAAG,UAAU,GAAG,YAAY;IAC9E;IACA,IAAIF,YAAY,KAAKE,gBAAgB,IAAIF,YAAY,KAAK,YAAY,IAAIA,YAAY,KAAK,UAAU,EAAE;MACrG,OAAOtrB,MAAM;IACf;IACAA,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACI,MAAM,IAAAzO,MAAA,CAAIwB,MAAM,CAAC2B,MAAM,CAACgL,sBAAsB,EAAAnO,MAAA,CAAGgtB,gBAAgB,CAAE,CAAC;IACxFxrB,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACG,GAAG,IAAAxO,MAAA,CAAIwB,MAAM,CAAC2B,MAAM,CAACgL,sBAAsB,EAAAnO,MAAA,CAAG8sB,YAAY,CAAE,CAAC;IACjFtrB,MAAM,CAACgmB,oBAAoB,CAAC,CAAC;IAC7BhmB,MAAM,CAAC2B,MAAM,CAACgU,SAAS,GAAG2V,YAAY;IACtCtrB,MAAM,CAACoH,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;MAC/B,IAAIgjB,YAAY,KAAK,UAAU,EAAE;QAC/BhjB,OAAO,CAACzL,KAAK,CAACgB,KAAK,GAAG,EAAE;MAC1B,CAAC,MAAM;QACLyK,OAAO,CAACzL,KAAK,CAACkB,MAAM,GAAG,EAAE;MAC3B;IACF,CAAC,CAAC;IACFiC,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAC9B,IAAIqrB,UAAU,EAAEvrB,MAAM,CAAC8T,MAAM,CAAC,CAAC;IAC/B,OAAO9T,MAAM;EACf;EACAyrB,uBAAuBA,CAAC9V,SAAS,EAAE;IACjC,MAAM3V,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC8G,GAAG,IAAI6O,SAAS,KAAK,KAAK,IAAI,CAAC3V,MAAM,CAAC8G,GAAG,IAAI6O,SAAS,KAAK,KAAK,EAAE;IAC7E3V,MAAM,CAAC8G,GAAG,GAAG6O,SAAS,KAAK,KAAK;IAChC3V,MAAM,CAAC6G,YAAY,GAAG7G,MAAM,CAAC2B,MAAM,CAACgU,SAAS,KAAK,YAAY,IAAI3V,MAAM,CAAC8G,GAAG;IAC5E,IAAI9G,MAAM,CAAC8G,GAAG,EAAE;MACd9G,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACG,GAAG,IAAAxO,MAAA,CAAIwB,MAAM,CAAC2B,MAAM,CAACgL,sBAAsB,QAAK,CAAC;MACrE3M,MAAM,CAACmB,EAAE,CAAC0U,GAAG,GAAG,KAAK;IACvB,CAAC,MAAM;MACL7V,MAAM,CAACmB,EAAE,CAAC0L,SAAS,CAACI,MAAM,IAAAzO,MAAA,CAAIwB,MAAM,CAAC2B,MAAM,CAACgL,sBAAsB,QAAK,CAAC;MACxE3M,MAAM,CAACmB,EAAE,CAAC0U,GAAG,GAAG,KAAK;IACvB;IACA7V,MAAM,CAAC8T,MAAM,CAAC,CAAC;EACjB;EACA4X,KAAKA,CAACC,OAAO,EAAE;IACb,MAAM3rB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC4rB,OAAO,EAAE,OAAO,IAAI;;IAE/B;IACA,IAAIzqB,EAAE,GAAGwqB,OAAO,IAAI3rB,MAAM,CAAC2B,MAAM,CAACR,EAAE;IACpC,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAC1BA,EAAE,GAAGzE,QAAQ,CAAC6U,aAAa,CAACpQ,EAAE,CAAC;IACjC;IACA,IAAI,CAACA,EAAE,EAAE;MACP,OAAO,KAAK;IACd;IACAA,EAAE,CAACnB,MAAM,GAAGA,MAAM;IAClB,IAAImB,EAAE,CAAC0qB,UAAU,IAAI1qB,EAAE,CAAC0qB,UAAU,CAACzP,IAAI,IAAIjb,EAAE,CAAC0qB,UAAU,CAACzP,IAAI,CAAC0C,QAAQ,KAAK9e,MAAM,CAAC2B,MAAM,CAACymB,qBAAqB,CAAC0D,WAAW,CAAC,CAAC,EAAE;MAC5H9rB,MAAM,CAAC8C,SAAS,GAAG,IAAI;IACzB;IACA,MAAMipB,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,WAAAvtB,MAAA,CAAW,CAACwB,MAAM,CAAC2B,MAAM,CAAC+mB,YAAY,IAAI,EAAE,EAAEsD,IAAI,CAAC,CAAC,CAAC3sB,KAAK,CAAC,GAAG,CAAC,CAACsrB,IAAI,CAAC,GAAG,CAAC;IAC3E,CAAC;IACD,MAAMsB,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI9qB,EAAE,IAAIA,EAAE,CAACsQ,UAAU,IAAItQ,EAAE,CAACsQ,UAAU,CAACF,aAAa,EAAE;QACtD,MAAM2a,GAAG,GAAG/qB,EAAE,CAACsQ,UAAU,CAACF,aAAa,CAACwa,kBAAkB,CAAC,CAAC,CAAC;QAC7D;QACA,OAAOG,GAAG;MACZ;MACA,OAAOxxB,eAAe,CAACyG,EAAE,EAAE4qB,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD;IACA,IAAIxoB,SAAS,GAAG0oB,UAAU,CAAC,CAAC;IAC5B,IAAI,CAAC1oB,SAAS,IAAIvD,MAAM,CAAC2B,MAAM,CAAC0mB,cAAc,EAAE;MAC9C9kB,SAAS,GAAG3H,aAAa,CAAC,KAAK,EAAEoE,MAAM,CAAC2B,MAAM,CAAC+mB,YAAY,CAAC;MAC5DvnB,EAAE,CAACkY,MAAM,CAAC9V,SAAS,CAAC;MACpB7I,eAAe,CAACyG,EAAE,MAAA3C,MAAA,CAAMwB,MAAM,CAAC2B,MAAM,CAAC0F,UAAU,CAAE,CAAC,CAACvG,OAAO,CAACwH,OAAO,IAAI;QACrE/E,SAAS,CAAC8V,MAAM,CAAC/Q,OAAO,CAAC;MAC3B,CAAC,CAAC;IACJ;IACArC,MAAM,CAACC,MAAM,CAAClG,MAAM,EAAE;MACpBmB,EAAE;MACFoC,SAAS;MACToD,QAAQ,EAAE3G,MAAM,CAAC8C,SAAS,IAAI,CAAC3B,EAAE,CAAC0qB,UAAU,CAACzP,IAAI,CAAC+P,UAAU,GAAGhrB,EAAE,CAAC0qB,UAAU,CAACzP,IAAI,GAAG7Y,SAAS;MAC7FH,MAAM,EAAEpD,MAAM,CAAC8C,SAAS,GAAG3B,EAAE,CAAC0qB,UAAU,CAACzP,IAAI,GAAGjb,EAAE;MAClDyqB,OAAO,EAAE,IAAI;MACb;MACA9kB,GAAG,EAAE3F,EAAE,CAAC0U,GAAG,CAAC7W,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIxE,YAAY,CAAC2G,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK;MAC9E0F,YAAY,EAAE7G,MAAM,CAAC2B,MAAM,CAACgU,SAAS,KAAK,YAAY,KAAKxU,EAAE,CAAC0U,GAAG,CAAC7W,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIxE,YAAY,CAAC2G,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,CAAC;MACrI4F,QAAQ,EAAEvM,YAAY,CAAC+I,SAAS,EAAE,SAAS,CAAC,KAAK;IACnD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAN,IAAIA,CAAC9B,EAAE,EAAE;IACP,MAAMnB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACO,WAAW,EAAE,OAAOP,MAAM;IACrC,MAAM4rB,OAAO,GAAG5rB,MAAM,CAAC0rB,KAAK,CAACvqB,EAAE,CAAC;IAChC,IAAIyqB,OAAO,KAAK,KAAK,EAAE,OAAO5rB,MAAM;IACpCA,MAAM,CAACE,IAAI,CAAC,YAAY,CAAC;;IAEzB;IACA,IAAIF,MAAM,CAAC2B,MAAM,CAAC0H,WAAW,EAAE;MAC7BrJ,MAAM,CAACsjB,aAAa,CAAC,CAAC;IACxB;;IAEA;IACAtjB,MAAM,CAAC4nB,UAAU,CAAC,CAAC;;IAEnB;IACA5nB,MAAM,CAAC0F,UAAU,CAAC,CAAC;;IAEnB;IACA1F,MAAM,CAACoG,YAAY,CAAC,CAAC;IACrB,IAAIpG,MAAM,CAAC2B,MAAM,CAAC2K,aAAa,EAAE;MAC/BtM,MAAM,CAACuM,aAAa,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIvM,MAAM,CAAC2B,MAAM,CAACka,UAAU,IAAI7b,MAAM,CAACkH,OAAO,EAAE;MAC9ClH,MAAM,CAACsb,aAAa,CAAC,CAAC;IACxB;;IAEA;IACA,IAAItb,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,IAAInL,MAAM,CAACiH,OAAO,IAAIjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO,CAACC,OAAO,EAAE;MACzElH,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAAC2B,MAAM,CAACgV,YAAY,GAAG3W,MAAM,CAACiH,OAAO,CAACqE,YAAY,EAAE,CAAC,EAAEtL,MAAM,CAAC2B,MAAM,CAACyR,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC5H,CAAC,MAAM;MACLpT,MAAM,CAACgW,OAAO,CAAChW,MAAM,CAAC2B,MAAM,CAACgV,YAAY,EAAE,CAAC,EAAE3W,MAAM,CAAC2B,MAAM,CAACyR,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC9F;;IAEA;IACA,IAAIpT,MAAM,CAAC2B,MAAM,CAACwJ,IAAI,EAAE;MACtBnL,MAAM,CAAC8Y,UAAU,CAAClF,SAAS,EAAE,IAAI,CAAC;IACpC;;IAEA;IACA5T,MAAM,CAAC8kB,YAAY,CAAC,CAAC;IACrB,MAAMsH,YAAY,GAAG,CAAC,GAAGpsB,MAAM,CAACmB,EAAE,CAACooB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACxE,IAAIvpB,MAAM,CAAC8C,SAAS,EAAE;MACpBspB,YAAY,CAACppB,IAAI,CAAC,GAAGhD,MAAM,CAACoD,MAAM,CAACmmB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IAC1E;IACA6C,YAAY,CAACtrB,OAAO,CAACqQ,OAAO,IAAI;MAC9B,IAAIA,OAAO,CAACga,QAAQ,EAAE;QACpBja,oBAAoB,CAAClR,MAAM,EAAEmR,OAAO,CAAC;MACvC,CAAC,MAAM;QACLA,OAAO,CAACtP,gBAAgB,CAAC,MAAM,EAAEpH,CAAC,IAAI;UACpCyW,oBAAoB,CAAClR,MAAM,EAAEvF,CAAC,CAACyG,MAAM,CAAC;QACxC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF0Q,OAAO,CAAC5R,MAAM,CAAC;;IAEf;IACAA,MAAM,CAACO,WAAW,GAAG,IAAI;IACzBqR,OAAO,CAAC5R,MAAM,CAAC;;IAEf;IACAA,MAAM,CAACE,IAAI,CAAC,MAAM,CAAC;IACnBF,MAAM,CAACE,IAAI,CAAC,WAAW,CAAC;IACxB,OAAOF,MAAM;EACf;EACAwD,OAAOA,CAAC6oB,cAAc,EAAEC,WAAW,EAAE;IACnC,IAAID,cAAc,KAAK,KAAK,CAAC,EAAE;MAC7BA,cAAc,GAAG,IAAI;IACvB;IACA,IAAIC,WAAW,KAAK,KAAK,CAAC,EAAE;MAC1BA,WAAW,GAAG,IAAI;IACpB;IACA,MAAMtsB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ2B,MAAM;MACNR,EAAE;MACFoC,SAAS;MACT6D;IACF,CAAC,GAAGpH,MAAM;IACV,IAAI,OAAOA,MAAM,CAAC2B,MAAM,KAAK,WAAW,IAAI3B,MAAM,CAACM,SAAS,EAAE;MAC5D,OAAO,IAAI;IACb;IACAN,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;;IAE5B;IACAF,MAAM,CAACO,WAAW,GAAG,KAAK;;IAE1B;IACAP,MAAM,CAACglB,YAAY,CAAC,CAAC;;IAErB;IACA,IAAIrjB,MAAM,CAACwJ,IAAI,EAAE;MACfnL,MAAM,CAACmb,WAAW,CAAC,CAAC;IACtB;;IAEA;IACA,IAAImR,WAAW,EAAE;MACftsB,MAAM,CAAC8nB,aAAa,CAAC,CAAC;MACtB,IAAI3mB,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;QAChCA,EAAE,CAACwQ,eAAe,CAAC,OAAO,CAAC;MAC7B;MACA,IAAIpO,SAAS,EAAE;QACbA,SAAS,CAACoO,eAAe,CAAC,OAAO,CAAC;MACpC;MACA,IAAIvK,MAAM,IAAIA,MAAM,CAAC3E,MAAM,EAAE;QAC3B2E,MAAM,CAACtG,OAAO,CAACwH,OAAO,IAAI;UACxBA,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACtL,MAAM,CAACoN,iBAAiB,EAAEpN,MAAM,CAACqN,sBAAsB,EAAErN,MAAM,CAACmP,gBAAgB,EAAEnP,MAAM,CAACoP,cAAc,EAAEpP,MAAM,CAACqP,cAAc,CAAC;UACxJ1I,OAAO,CAACqJ,eAAe,CAAC,OAAO,CAAC;UAChCrJ,OAAO,CAACqJ,eAAe,CAAC,yBAAyB,CAAC;QACpD,CAAC,CAAC;MACJ;IACF;IACA3R,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA+F,MAAM,CAACqD,IAAI,CAACtJ,MAAM,CAACgE,eAAe,CAAC,CAAClD,OAAO,CAACkpB,SAAS,IAAI;MACvDhqB,MAAM,CAACqE,GAAG,CAAC2lB,SAAS,CAAC;IACvB,CAAC,CAAC;IACF,IAAIqC,cAAc,KAAK,KAAK,EAAE;MAC5B,IAAIrsB,MAAM,CAACmB,EAAE,IAAI,OAAOnB,MAAM,CAACmB,EAAE,KAAK,QAAQ,EAAE;QAC9CnB,MAAM,CAACmB,EAAE,CAACnB,MAAM,GAAG,IAAI;MACzB;MACA1D,WAAW,CAAC0D,MAAM,CAAC;IACrB;IACAA,MAAM,CAACM,SAAS,GAAG,IAAI;IACvB,OAAO,IAAI;EACb;EACA,OAAOisB,cAAcA,CAACC,WAAW,EAAE;IACjCtwB,MAAM,CAACitB,gBAAgB,EAAEqD,WAAW,CAAC;EACvC;EACA,WAAWrD,gBAAgBA,CAAA,EAAG;IAC5B,OAAOA,gBAAgB;EACzB;EACA,WAAWhB,QAAQA,CAAA,EAAG;IACpB,OAAOA,QAAQ;EACjB;EACA,OAAOsE,aAAaA,CAAC5C,GAAG,EAAE;IACxB,IAAI,CAACT,MAAM,CAACC,SAAS,CAACO,WAAW,EAAER,MAAM,CAACC,SAAS,CAACO,WAAW,GAAG,EAAE;IACpE,MAAMD,OAAO,GAAGP,MAAM,CAACC,SAAS,CAACO,WAAW;IAC5C,IAAI,OAAOC,GAAG,KAAK,UAAU,IAAIF,OAAO,CAACprB,OAAO,CAACsrB,GAAG,CAAC,GAAG,CAAC,EAAE;MACzDF,OAAO,CAAC3mB,IAAI,CAAC6mB,GAAG,CAAC;IACnB;EACF;EACA,OAAO6C,GAAGA,CAACC,MAAM,EAAE;IACjB,IAAIjoB,KAAK,CAACY,OAAO,CAACqnB,MAAM,CAAC,EAAE;MACzBA,MAAM,CAAC7rB,OAAO,CAAC8rB,CAAC,IAAIxD,MAAM,CAACqD,aAAa,CAACG,CAAC,CAAC,CAAC;MAC5C,OAAOxD,MAAM;IACf;IACAA,MAAM,CAACqD,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAOvD,MAAM;EACf;AACF;AACAnjB,MAAM,CAACqD,IAAI,CAAC4f,UAAU,CAAC,CAACpoB,OAAO,CAAC+rB,cAAc,IAAI;EAChD5mB,MAAM,CAACqD,IAAI,CAAC4f,UAAU,CAAC2D,cAAc,CAAC,CAAC,CAAC/rB,OAAO,CAACgsB,WAAW,IAAI;IAC7D1D,MAAM,CAACC,SAAS,CAACyD,WAAW,CAAC,GAAG5D,UAAU,CAAC2D,cAAc,CAAC,CAACC,WAAW,CAAC;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACF1D,MAAM,CAACsD,GAAG,CAAC,CAAC5sB,MAAM,EAAEiC,QAAQ,CAAC,CAAC;AAE9B,SAASqnB,MAAM,IAAI2D,CAAC,EAAE5E,QAAQ,IAAIpsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}