{"ast": null, "code": "import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\nfunction createMixers(output, ease, customMixer) {\n  const mixers = [];\n  const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n  const numMixers = output.length - 1;\n  for (let i = 0; i < numMixers; i++) {\n    let mixer = mixerFactory(output[i], output[i + 1]);\n    if (ease) {\n      const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n      mixer = pipe(easingFunction, mixer);\n    }\n    mixers.push(mixer);\n  }\n  return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output) {\n  let {\n    clamp: isClamp = true,\n    ease,\n    mixer\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const inputLength = input.length;\n  invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n  /**\n   * If we're only provided a single input, we can just make a function\n   * that returns the output.\n   */\n  if (inputLength === 1) return () => output[0];\n  if (inputLength === 2 && output[0] === output[1]) return () => output[1];\n  const isZeroDeltaRange = input[0] === input[1];\n  // If input runs highest -> lowest, reverse both arrays\n  if (input[0] > input[inputLength - 1]) {\n    input = [...input].reverse();\n    output = [...output].reverse();\n  }\n  const mixers = createMixers(output, ease, mixer);\n  const numMixers = mixers.length;\n  const interpolator = v => {\n    if (isZeroDeltaRange && v < input[0]) return output[0];\n    let i = 0;\n    if (numMixers > 1) {\n      for (; i < input.length - 2; i++) {\n        if (v < input[i + 1]) break;\n      }\n    }\n    const progressInRange = progress(input[i], input[i + 1], v);\n    return mixers[i](progressInRange);\n  };\n  return isClamp ? v => interpolator(clamp(input[0], input[inputLength - 1], v)) : interpolator;\n}\nexport { interpolate };", "map": {"version": 3, "names": ["invariant", "clamp", "MotionGlobalConfig", "noop", "pipe", "progress", "mix", "createMixers", "output", "ease", "customMixer", "mixers", "mixerFactory", "numMixers", "length", "i", "mixer", "easingFunction", "Array", "isArray", "push", "interpolate", "input", "isClamp", "arguments", "undefined", "inputLength", "isZeroDeltaRange", "reverse", "interpolator", "v", "progressInRange"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/utils/interpolate.mjs"], "sourcesContent": ["import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACzF,SAASC,GAAG,QAAQ,iBAAiB;AAErC,SAASC,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAC7C,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGF,WAAW,IAAIR,kBAAkB,CAACI,GAAG,IAAIA,GAAG;EACjE,MAAMO,SAAS,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAChC,IAAIC,KAAK,GAAGJ,YAAY,CAACJ,MAAM,CAACO,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAIN,IAAI,EAAE;MACN,MAAMQ,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,GAAGA,IAAI,CAACM,CAAC,CAAC,IAAIZ,IAAI,GAAGM,IAAI;MACnEO,KAAK,GAAGZ,IAAI,CAACa,cAAc,EAAED,KAAK,CAAC;IACvC;IACAL,MAAM,CAACS,IAAI,CAACJ,KAAK,CAAC;EACtB;EACA,OAAOL,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAACC,KAAK,EAAEd,MAAM,EAA+C;EAAA,IAA7C;IAAEP,KAAK,EAAEsB,OAAO,GAAG,IAAI;IAAEd,IAAI;IAAEO;EAAM,CAAC,GAAAQ,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC3E,MAAME,WAAW,GAAGJ,KAAK,CAACR,MAAM;EAChCd,SAAS,CAAC0B,WAAW,KAAKlB,MAAM,CAACM,MAAM,EAAE,sDAAsD,CAAC;EAChG;AACJ;AACA;AACA;EACI,IAAIY,WAAW,KAAK,CAAC,EACjB,OAAO,MAAMlB,MAAM,CAAC,CAAC,CAAC;EAC1B,IAAIkB,WAAW,KAAK,CAAC,IAAIlB,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAC5C,OAAO,MAAMA,MAAM,CAAC,CAAC,CAAC;EAC1B,MAAMmB,gBAAgB,GAAGL,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC;EAC9C;EACA,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACI,WAAW,GAAG,CAAC,CAAC,EAAE;IACnCJ,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC;IAC5BpB,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC,CAACoB,OAAO,CAAC,CAAC;EAClC;EACA,MAAMjB,MAAM,GAAGJ,YAAY,CAACC,MAAM,EAAEC,IAAI,EAAEO,KAAK,CAAC;EAChD,MAAMH,SAAS,GAAGF,MAAM,CAACG,MAAM;EAC/B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IACxB,IAAIH,gBAAgB,IAAIG,CAAC,GAAGR,KAAK,CAAC,CAAC,CAAC,EAChC,OAAOd,MAAM,CAAC,CAAC,CAAC;IACpB,IAAIO,CAAC,GAAG,CAAC;IACT,IAAIF,SAAS,GAAG,CAAC,EAAE;MACf,OAAOE,CAAC,GAAGO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC9B,IAAIe,CAAC,GAAGR,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAChB;MACR;IACJ;IACA,MAAMgB,eAAe,GAAG1B,QAAQ,CAACiB,KAAK,CAACP,CAAC,CAAC,EAAEO,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAAEe,CAAC,CAAC;IAC3D,OAAOnB,MAAM,CAACI,CAAC,CAAC,CAACgB,eAAe,CAAC;EACrC,CAAC;EACD,OAAOR,OAAO,GACPO,CAAC,IAAKD,YAAY,CAAC5B,KAAK,CAACqB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACI,WAAW,GAAG,CAAC,CAAC,EAAEI,CAAC,CAAC,CAAC,GAC/DD,YAAY;AACtB;AAEA,SAASR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}