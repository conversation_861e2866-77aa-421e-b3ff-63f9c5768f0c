{"ast": null, "code": "import React,{useEffect,useState,useCallback}from'react';import styles from'./offers.module.css';import AOS from'aos';import'aos/dist/aos.css';import arrowIcon from'../../asset/imgs/icons/arrow.svg';import{useNavigate}from'react-router-dom';import Notification from'../../components/Notification/Notification';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SkeletonCard=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(styles.card,\" \").concat(styles.skeletonCard),children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.img,\" \").concat(styles.skeleton_img)}),/*#__PURE__*/_jsxs(\"div\",{className:styles.title,children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.textLine)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.textLine)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.linkLine)})]});const Offers=()=>{const[pageLoading,setPageLoading]=useState(true);const[loading,setLoading]=useState(true);const[news,setNews]=useState([]);const navigate=useNavigate();const[notification,setNotification]=useState({message:'',type:''});const showNotification=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);};useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';setPageLoading(false);setLoading(true);fetch('https://api.gwm.tj/api/v1/promo').then(res=>res.json()).then(json=>{// Исправляем — теперь берем promo, а не news\nsetNews(json.promo||[]);setLoading(false);document.body.style.overflow='visible';AOS.refresh();}).catch(err=>{setLoading(false);showNotification('Не удалось загрузить список. Возможно идет тех.работа','error');document.body.style.overflow='visible';});return()=>{document.body.style.overflow='visible';};},[]);const handleNavigation=useCallback(slug=>{navigate(\"/offers/\".concat(slug));},[navigate]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),pageLoading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"50\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041E\\u0422\\u041A\\u0420\\u041E\\u0419\\u0422\\u0415 \\u0414\\u041B\\u042F \\u0421\\u0415\\u0411\\u042F \\u0423\\u041D\\u0418\\u041A\\u0410\\u041B\\u042C\\u041D\\u042B\\u0415 \\u0412\\u041E\\u0417\\u041C\\u041E\\u0416\\u041D\\u041E\\u0421\\u0422\\u0418\"})}),/*#__PURE__*/_jsx(\"i\",{className:\"redLine\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\\u043C\\u0438, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u044B \\u043F\\u043E\\u0434\\u0433\\u043E\\u0442\\u043E\\u0432\\u0438\\u043B\\u0438 \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441 \\u0432 \\u044D\\u0442\\u043E\\u043C \\u043C\\u0435\\u0441\\u044F\\u0446\\u0435.\"})]}),/*#__PURE__*/_jsx(\"section\",{children:/*#__PURE__*/_jsx(\"div\",{className:styles.flexContent,children:loading?Array.from({length:4}).map((_,index)=>/*#__PURE__*/_jsx(SkeletonCard,{},index)):Array.isArray(news)&&news.length>0?news.map(item=>{var _item$title;const title=(item===null||item===void 0?void 0:(_item$title=item.title)===null||_item$title===void 0?void 0:_item$title.length)>60?item.title.slice(0,60).trim()+'...':item.title;return/*#__PURE__*/_jsxs(\"div\",{className:styles.card,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,children:(item===null||item===void 0?void 0:item.preview)&&/*#__PURE__*/_jsx(\"img\",{src:item.preview,alt:item.title||'Image'})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.title,children:[(item===null||item===void 0?void 0:item.created_at)&&/*#__PURE__*/_jsx(\"span\",{children:new Date(item.created_at).toLocaleDateString('ru-RU')}),title&&/*#__PURE__*/_jsx(\"h2\",{children:title})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"link\",onClick:()=>handleNavigation(item.slug),children:[/*#__PURE__*/_jsx(\"b\",{children:\"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u0435\\u0435\"}),/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]})]},item.id);}):/*#__PURE__*/_jsx(\"p\",{children:\"\\u041D\\u0435\\u0442 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0438.\"})})})]})})]});};export default Offers;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "styles", "AOS", "arrowIcon", "useNavigate", "Notification", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SkeletonCard", "className", "concat", "card", "skeletonCard", "children", "img", "skeleton_img", "title", "skeleton", "textLine", "linkLine", "Offers", "pageLoading", "setPageLoading", "loading", "setLoading", "news", "setNews", "navigate", "notification", "setNotification", "message", "type", "showNotification", "arguments", "length", "undefined", "setTimeout", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "fetch", "then", "res", "json", "promo", "refresh", "catch", "err", "handleNavigation", "slug", "flexContent", "Array", "from", "map", "_", "index", "isArray", "item", "_item$title", "slice", "trim", "preview", "src", "alt", "created_at", "Date", "toLocaleDateString", "onClick", "id"], "sources": ["/var/www/html/gwm.tj/src/pages/Offer/Offers.jsx"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from 'react';\nimport styles from './offers.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useNavigate } from 'react-router-dom';\nimport Notification from '../../components/Notification/Notification';\n\nconst SkeletonCard = () => (\n  <div className={`${styles.card} ${styles.skeletonCard}`}>\n    <div className={`${styles.img} ${styles.skeleton_img}`}></div>\n    <div className={styles.title}>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n    </div>\n    <div className={`${styles.skeleton} ${styles.linkLine}`}></div>\n  </div>\n);\n\nconst Offers = () => {\n  const [pageLoading, setPageLoading] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [news, setNews] = useState([]);\n  const navigate = useNavigate();\n  const [notification, setNotification] = useState({ message: '', type: '' });\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  };\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    setPageLoading(false);\n    setLoading(true);\n\n    fetch('https://api.gwm.tj/api/v1/promo')\n      .then((res) => res.json())\n      .then((json) => {\n        // Исправляем — теперь берем promo, а не news\n        setNews(json.promo || []);\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n        AOS.refresh();\n      })\n      .catch((err) => {\n        setLoading(false);\n        showNotification(\n          'Не удалось загрузить список. Возможно идет тех.работа',\n          'error'\n        );\n        document.body.style.overflow = 'visible';\n      });\n\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  const handleNavigation = useCallback(\n    (slug) => {\n      navigate(`/offers/${slug}`);\n    },\n    [navigate]\n  );\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {pageLoading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"50\">\n                <strong>ОТКРОЙТЕ ДЛЯ СЕБЯ УНИКАЛЬНЫЕ ВОЗМОЖНОСТИ</strong>\n              </h1>\n              <i\n                className=\"redLine\"\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n              ></i>\n              <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Ознакомьтесь с предложениями, которые мы подготовили для вас в\n                этом месяце.\n              </p>\n            </div>\n            <section>\n              <div className={styles.flexContent}>\n                {loading ? (\n                  Array.from({ length: 4 }).map((_, index) => (\n                    <SkeletonCard key={index} />\n                  ))\n                ) : Array.isArray(news) && news.length > 0 ? (\n                  news.map((item) => {\n                    const title =\n                      item?.title?.length > 60\n                        ? item.title.slice(0, 60).trim() + '...'\n                        : item.title;\n                    return (\n                      <div className={styles.card} key={item.id}>\n                        <div className={styles.img}>\n                          {item?.preview && (\n                            <img\n                              src={item.preview}\n                              alt={item.title || 'Image'}\n                            />\n                          )}\n                        </div>\n                        <div className={styles.title}>\n                          {item?.created_at && (\n                            <span>\n                              {new Date(item.created_at).toLocaleDateString(\n                                'ru-RU'\n                              )}\n                            </span>\n                          )}\n                          {title && <h2>{title}</h2>}\n                          {/*  sub_title */}\n                          {/* <p>{item.type}</p> */}\n                        </div>\n\n                        <div\n                          className=\"link\"\n                          onClick={() => handleNavigation(item.slug)}\n                        >\n                          <b>Подробнее</b>\n                          <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                        </div>\n                      </div>\n                    );\n                  })\n                ) : (\n                  <p>Нет предложении.</p>\n                )}\n              </div>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Offers;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAC/D,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,YAAY,KAAM,4CAA4C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtE,KAAM,CAAAC,YAAY,CAAGA,CAAA,gBACnBH,KAAA,QAAKI,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACc,IAAI,MAAAD,MAAA,CAAIb,MAAM,CAACe,YAAY,CAAG,CAAAC,QAAA,eACtDV,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACiB,GAAG,MAAAJ,MAAA,CAAIb,MAAM,CAACkB,YAAY,CAAG,CAAM,CAAC,cAC9DV,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACmB,KAAM,CAAAH,QAAA,eAC3BV,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACqB,QAAQ,CAAG,CAAM,CAAC,cAC/Df,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACqB,QAAQ,CAAG,CAAM,CAAC,EAC5D,CAAC,cACNf,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACsB,QAAQ,CAAG,CAAM,CAAC,EAC5D,CACN,CAED,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,IAAI,CAAEC,OAAO,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAAgC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC4B,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,CAAEmC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAE3E,KAAM,CAAAC,gBAAgB,CAAG,QAAAA,CAACF,OAAO,CAAuB,IAArB,CAAAC,IAAI,CAAAE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACjDJ,eAAe,CAAC,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAC,CAClCK,UAAU,CAAC,IAAMP,eAAe,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAEDrC,SAAS,CAAC,IAAM,CACdI,GAAG,CAACuC,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvCvB,cAAc,CAAC,KAAK,CAAC,CACrBE,UAAU,CAAC,IAAI,CAAC,CAEhBsB,KAAK,CAAC,iCAAiC,CAAC,CACrCC,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEE,IAAI,EAAK,CACd;AACAvB,OAAO,CAACuB,IAAI,CAACC,KAAK,EAAI,EAAE,CAAC,CACzB1B,UAAU,CAAC,KAAK,CAAC,CACjBkB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CACxC/C,GAAG,CAACqD,OAAO,CAAC,CAAC,CACf,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,EAAK,CACd7B,UAAU,CAAC,KAAK,CAAC,CACjBQ,gBAAgB,CACd,uDAAuD,CACvD,OACF,CAAC,CACDU,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAC,CAEJ,MAAO,IAAM,CACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,gBAAgB,CAAG1D,WAAW,CACjC2D,IAAI,EAAK,CACR5B,QAAQ,YAAAjB,MAAA,CAAY6C,IAAI,CAAE,CAAC,CAC7B,CAAC,CACD,CAAC5B,QAAQ,CACX,CAAC,CAED,mBACEtB,KAAA,CAAAE,SAAA,EAAAM,QAAA,eACEV,IAAA,CAACF,YAAY,EAAC6B,OAAO,CAAEF,YAAY,CAACE,OAAQ,CAACC,IAAI,CAAEH,YAAY,CAACG,IAAK,CAAE,CAAC,CACvEV,WAAW,cACVlB,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAI,QAAA,cAC5BV,IAAA,QAAKM,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENN,IAAA,QAAKM,SAAS,CAAC,SAAS,CAAAI,QAAA,cACtBR,KAAA,QAAKI,SAAS,CAAC,WAAW,CAAAI,QAAA,eACxBR,KAAA,QAAKI,SAAS,CAAC,SAAS,CAAAI,QAAA,eACtBV,IAAA,OAAIM,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAC,iBAAe,IAAI,CAAAI,QAAA,cAC1DV,IAAA,WAAAU,QAAA,CAAQ,8NAAwC,CAAQ,CAAC,CACvD,CAAC,cACLV,IAAA,MACEM,SAAS,CAAC,SAAS,CACnB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAClB,CAAC,cACLN,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAU,QAAA,CAAC,wYAG3C,CAAG,CAAC,EACD,CAAC,cACNV,IAAA,YAAAU,QAAA,cACEV,IAAA,QAAKM,SAAS,CAAEZ,MAAM,CAAC2D,WAAY,CAAA3C,QAAA,CAChCU,OAAO,CACNkC,KAAK,CAACC,IAAI,CAAC,CAAExB,MAAM,CAAE,CAAE,CAAC,CAAC,CAACyB,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,gBACrC1D,IAAA,CAACK,YAAY,IAAMqD,KAAQ,CAC5B,CAAC,CACAJ,KAAK,CAACK,OAAO,CAACrC,IAAI,CAAC,EAAIA,IAAI,CAACS,MAAM,CAAG,CAAC,CACxCT,IAAI,CAACkC,GAAG,CAAEI,IAAI,EAAK,KAAAC,WAAA,CACjB,KAAM,CAAAhD,KAAK,CACT,CAAA+C,IAAI,SAAJA,IAAI,kBAAAC,WAAA,CAAJD,IAAI,CAAE/C,KAAK,UAAAgD,WAAA,iBAAXA,WAAA,CAAa9B,MAAM,EAAG,EAAE,CACpB6B,IAAI,CAAC/C,KAAK,CAACiD,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAG,KAAK,CACtCH,IAAI,CAAC/C,KAAK,CAChB,mBACEX,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACc,IAAK,CAAAE,QAAA,eAC1BV,IAAA,QAAKM,SAAS,CAAEZ,MAAM,CAACiB,GAAI,CAAAD,QAAA,CACxB,CAAAkD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,gBACZhE,IAAA,QACEiE,GAAG,CAAEL,IAAI,CAACI,OAAQ,CAClBE,GAAG,CAAEN,IAAI,CAAC/C,KAAK,EAAI,OAAQ,CAC5B,CACF,CACE,CAAC,cACNX,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACmB,KAAM,CAAAH,QAAA,EAC1B,CAAAkD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEO,UAAU,gBACfnE,IAAA,SAAAU,QAAA,CACG,GAAI,CAAA0D,IAAI,CAACR,IAAI,CAACO,UAAU,CAAC,CAACE,kBAAkB,CAC3C,OACF,CAAC,CACG,CACP,CACAxD,KAAK,eAAIb,IAAA,OAAAU,QAAA,CAAKG,KAAK,CAAK,CAAC,EAGvB,CAAC,cAENX,KAAA,QACEI,SAAS,CAAC,MAAM,CAChBgE,OAAO,CAAEA,CAAA,GAAMnB,gBAAgB,CAACS,IAAI,CAACR,IAAI,CAAE,CAAA1C,QAAA,eAE3CV,IAAA,MAAAU,QAAA,CAAG,wDAAS,CAAG,CAAC,cAChBV,IAAA,QAAKiE,GAAG,CAAErE,SAAU,CAACsE,GAAG,CAAC,EAAE,CAAC5D,SAAS,CAAC,UAAU,CAAE,CAAC,EAChD,CAAC,GA5B0BsD,IAAI,CAACW,EA6BlC,CAAC,CAEV,CAAC,CAAC,cAEFvE,IAAA,MAAAU,QAAA,CAAG,wFAAgB,CAAG,CACvB,CACE,CAAC,CACC,CAAC,EACP,CAAC,CACH,CACN,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAO,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}