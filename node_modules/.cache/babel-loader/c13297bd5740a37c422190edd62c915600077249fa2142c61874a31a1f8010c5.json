{"ast": null, "code": "import{useState,useEffect,useRef}from'react';import{Link}from'react-router-dom';import{FiChevronDown,FiChevronUp}from'react-icons/fi';// sotcial icons\nimport{PiFacebookLogo}from'react-icons/pi';import{<PERSON><PERSON>nstagramLogo}from'react-icons/pi';import{PiYoutubeLogo}from'react-icons/pi';import{PiTelegramLogo}from'react-icons/pi';import logo from'../../asset/imgs/logo/PcLogo.svg';import vector from'../../asset/imgs/logo/vector.webp';import{btnListData,footerMenuData}from'../../asset/data/footerData';import styles from'./footer.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const useIsMobile=function(){let breakpoint=arguments.length>0&&arguments[0]!==undefined?arguments[0]:768;const[isMobile,setIsMobile]=useState(window.innerWidth<=breakpoint);useEffect(()=>{const handleResize=()=>setIsMobile(window.innerWidth<=breakpoint);window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[breakpoint]);return isMobile;};const Footer=()=>{const isMobile=useIsMobile();const[openSections,setOpenSections]=useState({});const sectionRefs=useRef({});const[socialLinks,setSocialLinks]=useState([]);const toggleSection=id=>{setOpenSections(prev=>({[id]:!prev[id]// откроется/закроется только один в моб версии\n}));};useEffect(()=>{fetch('https://api.gwm.tj/api/v1/contacts').then(res=>res.json()).then(data=>{const contacts=data.contacts||{};const links=[];if(contacts.facebook)links.push({id:'facebook',url:contacts.facebook,icon:/*#__PURE__*/_jsx(PiFacebookLogo,{}),title:'Facebook'});if(contacts.instagram)links.push({id:'instagram',url:contacts.instagram,icon:/*#__PURE__*/_jsx(PiInstagramLogo,{}),title:'Instagram'});if(contacts.youtube)links.push({id:'youtube',url:contacts.youtube,icon:/*#__PURE__*/_jsx(PiYoutubeLogo,{}),title:'YouTube'});if(contacts.telegram)links.push({id:'telegram',url:contacts.telegram,icon:/*#__PURE__*/_jsx(PiTelegramLogo,{}),title:'Telegram'});setSocialLinks(links);});},[]);return/*#__PURE__*/_jsx(\"footer\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.content,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.logo,children:/*#__PURE__*/_jsx(Link,{to:\"/\",\"aria-label\":\"\\u041D\\u0430 \\u0433\\u043B\\u0430\\u0432\\u043D\\u0443\\u044E\",children:/*#__PURE__*/_jsx(\"img\",{src:logo,alt:\"GWM \\u041B\\u043E\\u0433\\u043E\\u0442\\u0438\\u043F\",loading:\"lazy\"})})}),/*#__PURE__*/_jsx(\"div\",{className:styles.btnsList,children:btnListData.map(_ref=>{let{id,url,icon,title}=_ref;return/*#__PURE__*/_jsx(Link,{to:url,className:styles.btnItem,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.item,children:[/*#__PURE__*/_jsx(\"img\",{src:icon,alt:title,loading:\"lazy\"}),/*#__PURE__*/_jsx(\"span\",{children:title})]})},id);})}),/*#__PURE__*/_jsx(\"div\",{className:styles.menu,children:/*#__PURE__*/_jsx(\"div\",{className:styles.menuContainer,children:footerMenuData.map(_ref2=>{var _sectionRefs$current$;let{id,title,items}=_ref2;const isOpen=openSections[id];return/*#__PURE__*/_jsxs(\"div\",{className:styles.menuItem,children:[/*#__PURE__*/_jsxs(\"button\",{className:styles.menuItemTitle,onClick:()=>isMobile&&toggleSection(id),\"aria-expanded\":isOpen,\"aria-controls\":\"footer-menu-\".concat(id),style:{cursor:isMobile?'pointer':'default',display:'flex',alignItems:'center',justifyContent:'space-between',background:'none',border:'none',color:'#fff',width:'100%'},children:[title,isMobile&&(isOpen?/*#__PURE__*/_jsx(FiChevronUp,{size:24}):/*#__PURE__*/_jsx(FiChevronDown,{size:24}))]}),/*#__PURE__*/_jsx(\"div\",{id:\"footer-menu-\".concat(id),className:\"\".concat(styles.menuLinks,\" \").concat(isMobile?styles.collapsible:'',\" \").concat(isOpen?styles.open:''),ref:el=>sectionRefs.current[id]=el,style:isMobile?{maxHeight:isOpen?\"\".concat((_sectionRefs$current$=sectionRefs.current[id])===null||_sectionRefs$current$===void 0?void 0:_sectionRefs$current$.scrollHeight,\"px\"):'0px',overflow:'hidden',transition:'max-height 0.3s ease'}:{},children:items.map(_ref3=>{let{id:linkId,url,title}=_ref3;return/*#__PURE__*/_jsx(Link,{to:url,children:title},linkId);})})]},id);})})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.related,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.social,children:socialLinks.map(_ref4=>{let{id,url,icon,title}=_ref4;return/*#__PURE__*/_jsx(\"a\",{href:url,target:\"_blank\",rel:\"noopener noreferrer\",\"aria-label\":title,children:typeof icon==='string'?/*#__PURE__*/_jsx(\"img\",{src:icon,alt:title,loading:\"lazy\"}):/*#__PURE__*/_jsx(\"span\",{className:styles.iconWrapper,children:icon})},id);})}),/*#__PURE__*/_jsx(\"div\",{className:styles.vector,children:/*#__PURE__*/_jsx(\"a\",{href:\"https://vector.tj\",target:\"_black\",children:/*#__PURE__*/_jsx(\"img\",{src:vector,alt:\"GWM \\u041B\\u043E\\u0433\\u043E\\u0442\\u0438\\u043F\",loading:\"lazy\"})})}),/*#__PURE__*/_jsx(\"div\",{className:styles.legacy,children:/*#__PURE__*/_jsx(Link,{to:\"/privacy\",children:\"\\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430 \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438\"})}),/*#__PURE__*/_jsx(\"div\",{className:styles.copy,children:\"\\xA9 2025 GWM \\u0422\\u0430\\u0434\\u0436\\u0438\\u043A\\u0438\\u0441\\u0442\\u0430\\u043D\"})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.desc,children:/*#__PURE__*/_jsx(\"span\",{children:\"Great Wall Motor Co. Ltd. (\\u0434\\u0430\\u043B\\u0435\\u0435 \\u0438\\u043C\\u0435\\u043D\\u0443\\u0435\\u043C\\u0430\\u044F \\xABGWM\\xBB) \\u2014 \\u043C\\u0435\\u0436\\u0434\\u0443\\u043D\\u0430\\u0440\\u043E\\u0434\\u043D\\u044B\\u0439 \\u043C\\u0443\\u043B\\u044C\\u0442\\u0438\\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0441\\u043E \\u0448\\u0442\\u0430\\u0431-\\u043A\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u043E\\u0439 \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435. \\u042D\\u0442\\u043E \\u0432\\u0441\\u0435\\u043C\\u0438\\u0440\\u043D\\u043E \\u0438\\u0437\\u0432\\u0435\\u0441\\u0442\\u043D\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u044C \\u0432\\u043D\\u0435\\u0434\\u043E\\u0440\\u043E\\u0436\\u043D\\u0438\\u043A\\u043E\\u0432 \\u0438 \\u043F\\u0438\\u043A\\u0430\\u043F\\u043E\\u0432, \\u0432\\u043B\\u0430\\u0434\\u0435\\u044E\\u0449\\u0438\\u0439 \\u043F\\u044F\\u0442\\u044C\\u044E \\u043B\\u0438\\u043D\\u0435\\u0439\\u043A\\u0430\\u043C\\u0438 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438, \\u0430 \\u0438\\u043C\\u0435\\u043D\\u043D\\u043E GWM HAVAL, GWM WEY, GWM ORA, GWM TANK \\u0438 GWM Pickup, \\u043E\\u0445\\u0432\\u0430\\u0442\\u044B\\u0432\\u0430\\u044E\\u0449\\u0438\\u043C\\u0438 \\u043B\\u0435\\u0433\\u043A\\u043E\\u0432\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u0438 \\u043F\\u0438\\u043A\\u0430\\u043F\\u044B. GWM \\u0438\\u043C\\u0435\\u0435\\u0442 \\u0441\\u0430\\u043C\\u043E\\u0434\\u043E\\u0441\\u0442\\u0430\\u0442\\u043E\\u0447\\u043D\\u0443\\u044E \\u0446\\u0435\\u043F\\u043E\\u0447\\u043A\\u0443 \\u0434\\u0432\\u0438\\u0433\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439, \\u0442\\u0440\\u0430\\u043D\\u0441\\u043C\\u0438\\u0441\\u0441\\u0438\\u0439 \\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u043E\\u0441\\u043D\\u043E\\u0432\\u043D\\u044B\\u0445 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u043E\\u0432. \\u0412 2003 \\u0433\\u043E\\u0434\\u0443 \\u043E\\u043D\\u0430 \\u0431\\u044B\\u043B\\u0430 \\u0432\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0430 \\u0432 \\u0441\\u043F\\u0438\\u0441\\u043E\\u043A \\u0430\\u043A\\u0446\\u0438\\u0439 H \\u0432 \\u0413\\u043E\\u043D\\u043A\\u043E\\u043D\\u0433\\u0435, \\u0430 \\u0432 2011 \\u0433\\u043E\\u0434\\u0443 \\u2014 \\u0430\\u043A\\u0446\\u0438\\u0439 A \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435. \\u041A \\u043A\\u043E\\u043D\\u0446\\u0443 2023 \\u0433\\u043E\\u0434\\u0430 \\u0435\\u0435 \\u0430\\u043A\\u0442\\u0438\\u0432\\u044B \\u0441\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\\u043B\\u0438 201,93 \\u043C\\u043B\\u0440\\u0434 \\u044E\\u0430\\u043D\\u0435\\u0439, \\u0430 \\u043E\\u0431\\u044A\\u0435\\u043C \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436 \\u043F\\u0440\\u0435\\u0432\\u044B\\u0441\\u0438\\u043B \\u043E\\u0434\\u0438\\u043D \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D \\u0432 \\u0442\\u0435\\u0447\\u0435\\u043D\\u0438\\u0435 8 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0434\\u0440\\u044F\"})})]})})});};export default Footer;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "Link", "FiChevronDown", "FiChevronUp", "PiFacebookLogo", "PiInstagramLogo", "PiYoutubeLogo", "PiTelegramLogo", "logo", "vector", "btnListData", "footerMenuData", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "useIsMobile", "breakpoint", "arguments", "length", "undefined", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "Footer", "openSections", "setOpenSections", "sectionRefs", "socialLinks", "setSocialLinks", "toggleSection", "id", "prev", "fetch", "then", "res", "json", "data", "contacts", "links", "facebook", "push", "url", "icon", "title", "instagram", "youtube", "telegram", "children", "className", "content", "to", "src", "alt", "loading", "btnsList", "map", "_ref", "btnItem", "item", "menu", "menuContainer", "_ref2", "_sectionRefs$current$", "items", "isOpen", "menuItem", "menuItemTitle", "onClick", "concat", "style", "cursor", "display", "alignItems", "justifyContent", "background", "border", "color", "width", "size", "menuLinks", "collapsible", "open", "ref", "el", "current", "maxHeight", "scrollHeight", "overflow", "transition", "_ref3", "linkId", "related", "social", "_ref4", "href", "target", "rel", "iconWrapper", "legacy", "copy", "desc"], "sources": ["/var/www/html/gwm.tj/src/layout/Footer/Footer.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiChevronDown, FiChevronUp } from 'react-icons/fi';\n// sotcial icons\nimport { PiFacebookLogo } from 'react-icons/pi'\nimport { <PERSON><PERSON>nstagramLogo } from 'react-icons/pi'\nimport { PiYoutubeLogo } from 'react-icons/pi'\nimport { PiTelegramLogo } from 'react-icons/pi';\n\nimport logo from '../../asset/imgs/logo/PcLogo.svg';\nimport vector from '../../asset/imgs/logo/vector.webp';\nimport { btnListData, footerMenuData } from '../../asset/data/footerData';\n\nimport styles from './footer.module.css';\n\nconst useIsMobile = (breakpoint = 768) => {\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= breakpoint);\n\n  useEffect(() => {\n    const handleResize = () => setIsMobile(window.innerWidth <= breakpoint);\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [breakpoint]);\n\n  return isMobile;\n};\n\nconst Footer = () => {\n  const isMobile = useIsMobile();\n  const [openSections, setOpenSections] = useState({});\n  const sectionRefs = useRef({});\n  const [socialLinks, setSocialLinks] = useState([]);\n\n  const toggleSection = (id) => {\n    setOpenSections((prev) => ({\n      [id]: !prev[id], // откроется/закроется только один в моб версии\n    }));\n  };\n  useEffect(() => {\n    fetch('https://api.gwm.tj/api/v1/contacts')\n      .then((res) => res.json())\n      .then((data) => {\n        const contacts = data.contacts || {};\n        const links = [];\n\n        if (contacts.facebook)\n          links.push({\n            id: 'facebook',\n            url: contacts.facebook,\n            icon: <PiFacebookLogo />,\n            title: 'Facebook',\n          });\n        if (contacts.instagram)\n          links.push({\n            id: 'instagram',\n            url: contacts.instagram,\n            icon: <PiInstagramLogo />,\n            title: 'Instagram',\n          });\n        if (contacts.youtube)\n          links.push({\n            id: 'youtube',\n            url: contacts.youtube,\n            icon: <PiYoutubeLogo />,\n            title: 'YouTube',\n          });\n        if (contacts.telegram)\n          links.push({\n            id: 'telegram',\n            url: contacts.telegram,\n            icon: <PiTelegramLogo />,\n            title: 'Telegram',\n          });\n\n        setSocialLinks(links);\n      });\n  }, []);\n\n  return (\n    <footer>\n      <div className=\"container\">\n        <div className={styles.content}>\n          {/* Логотип */}\n          <div className={styles.logo}>\n            <Link to=\"/\" aria-label=\"На главную\">\n              <img src={logo} alt=\"GWM Логотип\" loading=\"lazy\" />\n            </Link>\n          </div>\n\n          {/* Кнопки */}\n          <div className={styles.btnsList}>\n            {btnListData.map(({ id, url, icon, title }) => (\n              <Link to={url} key={id} className={styles.btnItem}>\n                <div className={styles.item}>\n                  <img src={icon} alt={title} loading=\"lazy\" />\n                  <span>{title}</span>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Меню */}\n          <div className={styles.menu}>\n            <div className={styles.menuContainer}>\n              {footerMenuData.map(({ id, title, items }) => {\n                const isOpen = openSections[id];\n\n                return (\n                  <div className={styles.menuItem} key={id}>\n                    <button\n                      className={styles.menuItemTitle}\n                      onClick={() => isMobile && toggleSection(id)}\n                      aria-expanded={isOpen}\n                      aria-controls={`footer-menu-${id}`}\n                      style={{\n                        cursor: isMobile ? 'pointer' : 'default',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        background: 'none',\n                        border: 'none',\n                        color: '#fff',\n                        width: '100%',\n                      }}\n                    >\n                      {title}\n                      {isMobile &&\n                        (isOpen ? (\n                          <FiChevronUp size={24} />\n                        ) : (\n                          <FiChevronDown size={24} />\n                        ))}\n                    </button>\n\n                    <div\n                      id={`footer-menu-${id}`}\n                      className={`${styles.menuLinks} ${isMobile ? styles.collapsible : ''\n                        } ${isOpen ? styles.open : ''}`}\n                      ref={(el) => (sectionRefs.current[id] = el)}\n                      style={\n                        isMobile\n                          ? {\n                            maxHeight: isOpen\n                              ? `${sectionRefs.current[id]?.scrollHeight}px`\n                              : '0px',\n                            overflow: 'hidden',\n                            transition: 'max-height 0.3s ease',\n                          }\n                          : {}\n                      }\n                    >\n                      {items.map(({ id: linkId, url, title }) => (\n                        <Link to={url} key={linkId}>\n                          {title}\n                        </Link>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Соцсети и копирайт */}\n          <div className={styles.related}>\n            <div className={styles.social}>\n              {socialLinks.map(({ id, url, icon, title }) => (\n                <a\n                  key={id}\n                  href={url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  aria-label={title}\n                >\n                  {typeof icon === 'string' ? (\n                    <img src={icon} alt={title} loading=\"lazy\" />\n                  ) : (\n                    <span className={styles.iconWrapper}>{icon}</span>\n                  )}\n                </a>\n              ))}\n            </div>\n\n            <div className={styles.vector}>\n              <a href=\"https://vector.tj\" target=\"_black\">\n                <img src={vector} alt=\"GWM Логотип\" loading=\"lazy\" />\n              </a>\n            </div>\n\n            <div className={styles.legacy}>\n              <Link to=\"/privacy\">Политика конфиденциальности</Link>\n            </div>\n\n            <div className={styles.copy}>© 2025 GWM Таджикистан</div>\n          </div>\n\n          {/* Описание */}\n          <div className={styles.desc}>\n            <span>\n              Great Wall Motor Co. Ltd. (далее именуемая «GWM») — международный\n              мультибрендовый производитель автомобилей со штаб-квартирой в\n              Китае. Это всемирно известный производитель внедорожников и\n              пикапов, владеющий пятью линейками продукции, а именно GWM HAVAL,\n              GWM WEY, GWM ORA, GWM TANK и GWM Pickup, охватывающими легковые\n              автомобили и пикапы. GWM имеет самодостаточную цепочку двигателей,\n              трансмиссий и других основных компонентов. В 2003 году она была\n              включена в список акций H в Гонконге, а в 2011 году — акций A в\n              Китае. К концу 2023 года ее активы составили 201,93 млрд юаней, а\n              объем продаж превысил один миллион в течение 8 лет подря\n            </span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CACnD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,aAAa,CAAEC,WAAW,KAAQ,gBAAgB,CAC3D;AACA,OAASC,cAAc,KAAQ,gBAAgB,CAC/C,OAASC,eAAe,KAAQ,gBAAgB,CAChD,OAASC,aAAa,KAAQ,gBAAgB,CAC9C,OAASC,cAAc,KAAQ,gBAAgB,CAE/C,MAAO,CAAAC,IAAI,KAAM,kCAAkC,CACnD,MAAO,CAAAC,MAAM,KAAM,mCAAmC,CACtD,OAASC,WAAW,CAAEC,cAAc,KAAQ,6BAA6B,CAEzE,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,WAAW,CAAG,QAAAA,CAAA,CAAsB,IAArB,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CACnC,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC0B,MAAM,CAACC,UAAU,EAAIP,UAAU,CAAC,CAEzEnB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2B,YAAY,CAAGA,CAAA,GAAMH,WAAW,CAACC,MAAM,CAACC,UAAU,EAAIP,UAAU,CAAC,CACvEM,MAAM,CAACG,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,CAACR,UAAU,CAAC,CAAC,CAEhB,MAAO,CAAAI,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAO,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAAP,QAAQ,CAAGL,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACa,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpD,KAAM,CAAAkC,WAAW,CAAGhC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC9B,KAAM,CAACiC,WAAW,CAAEC,cAAc,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAAqC,aAAa,CAAIC,EAAE,EAAK,CAC5BL,eAAe,CAAEM,IAAI,GAAM,CACzB,CAACD,EAAE,EAAG,CAACC,IAAI,CAACD,EAAE,CAAG;AACnB,CAAC,CAAC,CAAC,CACL,CAAC,CACDrC,SAAS,CAAC,IAAM,CACduC,KAAK,CAAC,oCAAoC,CAAC,CACxCC,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,EAAK,CACd,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAACC,QAAQ,EAAI,CAAC,CAAC,CACpC,KAAM,CAAAC,KAAK,CAAG,EAAE,CAEhB,GAAID,QAAQ,CAACE,QAAQ,CACnBD,KAAK,CAACE,IAAI,CAAC,CACTV,EAAE,CAAE,UAAU,CACdW,GAAG,CAAEJ,QAAQ,CAACE,QAAQ,CACtBG,IAAI,cAAElC,IAAA,CAACV,cAAc,GAAE,CAAC,CACxB6C,KAAK,CAAE,UACT,CAAC,CAAC,CACJ,GAAIN,QAAQ,CAACO,SAAS,CACpBN,KAAK,CAACE,IAAI,CAAC,CACTV,EAAE,CAAE,WAAW,CACfW,GAAG,CAAEJ,QAAQ,CAACO,SAAS,CACvBF,IAAI,cAAElC,IAAA,CAACT,eAAe,GAAE,CAAC,CACzB4C,KAAK,CAAE,WACT,CAAC,CAAC,CACJ,GAAIN,QAAQ,CAACQ,OAAO,CAClBP,KAAK,CAACE,IAAI,CAAC,CACTV,EAAE,CAAE,SAAS,CACbW,GAAG,CAAEJ,QAAQ,CAACQ,OAAO,CACrBH,IAAI,cAAElC,IAAA,CAACR,aAAa,GAAE,CAAC,CACvB2C,KAAK,CAAE,SACT,CAAC,CAAC,CACJ,GAAIN,QAAQ,CAACS,QAAQ,CACnBR,KAAK,CAACE,IAAI,CAAC,CACTV,EAAE,CAAE,UAAU,CACdW,GAAG,CAAEJ,QAAQ,CAACS,QAAQ,CACtBJ,IAAI,cAAElC,IAAA,CAACP,cAAc,GAAE,CAAC,CACxB0C,KAAK,CAAE,UACT,CAAC,CAAC,CAEJf,cAAc,CAACU,KAAK,CAAC,CACvB,CAAC,CAAC,CACN,CAAC,CAAE,EAAE,CAAC,CAEN,mBACE9B,IAAA,WAAAuC,QAAA,cACEvC,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBrC,KAAA,QAAKsC,SAAS,CAAE1C,MAAM,CAAC2C,OAAQ,CAAAF,QAAA,eAE7BvC,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACJ,IAAK,CAAA6C,QAAA,cAC1BvC,IAAA,CAACb,IAAI,EAACuD,EAAE,CAAC,GAAG,CAAC,aAAW,yDAAY,CAAAH,QAAA,cAClCvC,IAAA,QAAK2C,GAAG,CAAEjD,IAAK,CAACkD,GAAG,CAAC,gDAAa,CAACC,OAAO,CAAC,MAAM,CAAE,CAAC,CAC/C,CAAC,CACJ,CAAC,cAGN7C,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACgD,QAAS,CAAAP,QAAA,CAC7B3C,WAAW,CAACmD,GAAG,CAACC,IAAA,MAAC,CAAE1B,EAAE,CAAEW,GAAG,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAAa,IAAA,oBACxChD,IAAA,CAACb,IAAI,EAACuD,EAAE,CAAET,GAAI,CAAUO,SAAS,CAAE1C,MAAM,CAACmD,OAAQ,CAAAV,QAAA,cAChDrC,KAAA,QAAKsC,SAAS,CAAE1C,MAAM,CAACoD,IAAK,CAAAX,QAAA,eAC1BvC,IAAA,QAAK2C,GAAG,CAAET,IAAK,CAACU,GAAG,CAAET,KAAM,CAACU,OAAO,CAAC,MAAM,CAAE,CAAC,cAC7C7C,IAAA,SAAAuC,QAAA,CAAOJ,KAAK,CAAO,CAAC,EACjB,CAAC,EAJYb,EAKd,CAAC,EACR,CAAC,CACC,CAAC,cAGNtB,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACqD,IAAK,CAAAZ,QAAA,cAC1BvC,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACsD,aAAc,CAAAb,QAAA,CAClC1C,cAAc,CAACkD,GAAG,CAACM,KAAA,EAA0B,KAAAC,qBAAA,IAAzB,CAAEhC,EAAE,CAAEa,KAAK,CAAEoB,KAAM,CAAC,CAAAF,KAAA,CACvC,KAAM,CAAAG,MAAM,CAAGxC,YAAY,CAACM,EAAE,CAAC,CAE/B,mBACEpB,KAAA,QAAKsC,SAAS,CAAE1C,MAAM,CAAC2D,QAAS,CAAAlB,QAAA,eAC9BrC,KAAA,WACEsC,SAAS,CAAE1C,MAAM,CAAC4D,aAAc,CAChCC,OAAO,CAAEA,CAAA,GAAMnD,QAAQ,EAAIa,aAAa,CAACC,EAAE,CAAE,CAC7C,gBAAekC,MAAO,CACtB,+BAAAI,MAAA,CAA8BtC,EAAE,CAAG,CACnCuC,KAAK,CAAE,CACLC,MAAM,CAAEtD,QAAQ,CAAG,SAAS,CAAG,SAAS,CACxCuD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,MACT,CAAE,CAAA9B,QAAA,EAEDJ,KAAK,CACL3B,QAAQ,GACNgD,MAAM,cACLxD,IAAA,CAACX,WAAW,EAACiF,IAAI,CAAE,EAAG,CAAE,CAAC,cAEzBtE,IAAA,CAACZ,aAAa,EAACkF,IAAI,CAAE,EAAG,CAAE,CAC3B,CAAC,EACE,CAAC,cAETtE,IAAA,QACEsB,EAAE,gBAAAsC,MAAA,CAAiBtC,EAAE,CAAG,CACxBkB,SAAS,IAAAoB,MAAA,CAAK9D,MAAM,CAACyE,SAAS,MAAAX,MAAA,CAAIpD,QAAQ,CAAGV,MAAM,CAAC0E,WAAW,CAAG,EAAE,MAAAZ,MAAA,CAC9DJ,MAAM,CAAG1D,MAAM,CAAC2E,IAAI,CAAG,EAAE,CAAG,CAClCC,GAAG,CAAGC,EAAE,EAAMzD,WAAW,CAAC0D,OAAO,CAACtD,EAAE,CAAC,CAAGqD,EAAI,CAC5Cd,KAAK,CACHrD,QAAQ,CACJ,CACAqE,SAAS,CAAErB,MAAM,IAAAI,MAAA,EAAAN,qBAAA,CACVpC,WAAW,CAAC0D,OAAO,CAACtD,EAAE,CAAC,UAAAgC,qBAAA,iBAAvBA,qBAAA,CAAyBwB,YAAY,OACxC,KAAK,CACTC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,sBACd,CAAC,CACC,CAAC,CACN,CAAAzC,QAAA,CAEAgB,KAAK,CAACR,GAAG,CAACkC,KAAA,MAAC,CAAE3D,EAAE,CAAE4D,MAAM,CAAEjD,GAAG,CAAEE,KAAM,CAAC,CAAA8C,KAAA,oBACpCjF,IAAA,CAACb,IAAI,EAACuD,EAAE,CAAET,GAAI,CAAAM,QAAA,CACXJ,KAAK,EADY+C,MAEd,CAAC,EACR,CAAC,CACC,CAAC,GAhD8B5D,EAiDjC,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CACH,CAAC,cAGNpB,KAAA,QAAKsC,SAAS,CAAE1C,MAAM,CAACqF,OAAQ,CAAA5C,QAAA,eAC7BvC,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACsF,MAAO,CAAA7C,QAAA,CAC3BpB,WAAW,CAAC4B,GAAG,CAACsC,KAAA,MAAC,CAAE/D,EAAE,CAAEW,GAAG,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAAkD,KAAA,oBACxCrF,IAAA,MAEEsF,IAAI,CAAErD,GAAI,CACVsD,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB,aAAYrD,KAAM,CAAAI,QAAA,CAEjB,MAAO,CAAAL,IAAI,GAAK,QAAQ,cACvBlC,IAAA,QAAK2C,GAAG,CAAET,IAAK,CAACU,GAAG,CAAET,KAAM,CAACU,OAAO,CAAC,MAAM,CAAE,CAAC,cAE7C7C,IAAA,SAAMwC,SAAS,CAAE1C,MAAM,CAAC2F,WAAY,CAAAlD,QAAA,CAAEL,IAAI,CAAO,CAClD,EAVIZ,EAWJ,CAAC,EACL,CAAC,CACC,CAAC,cAENtB,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAACH,MAAO,CAAA4C,QAAA,cAC5BvC,IAAA,MAAGsF,IAAI,CAAC,mBAAmB,CAACC,MAAM,CAAC,QAAQ,CAAAhD,QAAA,cACzCvC,IAAA,QAAK2C,GAAG,CAAEhD,MAAO,CAACiD,GAAG,CAAC,gDAAa,CAACC,OAAO,CAAC,MAAM,CAAE,CAAC,CACpD,CAAC,CACD,CAAC,cAEN7C,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAAC4F,MAAO,CAAAnD,QAAA,cAC5BvC,IAAA,CAACb,IAAI,EAACuD,EAAE,CAAC,UAAU,CAAAH,QAAA,CAAC,+JAA2B,CAAM,CAAC,CACnD,CAAC,cAENvC,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAAC6F,IAAK,CAAApD,QAAA,CAAC,kFAAsB,CAAK,CAAC,EACtD,CAAC,cAGNvC,IAAA,QAAKwC,SAAS,CAAE1C,MAAM,CAAC8F,IAAK,CAAArD,QAAA,cAC1BvC,IAAA,SAAAuC,QAAA,CAAM,uwFAWN,CAAM,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAxB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}