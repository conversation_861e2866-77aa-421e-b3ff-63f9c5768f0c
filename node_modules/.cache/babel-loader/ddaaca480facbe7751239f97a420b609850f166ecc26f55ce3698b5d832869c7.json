{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'../../owners.module.css';import Sidebar from'../../components/sidebar/Sidebar';import PartsList from'../../components/PartsList/PartsList';// animation\nimport AOS from'aos';import'aos/dist/aos.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Accessories=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';// Отключаем скролл при загрузке\nconst timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';// Возвращаем скролл после загрузки\n},300);// 1 секунда для имитации загрузки\nreturn()=>{clearTimeout(timer);document.body.style.overflow='visible';// На случай размонтирования компонента\n};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041E\\u0420\\u0418\\u0413\\u0418\\u041D\\u0410\\u041B\\u042C\\u041D\\u042B\\u0415 \\u0410\\u041A\\u0421\\u0415\\u0421\\u0421\\u0423\\u0410\\u0420\\u042B GWM\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",className:styles.redLine}),/*#__PURE__*/_jsxs(\"div\",{className:styles.textContent,children:[/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u041F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043D\\u043E\\u0432\\u043E\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F GWM \\u0441\\u0442\\u0430\\u043B\\u0430 \\u043F\\u0440\\u043E\\u0449\\u0435, \\u0447\\u0435\\u043C \\u043A\\u043E\\u0433\\u0434\\u0430-\\u043B\\u0438\\u0431\\u043E, \\u0441 \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u0430\\u043C\\u0438 GWM!\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:\"\\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u044B \\u0434\\u043B\\u044F \\u0443\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u044F \\u0441\\u0442\\u0438\\u043B\\u044F, \\u043F\\u043E\\u0434\\u0433\\u043E\\u043D\\u043A\\u0438, \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u043E\\u043F\\u0442\\u0438\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0440\\u0430\\u0431\\u043E\\u0442\\u044B. \\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u0438\\u0432\\u0430\\u044E\\u0442 \\u0438 \\u043F\\u043E\\u0432\\u044B\\u0448\\u0430\\u044E\\u0442 \\u043E\\u0431\\u0449\\u0443\\u044E \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F. \\u041D\\u0435 \\u0438\\u0434\\u0438\\u0442\\u0435 \\u043D\\u0430 \\u043A\\u043E\\u043C\\u043F\\u0440\\u043E\\u043C\\u0438\\u0441\\u0441 \\u043E\\u0442\\u043D\\u043E\\u0441\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u043D\\u0430\\u0441\\u0442\\u0430\\u0438\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0438 \\u0443\\u0441\\u0442\\u0430\\u043D\\u0430\\u0432\\u043B\\u0438\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u0430 \\u0441\\u0432\\u043E\\u0439 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B GWM!\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:\"\\u041F\\u0440\\u043E\\u0441\\u0442\\u043E \\u0432\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u0438\\u0437 \\u043D\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0441\\u0441\\u043E\\u0440\\u0442\\u0438\\u043C\\u0435\\u043D\\u0442\\u0430 \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B GWM \\u0434\\u043B\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F!\"})]}),/*#__PURE__*/_jsx(PartsList,{})]})})]})})})});};export default Accessories;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "Sidebar", "PartsList", "AOS", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Accessories", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "redLine", "textContent"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Accessories/Accessories.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from '../../owners.module.css';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport PartsList from '../../components/PartsList/PartsList';\n\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Accessories = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                     <strong>ОРИГИНАЛЬНЫЕ АКСЕССУАРЫ GWM</strong>\n                  </h1>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <div className={styles.textContent}>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                      Персонализация вашего нового автомобиля GWM стала проще,\n                      чем когда-либо, с оригинальными аксессуарами GWM!\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Оригинальные аксессуары специально разработаны для\n                      улучшения стиля, подгонки, функциональности и оптимальной\n                      работы. Оригинальные аксессуары поддерживают и повышают\n                      общую ценность вашего автомобиля. Не идите на компромисс\n                      относительно качества и всегда настаивайте и\n                      устанавливайте на свой автомобиль только оригинальные\n                      аксессуары GWM!\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Просто выберите из нашего ассортимента оригинальные\n                      аксессуары GWM для вашего автомобиля!\n                    </p>\n                  </div>\n                  {/* <OwnersForm /> */}\n                  <PartsList />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Accessories;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,SAAS,KAAM,sCAAsC,CAE5D;AACA,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdK,GAAG,CAACU,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAAE;AAEzC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAE1B,MAAM,CAAC2B,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACJ,OAAO,GAAE,CAAC,cACXI,IAAA,SAAMqB,SAAS,CAAE1B,MAAM,CAAC4B,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAE1B,MAAM,CAAC6B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACnBpB,IAAA,WAAAoB,QAAA,CAAQ,2IAA2B,CAAQ,CAAC,CAC3C,CAAC,cACLpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAE1B,MAAM,CAAC8B,OAAQ,CACxB,CAAC,cAELvB,KAAA,QAAKmB,SAAS,CAAE1B,MAAM,CAAC+B,WAAY,CAAAN,QAAA,eACjCpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,ghBAG3C,CAAG,CAAC,cACJpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,4vDAQ3C,CAAG,CAAC,cACJpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,kdAG3C,CAAG,CAAC,EACD,CAAC,cAENpB,IAAA,CAACH,SAAS,GAAE,CAAC,EACV,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}