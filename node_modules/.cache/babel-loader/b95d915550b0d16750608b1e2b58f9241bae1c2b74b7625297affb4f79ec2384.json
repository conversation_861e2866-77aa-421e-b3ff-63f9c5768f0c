{"ast": null, "code": "import React from'react';// swiper\nimport{Swiper,SwiperSlide}from'swiper/react';// Import Swiper styles\nimport'swiper/css';import styles from'./filter.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterSwiper=_ref=>{let{activeModel,setActiveModel,cars}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:styles.carsModelsList,children:/*#__PURE__*/_jsxs(Swiper,{breakpoints:{320:{slidesPerView:2,spaceBetween:0},580:{slidesPerView:2.2,spaceBetween:0},750:{slidesPerView:3.2,spaceBetween:10},860:{slidesPerView:3.5,spaceBetween:0},1160:{slidesPerView:4.1,spaceBetween:0},1460:{slidesPerView:6.5,spaceBetween:0}},spaceBetween:10,grabCursor:true,className:\"mySwiper\",children:[cars.map(item=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.btn,\" \").concat(activeModel===item.id?styles.active:''),onClick:()=>setActiveModel(item.id),children:item.title})},item.id)),/*#__PURE__*/_jsx(\"div\",{className:styles.activeBar})]})});};export default FilterSwiper;", "map": {"version": 3, "names": ["React", "Swiper", "SwiperSlide", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "FilterSwiper", "_ref", "activeModel", "setActiveModel", "cars", "className", "carsModelsList", "children", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "grabCursor", "map", "item", "concat", "btn", "id", "active", "onClick", "title", "activeBar"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/components/FilterSlide/FilterSwiper.jsx"], "sourcesContent": ["import React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\n\nconst FilterSwiper = ({ activeModel, setActiveModel, cars }) => {\n  return (\n    <div className={styles.carsModelsList}>\n      <Swiper\n        breakpoints={{\n          320: {\n            slidesPerView: 2,\n            spaceBetween: 0,\n          },\n          580: {\n            slidesPerView: 2.2,\n            spaceBetween: 0,\n          },\n          750: {\n            slidesPerView: 3.2,\n            spaceBetween: 10,\n          },\n          860: {\n            slidesPerView: 3.5,\n            spaceBetween: 0,\n          },\n          1160: {\n            slidesPerView: 4.1,\n            spaceBetween: 0,\n          },\n\n          1460: {\n            slidesPerView: 6.5,\n            spaceBetween: 0,\n          },\n        }}\n        spaceBetween={10}\n        grabCursor={true}\n        className=\"mySwiper\"\n      >\n        {cars.map((item) => (\n          <SwiperSlide key={item.id}>\n            <div\n              className={`${styles.btn} ${\n                activeModel === item.id ? styles.active : ''\n              }`}\n              onClick={() => setActiveModel(item.id)}\n            >\n              {item.title}\n            </div>\n          </SwiperSlide>\n        ))}\n        <div className={styles.activeBar}></div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default FilterSwiper;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB;AACA,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD;AACA,MAAO,YAAY,CACnB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,WAAW,CAAEC,cAAc,CAAEC,IAAK,CAAC,CAAAH,IAAA,CACzD,mBACEJ,IAAA,QAAKQ,SAAS,CAAEV,MAAM,CAACW,cAAe,CAAAC,QAAA,cACpCR,KAAA,CAACN,MAAM,EACLe,WAAW,CAAE,CACX,GAAG,CAAE,CACHC,aAAa,CAAE,CAAC,CAChBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,EAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CAED,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CACF,CAAE,CACFA,YAAY,CAAE,EAAG,CACjBC,UAAU,CAAE,IAAK,CACjBN,SAAS,CAAC,UAAU,CAAAE,QAAA,EAEnBH,IAAI,CAACQ,GAAG,CAAEC,IAAI,eACbhB,IAAA,CAACH,WAAW,EAAAa,QAAA,cACVV,IAAA,QACEQ,SAAS,IAAAS,MAAA,CAAKnB,MAAM,CAACoB,GAAG,MAAAD,MAAA,CACtBZ,WAAW,GAAKW,IAAI,CAACG,EAAE,CAAGrB,MAAM,CAACsB,MAAM,CAAG,EAAE,CAC3C,CACHC,OAAO,CAAEA,CAAA,GAAMf,cAAc,CAACU,IAAI,CAACG,EAAE,CAAE,CAAAT,QAAA,CAEtCM,IAAI,CAACM,KAAK,CACR,CAAC,EARUN,IAAI,CAACG,EASV,CACd,CAAC,cACFnB,IAAA,QAAKQ,SAAS,CAAEV,MAAM,CAACyB,SAAU,CAAM,CAAC,EAClC,CAAC,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}