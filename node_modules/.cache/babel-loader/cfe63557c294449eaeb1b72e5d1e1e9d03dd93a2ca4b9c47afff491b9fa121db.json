{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"isValidProp\"];\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig(_ref) {\n  let {\n      children,\n      isValidProp\n    } = _ref,\n    config = _objectWithoutProperties(_ref, _excluded);\n  isValidProp && loadExternalIsValidProp(isValidProp);\n  /**\n   * Inherit props from any parent MotionConfig components\n   */\n  config = _objectSpread(_objectSpread({}, useContext(MotionConfigContext)), config);\n  /**\n   * Don't allow isStatic to change between renders as it affects how many hooks\n   * motion components fire.\n   */\n  config.isStatic = useConstant(() => config.isStatic);\n  /**\n   * Creating a new config context object will re-render every `motion` component\n   * every time it renders. So we only want to create a new one sparingly.\n   */\n  const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n  return jsx(MotionConfigContext.Provider, {\n    value: context,\n    children: children\n  });\n}\nexport { MotionConfig };", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "jsx", "useContext", "useMemo", "MotionConfigContext", "loadExternalIsValidProp", "useConstant", "MotionConfig", "_ref", "children", "isValidProp", "config", "isStatic", "context", "JSON", "stringify", "transition", "transformPagePoint", "reducedMotion", "Provider", "value"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig({ children, isValidProp, ...config }) {\n    isValidProp && loadExternalIsValidProp(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */\n    config = { ...useContext(MotionConfigContext), ...config };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */\n    config.isStatic = useConstant(() => config.isStatic);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */\n    const context = useMemo(() => config, [\n        JSON.stringify(config.transition),\n        config.transformPagePoint,\n        config.reducedMotion,\n    ]);\n    return (jsx(MotionConfigContext.Provider, { value: context, children: children }));\n}\n\nexport { MotionConfig };\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AACb,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAAC,IAAA,EAAuC;EAAA,IAAtC;MAAEC,QAAQ;MAAEC;IAAuB,CAAC,GAAAF,IAAA;IAARG,MAAM,GAAAZ,wBAAA,CAAAS,IAAA,EAAAR,SAAA;EACpDU,WAAW,IAAIL,uBAAuB,CAACK,WAAW,CAAC;EACnD;AACJ;AACA;EACIC,MAAM,GAAAb,aAAA,CAAAA,aAAA,KAAQI,UAAU,CAACE,mBAAmB,CAAC,GAAKO,MAAM,CAAE;EAC1D;AACJ;AACA;AACA;EACIA,MAAM,CAACC,QAAQ,GAAGN,WAAW,CAAC,MAAMK,MAAM,CAACC,QAAQ,CAAC;EACpD;AACJ;AACA;AACA;EACI,MAAMC,OAAO,GAAGV,OAAO,CAAC,MAAMQ,MAAM,EAAE,CAClCG,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACK,UAAU,CAAC,EACjCL,MAAM,CAACM,kBAAkB,EACzBN,MAAM,CAACO,aAAa,CACvB,CAAC;EACF,OAAQjB,GAAG,CAACG,mBAAmB,CAACe,QAAQ,EAAE;IAAEC,KAAK,EAAEP,OAAO;IAAEJ,QAAQ,EAAEA;EAAS,CAAC,CAAC;AACrF;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}