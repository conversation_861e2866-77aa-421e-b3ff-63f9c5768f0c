{"ast": null, "code": "import { useState, useLayoutEffect } from 'react';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../../render/VisualElement.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\nconst createObject = () => ({});\nclass StateVisualElement extends VisualElement {\n  constructor() {\n    super(...arguments);\n    this.measureInstanceViewportBox = createBox;\n  }\n  build() {}\n  resetTransform() {}\n  restoreTransform() {}\n  removeValueFromRenderState() {}\n  renderInstance() {}\n  scrapeMotionValuesFromProps() {\n    return createObject();\n  }\n  getBaseTargetFromProps() {\n    return undefined;\n  }\n  readValueFromInstance(_state, key, options) {\n    return options.initialState[key] || 0;\n  }\n  sortInstanceNodePosition() {\n    return 0;\n  }\n}\nconst useVisualState = makeUseVisualState({\n  scrapeMotionValuesFromProps: createObject,\n  createRenderState: createObject\n});\n/**\n * This is not an officially supported API and may be removed\n * on any version.\n */\nfunction useAnimatedState(initialState) {\n  const [animationState, setAnimationState] = useState(initialState);\n  const visualState = useVisualState({}, false);\n  const element = useConstant(() => {\n    return new StateVisualElement({\n      props: {\n        onUpdate: v => {\n          setAnimationState({\n            ...v\n          });\n        }\n      },\n      visualState,\n      presenceContext: null\n    }, {\n      initialState\n    });\n  });\n  useLayoutEffect(() => {\n    element.mount({});\n    return () => element.unmount();\n  }, [element]);\n  const startAnimation = useConstant(() => animationDefinition => {\n    return animateVisualElement(element, animationDefinition);\n  });\n  return [animationState, startAnimation];\n}\nexport { useAnimatedState };", "map": {"version": 3, "names": ["useState", "useLayoutEffect", "useConstant", "makeUseVisualState", "createBox", "VisualElement", "animateVisualElement", "createObject", "StateVisualElement", "constructor", "arguments", "measureInstanceViewportBox", "build", "resetTransform", "restoreTransform", "removeValueFromRenderState", "renderInstance", "scrapeMotionValuesFromProps", "getBaseTargetFromProps", "undefined", "readValueFromInstance", "_state", "key", "options", "initialState", "sortInstanceNodePosition", "useVisualState", "createRenderState", "useAnimatedState", "animationState", "setAnimationState", "visualState", "element", "props", "onUpdate", "v", "presenceContext", "mount", "unmount", "startAnimation", "animationDefinition"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/hooks/use-animated-state.mjs"], "sourcesContent": ["import { useState, useLayoutEffect } from 'react';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../../render/VisualElement.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\n\nconst createObject = () => ({});\nclass StateVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.measureInstanceViewportBox = createBox;\n    }\n    build() { }\n    resetTransform() { }\n    restoreTransform() { }\n    removeValueFromRenderState() { }\n    renderInstance() { }\n    scrapeMotionValuesFromProps() {\n        return createObject();\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    readValueFromInstance(_state, key, options) {\n        return options.initialState[key] || 0;\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\nconst useVisualState = makeUseVisualState({\n    scrapeMotionValuesFromProps: createObject,\n    createRenderState: createObject,\n});\n/**\n * This is not an officially supported API and may be removed\n * on any version.\n */\nfunction useAnimatedState(initialState) {\n    const [animationState, setAnimationState] = useState(initialState);\n    const visualState = useVisualState({}, false);\n    const element = useConstant(() => {\n        return new StateVisualElement({\n            props: {\n                onUpdate: (v) => {\n                    setAnimationState({ ...v });\n                },\n            },\n            visualState,\n            presenceContext: null,\n        }, { initialState });\n    });\n    useLayoutEffect(() => {\n        element.mount({});\n        return () => element.unmount();\n    }, [element]);\n    const startAnimation = useConstant(() => (animationDefinition) => {\n        return animateVisualElement(element, animationDefinition);\n    });\n    return [animationState, startAnimation];\n}\n\nexport { useAnimatedState };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,QAAQ,OAAO;AACjD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE,MAAMC,YAAY,GAAGA,CAAA,MAAO,CAAC,CAAC,CAAC;AAC/B,MAAMC,kBAAkB,SAASH,aAAa,CAAC;EAC3CI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,0BAA0B,GAAGP,SAAS;EAC/C;EACAQ,KAAKA,CAAA,EAAG,CAAE;EACVC,cAAcA,CAAA,EAAG,CAAE;EACnBC,gBAAgBA,CAAA,EAAG,CAAE;EACrBC,0BAA0BA,CAAA,EAAG,CAAE;EAC/BC,cAAcA,CAAA,EAAG,CAAE;EACnBC,2BAA2BA,CAAA,EAAG;IAC1B,OAAOV,YAAY,CAAC,CAAC;EACzB;EACAW,sBAAsBA,CAAA,EAAG;IACrB,OAAOC,SAAS;EACpB;EACAC,qBAAqBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACxC,OAAOA,OAAO,CAACC,YAAY,CAACF,GAAG,CAAC,IAAI,CAAC;EACzC;EACAG,wBAAwBA,CAAA,EAAG;IACvB,OAAO,CAAC;EACZ;AACJ;AACA,MAAMC,cAAc,GAAGvB,kBAAkB,CAAC;EACtCc,2BAA2B,EAAEV,YAAY;EACzCoB,iBAAiB,EAAEpB;AACvB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,SAASqB,gBAAgBA,CAACJ,YAAY,EAAE;EACpC,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAACwB,YAAY,CAAC;EAClE,MAAMO,WAAW,GAAGL,cAAc,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EAC7C,MAAMM,OAAO,GAAG9B,WAAW,CAAC,MAAM;IAC9B,OAAO,IAAIM,kBAAkB,CAAC;MAC1ByB,KAAK,EAAE;QACHC,QAAQ,EAAGC,CAAC,IAAK;UACbL,iBAAiB,CAAC;YAAE,GAAGK;UAAE,CAAC,CAAC;QAC/B;MACJ,CAAC;MACDJ,WAAW;MACXK,eAAe,EAAE;IACrB,CAAC,EAAE;MAAEZ;IAAa,CAAC,CAAC;EACxB,CAAC,CAAC;EACFvB,eAAe,CAAC,MAAM;IAClB+B,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;IACjB,OAAO,MAAML,OAAO,CAACM,OAAO,CAAC,CAAC;EAClC,CAAC,EAAE,CAACN,OAAO,CAAC,CAAC;EACb,MAAMO,cAAc,GAAGrC,WAAW,CAAC,MAAOsC,mBAAmB,IAAK;IAC9D,OAAOlC,oBAAoB,CAAC0B,OAAO,EAAEQ,mBAAmB,CAAC;EAC7D,CAAC,CAAC;EACF,OAAO,CAACX,cAAc,EAAEU,cAAc,CAAC;AAC3C;AAEA,SAASX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}