{"ast": null, "code": "import React,{useEffect,useState}from'react';import Sidebar from'../../components/sidebar/Sidebar';import img from'../../../../asset/imgs/owners/2400_OWNERS_S1.webp';import{Link}from'react-router-dom';import styles from'../../owners.module.css';import AOS from'aos';import'aos/dist/aos.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Warranty=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false// Анимация запускается только один раз\n});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0413\\u0410\\u0420\\u0410\\u041D\\u0422\\u0418\\u042F \\u041E\\u0422 GWM\"})}),/*#__PURE__*/_jsx(\"span\",{className:styles.underText,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:\"\\u0412\\u0441\\u0435 \\u043D\\u043E\\u0432\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 GWM \\u043F\\u043E\\u043A\\u0440\\u044B\\u0432\\u0430\\u044E\\u0442\\u0441\\u044F \\u0413\\u0410\\u0420\\u0410\\u041D\\u0422\\u0418\\u0415\\u0419 \\u041D\\u0410 \\u041D\\u041E\\u0412\\u042B\\u0419 \\u0410\\u0412\\u0422\\u041E\\u041C\\u041E\\u0411\\u0418\\u041B\\u042C \\u0434\\u043B\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F.\"}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:styles.redLine}),/*#__PURE__*/_jsx(\"img\",{src:img,alt:\"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",className:styles.banner,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\"}),/*#__PURE__*/_jsxs(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"300\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u0435\\u0439 \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u043F\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0434\\u043B\\u044F \\u043A\\u043E\\u043D\\u043A\\u0440\\u0435\\u0442\\u043D\\u043E\\u0439 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u043E\\u0433\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u043E\\u0433\\u043E \\u043F\\u043E\\u043A\\u0440\\u044B\\u0442\\u0438\\u044F.\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441\",' ',/*#__PURE__*/_jsx(Link,{to:\"/owners/vehicle-reference-table\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u0435\\u0439 \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \"})}),\"\\u043F\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0434\\u043B\\u044F \\u043A\\u043E\\u043D\\u043A\\u0440\\u0435\\u0442\\u043D\\u043E\\u0439 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u043E\\u0433\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u043E\\u0433\\u043E \\u043F\\u043E\\u043A\\u0440\\u044B\\u0442\\u0438\\u044F.\"]}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0412\\u0430\\u0448\\u0430 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u044F GWM \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0438\\u0432\\u0430\\u0435\\u0442 \\u0432\\u0430\\u043C \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u0435 \\u0438 \\u0437\\u0430\\u0449\\u0438\\u0449\\u0430\\u0435\\u0442 \\u043E\\u0442 \\u043B\\u044E\\u0431\\u044B\\u0445 \\u043D\\u0435\\u043F\\u0440\\u0435\\u0434\\u0432\\u0438\\u0434\\u0435\\u043D\\u043D\\u044B\\u0445 \\u0440\\u0430\\u0441\\u0445\\u043E\\u0434\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u0441\\u0431\\u043E\\u0435\\u0432 \\u0438\\u0437-\\u0437\\u0430 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u044B\\u0445 \\u0434\\u0435\\u0444\\u0435\\u043A\\u0442\\u043E\\u0432, \\u0434\\u0435\\u0444\\u0435\\u043A\\u0442\\u043D\\u044B\\u0445 \\u043C\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043B\\u043E\\u0432 \\u0438 \\u0438\\u0437\\u0433\\u043E\\u0442\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u043F\\u0440\\u0438 \\u043D\\u043E\\u0440\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u044D\\u043A\\u0441\\u043F\\u043B\\u0443\\u0430\\u0442\\u0430\\u0446\\u0438\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0438, \\u0437\\u0430 \\u0438\\u0441\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435\\u043C \\u043B\\u044E\\u0431\\u043E\\u0433\\u043E \\u0438\\u0437\\u043D\\u043E\\u0441\\u0430, \\u043F\\u043E\\u0432\\u0440\\u0435\\u0436\\u0434\\u0435\\u043D\\u0438\\u0439 \\u0432 \\u0440\\u0435\\u0437\\u0443\\u043B\\u044C\\u0442\\u0430\\u0442\\u0435 \\u043D\\u0435\\u0441\\u0447\\u0430\\u0441\\u0442\\u043D\\u044B\\u0445 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0435\\u0432 \\u0438\\u043B\\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u043C. \\u041F\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438 \\u043B\\u044E\\u0431\\u043E\\u0439 \\u043F\\u043E\\u0434\\u0442\\u0432\\u0435\\u0440\\u0436\\u0434\\u0435\\u043D\\u043D\\u044B\\u0439 \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u044B\\u0439 \\u0440\\u0435\\u043C\\u043E\\u043D\\u0442 \\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u044F\\u0435\\u0442\\u0441\\u044F \\u0431\\u0435\\u0441\\u043F\\u043B\\u0430\\u0442\\u043D\\u043E \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441.\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u044B\\u0445 \\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u0439, \\u043F\\u043E\\u0436\\u0430\\u043B\\u0443\\u0439\\u0441\\u0442\\u0430, \\u043E\\u0431\\u0440\\u0430\\u0442\\u0438\\u0442\\u0435\\u0441\\u044C \\u043A \\u0431\\u0443\\u043A\\u043B\\u0435\\u0442\\u0443 \\xAB\\u0420\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E \\u043F\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E\\xBB,\",' ',/*#__PURE__*/_jsx(Link,{to:\"/contact\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043D\\u0430\\u043C\\u0438\"})}),\".\"]})]})]})})]})})})});};export default Warranty;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "Link", "styles", "AOS", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Warranty", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "underText", "redLine", "src", "alt", "banner", "textContent", "to"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Warranty/Warranty.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/2400_OWNERS_S1.webp';\nimport { Link } from 'react-router-dom';\nimport styles from '../../owners.module.css';\n\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Warranty = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false, // Анимация запускается только один раз\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ГАРАНТИЯ ОТ GWM</strong>\n                  </h1>\n\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Все новые автомобили GWM покрываются ГАРАНТИЕЙ НА НОВЫЙ\n                    АВТОМОБИЛЬ для вашего спокойствия.\n                  </span>\n\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <img\n                    src={img}\n                    alt=\"Баннер владельцев GWM\"\n                    className={styles.banner}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  />\n\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"300\"\n                  >\n                    <p>\n                      Ознакомьтесь с таблицей справочных данных по автомобилям\n                      для конкретной модели автомобиля и применимого\n                      гарантийного покрытия.\n                    </p>\n                    <p>\n                      Ознакомьтесь с{' '}\n                      <Link to=\"/owners/vehicle-reference-table\">\n                        <strong>таблицей справочных данных </strong>\n                      </Link>\n                      по автомобилям для конкретной модели автомобиля и\n                      применимого гарантийного покрытия.\n                    </p>\n                    <p>\n                      Ваша гарантия GWM обеспечивает вам спокойствие и защищает\n                      от любых непредвиденных расходов или сбоев из-за\n                      производственных дефектов, дефектных материалов и\n                      изготовления при нормальной эксплуатации и обслуживании,\n                      за исключением любого износа, повреждений в результате\n                      несчастных случаев или обслуживания владельцем. По\n                      гарантии любой подтвержденный необходимый ремонт\n                      выполняется бесплатно для вас.\n                    </p>\n                    <p>\n                      Для получения дополнительной информации и применимых\n                      положений и условий, пожалуйста, обратитесь к буклету\n                      «Руководство по гарантии и обслуживанию»,{' '}\n                      <Link to=\"/contact\">\n                        <strong>свяжитесь с нами</strong>\n                      </Link>\n                      .\n                    </p>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Warranty;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,GAAG,KAAM,mDAAmD,CACnE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAE5C,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdM,GAAG,CAACU,IAAI,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,KAAO;AACf,CAAC,CAAC,CAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAExB,MAAM,CAACyB,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACN,OAAO,GAAE,CAAC,cACXM,IAAA,SAAMqB,SAAS,CAAExB,MAAM,CAAC0B,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAExB,MAAM,CAAC2B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACpBpB,IAAA,WAAAoB,QAAA,CAAQ,mEAAe,CAAQ,CAAC,CAC9B,CAAC,cAELpB,IAAA,SACEqB,SAAS,CAAExB,MAAM,CAAC4B,SAAU,CAC5B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAL,QAAA,CACrB,mdAGD,CAAM,CAAC,cAEPpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAExB,MAAM,CAAC6B,OAAQ,CACxB,CAAC,cAEL1B,IAAA,QACE2B,GAAG,CAAEhC,GAAI,CACTiC,GAAG,CAAC,uGAAuB,CAC3BP,SAAS,CAAExB,MAAM,CAACgC,MAAO,CACzB,WAAS,SAAS,CAClB,iBAAe,KAAK,CACrB,CAAC,cAEF3B,KAAA,QACEmB,SAAS,CAAExB,MAAM,CAACiC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAV,QAAA,eAEpBpB,IAAA,MAAAoB,QAAA,CAAG,2qBAIH,CAAG,CAAC,cACJlB,KAAA,MAAAkB,QAAA,EAAG,iFACa,CAAC,GAAG,cAClBpB,IAAA,CAACJ,IAAI,EAACmC,EAAE,CAAC,iCAAiC,CAAAX,QAAA,cACxCpB,IAAA,WAAAoB,QAAA,CAAQ,qJAA2B,CAAQ,CAAC,CACxC,CAAC,ycAGT,EAAG,CAAC,cACJpB,IAAA,MAAAoB,QAAA,CAAG,okEASH,CAAG,CAAC,cACJlB,KAAA,MAAAkB,QAAA,EAAG,kxBAGwC,CAAC,GAAG,cAC7CpB,IAAA,CAACJ,IAAI,EAACmC,EAAE,CAAC,UAAU,CAAAX,QAAA,cACjBpB,IAAA,WAAAoB,QAAA,CAAQ,wFAAgB,CAAQ,CAAC,CAC7B,CAAC,IAET,EAAG,CAAC,EACD,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAf,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}