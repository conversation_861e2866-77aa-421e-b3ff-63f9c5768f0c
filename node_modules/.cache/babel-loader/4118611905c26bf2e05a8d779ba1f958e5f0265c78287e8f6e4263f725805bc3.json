{"ast": null, "code": "import{useEffect,useState}from'react';import styles from'./privacy.module.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Privacy=()=>{const[loading,setLoading]=useState(true);const[openIndex,setOpenIndex]=useState(null);useEffect(()=>{window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);const toggleSection=index=>{setOpenIndex(openIndex===index?null:index);};const sections=[{title:'Введение',content:\"\\u041A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044F Haval Motors Tajikistan \\u0443\\u0432\\u0430\\u0436\\u0430\\u0435\\u0442 \\u043F\\u0440\\u0430\\u0432\\u043E \\u043A\\u0430\\u0436\\u0434\\u043E\\u0433\\u043E \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044F \\u043D\\u0430 \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C \\u0438 \\u0437\\u0430\\u0449\\u0438\\u0442\\u0443 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445. \\u041D\\u0430\\u0441\\u0442\\u043E\\u044F\\u0449\\u0430\\u044F \\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0430 \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u0430 \\u0441 \\u0446\\u0435\\u043B\\u044C\\u044E \\u043E\\u0431\\u044A\\u044F\\u0441\\u043D\\u0438\\u0442\\u044C, \\u043A\\u0430\\u043A\\u0438\\u0435 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u043C\\u044B \\u0441\\u043E\\u0431\\u0438\\u0440\\u0430\\u0435\\u043C, \\u043A\\u0430\\u043A \\u043C\\u044B \\u0438\\u0445 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u0443\\u0435\\u043C, \\u0445\\u0440\\u0430\\u043D\\u0438\\u043C \\u0438 \\u0437\\u0430\\u0449\\u0438\\u0449\\u0430\\u0435\\u043C, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043A\\u0430\\u043A\\u0438\\u0435 \\u0443 \\u0432\\u0430\\u0441 \\u0435\\u0441\\u0442\\u044C \\u043F\\u0440\\u0430\\u0432\\u0430 \\u0432 \\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u0438 \\u044D\\u0442\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438.\\n\\u0412 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u0445 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0440\\u0430\\u0437\\u0432\\u0438\\u0442\\u0438\\u044F \\u0446\\u0438\\u0444\\u0440\\u043E\\u0432\\u044B\\u0445 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0439 \\u0438 \\u0440\\u0430\\u0441\\u0448\\u0438\\u0440\\u0435\\u043D\\u0438\\u044F \\u043E\\u043D\\u043B\\u0430\\u0439\\u043D-\\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043E\\u0432 \\u0437\\u0430\\u0449\\u0438\\u0442\\u0430 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0438\\u0442\\u0441\\u044F \\u043A\\u0440\\u0430\\u0439\\u043D\\u0435 \\u0432\\u0430\\u0436\\u043D\\u043E\\u0439 \\u0437\\u0430\\u0434\\u0430\\u0447\\u0435\\u0439. \\u041C\\u044B \\u0441\\u0442\\u0440\\u043E\\u0433\\u043E \\u0441\\u043E\\u0431\\u043B\\u044E\\u0434\\u0430\\u0435\\u043C \\u0442\\u0440\\u0435\\u0431\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0435\\u0433\\u043E \\u0437\\u0430\\u043A\\u043E\\u043D\\u043E\\u0434\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u0442\\u0432\\u0430 \\u0420\\u0435\\u0441\\u043F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0438 \\u0422\\u0430\\u0434\\u0436\\u0438\\u043A\\u0438\\u0441\\u0442\\u0430\\u043D \\u0432 \\u043E\\u0431\\u043B\\u0430\\u0441\\u0442\\u0438 \\u0437\\u0430\\u0449\\u0438\\u0442\\u044B \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0438 \\u043C\\u0435\\u0436\\u0434\\u0443\\u043D\\u0430\\u0440\\u043E\\u0434\\u043D\\u044B\\u0435 \\u0441\\u0442\\u0430\\u043D\\u0434\\u0430\\u0440\\u0442\\u044B, \\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u044F \\u043F\\u0440\\u0438\\u043D\\u0446\\u0438\\u043F\\u044B \\u0437\\u0430\\u043A\\u043E\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438, \\u043F\\u0440\\u043E\\u0437\\u0440\\u0430\\u0447\\u043D\\u043E\\u0441\\u0442\\u0438, \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0441\\u0442\\u0438.\\n\\u0418\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u0443\\u044F \\u043D\\u0430\\u0448 \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442, \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438, \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u044B \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u044F\\u044F \\u043D\\u0430\\u043C \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044E \\u0438\\u043D\\u044B\\u043C \\u0441\\u043F\\u043E\\u0441\\u043E\\u0431\\u043E\\u043C, \\u0432\\u044B \\u0441\\u043E\\u0433\\u043B\\u0430\\u0448\\u0430\\u0435\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F\\u043C\\u0438 \\u043D\\u0430\\u0441\\u0442\\u043E\\u044F\\u0449\\u0435\\u0439 \\u041F\\u043E\\u043B\\u0438\\u0442\\u0438\\u043A\\u0438 \\u043A\\u043E\\u043D\\u0444\\u0438\\u0434\\u0435\\u043D\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438.\"},{title:'Что такое личная информация?',content:\"\\u041B\\u0438\\u0447\\u043D\\u0430\\u044F \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u2014 \\u044D\\u0442\\u043E \\u043B\\u044E\\u0431\\u044B\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043F\\u0440\\u044F\\u043C\\u043E \\u0438\\u043B\\u0438 \\u043A\\u043E\\u0441\\u0432\\u0435\\u043D\\u043D\\u043E \\u043F\\u043E\\u0437\\u0432\\u043E\\u043B\\u044F\\u044E\\u0442 \\u0438\\u0434\\u0435\\u043D\\u0442\\u0438\\u0444\\u0438\\u0446\\u0438\\u0440\\u043E\\u0432\\u0430\\u0442\\u044C \\u0432\\u0430\\u0441 \\u043A\\u0430\\u043A \\u043B\\u0438\\u0447\\u043D\\u043E\\u0441\\u0442\\u044C. \\n    \\u041A \\u0442\\u0430\\u043A\\u0438\\u043C \\u0434\\u0430\\u043D\\u043D\\u044B\\u043C \\u043E\\u0442\\u043D\\u043E\\u0441\\u044F\\u0442\\u0441\\u044F, \\u0432 \\u0447\\u0430\\u0441\\u0442\\u043D\\u043E\\u0441\\u0442\\u0438:\\n    \\u2022 \\u0424\\u0418\\u041E (\\u0438\\u043C\\u044F, \\u0444\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F, \\u043E\\u0442\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E);  \\n    \\u2022 \\u043D\\u043E\\u043C\\u0435\\u0440 \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430;  \\n    \\u2022 \\u0430\\u0434\\u0440\\u0435\\u0441 \\u044D\\u043B\\u0435\\u043A\\u0442\\u0440\\u043E\\u043D\\u043D\\u043E\\u0439 \\u043F\\u043E\\u0447\\u0442\\u044B;  \\n    \\u2022 \\u0430\\u0434\\u0440\\u0435\\u0441 \\u043F\\u0440\\u043E\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438\\u043B\\u0438 \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u0438;  \\n    \\u2022 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u0443\\u0434\\u043E\\u0441\\u0442\\u043E\\u0432\\u0435\\u0440\\u0435\\u043D\\u0438\\u044F \\u043B\\u0438\\u0447\\u043D\\u043E\\u0441\\u0442\\u0438 (\\u043F\\u0430\\u0441\\u043F\\u043E\\u0440\\u0442, ID-\\u043A\\u0430\\u0440\\u0442\\u0430 \\u0438 \\u0442.\\u0434.);  \\n    \\u2022 VIN-\\u043A\\u043E\\u0434 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F \\u0438\\u043B\\u0438 \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0435 \\u043D\\u043E\\u043C\\u0435\\u0440\\u0430;  \\n    \\u2022 IP-\\u0430\\u0434\\u0440\\u0435\\u0441 \\u0438\\u043B\\u0438 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u043E\\u0431 \\u0443\\u0441\\u0442\\u0440\\u043E\\u0439\\u0441\\u0442\\u0432\\u0435 \\u043F\\u0440\\u0438 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u0438 \\u043E\\u043D\\u043B\\u0430\\u0439\\u043D-\\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043E\\u0432.\\n    \\n    \\u041B\\u0438\\u0447\\u043D\\u0430\\u044F \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043C\\u043E\\u0436\\u0435\\u0442 \\u0441\\u043E\\u0431\\u0438\\u0440\\u0430\\u0442\\u044C\\u0441\\u044F \\u043D\\u0430\\u043C\\u0438 \\u043F\\u0440\\u0438 \\u043E\\u0431\\u0440\\u0430\\u0449\\u0435\\u043D\\u0438\\u0438 \\u0432 \\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043D\\u044B\\u0435 \\u0446\\u0435\\u043D\\u0442\\u0440\\u044B, \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u0438 \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u0430, \\u0443\\u0447\\u0430\\u0441\\u0442\\u0438\\u0438 \\u0432 \\u043E\\u043F\\u0440\\u043E\\u0441\\u0430\\u0445 \\u0438\\u043B\\u0438 \\u0430\\u043A\\u0446\\u0438\\u044F\\u0445, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043F\\u0440\\u0438 \\u043E\\u0444\\u043E\\u0440\\u043C\\u043B\\u0435\\u043D\\u0438\\u0438 \\u0437\\u0430\\u044F\\u0432\\u043E\\u043A \\u043D\\u0430 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438.\"},{title:'Является ли предоставление персональной информации добровольным или обязательным?',content:\"\\u0412 \\u0437\\u0430\\u0432\\u0438\\u0441\\u0438\\u043C\\u043E\\u0441\\u0442\\u0438 \\u043E\\u0442 \\u0445\\u0430\\u0440\\u0430\\u043A\\u0442\\u0435\\u0440\\u0430 \\u0437\\u0430\\u043F\\u0440\\u0430\\u0448\\u0438\\u0432\\u0430\\u0435\\u043C\\u043E\\u0439 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438, \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043B\\u0438\\u0447\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u043C\\u043E\\u0436\\u0435\\u0442 \\u0431\\u044B\\u0442\\u044C \\u043A\\u0430\\u043A \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u043C, \\u0442\\u0430\\u043A \\u0438 \\u0434\\u043E\\u0431\\u0440\\u043E\\u0432\\u043E\\u043B\\u044C\\u043D\\u044B\\u043C.\\n    \\n    \\u0412 \\u0441\\u043B\\u0443\\u0447\\u0430\\u044F\\u0445, \\u043A\\u043E\\u0433\\u0434\\u0430 \\u0442\\u0430\\u043A\\u0438\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u044B \\u0434\\u043B\\u044F \\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u0438\\u044F \\u043D\\u0430\\u0448\\u0438\\u0445 \\u044E\\u0440\\u0438\\u0434\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0445 \\u0438\\u043B\\u0438 \\u0434\\u043E\\u0433\\u043E\\u0432\\u043E\\u0440\\u043D\\u044B\\u0445 \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u0442\\u0432 (\\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0430 \\u0437\\u0430\\u044F\\u0432\\u043A\\u0438, \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u044F \\u043D\\u0430 \\u0441\\u0435\\u0440\\u0432\\u0438\\u0441, \\u0432\\u044B\\u0434\\u0430\\u0447\\u0430 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438 \\u0438 \\u0442.\\u043F.), \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u044F\\u0432\\u043B\\u044F\\u0435\\u0442\\u0441\\u044F \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u043C. \\u0411\\u0435\\u0437 \\u044D\\u0442\\u0438\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u043C\\u044B \\u043D\\u0435 \\u0441\\u043C\\u043E\\u0436\\u0435\\u043C \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\\u0442\\u044C \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0443\\u044E \\u0443\\u0441\\u043B\\u0443\\u0433\\u0443.\\n    \\n    \\u0412 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u0441\\u0438\\u0442\\u0443\\u0430\\u0446\\u0438\\u044F\\u0445 (\\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u043F\\u0440\\u0438 \\u0443\\u0447\\u0430\\u0441\\u0442\\u0438\\u0438 \\u0432 \\u043E\\u043F\\u0440\\u043E\\u0441\\u0430\\u0445, \\u043C\\u0430\\u0440\\u043A\\u0435\\u0442\\u0438\\u043D\\u0433\\u043E\\u0432\\u044B\\u0445 \\u0440\\u0430\\u0441\\u0441\\u044B\\u043B\\u043A\\u0430\\u0445 \\u0438\\u043B\\u0438 \\u0434\\u043E\\u0431\\u0440\\u043E\\u0432\\u043E\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0430\\u0446\\u0438\\u0438 \\u043D\\u0430 \\u043C\\u0435\\u0440\\u043E\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0435), \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u043E\\u0441\\u0442\\u0430\\u0435\\u0442\\u0441\\u044F \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0435 \\u0443\\u0441\\u043C\\u043E\\u0442\\u0440\\u0435\\u043D\\u0438\\u0435 \\u0438 \\u043D\\u043E\\u0441\\u0438\\u0442 \\u0434\\u043E\\u0431\\u0440\\u043E\\u0432\\u043E\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0445\\u0430\\u0440\\u0430\\u043A\\u0442\\u0435\\u0440.\\n    \\n    \\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0443\\u043A\\u0430\\u0437\\u044B\\u0432\\u0430\\u0435\\u043C, \\u043A\\u0430\\u043A\\u0438\\u0435 \\u043F\\u043E\\u043B\\u044F \\u044F\\u0432\\u043B\\u044F\\u044E\\u0442\\u0441\\u044F \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438, \\u0430 \\u043A\\u0430\\u043A\\u0438\\u0435 \\u2014 \\u043E\\u043F\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438.\"},{title:'Когда мы будем обрабатывать вашу личную информацию?',content:\"\\u041C\\u044B \\u0431\\u0443\\u0434\\u0435\\u043C \\u043E\\u0431\\u0440\\u0430\\u0431\\u0430\\u0442\\u044B\\u0432\\u0430\\u0442\\u044C \\u0412\\u0430\\u0448\\u0438 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u0432 \\u0437\\u0430\\u043A\\u043E\\u043D\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043B\\u044F\\u0445, \\u0441\\u0432\\u044F\\u0437\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0441 \\u043D\\u0430\\u0448\\u0438\\u043C \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441\\u043E\\u043C, \\u0435\\u0441\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u043E \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0435\\u0435:\\n\\u0435\\u0441\\u043B\\u0438 \\u0412\\u044B \\u0434\\u0430\\u043B\\u0438 \\u043D\\u0430 \\u044D\\u0442\\u043E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435;\\n\\u0435\\u0441\\u043B\\u0438 \\u043B\\u0438\\u0446\\u043E, \\u0437\\u0430\\u043A\\u043E\\u043D\\u043D\\u043E \\u0443\\u043F\\u043E\\u043B\\u043D\\u043E\\u043C\\u043E\\u0447\\u0435\\u043D\\u043D\\u043E\\u0435 \\u0412\\u0430\\u043C\\u0438, \\u0437\\u0430\\u043A\\u043E\\u043D\\u043E\\u043C \\u0438\\u043B\\u0438 \\u0441\\u0443\\u0434\\u043E\\u043C, \\u0434\\u0430\\u043B\\u043E \\u043D\\u0430 \\u044D\\u0442\\u043E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435;\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u043E \\u0434\\u043B\\u044F \\u0437\\u0430\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u044F \\u0438\\u043B\\u0438 \\u0438\\u0441\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u0433\\u043E\\u0432\\u043E\\u0440\\u0430, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0439 \\u043C\\u044B \\u0437\\u0430\\u043A\\u043B\\u044E\\u0447\\u0438\\u043B\\u0438 \\u0441 \\u0412\\u0430\\u043C\\u0438;\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u0442\\u0440\\u0435\\u0431\\u0443\\u0435\\u0442\\u0441\\u044F \\u0438\\u043B\\u0438 \\u0440\\u0430\\u0437\\u0440\\u0435\\u0448\\u0430\\u0435\\u0442\\u0441\\u044F \\u0437\\u0430\\u043A\\u043E\\u043D\\u043E\\u043C;\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u0442\\u0440\\u0435\\u0431\\u0443\\u0435\\u0442\\u0441\\u044F \\u0434\\u043B\\u044F \\u0437\\u0430\\u0449\\u0438\\u0442\\u044B \\u0438\\u043B\\u0438 \\u043E\\u0441\\u0443\\u0449\\u0435\\u0441\\u0442\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0412\\u0430\\u0448\\u0438\\u0445, \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0438\\u043B\\u0438 \\u0437\\u0430\\u043A\\u043E\\u043D\\u043D\\u044B\\u0445 \\u0438\\u043D\\u0442\\u0435\\u0440\\u0435\\u0441\\u043E\\u0432 \\u0442\\u0440\\u0435\\u0442\\u044C\\u0435\\u0439 \\u0441\\u0442\\u043E\\u0440\\u043E\\u043D\\u044B.\"},{title:'Причины, по которым нам необходимо обрабатывать вашу персональную информацию',content:\"\\u041C\\u044B \\u0431\\u0443\\u0434\\u0435\\u043C \\u043E\\u0431\\u0440\\u0430\\u0431\\u0430\\u0442\\u044B\\u0432\\u0430\\u0442\\u044C \\u0432\\u0430\\u0448\\u0443 \\u043B\\u0438\\u0447\\u043D\\u0443\\u044E \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044E \\u043F\\u043E \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0438\\u043C \\u043F\\u0440\\u0438\\u0447\\u0438\\u043D\\u0430\\u043C:\\n\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0432\\u0430\\u043C \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432, \\u0442\\u043E\\u0432\\u0430\\u0440\\u043E\\u0432 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u043E\\u0434\\u0432\\u0438\\u0436\\u0435\\u043D\\u0438\\u044F \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432, \\u0442\\u043E\\u0432\\u0430\\u0440\\u043E\\u0432 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433 \\u043D\\u0430 \\u0432\\u0430\\u0448 \\u0440\\u044B\\u043D\\u043E\\u043A.\\n\\u0414\\u043B\\u044F \\u043E\\u0442\\u0432\\u0435\\u0442\\u0430 \\u043D\\u0430 \\u0432\\u0430\\u0448\\u0438 \\u0437\\u0430\\u043F\\u0440\\u043E\\u0441\\u044B \\u0438 \\u0436\\u0430\\u043B\\u043E\\u0431\\u044B.\\n\\u0414\\u043B\\u044F \\u0441\\u043E\\u0431\\u043B\\u044E\\u0434\\u0435\\u043D\\u0438\\u044F \\u0437\\u0430\\u043A\\u043E\\u043D\\u043E\\u0434\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u0445, \\u043D\\u043E\\u0440\\u043C\\u0430\\u0442\\u0438\\u0432\\u043D\\u044B\\u0445 \\u0442\\u0440\\u0435\\u0431\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439, \\u0442\\u0440\\u0435\\u0431\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439 \\u0440\\u0438\\u0441\\u043A\\u0430 \\u0438 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0438\\u044F (\\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u044F \\u0434\\u0438\\u0440\\u0435\\u043A\\u0442\\u0438\\u0432\\u044B, \\u0441\\u0430\\u043D\\u043A\\u0446\\u0438\\u0438 \\u0438 \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u0430), \\u0434\\u043E\\u0431\\u0440\\u043E\\u0432\\u043E\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0438 \\u043D\\u0435\\u0434\\u043E\\u0431\\u0440\\u043E\\u0432\\u043E\\u043B\\u044C\\u043D\\u044B\\u0445 \\u043A\\u043E\\u0434\\u0435\\u043A\\u0441\\u043E\\u0432 \\u043F\\u043E\\u0432\\u0435\\u0434\\u0435\\u043D\\u0438\\u044F \\u0438 \\u043E\\u0442\\u0440\\u0430\\u0441\\u043B\\u0435\\u0432\\u044B\\u0445 \\u0441\\u043E\\u0433\\u043B\\u0430\\u0448\\u0435\\u043D\\u0438\\u0439 \\u0438\\u043B\\u0438 \\u0434\\u043B\\u044F \\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u0438\\u044F \\u0442\\u0440\\u0435\\u0431\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439 \\u043F\\u043E \\u043E\\u0442\\u0447\\u0435\\u0442\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u0437\\u0430\\u043F\\u0440\\u043E\\u0441\\u043E\\u0432 \\u043D\\u0430 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044E.\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u043E\\u0432\\u0435\\u0434\\u0435\\u043D\\u0438\\u044F \\u0440\\u044B\\u043D\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0438 \\u043F\\u043E\\u0432\\u0435\\u0434\\u0435\\u043D\\u0447\\u0435\\u0441\\u043A\\u0438\\u0445 \\u0438\\u0441\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439, \\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u044F \\u043E\\u0446\\u0435\\u043D\\u043A\\u0443 \\u0438 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0437, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043E\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0438\\u0442\\u044C, \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0443\\u0435\\u0442\\u0435 \\u043B\\u0438 \\u0432\\u044B \\u0442\\u0440\\u0435\\u0431\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F\\u043C \\u0434\\u043B\\u044F \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433 \\u0438\\u043B\\u0438 \\u043E\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0438\\u0442\\u044C \\u0432\\u0430\\u0448 \\u043A\\u0440\\u0435\\u0434\\u0438\\u0442\\u043D\\u044B\\u0439 \\u0438\\u043B\\u0438 \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u043E\\u0439 \\u0440\\u0438\\u0441\\u043A.\\n\\u0414\\u043B\\u044F \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0438, \\u0442\\u0435\\u0441\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0443\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u044F \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433 \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441.\\n\\u0414\\u043B\\u044F \\u0438\\u0441\\u0442\\u043E\\u0440\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0445, \\u0441\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0445 \\u0438 \\u0438\\u0441\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u0438\\u0445 \\u0446\\u0435\\u043B\\u0435\\u0439, \\u0442\\u0430\\u043A\\u0438\\u0445 \\u043A\\u0430\\u043A \\u0441\\u0435\\u0433\\u043C\\u0435\\u043D\\u0442\\u0430\\u0446\\u0438\\u044F \\u0440\\u044B\\u043D\\u043A\\u0430.\\n\\u0414\\u043B\\u044F \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0438 \\u043F\\u043B\\u0430\\u0442\\u0435\\u0436\\u043D\\u044B\\u0445 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u043E\\u0432.\\n\\u0414\\u043B\\u044F \\u0441\\u043E\\u0437\\u0434\\u0430\\u043D\\u0438\\u044F, \\u0438\\u0437\\u0433\\u043E\\u0442\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0438 \\u043F\\u0435\\u0447\\u0430\\u0442\\u0438 \\u043F\\u043B\\u0430\\u0442\\u0435\\u0436\\u043D\\u044B\\u0445 \\u0434\\u043E\\u043A\\u0443\\u043C\\u0435\\u043D\\u0442\\u043E\\u0432 (\\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u0440\\u0430\\u0441\\u0447\\u0435\\u0442\\u043D\\u043E\\u0433\\u043E \\u043B\\u0438\\u0441\\u0442\\u043A\\u0430)\\n\\u0414\\u043B\\u044F \\u0442\\u043E\\u0433\\u043E, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043C\\u044B \\u043C\\u043E\\u0433\\u043B\\u0438 \\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u044F\\u0442\\u044C \\u0432\\u0430\\u043C \\u0442\\u043E\\u0432\\u0430\\u0440\\u044B, \\u0434\\u043E\\u043A\\u0443\\u043C\\u0435\\u043D\\u0442\\u044B \\u0438\\u043B\\u0438 \\u0443\\u0432\\u0435\\u0434\\u043E\\u043C\\u043B\\u0435\\u043D\\u0438\\u044F.\\n\\u0414\\u043B\\u044F \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0435\\u043D\\u0438\\u044F \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0441\\u0442\\u0438, \\u043F\\u0440\\u043E\\u0432\\u0435\\u0440\\u043A\\u0438 \\u043B\\u0438\\u0447\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u043F\\u0440\\u043E\\u0432\\u0435\\u0440\\u043A\\u0438 \\u0442\\u043E\\u0447\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0432\\u0430\\u0448\\u0435\\u0439 \\u043B\\u0438\\u0447\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438.\\n\\u0414\\u043B\\u044F \\u0441\\u0432\\u044F\\u0437\\u0438 \\u0441 \\u0432\\u0430\\u043C\\u0438 \\u0438 \\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u0438\\u044F \\u0432\\u0430\\u0448\\u0438\\u0445 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043A\\u0446\\u0438\\u0439 \\u0438 \\u0437\\u0430\\u043F\\u0440\\u043E\\u0441\\u043E\\u0432.\\n\\u0414\\u043B\\u044F \\u043E\\u043F\\u0440\\u043E\\u0441\\u043E\\u0432 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432, \\u0440\\u0435\\u043A\\u043B\\u0430\\u043C\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439,\\n\\u0430\\u043D\\u0434\\u0435\\u0440\\u0440\\u0430\\u0439\\u0442\\u0438\\u043D\\u0433\\u0430 \\u0438 \\u0430\\u0434\\u043C\\u0438\\u043D\\u0438\\u0441\\u0442\\u0440\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F.\\n\\u0414\\u043B\\u044F \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0438, \\u0440\\u0430\\u0441\\u0441\\u043C\\u043E\\u0442\\u0440\\u0435\\u043D\\u0438\\u044F \\u0438\\u043B\\u0438 \\u043E\\u0446\\u0435\\u043D\\u043A\\u0438 \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u044B\\u0445 \\u0438\\u043B\\u0438 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0442\\u0435\\u043D\\u0437\\u0438\\u0439.\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u044B\\u0445 \\u0438 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u044B\\u0445 \\u043F\\u043E\\u043B\\u0438\\u0441\\u043E\\u0432, \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432 \\u0438 \\u0441\\u043E\\u043F\\u0443\\u0442\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0438\\u0445 \\u0443\\u0441\\u043B\\u0443\\u0433.\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0432\\u0430\\u043C \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0443\\u0447\\u0430\\u0441\\u0442\\u0432\\u043E\\u0432\\u0430\\u0442\\u044C \\u0432 \\u043F\\u0440\\u043E\\u0433\\u0440\\u0430\\u043C\\u043C\\u0430\\u0445 \\u043B\\u043E\\u044F\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432, \\u043E\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0435\\u043D\\u0438\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0438\\u044F \\u043A\\u0440\\u0438\\u0442\\u0435\\u0440\\u0438\\u044F\\u043C \\u0443\\u0447\\u0430\\u0441\\u0442\\u0438\\u044F, \\u043D\\u0430\\u043A\\u043E\\u043F\\u043B\\u0435\\u043D\\u0438\\u044F \\u0431\\u043E\\u043D\\u0443\\u0441\\u043D\\u044B\\u0445 \\u0431\\u0430\\u043B\\u043B\\u043E\\u0432, \\u043E\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0435\\u043D\\u0438\\u044F \\u0443\\u0440\\u043E\\u0432\\u043D\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0432\\u043E\\u0437\\u043D\\u0430\\u0433\\u0440\\u0430\\u0436\\u0434\\u0435\\u043D\\u0438\\u044F, \\u043E\\u0442\\u0441\\u043B\\u0435\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043F\\u043E\\u043A\\u0443\\u043F\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u043E\\u0433\\u043E \\u043F\\u043E\\u0432\\u0435\\u0434\\u0435\\u043D\\u0438\\u044F \\u0443 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043F\\u0430\\u0440\\u0442\\u043D\\u0435\\u0440\\u043E\\u0432 \\u043F\\u043E \\u043F\\u0440\\u043E\\u0433\\u0440\\u0430\\u043C\\u043C\\u0435 \\u043B\\u043E\\u044F\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0434\\u043B\\u044F \\u043D\\u0430\\u0447\\u0438\\u0441\\u043B\\u0435\\u043D\\u0438\\u044F \\u043A\\u043E\\u0440\\u0440\\u0435\\u043A\\u0442\\u043D\\u044B\\u0445 \\u0431\\u0430\\u043B\\u043B\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0432\\u0430\\u0441 \\u043E \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u0430\\u0445, \\u0442\\u043E\\u0432\\u0430\\u0440\\u0430\\u0445 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0430\\u0445, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0432\\u0430\\u0441 \\u0437\\u0430\\u0438\\u043D\\u0442\\u0435\\u0440\\u0435\\u0441\\u043E\\u0432\\u0430\\u0442\\u044C, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0434\\u043B\\u044F \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043F\\u0430\\u0440\\u0442\\u043D\\u0435\\u0440\\u043E\\u0432 \\u043F\\u043E \\u043F\\u0440\\u043E\\u0433\\u0440\\u0430\\u043C\\u043C\\u0435 \\u043B\\u043E\\u044F\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043E \\u0432\\u0430\\u0448\\u0435\\u043C \\u043F\\u043E\\u043A\\u0443\\u043F\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u043E\\u043C \\u043F\\u043E\\u0432\\u0435\\u0434\\u0435\\u043D\\u0438\\u0438.\\n\\u0414\\u043B\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u0432\\u0430\\u043C \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0443\\u0447\\u0430\\u0441\\u0442\\u0432\\u043E\\u0432\\u0430\\u0442\\u044C \\u0432 \\u043F\\u0440\\u043E\\u0433\\u0440\\u0430\\u043C\\u043C\\u0430\\u0445 \\u0438 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0430\\u0445 \\u0441 \\u0434\\u043E\\u0431\\u0430\\u0432\\u043B\\u0435\\u043D\\u043D\\u043E\\u0439 \\u0441\\u0442\\u043E\\u0438\\u043C\\u043E\\u0441\\u0442\\u044C\\u044E \\u0438 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u044C\\u0441\\u044F \\u0438\\u043C\\u0438.\\n\\u0414\\u043B\\u044F \\u043E\\u0446\\u0435\\u043D\\u043A\\u0438 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043A\\u0440\\u0435\\u0434\\u0438\\u0442\\u043D\\u044B\\u0445 \\u0438 \\u0441\\u0442\\u0440\\u0430\\u0445\\u043E\\u0432\\u044B\\u0445 \\u0440\\u0438\\u0441\\u043A\\u043E\\u0432; \\u0438/\\u0438\\u043B\\u0438\\n\\u0434\\u043B\\u044F \\u043B\\u044E\\u0431\\u044B\\u0445 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u0441\\u0432\\u044F\\u0437\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043B\\u0435\\u0439.\"},{title:'Когда, как и с кем мы делимся вашей личной информацией?',content:\"\\u041A\\u0430\\u043A \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E, \\u043C\\u044B \\u043F\\u0435\\u0440\\u0435\\u0434\\u0430\\u0435\\u043C \\u0432\\u0430\\u0448\\u0438 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0435 \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u0432 \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0438\\u0445 \\u0441\\u043B\\u0443\\u0447\\u0430\\u044F\\u0445:\\n\\n\\u0435\\u0441\\u043B\\u0438 \\u0432\\u044B \\u0434\\u0430\\u043B\\u0438 \\u043D\\u0430 \\u044D\\u0442\\u043E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435;\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u043E \\u0434\\u043B\\u044F \\u0437\\u0430\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u044F \\u0438\\u043B\\u0438 \\u0438\\u0441\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u0433\\u043E\\u0432\\u043E\\u0440\\u0430, \\u0437\\u0430\\u043A\\u043B\\u044E\\u0447\\u0451\\u043D\\u043D\\u043E\\u0433\\u043E \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043D\\u0430\\u043C\\u0438;\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u0442\\u0440\\u0435\\u0431\\u0443\\u0435\\u0442\\u0441\\u044F \\u0437\\u0430\\u043A\\u043E\\u043D\\u043E\\u043C; \\u0438/\\u0438\\u043B\\u0438\\n\\u0435\\u0441\\u043B\\u0438 \\u044D\\u0442\\u043E \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u043E \\u0434\\u043B\\u044F \\u0437\\u0430\\u0449\\u0438\\u0442\\u044B \\u0438\\u043B\\u0438 \\u0440\\u0435\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u0438 \\u0432\\u0430\\u0448\\u0438\\u0445, \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0438\\u043B\\u0438 \\u0442\\u0440\\u0435\\u0442\\u044C\\u0438\\u0445 \\u043B\\u0438\\u0446 \\u0437\\u0430\\u043A\\u043E\\u043D\\u043D\\u044B\\u0445 \\u0438\\u043D\\u0442\\u0435\\u0440\\u0435\\u0441\\u043E\\u0432.\"},{title:'Как мы защищаем вашу личную информацию?',content:\"\\u041C\\u044B \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u043C\\u0435\\u043C \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u044B\\u0435 \\u0438 \\u0440\\u0430\\u0437\\u0443\\u043C\\u043D\\u044B\\u0435 \\u0442\\u0435\\u0445\\u043D\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0435 \\u0438 \\u043E\\u0440\\u0433\\u0430\\u043D\\u0438\\u0437\\u0430\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0435 \\u043C\\u0435\\u0440\\u044B \\u0434\\u043B\\u044F \\u0437\\u0430\\u0449\\u0438\\u0442\\u044B \\u0432\\u0430\\u0448\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0432 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0438\\u0438 \\u0441 \\u043F\\u0435\\u0440\\u0435\\u0434\\u043E\\u0432\\u044B\\u043C\\u0438 \\u043E\\u0442\\u0440\\u0430\\u0441\\u043B\\u0435\\u0432\\u044B\\u043C\\u0438 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A\\u0430\\u043C\\u0438. \\u041D\\u0430\\u0448\\u0438 \\u043C\\u0435\\u0440\\u044B \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0441\\u0442\\u0438 (\\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u044F \\u0444\\u0438\\u0437\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0435, \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0435 \\u0438 \\u043F\\u0440\\u043E\\u0446\\u0435\\u0441\\u0441\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438) \\u0431\\u0443\\u0434\\u0443\\u0442 \\u043D\\u0430\\u0434\\u043B\\u0435\\u0436\\u0430\\u0449\\u0438\\u043C\\u0438 \\u0438 \\u0440\\u0430\\u0437\\u0443\\u043C\\u043D\\u044B\\u043C\\u0438. \\u042D\\u0442\\u043E \\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u0435\\u0442 \\u0432 \\u0441\\u0435\\u0431\\u044F \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0435\\u0435:\\n\\n\\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0435\\u043D\\u0438\\u0435 \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C (\\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u043C\\u043E\\u043D\\u0438\\u0442\\u043E\\u0440\\u0438\\u043D\\u0433 \\u0434\\u043E\\u0441\\u0442\\u0443\\u043F\\u0430 \\u0438 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F);\\n\\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0435 \\u0445\\u0440\\u0430\\u043D\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0437\\u0430\\u043F\\u0438\\u0441\\u0435\\u0439;\\n\\u043A\\u043E\\u043D\\u0442\\u0440\\u043E\\u043B\\u044C \\u0434\\u043E\\u0441\\u0442\\u0443\\u043F\\u0430 \\u043A \\u043D\\u0430\\u0448\\u0438\\u043C \\u0437\\u0434\\u0430\\u043D\\u0438\\u044F\\u043C, \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430\\u043C \\u0438/\\u0438\\u043B\\u0438 \\u0437\\u0430\\u043F\\u0438\\u0441\\u044F\\u043C;\\n\\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0435 \\u0443\\u043D\\u0438\\u0447\\u0442\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u0438\\u043B\\u0438 \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0437\\u0430\\u043F\\u0438\\u0441\\u0435\\u0439.\\n\\u041E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0438\\u0442\\u044C \\u0441\\u043E\\u0431\\u043B\\u044E\\u0434\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0435\\u0440\\u0435\\u0434\\u043E\\u0432\\u044B\\u0445 \\u043F\\u0440\\u0430\\u043A\\u0442\\u0438\\u043A.\"},{title:'Наша политика в отношении файлов cookie',content:\"\\u0424\\u0430\\u0439\\u043B cookie \\u2014 \\u044D\\u0442\\u043E \\u043D\\u0435\\u0431\\u043E\\u043B\\u044C\\u0448\\u043E\\u0439 \\u0444\\u0440\\u0430\\u0433\\u043C\\u0435\\u043D\\u0442 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445, \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0435\\u043C\\u044B\\u0439 \\u043D\\u0430\\u0448\\u0438\\u043C\\u0438 \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u0430\\u043C\\u0438 \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\\u043C\\u0438 \\u043D\\u0430 \\u0436\\u0435\\u0441\\u0442\\u043A\\u0438\\u0439 \\u0434\\u0438\\u0441\\u043A \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043A\\u043E\\u043C\\u043F\\u044C\\u044E\\u0442\\u0435\\u0440\\u0430 \\u0438\\u043B\\u0438 \\u0443\\u0441\\u0442\\u0440\\u043E\\u0439\\u0441\\u0442\\u0432\\u0430, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0432 \\u0438\\u043D\\u0442\\u0435\\u0440\\u043D\\u0435\\u0442-\\u0431\\u0440\\u0430\\u0443\\u0437\\u0435\\u0440, \\u0433\\u0434\\u0435 \\u043E\\u043D \\u0441\\u043E\\u0445\\u0440\\u0430\\u043D\\u044F\\u0435\\u0442\\u0441\\u044F. \\u0424\\u0430\\u0439\\u043B cookie \\u0441\\u043E\\u0434\\u0435\\u0440\\u0436\\u0438\\u0442 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044E, \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u0438\\u0437\\u0438\\u0440\\u0443\\u044E\\u0449\\u0443\\u044E \\u0432\\u0430\\u0448 \\u043E\\u043F\\u044B\\u0442 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439, \\u0438 \\u043C\\u043E\\u0436\\u0435\\u0442 \\u0443\\u043B\\u0443\\u0447\\u0448\\u0438\\u0442\\u044C \\u0432\\u0430\\u0448 \\u043E\\u043F\\u044B\\u0442 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439. \\u0424\\u0430\\u0439\\u043B cookie \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0438\\u0434\\u0435\\u043D\\u0442\\u0438\\u0444\\u0438\\u0446\\u0438\\u0440\\u0443\\u0435\\u0442 \\u0432\\u0430\\u0448\\u0435 \\u0443\\u0441\\u0442\\u0440\\u043E\\u0439\\u0441\\u0442\\u0432\\u043E, \\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u043A\\u043E\\u043C\\u043F\\u044C\\u044E\\u0442\\u0435\\u0440 \\u0438\\u043B\\u0438 \\u0441\\u043C\\u0430\\u0440\\u0442\\u0444\\u043E\\u043D.\\n\\n\\u0418\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u0443\\u044F \\u043D\\u0430\\u0448\\u0438 \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u044B \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F, \\u0432\\u044B \\u0441\\u043E\\u0433\\u043B\\u0430\\u0448\\u0430\\u0435\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0442\\u0435\\u043C, \\u0447\\u0442\\u043E \\u0444\\u0430\\u0439\\u043B\\u044B cookie \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0431\\u044B\\u0442\\u044C \\u043F\\u0435\\u0440\\u0435\\u0441\\u043B\\u0430\\u043D\\u044B \\u0441 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0435\\u0433\\u043E \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442\\u0430 \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F \\u043D\\u0430 \\u0432\\u0430\\u0448 \\u043A\\u043E\\u043C\\u043F\\u044C\\u044E\\u0442\\u0435\\u0440 \\u0438\\u043B\\u0438 \\u0443\\u0441\\u0442\\u0440\\u043E\\u0439\\u0441\\u0442\\u0432\\u043E. \\u0424\\u0430\\u0439\\u043B cookie \\u043F\\u043E\\u0437\\u0432\\u043E\\u043B\\u0438\\u0442 \\u043D\\u0430\\u043C \\u0443\\u0437\\u043D\\u0430\\u0442\\u044C, \\u043F\\u043E\\u0441\\u0435\\u0449\\u0430\\u043B\\u0438 \\u043B\\u0438 \\u0432\\u044B \\u0432\\u0435\\u0431-\\u0441\\u0430\\u0439\\u0442 \\u0438\\u043B\\u0438 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u0440\\u0430\\u043D\\u0435\\u0435, \\u0438 \\u0438\\u0434\\u0435\\u043D\\u0442\\u0438\\u0444\\u0438\\u0446\\u0438\\u0440\\u043E\\u0432\\u0430\\u0442\\u044C \\u0432\\u0430\\u0441. \\u041C\\u044B \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043C\\u043E\\u0436\\u0435\\u043C \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u044C \\u0444\\u0430\\u0439\\u043B cookie \\u0434\\u043B\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0442\\u0432\\u0440\\u0430\\u0449\\u0435\\u043D\\u0438\\u044F \\u043C\\u043E\\u0448\\u0435\\u043D\\u043D\\u0438\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u0430\\u043D\\u0430\\u043B\\u0438\\u0442\\u0438\\u043A\\u0438.\"}];return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"section\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041F\\u041E\\u041B\\u0418\\u0422\\u0418\\u041A\\u0410 \\u041A\\u041E\\u041D\\u0424\\u0418\\u0414\\u0415\\u041D\\u0426\\u0418\\u0410\\u041B\\u042C\\u041D\\u041E\\u0421\\u0422\\u0418\"})}),/*#__PURE__*/_jsx(\"i\",{className:\"redLine\"}),/*#__PURE__*/_jsx(\"div\",{className:styles.content,children:/*#__PURE__*/_jsx(\"div\",{className:\"box\",children:sections.map((section,index)=>/*#__PURE__*/_jsxs(\"div\",{className:styles.accordion,children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>toggleSection(index),className:styles.accordionHeader,children:[section.title,/*#__PURE__*/_jsx(\"span\",{children:openIndex===index?'-':'+'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.accordionContentWrapper,\" \").concat(openIndex===index?styles.open:''),children:/*#__PURE__*/_jsx(\"div\",{className:styles.accordionContent,children:/*#__PURE__*/_jsx(\"p\",{children:section.content})})})]},index))})})]})})});};export default Privacy;", "map": {"version": 3, "names": ["useEffect", "useState", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Privacy", "loading", "setLoading", "openIndex", "setOpenIndex", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "toggleSection", "index", "sections", "title", "content", "children", "className", "map", "section", "accordion", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "concat", "accordionContentWrapper", "open", "accordion<PERSON>ontent"], "sources": ["/var/www/html/gwm.tj/src/pages/Privacy/Privacy.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport styles from './privacy.module.css';\n\nconst Privacy = () => {\n  const [loading, setLoading] = useState(true);\n  const [openIndex, setOpenIndex] = useState(null);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  const toggleSection = (index) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  const sections = [\n    {\n      title: 'Введение',\n      content: `Компания Haval Motors Tajikistan уважает право каждого пользователя на конфиденциальность и защиту персональных данных. Настоящая Политика конфиденциальности разработана с целью объяснить, какие персональные данные мы собираем, как мы их используем, храним и защищаем, а также какие у вас есть права в отношении этой информации.\nВ условиях стремительного развития цифровых технологий и расширения онлайн-сервисов защита персональных данных становится крайне важной задачей. Мы строго соблюдаем требования действующего законодательства Республики Таджикистан в области защиты персональных данных и международные стандарты, включая принципы законности, прозрачности, конфиденциальности и безопасности.\nИспользуя наш веб-сайт, услуги, продукты или предоставляя нам информацию иным способом, вы соглашаетесь с условиями настоящей Политики конфиденциальности.`,\n    },\n    {\n      title: 'Что такое личная информация?',\n      content: `Личная информация — это любые данные, которые прямо или косвенно позволяют идентифицировать вас как личность. \n    К таким данным относятся, в частности:\n    • ФИО (имя, фамилия, отчество);  \n    • номер телефона;  \n    • адрес электронной почты;  \n    • адрес проживания или регистрации;  \n    • данные удостоверения личности (паспорт, ID-карта и т.д.);  \n    • VIN-код автомобиля или регистрационные номера;  \n    • IP-адрес или данные об устройстве при использовании онлайн-сервисов.\n    \n    Личная информация может собираться нами при обращении в сервисные центры, использовании веб-сайта, участии в опросах или акциях, а также при оформлении заявок на услуги.`,\n    },\n    {\n      title:\n        'Является ли предоставление персональной информации добровольным или обязательным?',\n      content: `В зависимости от характера запрашиваемой услуги, предоставление личной информации может быть как обязательным, так и добровольным.\n    \n    В случаях, когда такие данные необходимы для выполнения наших юридических или договорных обязательств (например, обработка заявки, регистрация на сервис, выдача гарантии и т.п.), предоставление персональной информации является обязательным. Без этих данных мы не сможем предоставить соответствующую услугу.\n    \n    В других ситуациях (например, при участии в опросах, маркетинговых рассылках или добровольной регистрации на мероприятие), предоставление данных остается на ваше усмотрение и носит добровольный характер.\n    \n    Мы всегда указываем, какие поля являются обязательными, а какие — опциональными.`,\n    },\n    {\n      title: 'Когда мы будем обрабатывать вашу личную информацию?',\n      content: `Мы будем обрабатывать Ваши персональные данные только в законных целях, связанных с нашим бизнесом, если применимо следующее:\nесли Вы дали на это согласие;\nесли лицо, законно уполномоченное Вами, законом или судом, дало на это согласие;\nесли это необходимо для заключения или исполнения договора, который мы заключили с Вами;\nесли это требуется или разрешается законом;\nесли это требуется для защиты или осуществления Ваших, наших или законных интересов третьей стороны.`,\n    },\n    {\n      title:\n        'Причины, по которым нам необходимо обрабатывать вашу персональную информацию',\n      content: `Мы будем обрабатывать вашу личную информацию по следующим причинам:\n\nДля предоставления вам продуктов, товаров и услуг\nДля продвижения наших продуктов, товаров и услуг на ваш рынок.\nДля ответа на ваши запросы и жалобы.\nДля соблюдения законодательных, нормативных требований, требований риска и соответствия (включая директивы, санкции и правила), добровольных и недобровольных кодексов поведения и отраслевых соглашений или для выполнения требований по отчетности и запросов на информацию.\nДля проведения рыночных и поведенческих исследований, включая оценку и анализ, чтобы определить, соответствуете ли вы требованиям для продуктов и услуг или определить ваш кредитный или страховой риск.\nДля разработки, тестирования и улучшения продуктов и услуг для вас.\nДля исторических, статистических и исследовательских целей, таких как сегментация рынка.\nДля обработки платежных инструментов.\nДля создания, изготовления и печати платежных документов (например, расчетного листка)\nДля того, чтобы мы могли доставлять вам товары, документы или уведомления.\nДля обеспечения безопасности, проверки личности и проверки точности вашей личной информации.\nДля связи с вами и выполнения ваших инструкций и запросов.\nДля опросов удовлетворенности клиентов, рекламных предложений,\nандеррайтинга и администрирования страхования и страхования.\nДля обработки, рассмотрения или оценки страховых или гарантийных претензий.\nДля предоставления страховых и гарантийных полисов, продуктов и сопутствующих услуг.\nДля предоставления вам возможности участвовать в программах лояльности клиентов, определения вашего соответствия критериям участия, накопления бонусных баллов, определения уровня вашего вознаграждения, отслеживания вашего покупательского поведения у наших партнеров по программе лояльности для начисления корректных баллов или информирования вас о продуктах, товарах и услугах, которые могут вас заинтересовать, а также для информирования наших партнеров по программе лояльности о вашем покупательском поведении.\nДля предоставления вам возможности участвовать в программах и услугах с добавленной стоимостью и пользоваться ими.\nДля оценки наших кредитных и страховых рисков; и/или\nдля любых других связанных целей.`,\n    },\n    {\n      title: 'Когда, как и с кем мы делимся вашей личной информацией?',\n      content: `Как правило, мы передаем ваши персональные данные только в следующих случаях:\n\nесли вы дали на это согласие;\nесли это необходимо для заключения или исполнения договора, заключённого между нами;\nесли это требуется законом; и/или\nесли это необходимо для защиты или реализации ваших, наших или третьих лиц законных интересов.`,\n    },\n    {\n      title: 'Как мы защищаем вашу личную информацию?',\n      content: `Мы предпримем необходимые и разумные технические и организационные меры для защиты ваших персональных данных в соответствии с передовыми отраслевыми практиками. Наши меры безопасности (включая физические, технологические и процессуальные гарантии) будут надлежащими и разумными. Это включает в себя следующее:\n\nобеспечение безопасности наших систем (например, мониторинг доступа и использования);\nбезопасное хранение наших записей;\nконтроль доступа к нашим зданиям, системам и/или записям;\nбезопасное уничтожение или удаление записей.\nОбеспечить соблюдение передовых практик.`,\n    },\n    {\n      title: 'Наша политика в отношении файлов cookie',\n      content: `Файл cookie — это небольшой фрагмент данных, отправляемый нашими веб-сайтами или приложениями на жесткий диск вашего компьютера или устройства, а также в интернет-браузер, где он сохраняется. Файл cookie содержит информацию, персонализирующую ваш опыт использования наших веб-сайтов или приложений, и может улучшить ваш опыт использования веб-сайтов или приложений. Файл cookie также идентифицирует ваше устройство, например, компьютер или смартфон.\n\nИспользуя наши веб-сайты или приложения, вы соглашаетесь с тем, что файлы cookie могут быть пересланы с соответствующего веб-сайта или приложения на ваш компьютер или устройство. Файл cookie позволит нам узнать, посещали ли вы веб-сайт или приложение ранее, и идентифицировать вас. Мы также можем использовать файл cookie для предотвращения мошенничества и аналитики.`,\n    },\n  ];\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <section className=\"container\">\n            <h1 className=\"title\">\n              <strong>\n                ПОЛИТИКА КОНФИДЕНЦИАЛЬНОСТИ\n              </strong>\n            </h1>\n            <i className=\"redLine\"></i>\n            <div className={styles.content}>\n              <div className=\"box\">\n                {sections.map((section, index) => (\n                  <div key={index} className={styles.accordion}>\n                    <button\n                      onClick={() => toggleSection(index)}\n                      className={styles.accordionHeader}\n                    >\n                      {section.title}\n                      <span>{openIndex === index ? '-' : '+'}</span>\n                    </button>\n                    <div\n                      className={`${styles.accordionContentWrapper} ${\n                        openIndex === index ? styles.open : ''\n                      }`}\n                    >\n                      <div className={styles.accordionContent}>\n                        <p>{section.content}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </section>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Privacy;\n"], "mappings": "AAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1C,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGV,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACW,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAEhDD,SAAS,CAAC,IAAM,CACdc,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BV,UAAU,CAAC,KAAK,CAAC,CACjBK,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,aAAa,CAAIC,KAAK,EAAK,CAC/BX,YAAY,CAACD,SAAS,GAAKY,KAAK,CAAG,IAAI,CAAGA,KAAK,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEC,KAAK,CAAE,UAAU,CACjBC,OAAO,u1IAGT,CAAC,CACD,CACED,KAAK,CAAE,8BAA8B,CACrCC,OAAO,6jGAWT,CAAC,CACD,CACED,KAAK,CACH,mFAAmF,CACrFC,OAAO,quHAOT,CAAC,CACD,CACED,KAAK,CAAE,qDAAqD,CAC5DC,OAAO,43EAMT,CAAC,CACD,CACED,KAAK,CACH,8EAA8E,CAChFC,OAAO,0zWAsBT,CAAC,CACD,CACED,KAAK,CAAE,yDAAyD,CAChEC,OAAO,4nDAMT,CAAC,CACD,CACED,KAAK,CAAE,yCAAyC,CAChDC,OAAO,+9FAOT,CAAC,CACD,CACED,KAAK,CAAE,yCAAyC,CAChDC,OAAO,y+HAGT,CAAC,CACF,CAED,mBACEvB,IAAA,CAAAI,SAAA,EAAAoB,QAAA,CACGlB,OAAO,cACNN,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BxB,IAAA,QAAKyB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENzB,IAAA,QAAKyB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBtB,KAAA,YAASuB,SAAS,CAAC,WAAW,CAAAD,QAAA,eAC5BxB,IAAA,OAAIyB,SAAS,CAAC,OAAO,CAAAD,QAAA,cACnBxB,IAAA,WAAAwB,QAAA,CAAQ,+JAER,CAAQ,CAAC,CACP,CAAC,cACLxB,IAAA,MAAGyB,SAAS,CAAC,SAAS,CAAI,CAAC,cAC3BzB,IAAA,QAAKyB,SAAS,CAAE3B,MAAM,CAACyB,OAAQ,CAAAC,QAAA,cAC7BxB,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAD,QAAA,CACjBH,QAAQ,CAACK,GAAG,CAAC,CAACC,OAAO,CAAEP,KAAK,gBAC3BlB,KAAA,QAAiBuB,SAAS,CAAE3B,MAAM,CAAC8B,SAAU,CAAAJ,QAAA,eAC3CtB,KAAA,WACE2B,OAAO,CAAEA,CAAA,GAAMV,aAAa,CAACC,KAAK,CAAE,CACpCK,SAAS,CAAE3B,MAAM,CAACgC,eAAgB,CAAAN,QAAA,EAEjCG,OAAO,CAACL,KAAK,cACdtB,IAAA,SAAAwB,QAAA,CAAOhB,SAAS,GAAKY,KAAK,CAAG,GAAG,CAAG,GAAG,CAAO,CAAC,EACxC,CAAC,cACTpB,IAAA,QACEyB,SAAS,IAAAM,MAAA,CAAKjC,MAAM,CAACkC,uBAAuB,MAAAD,MAAA,CAC1CvB,SAAS,GAAKY,KAAK,CAAGtB,MAAM,CAACmC,IAAI,CAAG,EAAE,CACrC,CAAAT,QAAA,cAEHxB,IAAA,QAAKyB,SAAS,CAAE3B,MAAM,CAACoC,gBAAiB,CAAAV,QAAA,cACtCxB,IAAA,MAAAwB,QAAA,CAAIG,OAAO,CAACJ,OAAO,CAAI,CAAC,CACrB,CAAC,CACH,CAAC,GAhBEH,KAiBL,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACC,CAAC,CACP,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}