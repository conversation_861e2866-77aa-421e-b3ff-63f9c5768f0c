{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/layout/Navbar/components/mobPrimaryBar/PrimaryBar.jsx\";\nimport React from 'react';\nimport { FiChevronLeft } from 'react-icons/fi';\nimport styles from './primaryBar.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrimaryBar = ({\n  isVisible,\n  onClick\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.primaryBar} ${isVisible ? styles.show : ''}`,\n    onClick: onClick,\n    children: [/*#__PURE__*/_jsxDEV(FiChevronLeft, {\n      className: styles.icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\u041D\\u0430\\u0437\\u0430\\u0434\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = PrimaryBar;\nexport default PrimaryBar;\nvar _c;\n$RefreshReg$(_c, \"PrimaryBar\");", "map": {"version": 3, "names": ["React", "FiChevronLeft", "styles", "jsxDEV", "_jsxDEV", "PrimaryBar", "isVisible", "onClick", "className", "primaryBar", "show", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/layout/Navbar/components/mobPrimaryBar/PrimaryBar.jsx"], "sourcesContent": ["import React from 'react';\nimport { FiChevronLeft } from 'react-icons/fi';\nimport styles from './primaryBar.module.css';\n\nconst PrimaryBar = ({ isVisible, onClick }) => {\n  return (\n    <div\n      className={`${styles.primaryBar} ${isVisible ? styles.show : ''}`}\n      onClick={onClick}\n    >\n      <FiChevronLeft className={styles.icon} />\n      <span>Назад</span>\n    </div>\n  );\n};\n\nexport default PrimaryBar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,MAAM,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAC7C,oBACEH,OAAA;IACEI,SAAS,EAAE,GAAGN,MAAM,CAACO,UAAU,IAAIH,SAAS,GAAGJ,MAAM,CAACQ,IAAI,GAAG,EAAE,EAAG;IAClEH,OAAO,EAAEA,OAAQ;IAAAI,QAAA,gBAEjBP,OAAA,CAACH,aAAa;MAACO,SAAS,EAAEN,MAAM,CAACU;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCZ,OAAA;MAAAO,QAAA,EAAM;IAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIZ,UAAU;AAYhB,eAAeA,UAAU;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}