{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Discover/Contact/ContactForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useRef } from 'react';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../components/Notification/Notification';\nimport { formRateLimiter, sanitizeAndValidateForm } from '../../../utils/validation';\nimport { submitFeedback } from '../../../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CallbackForm = ({\n  formType = 'callback',\n  title\n}) => {\n  _s();\n  const [notification, setNotification] = useState({\n    message: '',\n    type: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification({\n      message: '',\n      type: ''\n    }), 3000);\n  }, []);\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: {\n      _: /\\d/\n    }\n  });\n  const formRef = useRef(null);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isSubmitting) return;\n    if (!formRateLimiter.isAllowed()) {\n      showNotification('Слишком много попыток отправки. Подождите минуту.', 'error');\n      return;\n    }\n    setIsSubmitting(true);\n    setFormErrors({});\n    const form = e.currentTarget;\n    const formDataObj = new FormData(form);\n    const data = Object.fromEntries(formDataObj.entries());\n    data.consent = form.consent.checked;\n    try {\n      const {\n        data: sanitizedData,\n        errors,\n        isValid\n      } = sanitizeAndValidateForm(data);\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        setIsSubmitting(false);\n        return;\n      }\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: '',\n        message: 'Обратный звонок',\n        consent: sanitizedData.consent,\n        formType\n      };\n      await submitFeedback(payload);\n      showNotification('Ваш запрос на обратный звонок отправлен!', 'success');\n      form.reset(); // сброс формы\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Notification, {\n      message: notification.message,\n      type: notification.type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), title && /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: styles.formTitle,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      ref: formRef,\n      onSubmit: handleSubmit,\n      className: styles.form,\n      noValidate: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"firstName\",\n            className: styles.label,\n            children: [\"\\u0418\\u043C\\u044F \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.required,\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"firstName\",\n            name: \"firstName\",\n            type: \"text\",\n            className: `${styles.input} ${formErrors.firstName ? styles.inputError : ''}`,\n            required: true,\n            minLength: 2,\n            maxLength: 50,\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), formErrors.firstName && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.errorMessage,\n            children: formErrors.firstName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"lastName\",\n            className: styles.label,\n            children: \"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"lastName\",\n            name: \"lastName\",\n            type: \"text\",\n            className: `${styles.input} ${formErrors.lastName ? styles.inputError : ''}`,\n            minLength: 2,\n            maxLength: 50,\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), formErrors.lastName && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.errorMessage,\n            children: formErrors.lastName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.inputGroup,\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"phone\",\n          className: styles.label,\n          children: [\"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.required,\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 28\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"phone\",\n          name: \"phone\",\n          type: \"tel\",\n          placeholder: \"+992 XXX-XX-XX-XX\",\n          className: `${styles.input} ${formErrors.phone ? styles.inputError : ''}`,\n          required: true,\n          ref: phoneRef,\n          disabled: isSubmitting\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), formErrors.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.errorMessage,\n          children: formErrors.phone\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.checkboxGroup,\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: styles.checkboxWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"consent\",\n            name: \"consent\",\n            type: \"checkbox\",\n            required: true,\n            className: styles.checkbox,\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.checkboxLabel,\n            children: \"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: `${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`,\n        disabled: isSubmitting,\n        children: isSubmitting ? 'Отправка...' : 'Отправить'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(CallbackForm, \"FLJYysyN4lXn/npL7n307cpj8mg=\", false, function () {\n  return [useMask];\n});\n_c = CallbackForm;\nexport default CallbackForm;\nvar _c;\n$RefreshReg$(_c, \"CallbackForm\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "styles", "useMask", "Notification", "formRateLimiter", "sanitizeAndValidateForm", "submitFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CallbackForm", "formType", "title", "_s", "notification", "setNotification", "message", "type", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "setTimeout", "phoneRef", "mask", "replacement", "_", "formRef", "handleSubmit", "e", "preventDefault", "isAllowed", "form", "currentTarget", "formDataObj", "FormData", "data", "Object", "fromEntries", "entries", "consent", "checked", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "firstName", "lastName", "phone", "topic", "reset", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "formTitle", "ref", "onSubmit", "noValidate", "row", "inputGroup", "htmlFor", "label", "required", "id", "name", "input", "inputError", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "disabled", "errorMessage", "placeholder", "checkboxGroup", "checkboxWrapper", "checkbox", "checkboxLabel", "button", "buttonLoading", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/Contact/ContactForm.jsx"], "sourcesContent": ["import React, { useState, useCallback, useRef } from 'react';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../components/Notification/Notification';\nimport {\n  formRateLimiter,\n  sanitizeAndValidateForm,\n} from '../../../utils/validation';\nimport { submitFeedback } from '../../../utils/api';\n\nconst CallbackForm = ({ formType = 'callback', title }) => {\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  const formRef = useRef(null);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (isSubmitting) return;\n\n    if (!formRateLimiter.isAllowed()) {\n      showNotification(\n        'Слишком много попыток отправки. Подождите минуту.',\n        'error'\n      );\n      return;\n    }\n\n    setIsSubmitting(true);\n    setFormErrors({});\n\n    const form = e.currentTarget;\n    const formDataObj = new FormData(form);\n    const data = Object.fromEntries(formDataObj.entries());\n    data.consent = form.consent.checked;\n\n    try {\n      const {\n        data: sanitizedData,\n        errors,\n        isValid,\n      } = sanitizeAndValidateForm(data);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        setIsSubmitting(false);\n        return;\n      }\n\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: '',\n        message: 'Обратный звонок',\n        consent: sanitizedData.consent,\n        formType,\n      };\n\n      await submitFeedback(payload);\n\n      showNotification('Ваш запрос на обратный звонок отправлен!', 'success');\n\n      form.reset(); // сброс формы\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {title && <h2 className={styles.formTitle}>{title}</h2>}\n      <form\n        ref={formRef}\n        onSubmit={handleSubmit}\n        className={styles.form}\n        noValidate\n      >\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"firstName\" className={styles.label}>\n              Имя <span className={styles.required}>*</span>\n            </label>\n            <input\n              id=\"firstName\"\n              name=\"firstName\"\n              type=\"text\"\n              className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n              required\n              minLength={2}\n              maxLength={50}\n              disabled={isSubmitting}\n            />\n            {formErrors.firstName && (\n              <div className={styles.errorMessage}>{formErrors.firstName}</div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"lastName\" className={styles.label}>\n              Фамилия\n            </label>\n            <input\n              id=\"lastName\"\n              name=\"lastName\"\n              type=\"text\"\n              className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n              minLength={2}\n              maxLength={50}\n              disabled={isSubmitting}\n            />\n            {formErrors.lastName && (\n              <div className={styles.errorMessage}>{formErrors.lastName}</div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.inputGroup}>\n          <label htmlFor=\"phone\" className={styles.label}>\n            Номер телефона <span className={styles.required}>*</span>\n          </label>\n          <input\n            id=\"phone\"\n            name=\"phone\"\n            type=\"tel\"\n            placeholder=\"+992 XXX-XX-XX-XX\"\n            className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n            required\n            ref={phoneRef}\n            disabled={isSubmitting}\n          />\n          {formErrors.phone && (\n            <div className={styles.errorMessage}>{formErrors.phone}</div>\n          )}\n        </div>\n\n        <div className={styles.checkboxGroup}>\n          <label className={styles.checkboxWrapper}>\n            <input\n              id=\"consent\"\n              name=\"consent\"\n              type=\"checkbox\"\n              required\n              className={styles.checkbox}\n              disabled={isSubmitting}\n            />\n            <span className={styles.checkboxLabel}>\n              Я даю согласие на обработку моих персональных данных\n            </span>\n          </label>\n        </div>\n\n        <button\n          type=\"submit\"\n          className={`${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`}\n          disabled={isSubmitting}\n        >\n          {isSubmitting ? 'Отправка...' : 'Отправить'}\n        </button>\n      </form>\n    </>\n  );\n};\n\nexport default CallbackForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAC5D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,+CAA+C;AACxE,SACEC,eAAe,EACfC,uBAAuB,QAClB,2BAA2B;AAClC,SAASC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ,GAAG,UAAU;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC;IAAEmB,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAC3E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMyB,gBAAgB,GAAGxB,WAAW,CAAC,CAACkB,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAClEF,eAAe,CAAC;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IAClCM,UAAU,CAAC,MAAMR,eAAe,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,QAAQ,GAAGvB,OAAO,CAAC;IACvBwB,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;MAAEC,CAAC,EAAE;IAAK;EACzB,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAE5B,MAAM8B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIX,YAAY,EAAE;IAElB,IAAI,CAACjB,eAAe,CAAC6B,SAAS,CAAC,CAAC,EAAE;MAChCV,gBAAgB,CACd,mDAAmD,EACnD,OACF,CAAC;MACD;IACF;IAEAD,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,CAAC,CAAC,CAAC;IAEjB,MAAMc,IAAI,GAAGH,CAAC,CAACI,aAAa;IAC5B,MAAMC,WAAW,GAAG,IAAIC,QAAQ,CAACH,IAAI,CAAC;IACtC,MAAMI,IAAI,GAAGC,MAAM,CAACC,WAAW,CAACJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC;IACtDH,IAAI,CAACI,OAAO,GAAGR,IAAI,CAACQ,OAAO,CAACC,OAAO;IAEnC,IAAI;MACF,MAAM;QACJL,IAAI,EAAEM,aAAa;QACnBC,MAAM;QACNC;MACF,CAAC,GAAGzC,uBAAuB,CAACiC,IAAI,CAAC;MAEjC,IAAI,CAACQ,OAAO,EAAE;QACZ1B,aAAa,CAACyB,MAAM,CAAC;QACrBtB,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,CAAC;QACjED,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA,MAAMyB,OAAO,GAAG;QACdC,SAAS,EAAEJ,aAAa,CAACI,SAAS;QAClCC,QAAQ,EAAEL,aAAa,CAACK,QAAQ;QAChCC,KAAK,EAAEN,aAAa,CAACM,KAAK;QAC1BC,KAAK,EAAE,EAAE;QACTlC,OAAO,EAAE,iBAAiB;QAC1ByB,OAAO,EAAEE,aAAa,CAACF,OAAO;QAC9B9B;MACF,CAAC;MAED,MAAMN,cAAc,CAACyC,OAAO,CAAC;MAE7BxB,gBAAgB,CAAC,0CAA0C,EAAE,SAAS,CAAC;MAEvEW,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MACdhC,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd9B,gBAAgB,CAAC8B,KAAK,CAACpC,OAAO,EAAE,OAAO,CAAC;IAC1C,CAAC,SAAS;MACRK,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACEd,OAAA,CAAAE,SAAA;IAAA4C,QAAA,gBACE9C,OAAA,CAACL,YAAY;MAACc,OAAO,EAAEF,YAAY,CAACE,OAAQ;MAACC,IAAI,EAAEH,YAAY,CAACG;IAAK;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvE7C,KAAK,iBAAIL,OAAA;MAAImD,SAAS,EAAE1D,MAAM,CAAC2D,SAAU;MAAAN,QAAA,EAAEzC;IAAK;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvDlD,OAAA;MACEqD,GAAG,EAAEhC,OAAQ;MACbiC,QAAQ,EAAEhC,YAAa;MACvB6B,SAAS,EAAE1D,MAAM,CAACiC,IAAK;MACvB6B,UAAU;MAAAT,QAAA,gBAEV9C,OAAA;QAAKmD,SAAS,EAAE1D,MAAM,CAAC+D,GAAI;QAAAV,QAAA,gBACzB9C,OAAA;UAAKmD,SAAS,EAAE1D,MAAM,CAACgE,UAAW;UAAAX,QAAA,gBAChC9C,OAAA;YAAO0D,OAAO,EAAC,WAAW;YAACP,SAAS,EAAE1D,MAAM,CAACkE,KAAM;YAAAb,QAAA,GAAC,qBAC9C,eAAA9C,OAAA;cAAMmD,SAAS,EAAE1D,MAAM,CAACmE,QAAS;cAAAd,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACRlD,OAAA;YACE6D,EAAE,EAAC,WAAW;YACdC,IAAI,EAAC,WAAW;YAChBpD,IAAI,EAAC,MAAM;YACXyC,SAAS,EAAE,GAAG1D,MAAM,CAACsE,KAAK,IAAIpD,UAAU,CAAC6B,SAAS,GAAG/C,MAAM,CAACuE,UAAU,GAAG,EAAE,EAAG;YAC9EJ,QAAQ;YACRK,SAAS,EAAE,CAAE;YACbC,SAAS,EAAE,EAAG;YACdC,QAAQ,EAAEtD;UAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACDvC,UAAU,CAAC6B,SAAS,iBACnBxC,OAAA;YAAKmD,SAAS,EAAE1D,MAAM,CAAC2E,YAAa;YAAAtB,QAAA,EAAEnC,UAAU,CAAC6B;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACjE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlD,OAAA;UAAKmD,SAAS,EAAE1D,MAAM,CAACgE,UAAW;UAAAX,QAAA,gBAChC9C,OAAA;YAAO0D,OAAO,EAAC,UAAU;YAACP,SAAS,EAAE1D,MAAM,CAACkE,KAAM;YAAAb,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlD,OAAA;YACE6D,EAAE,EAAC,UAAU;YACbC,IAAI,EAAC,UAAU;YACfpD,IAAI,EAAC,MAAM;YACXyC,SAAS,EAAE,GAAG1D,MAAM,CAACsE,KAAK,IAAIpD,UAAU,CAAC8B,QAAQ,GAAGhD,MAAM,CAACuE,UAAU,GAAG,EAAE,EAAG;YAC7EC,SAAS,EAAE,CAAE;YACbC,SAAS,EAAE,EAAG;YACdC,QAAQ,EAAEtD;UAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACDvC,UAAU,CAAC8B,QAAQ,iBAClBzC,OAAA;YAAKmD,SAAS,EAAE1D,MAAM,CAAC2E,YAAa;YAAAtB,QAAA,EAAEnC,UAAU,CAAC8B;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAKmD,SAAS,EAAE1D,MAAM,CAACgE,UAAW;QAAAX,QAAA,gBAChC9C,OAAA;UAAO0D,OAAO,EAAC,OAAO;UAACP,SAAS,EAAE1D,MAAM,CAACkE,KAAM;UAAAb,QAAA,GAAC,kFAC/B,eAAA9C,OAAA;YAAMmD,SAAS,EAAE1D,MAAM,CAACmE,QAAS;YAAAd,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACRlD,OAAA;UACE6D,EAAE,EAAC,OAAO;UACVC,IAAI,EAAC,OAAO;UACZpD,IAAI,EAAC,KAAK;UACV2D,WAAW,EAAC,mBAAmB;UAC/BlB,SAAS,EAAE,GAAG1D,MAAM,CAACsE,KAAK,IAAIpD,UAAU,CAAC+B,KAAK,GAAGjD,MAAM,CAACuE,UAAU,GAAG,EAAE,EAAG;UAC1EJ,QAAQ;UACRP,GAAG,EAAEpC,QAAS;UACdkD,QAAQ,EAAEtD;QAAa;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EACDvC,UAAU,CAAC+B,KAAK,iBACf1C,OAAA;UAAKmD,SAAS,EAAE1D,MAAM,CAAC2E,YAAa;UAAAtB,QAAA,EAAEnC,UAAU,CAAC+B;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC7D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlD,OAAA;QAAKmD,SAAS,EAAE1D,MAAM,CAAC6E,aAAc;QAAAxB,QAAA,eACnC9C,OAAA;UAAOmD,SAAS,EAAE1D,MAAM,CAAC8E,eAAgB;UAAAzB,QAAA,gBACvC9C,OAAA;YACE6D,EAAE,EAAC,SAAS;YACZC,IAAI,EAAC,SAAS;YACdpD,IAAI,EAAC,UAAU;YACfkD,QAAQ;YACRT,SAAS,EAAE1D,MAAM,CAAC+E,QAAS;YAC3BL,QAAQ,EAAEtD;UAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFlD,OAAA;YAAMmD,SAAS,EAAE1D,MAAM,CAACgF,aAAc;YAAA3B,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QACEU,IAAI,EAAC,QAAQ;QACbyC,SAAS,EAAE,GAAG1D,MAAM,CAACiF,MAAM,IAAI7D,YAAY,GAAGpB,MAAM,CAACkF,aAAa,GAAG,EAAE,EAAG;QAC1ER,QAAQ,EAAEtD,YAAa;QAAAiC,QAAA,EAEtBjC,YAAY,GAAG,aAAa,GAAG;MAAW;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAAC5C,EAAA,CAxKIH,YAAY;EAAA,QAUCT,OAAO;AAAA;AAAAkF,EAAA,GAVpBzE,YAAY;AA0KlB,eAAeA,YAAY;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}