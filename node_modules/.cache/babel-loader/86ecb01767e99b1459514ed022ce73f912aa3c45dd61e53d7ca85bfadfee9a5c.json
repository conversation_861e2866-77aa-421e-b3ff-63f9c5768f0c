{"ast": null, "code": "import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = value => {\n  return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n  current: undefined\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n  /**\n   * @param init - The initiating value\n   * @param config - Optional configuration options\n   *\n   * -  `transformer`: A function to transform incoming values with.\n   */\n  constructor(init) {\n    var _this = this;\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * Tracks whether this value can output a velocity. Currently this is only true\n     * if the value is numerical, but we might be able to widen the scope here and support\n     * other value types.\n     *\n     * @internal\n     */\n    this.canTrackVelocity = null;\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    this.updateAndNotify = function (v) {\n      let render = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      const currentTime = time.now();\n      /**\n       * If we're updating the value during another frame or eventloop\n       * than the previous frame, then the we set the previous frame value\n       * to current.\n       */\n      if (_this.updatedAt !== currentTime) {\n        _this.setPrevFrameValue();\n      }\n      _this.prev = _this.current;\n      _this.setCurrent(v);\n      // Update update subscribers\n      if (_this.current !== _this.prev) {\n        var _this$events$change;\n        (_this$events$change = _this.events.change) === null || _this$events$change === void 0 || _this$events$change.notify(_this.current);\n        if (_this.dependents) {\n          for (const dependent of _this.dependents) {\n            dependent.dirty();\n          }\n        }\n      }\n      // Update render subscribers\n      if (render) {\n        var _this$events$renderRe;\n        (_this$events$renderRe = _this.events.renderRequest) === null || _this$events$renderRe === void 0 || _this$events$renderRe.notify(_this.current);\n      }\n    };\n    this.hasAnimated = false;\n    this.setCurrent(init);\n    this.owner = options.owner;\n  }\n  setCurrent(current) {\n    this.current = current;\n    this.updatedAt = time.now();\n    if (this.canTrackVelocity === null && current !== undefined) {\n      this.canTrackVelocity = isFloat(this.current);\n    }\n  }\n  setPrevFrameValue() {\n    let prevFrameValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.current;\n    this.prevFrameValue = prevFrameValue;\n    this.prevUpdatedAt = this.updatedAt;\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   *\n   * When calling `onChange` inside a React component, it should be wrapped with the\n   * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n   * from the `useEffect` function to ensure you don't add duplicate subscribers..\n   *\n   * ```jsx\n   * export const MyComponent = () => {\n   *   const x = useMotionValue(0)\n   *   const y = useMotionValue(0)\n   *   const opacity = useMotionValue(1)\n   *\n   *   useEffect(() => {\n   *     function updateOpacity() {\n   *       const maxXY = Math.max(x.get(), y.get())\n   *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n   *       opacity.set(newOpacity)\n   *     }\n   *\n   *     const unsubscribeX = x.on(\"change\", updateOpacity)\n   *     const unsubscribeY = y.on(\"change\", updateOpacity)\n   *\n   *     return () => {\n   *       unsubscribeX()\n   *       unsubscribeY()\n   *     }\n   *   }, [])\n   *\n   *   return <motion.div style={{ x }} />\n   * }\n   * ```\n   *\n   * @param subscriber - A function that receives the latest value.\n   * @returns A function that, when called, will cancel this subscription.\n   *\n   * @deprecated\n   */\n  onChange(subscription) {\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(false, \"value.onChange(callback) is deprecated. Switch to value.on(\\\"change\\\", callback).\");\n    }\n    return this.on(\"change\", subscription);\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    const unsubscribe = this.events[eventName].add(callback);\n    if (eventName === \"change\") {\n      return () => {\n        unsubscribe();\n        /**\n         * If we have no more change listeners by the start\n         * of the next frame, stop active animations.\n         */\n        frame.read(() => {\n          if (!this.events.change.getSize()) {\n            this.stop();\n          }\n        });\n      };\n    }\n    return unsubscribe;\n  }\n  clearListeners() {\n    for (const eventManagers in this.events) {\n      this.events[eventManagers].clear();\n    }\n  }\n  /**\n   * Attaches a passive effect to the `MotionValue`.\n   */\n  attach(passiveEffect, stopPassiveEffect) {\n    this.passiveEffect = passiveEffect;\n    this.stopPassiveEffect = stopPassiveEffect;\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @remarks\n   *\n   * ```jsx\n   * const x = useMotionValue(0)\n   * x.set(10)\n   * ```\n   *\n   * @param latest - Latest value to set.\n   * @param render - Whether to notify render subscribers. Defaults to `true`\n   *\n   * @public\n   */\n  set(v) {\n    let render = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    if (!render || !this.passiveEffect) {\n      this.updateAndNotify(v, render);\n    } else {\n      this.passiveEffect(v, this.updateAndNotify);\n    }\n  }\n  setWithVelocity(prev, current, delta) {\n    this.set(current);\n    this.prev = undefined;\n    this.prevFrameValue = prev;\n    this.prevUpdatedAt = this.updatedAt - delta;\n  }\n  /**\n   * Set the state of the `MotionValue`, stopping any active animations,\n   * effects, and resets velocity to `0`.\n   */\n  jump(v) {\n    let endAnimation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    this.updateAndNotify(v);\n    this.prev = v;\n    this.prevUpdatedAt = this.prevFrameValue = undefined;\n    endAnimation && this.stop();\n    if (this.stopPassiveEffect) this.stopPassiveEffect();\n  }\n  dirty() {\n    var _this$events$change2;\n    (_this$events$change2 = this.events.change) === null || _this$events$change2 === void 0 || _this$events$change2.notify(this.current);\n  }\n  addDependent(dependent) {\n    if (!this.dependents) {\n      this.dependents = new Set();\n    }\n    this.dependents.add(dependent);\n  }\n  removeDependent(dependent) {\n    if (this.dependents) {\n      this.dependents.delete(dependent);\n    }\n  }\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   *\n   * @public\n   */\n  get() {\n    if (collectMotionValues.current) {\n      collectMotionValues.current.push(this);\n    }\n    return this.current;\n  }\n  /**\n   * @public\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   *\n   * @public\n   */\n  getVelocity() {\n    const currentTime = time.now();\n    if (!this.canTrackVelocity || this.prevFrameValue === undefined || currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n      return 0;\n    }\n    const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n    // Casts because of parseFloat's poor typing\n    return velocityPerSecond(parseFloat(this.current) - parseFloat(this.prevFrameValue), delta);\n  }\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   *\n   * ```jsx\n   * value.start()\n   * ```\n   *\n   * @param animation - A function that starts the provided animation\n   */\n  start(startAnimation) {\n    this.stop();\n    return new Promise(resolve => {\n      this.hasAnimated = true;\n      this.animation = startAnimation(resolve);\n      if (this.events.animationStart) {\n        this.events.animationStart.notify();\n      }\n    }).then(() => {\n      if (this.events.animationComplete) {\n        this.events.animationComplete.notify();\n      }\n      this.clearAnimation();\n    });\n  }\n  /**\n   * Stop the currently active animation.\n   *\n   * @public\n   */\n  stop() {\n    if (this.animation) {\n      this.animation.stop();\n      if (this.events.animationCancel) {\n        this.events.animationCancel.notify();\n      }\n    }\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   *\n   * @public\n   */\n  isAnimating() {\n    return !!this.animation;\n  }\n  clearAnimation() {\n    delete this.animation;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   *\n   * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n   * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n   * created a `MotionValue` via the `motionValue` function.\n   *\n   * @public\n   */\n  destroy() {\n    var _this$dependents, _this$events$destroy;\n    (_this$dependents = this.dependents) === null || _this$dependents === void 0 || _this$dependents.clear();\n    (_this$events$destroy = this.events.destroy) === null || _this$events$destroy === void 0 || _this$events$destroy.notify();\n    this.clearListeners();\n    this.stop();\n    if (this.stopPassiveEffect) {\n      this.stopPassiveEffect();\n    }\n  }\n}\nfunction motionValue(init, options) {\n  return new MotionValue(init, options);\n}\nexport { MotionValue, collectMotionValues, motionValue };", "map": {"version": 3, "names": ["warnOnce", "SubscriptionManager", "velocityPerSecond", "time", "frame", "MAX_VELOCITY_DELTA", "isFloat", "value", "isNaN", "parseFloat", "collectMotionValues", "current", "undefined", "MotionValue", "constructor", "init", "_this", "options", "arguments", "length", "canTrackVelocity", "events", "updateAndNotify", "v", "render", "currentTime", "now", "updatedAt", "setPrevFrameValue", "prev", "setCurrent", "_this$events$change", "change", "notify", "dependents", "dependent", "dirty", "_this$events$renderRe", "renderRequest", "hasAnimated", "owner", "prevFrameValue", "prevUpdatedAt", "onChange", "subscription", "process", "env", "NODE_ENV", "on", "eventName", "callback", "unsubscribe", "add", "read", "getSize", "stop", "clearListeners", "eventManagers", "clear", "attach", "passiveEffect", "stopPassiveEffect", "set", "setWithVelocity", "delta", "jump", "endAnimation", "_this$events$change2", "addDependent", "Set", "removeDependent", "delete", "get", "push", "getPrevious", "getVelocity", "Math", "min", "start", "startAnimation", "Promise", "resolve", "animation", "animationStart", "then", "animationComplete", "clearAnimation", "animationCancel", "isAnimating", "destroy", "_this$dependents", "_this$events$destroy", "motionValue"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/value/index.mjs"], "sourcesContent": ["import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n            // Update render subscribers\n            if (render) {\n                this.events.renderRequest?.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,mBAAmB,EAAEC,iBAAiB,QAAQ,cAAc;AAC/E,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,OAAO,GAAIC,KAAK,IAAK;EACvB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;AACpC,CAAC;AACD,MAAMG,mBAAmB,GAAG;EACxBC,OAAO,EAAEC;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAgB;IAAA,IAAAC,KAAA;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAN,SAAA,GAAAM,SAAA,MAAG,CAAC,CAAC;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,eAAe,GAAG,UAACC,CAAC,EAAoB;MAAA,IAAlBC,MAAM,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAN,SAAA,GAAAM,SAAA,MAAG,IAAI;MACpC,MAAMO,WAAW,GAAGtB,IAAI,CAACuB,GAAG,CAAC,CAAC;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAIV,KAAI,CAACW,SAAS,KAAKF,WAAW,EAAE;QAChCT,KAAI,CAACY,iBAAiB,CAAC,CAAC;MAC5B;MACAZ,KAAI,CAACa,IAAI,GAAGb,KAAI,CAACL,OAAO;MACxBK,KAAI,CAACc,UAAU,CAACP,CAAC,CAAC;MAClB;MACA,IAAIP,KAAI,CAACL,OAAO,KAAKK,KAAI,CAACa,IAAI,EAAE;QAAA,IAAAE,mBAAA;QAC5B,CAAAA,mBAAA,GAAAf,KAAI,CAACK,MAAM,CAACW,MAAM,cAAAD,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,CAACjB,KAAI,CAACL,OAAO,CAAC;QACxC,IAAIK,KAAI,CAACkB,UAAU,EAAE;UACjB,KAAK,MAAMC,SAAS,IAAInB,KAAI,CAACkB,UAAU,EAAE;YACrCC,SAAS,CAACC,KAAK,CAAC,CAAC;UACrB;QACJ;MACJ;MACA;MACA,IAAIZ,MAAM,EAAE;QAAA,IAAAa,qBAAA;QACR,CAAAA,qBAAA,GAAArB,KAAI,CAACK,MAAM,CAACiB,aAAa,cAAAD,qBAAA,eAAzBA,qBAAA,CAA2BJ,MAAM,CAACjB,KAAI,CAACL,OAAO,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAAC4B,WAAW,GAAG,KAAK;IACxB,IAAI,CAACT,UAAU,CAACf,IAAI,CAAC;IACrB,IAAI,CAACyB,KAAK,GAAGvB,OAAO,CAACuB,KAAK;EAC9B;EACAV,UAAUA,CAACnB,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACgB,SAAS,GAAGxB,IAAI,CAACuB,GAAG,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACN,gBAAgB,KAAK,IAAI,IAAIT,OAAO,KAAKC,SAAS,EAAE;MACzD,IAAI,CAACQ,gBAAgB,GAAGd,OAAO,CAAC,IAAI,CAACK,OAAO,CAAC;IACjD;EACJ;EACAiB,iBAAiBA,CAAA,EAAgC;IAAA,IAA/Ba,cAAc,GAAAvB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAN,SAAA,GAAAM,SAAA,MAAG,IAAI,CAACP,OAAO;IAC3C,IAAI,CAAC8B,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACf,SAAS;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgB,QAAQA,CAACC,YAAY,EAAE;IACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC/C,QAAQ,CAAC,KAAK,qFAAmF,CAAC;IACtG;IACA,OAAO,IAAI,CAACgD,EAAE,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EAC1C;EACAI,EAAEA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC7B,MAAM,CAAC4B,SAAS,CAAC,EAAE;MACzB,IAAI,CAAC5B,MAAM,CAAC4B,SAAS,CAAC,GAAG,IAAIhD,mBAAmB,CAAC,CAAC;IACtD;IACA,MAAMkD,WAAW,GAAG,IAAI,CAAC9B,MAAM,CAAC4B,SAAS,CAAC,CAACG,GAAG,CAACF,QAAQ,CAAC;IACxD,IAAID,SAAS,KAAK,QAAQ,EAAE;MACxB,OAAO,MAAM;QACTE,WAAW,CAAC,CAAC;QACb;AAChB;AACA;AACA;QACgB/C,KAAK,CAACiD,IAAI,CAAC,MAAM;UACb,IAAI,CAAC,IAAI,CAAChC,MAAM,CAACW,MAAM,CAACsB,OAAO,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,OAAOJ,WAAW;EACtB;EACAK,cAAcA,CAAA,EAAG;IACb,KAAK,MAAMC,aAAa,IAAI,IAAI,CAACpC,MAAM,EAAE;MACrC,IAAI,CAACA,MAAM,CAACoC,aAAa,CAAC,CAACC,KAAK,CAAC,CAAC;IACtC;EACJ;EACA;AACJ;AACA;EACIC,MAAMA,CAACC,aAAa,EAAEC,iBAAiB,EAAE;IACrC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACvC,CAAC,EAAiB;IAAA,IAAfC,MAAM,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAN,SAAA,GAAAM,SAAA,MAAG,IAAI;IAChB,IAAI,CAACM,MAAM,IAAI,CAAC,IAAI,CAACoC,aAAa,EAAE;MAChC,IAAI,CAACtC,eAAe,CAACC,CAAC,EAAEC,MAAM,CAAC;IACnC,CAAC,MACI;MACD,IAAI,CAACoC,aAAa,CAACrC,CAAC,EAAE,IAAI,CAACD,eAAe,CAAC;IAC/C;EACJ;EACAyC,eAAeA,CAAClC,IAAI,EAAElB,OAAO,EAAEqD,KAAK,EAAE;IAClC,IAAI,CAACF,GAAG,CAACnD,OAAO,CAAC;IACjB,IAAI,CAACkB,IAAI,GAAGjB,SAAS;IACrB,IAAI,CAAC6B,cAAc,GAAGZ,IAAI;IAC1B,IAAI,CAACa,aAAa,GAAG,IAAI,CAACf,SAAS,GAAGqC,KAAK;EAC/C;EACA;AACJ;AACA;AACA;EACIC,IAAIA,CAAC1C,CAAC,EAAuB;IAAA,IAArB2C,YAAY,GAAAhD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAN,SAAA,GAAAM,SAAA,MAAG,IAAI;IACvB,IAAI,CAACI,eAAe,CAACC,CAAC,CAAC;IACvB,IAAI,CAACM,IAAI,GAAGN,CAAC;IACb,IAAI,CAACmB,aAAa,GAAG,IAAI,CAACD,cAAc,GAAG7B,SAAS;IACpDsD,YAAY,IAAI,IAAI,CAACX,IAAI,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACM,iBAAiB,EACtB,IAAI,CAACA,iBAAiB,CAAC,CAAC;EAChC;EACAzB,KAAKA,CAAA,EAAG;IAAA,IAAA+B,oBAAA;IACJ,CAAAA,oBAAA,OAAI,CAAC9C,MAAM,CAACW,MAAM,cAAAmC,oBAAA,eAAlBA,oBAAA,CAAoBlC,MAAM,CAAC,IAAI,CAACtB,OAAO,CAAC;EAC5C;EACAyD,YAAYA,CAACjC,SAAS,EAAE;IACpB,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAImC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACnC,UAAU,CAACkB,GAAG,CAACjB,SAAS,CAAC;EAClC;EACAmC,eAAeA,CAACnC,SAAS,EAAE;IACvB,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACqC,MAAM,CAACpC,SAAS,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqC,GAAGA,CAAA,EAAG;IACF,IAAI9D,mBAAmB,CAACC,OAAO,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,CAAC8D,IAAI,CAAC,IAAI,CAAC;IAC1C;IACA,OAAO,IAAI,CAAC9D,OAAO;EACvB;EACA;AACJ;AACA;EACI+D,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7C,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8C,WAAWA,CAAA,EAAG;IACV,MAAMlD,WAAW,GAAGtB,IAAI,CAACuB,GAAG,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAACN,gBAAgB,IACtB,IAAI,CAACqB,cAAc,KAAK7B,SAAS,IACjCa,WAAW,GAAG,IAAI,CAACE,SAAS,GAAGtB,kBAAkB,EAAE;MACnD,OAAO,CAAC;IACZ;IACA,MAAM2D,KAAK,GAAGY,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,SAAS,GAAG,IAAI,CAACe,aAAa,EAAErC,kBAAkB,CAAC;IAC/E;IACA,OAAOH,iBAAiB,CAACO,UAAU,CAAC,IAAI,CAACE,OAAO,CAAC,GAC7CF,UAAU,CAAC,IAAI,CAACgC,cAAc,CAAC,EAAEuB,KAAK,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,KAAKA,CAACC,cAAc,EAAE;IAClB,IAAI,CAACxB,IAAI,CAAC,CAAC;IACX,OAAO,IAAIyB,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI,CAAC1C,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC2C,SAAS,GAAGH,cAAc,CAACE,OAAO,CAAC;MACxC,IAAI,IAAI,CAAC5D,MAAM,CAAC8D,cAAc,EAAE;QAC5B,IAAI,CAAC9D,MAAM,CAAC8D,cAAc,CAAClD,MAAM,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC,CAACmD,IAAI,CAAC,MAAM;MACV,IAAI,IAAI,CAAC/D,MAAM,CAACgE,iBAAiB,EAAE;QAC/B,IAAI,CAAChE,MAAM,CAACgE,iBAAiB,CAACpD,MAAM,CAAC,CAAC;MAC1C;MACA,IAAI,CAACqD,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI/B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC2B,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC3B,IAAI,CAAC,CAAC;MACrB,IAAI,IAAI,CAAClC,MAAM,CAACkE,eAAe,EAAE;QAC7B,IAAI,CAAClE,MAAM,CAACkE,eAAe,CAACtD,MAAM,CAAC,CAAC;MACxC;IACJ;IACA,IAAI,CAACqD,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACN,SAAS;EAC3B;EACAI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,OAAOA,CAAA,EAAG;IAAA,IAAAC,gBAAA,EAAAC,oBAAA;IACN,CAAAD,gBAAA,OAAI,CAACxD,UAAU,cAAAwD,gBAAA,eAAfA,gBAAA,CAAiBhC,KAAK,CAAC,CAAC;IACxB,CAAAiC,oBAAA,OAAI,CAACtE,MAAM,CAACoE,OAAO,cAAAE,oBAAA,eAAnBA,oBAAA,CAAqB1D,MAAM,CAAC,CAAC;IAC7B,IAAI,CAACuB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACD,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B;EACJ;AACJ;AACA,SAAS+B,WAAWA,CAAC7E,IAAI,EAAEE,OAAO,EAAE;EAChC,OAAO,IAAIJ,WAAW,CAACE,IAAI,EAAEE,OAAO,CAAC;AACzC;AAEA,SAASJ,WAAW,EAAEH,mBAAmB,EAAEkF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}