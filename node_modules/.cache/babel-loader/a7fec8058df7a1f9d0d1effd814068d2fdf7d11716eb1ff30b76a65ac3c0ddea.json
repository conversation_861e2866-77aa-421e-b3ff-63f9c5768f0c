{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/components/ToolBar/ToolBar.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './toolBar.module.css';\n// icons\nimport icon_1 from '../../asset/imgs/icons/icon1-black.svg';\nimport icon_2 from '../../asset/imgs/icons/icon3-black.svg';\nimport icon_3 from '../../asset/imgs/icons/icon2-black.svg';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst btnListData = [{\n  id: 1,\n  title: 'Тест-драйв',\n  icon: icon_1,\n  url: '/book-a-test-drive'\n}, {\n  id: 2,\n  title: 'Дилерский центр',\n  icon: icon_2,\n  url: '/contact'\n}, {\n  id: 3,\n  title: 'Связаться с нами',\n  icon: icon_3,\n  url: '/contact'\n}];\nconst ToolBar = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: styles.section,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.content,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0427\\u0435\\u043C \\u043C\\u044B \\u043C\\u043E\\u0436\\u0435\\u043C \\u0432\\u0430\\u043C \\u043F\\u043E\\u043C\\u043E\\u0447\\u044C?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.btnsList,\n          children: btnListData.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.url,\n            className: styles.btnItem,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.item,\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.icon,\n                alt: item.title,\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = ToolBar;\nexport default ToolBar;\nvar _c;\n$RefreshReg$(_c, \"ToolBar\");", "map": {"version": 3, "names": ["React", "Link", "styles", "icon_1", "icon_2", "icon_3", "jsxDEV", "_jsxDEV", "btnListData", "id", "title", "icon", "url", "<PERSON><PERSON><PERSON><PERSON>", "className", "section", "children", "content", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "btnsList", "map", "item", "to", "btnItem", "src", "alt", "loading", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/components/ToolBar/ToolBar.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './toolBar.module.css';\n// icons\nimport icon_1 from '../../asset/imgs/icons/icon1-black.svg';\nimport icon_2 from '../../asset/imgs/icons/icon3-black.svg';\nimport icon_3 from '../../asset/imgs/icons/icon2-black.svg';\n\nconst btnListData = [\n  {\n    id: 1,\n    title: 'Тест-драйв',\n    icon: icon_1,\n    url: '/book-a-test-drive',\n  },\n\n  {\n    id: 2,\n    title: 'Дилерский центр',\n    icon: icon_2,\n    url: '/contact',\n  },\n  {\n    id: 3,\n    title: 'Связаться с нами',\n    icon: icon_3,\n    url: '/contact',\n  },\n];\n\nconst ToolBar = () => {\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.content}>\n          <h3>Чем мы можем вам помочь?</h3>\n          {/* Кнопки */}\n          <div className={styles.btnsList}>\n            {btnListData.map((item) => (\n              <Link to={item.url} key={item.id} className={styles.btnItem}>\n                <div className={styles.item}>\n                  <img src={item.icon} alt={item.title} loading=\"lazy\" />\n                  <span>{item.title}</span>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ToolBar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC;AACA,OAAOC,MAAM,MAAM,wCAAwC;AAC3D,OAAOC,MAAM,MAAM,wCAAwC;AAC3D,OAAOC,MAAM,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,WAAW,GAAG,CAClB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAER,MAAM;EACZS,GAAG,EAAE;AACP,CAAC,EAED;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAEP,MAAM;EACZQ,GAAG,EAAE;AACP,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAEN,MAAM;EACZO,GAAG,EAAE;AACP,CAAC,CACF;AAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACEN,OAAA;IAASO,SAAS,EAAEZ,MAAM,CAACa,OAAQ;IAAAC,QAAA,eACjCT,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAE,QAAA,eACxBT,OAAA;QAAKO,SAAS,EAAEZ,MAAM,CAACe,OAAQ;QAAAD,QAAA,gBAC7BT,OAAA;UAAAS,QAAA,EAAI;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEjCd,OAAA;UAAKO,SAAS,EAAEZ,MAAM,CAACoB,QAAS;UAAAN,QAAA,EAC7BR,WAAW,CAACe,GAAG,CAAEC,IAAI,iBACpBjB,OAAA,CAACN,IAAI;YAACwB,EAAE,EAAED,IAAI,CAACZ,GAAI;YAAeE,SAAS,EAAEZ,MAAM,CAACwB,OAAQ;YAAAV,QAAA,eAC1DT,OAAA;cAAKO,SAAS,EAAEZ,MAAM,CAACsB,IAAK;cAAAR,QAAA,gBAC1BT,OAAA;gBAAKoB,GAAG,EAAEH,IAAI,CAACb,IAAK;gBAACiB,GAAG,EAAEJ,IAAI,CAACd,KAAM;gBAACmB,OAAO,EAAC;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDd,OAAA;gBAAAS,QAAA,EAAOQ,IAAI,CAACd;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC,GAJiBG,IAAI,CAACf,EAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAK1B,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,EAAA,GArBIjB,OAAO;AAuBb,eAAeA,OAAO;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}