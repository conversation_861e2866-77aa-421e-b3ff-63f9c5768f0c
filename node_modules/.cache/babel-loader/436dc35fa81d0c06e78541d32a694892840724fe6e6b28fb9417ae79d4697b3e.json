{"ast": null, "code": "import { frame, cancelFrame } from 'motion-dom';\nimport { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nfunction useAnimationFrame(callback) {\n  const initialTimestamp = useRef(0);\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  useEffect(() => {\n    if (isStatic) return;\n    const provideTimeSinceStart = ({\n      timestamp,\n      delta\n    }) => {\n      if (!initialTimestamp.current) initialTimestamp.current = timestamp;\n      callback(timestamp - initialTimestamp.current, delta);\n    };\n    frame.update(provideTimeSinceStart, true);\n    return () => cancelFrame(provideTimeSinceStart);\n  }, [callback]);\n}\nexport { useAnimationFrame };", "map": {"version": 3, "names": ["frame", "cancelFrame", "useRef", "useContext", "useEffect", "MotionConfigContext", "useAnimationFrame", "callback", "initialTimestamp", "isStatic", "provideTimeSinceStart", "timestamp", "delta", "current", "update"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/use-animation-frame.mjs"], "sourcesContent": ["import { frame, cancelFrame } from 'motion-dom';\nimport { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\n\nfunction useAnimationFrame(callback) {\n    const initialTimestamp = useRef(0);\n    const { isStatic } = useContext(MotionConfigContext);\n    useEffect(() => {\n        if (isStatic)\n            return;\n        const provideTimeSinceStart = ({ timestamp, delta }) => {\n            if (!initialTimestamp.current)\n                initialTimestamp.current = timestamp;\n            callback(timestamp - initialTimestamp.current, delta);\n        };\n        frame.update(provideTimeSinceStart, true);\n        return () => cancelFrame(provideTimeSinceStart);\n    }, [callback]);\n}\n\nexport { useAnimationFrame };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,YAAY;AAC/C,SAASC,MAAM,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,gBAAgB,GAAGN,MAAM,CAAC,CAAC,CAAC;EAClC,MAAM;IAAEO;EAAS,CAAC,GAAGN,UAAU,CAACE,mBAAmB,CAAC;EACpDD,SAAS,CAAC,MAAM;IACZ,IAAIK,QAAQ,EACR;IACJ,MAAMC,qBAAqB,GAAGA,CAAC;MAAEC,SAAS;MAAEC;IAAM,CAAC,KAAK;MACpD,IAAI,CAACJ,gBAAgB,CAACK,OAAO,EACzBL,gBAAgB,CAACK,OAAO,GAAGF,SAAS;MACxCJ,QAAQ,CAACI,SAAS,GAAGH,gBAAgB,CAACK,OAAO,EAAED,KAAK,CAAC;IACzD,CAAC;IACDZ,KAAK,CAACc,MAAM,CAACJ,qBAAqB,EAAE,IAAI,CAAC;IACzC,OAAO,MAAMT,WAAW,CAACS,qBAAqB,CAAC;EACnD,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;AAClB;AAEA,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}