{"ast": null, "code": "import React,{useEffect,useState}from'react';import Sidebar from'../../components/sidebar/Sidebar';import img from'../../../../asset/imgs/owners/962_OWNERS_S3.webp';import{Link}from'react-router-dom';import AOS from'aos';import'aos/dist/aos.css';import styles from'../../owners.module.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Service=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0421\\u0415\\u0420\\u0412\\u0418\\u0421 \\u0418 \\u041E\\u0411\\u0421\\u041B\\u0423\\u0416\\u0418\\u0412\\u0410\\u041D\\u0418\\u0415\"})}),/*#__PURE__*/_jsx(\"span\",{className:styles.underText,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:\"\\u041F\\u043E\\u0437\\u0430\\u0431\\u043E\\u0442\\u044C\\u0442\\u0435\\u0441\\u044C \\u043E \\u0441\\u0432\\u043E\\u0435\\u043C \\u0434\\u0440\\u0430\\u0433\\u043E\\u0446\\u0435\\u043D\\u043D\\u043E\\u043C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435, \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0438\\u0432\\u0430\\u044F \\u0435\\u0433\\u043E \\u0441\\u0432\\u043E\\u0435\\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u043D\\u043E\\u0435 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0438 \\u0440\\u0435\\u043C\\u043E\\u043D\\u0442 \\u0441 \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435\\u043C \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0435\\u0439 GWM \\u0438 \\u0443 \\u0434\\u0438\\u043B\\u0435\\u0440\\u0430 GWM.\"}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:styles.redLine}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[/*#__PURE__*/_jsx(\"img\",{src:img,alt:\"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",className:styles.banner}),/*#__PURE__*/_jsx(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"300\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u0412\\u0441\\u0435 \\u043D\\u0430\\u0448\\u0438 \\u0434\\u0438\\u043B\\u0435\\u0440\\u044B GWM \\u0438\\u043C\\u0435\\u044E\\u0442 \\u043E\\u0431\\u0443\\u0447\\u0435\\u043D\\u043D\\u044B\\u0445 \\u0438 \\u043E\\u043F\\u044B\\u0442\\u043D\\u044B\\u0445 \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0441\\u0442\\u043E\\u0432, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u0437\\u043D\\u0430\\u044E\\u0442 \\u0432\\u0430\\u0448 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u043B\\u0443\\u0447\\u0448\\u0435 \\u0432\\u0441\\u0435\\u0445 \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0442 \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u044C\\u043D\\u044B\\u043C \\u043F\\u0440\\u043E\\u0446\\u0435\\u0434\\u0443\\u0440\\u0430\\u043C \\u0438 \\u0433\\u0440\\u0430\\u0444\\u0438\\u043A\\u0430\\u043C \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0438\\u0441\\u043F\\u043E\\u043B\\u044C\\u0437\\u0443\\u044F \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u043D\\u044B\\u0435 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u044B \\u0438 \\u0434\\u0438\\u0430\\u0433\\u043D\\u043E\\u0441\\u0442\\u0438\\u0447\\u0435\\u0441\\u043A\\u043E\\u0435 \\u043E\\u0431\\u043E\\u0440\\u0443\\u0434\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043E\\u0442 GWM.\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"400\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u0412\\u043E \\u0432\\u0440\\u0435\\u043C\\u044F \\u043F\\u043B\\u0430\\u043D\\u043E\\u0432\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0440\\u0435\\u043C\\u043E\\u043D\\u0442\\u0430 \\u0432\\u0430\\u0448 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u043F\\u0440\\u043E\\u0432\\u0435\\u0440\\u044F\\u0435\\u0442\\u0441\\u044F, \\u0430 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u044B \\u0437\\u0430\\u043C\\u0435\\u043D\\u044F\\u044E\\u0442\\u0441\\u044F \\u0438\\u043B\\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u044E\\u0442\\u0441\\u044F (\\u043D\\u0430\\u043F\\u0440\\u0438\\u043C\\u0435\\u0440, \\u0432\\u0441\\u0435 \\u0444\\u0438\\u043B\\u044C\\u0442\\u0440\\u044B, \\u043C\\u0430\\u0441\\u043B\\u043E, \\u0441\\u0432\\u0435\\u0447\\u0438 \\u0437\\u0430\\u0436\\u0438\\u0433\\u0430\\u043D\\u0438\\u044F, \\u043E\\u0445\\u043B\\u0430\\u0436\\u0434\\u0430\\u044E\\u0449\\u0430\\u044F \\u0436\\u0438\\u0434\\u043A\\u043E\\u0441\\u0442\\u044C) \\u0432 \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u044C\\u043D\\u043E\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F \\u0438 \\u043D\\u0430 \\u043D\\u0443\\u0436\\u043D\\u043E\\u043C \\u043F\\u0440\\u043E\\u0431\\u0435\\u0433\\u0435, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u0442\\u044C, \\u0447\\u0442\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u043F\\u0440\\u043E\\u0434\\u043E\\u043B\\u0436\\u0438\\u0442 \\u044D\\u043A\\u043E\\u043D\\u043E\\u043C\\u0438\\u0447\\u043D\\u043E \\u0441\\u043B\\u0443\\u0436\\u0438\\u0442\\u044C \\u0432\\u0430\\u043C \\u0441 \\u043D\\u0430\\u0434\\u0435\\u0436\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E \\u0438 \\u0434\\u043E\\u043B\\u0433\\u043E\\u0432\\u0435\\u0447\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u043E\\u0432. \\u041A\\u0440\\u043E\\u043C\\u0435 \\u0442\\u043E\\u0433\\u043E, \\u043E\\u0442\\u0441\\u0443\\u0442\\u0441\\u0442\\u0432\\u0438\\u0435 \\u0438\\u043B\\u0438 \\u0437\\u0430\\u0434\\u0435\\u0440\\u0436\\u043A\\u0430 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u043F\\u0440\\u0438\\u0432\\u043E\\u0434\\u0438\\u0442 \\u043A \\u043F\\u0440\\u0435\\u0436\\u0434\\u0435\\u0432\\u0440\\u0435\\u043C\\u0435\\u043D\\u043D\\u043E\\u043C\\u0443 \\u0438\\u0437\\u043D\\u043E\\u0441\\u0443 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u043E\\u0432 \\u0438 \\u0434\\u043E\\u043B\\u0433\\u043E\\u0441\\u0440\\u043E\\u0447\\u043D\\u043E\\u043C\\u0443 \\u043F\\u043E\\u0432\\u0440\\u0435\\u0436\\u0434\\u0435\\u043D\\u0438\\u044E. \\u041A\\u0440\\u043E\\u043C\\u0435 \\u0442\\u043E\\u0433\\u043E, \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u043C\\u043E\\u0436\\u0435\\u0442 \\u0431\\u044B\\u0442\\u044C \\u043E\\u0442\\u043E\\u0437\\u0432\\u0430\\u043D \\u0438\\u0437-\\u0437\\u0430 \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u043E\\u0441\\u0442\\u0438, \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u0439 \\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043D\\u044B\\u0445 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445, \\u0442\\u0430\\u043A\\u0438\\u0445 \\u043A\\u0430\\u043A \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u043F\\u0440\\u043E\\u0433\\u0440\\u0430\\u043C\\u043C\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0435\\u043D\\u0438\\u044F, \\u0438 \\u0435\\u0441\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u043F\\u0440\\u0435\\u0434\\u0441\\u0442\\u0430\\u0432\\u043B\\u0435\\u043D \\u0432\\u043E\\u0432\\u0440\\u0435\\u043C\\u044F \\u0434\\u043B\\u044F \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u043E\\u043D \\u043C\\u043E\\u0436\\u0435\\u0442 \\u0432\\u044B\\u0439\\u0442\\u0438 \\u0438\\u0437 \\u0441\\u0442\\u0440\\u043E\\u044F \\u0438\\u043B\\u0438 \\u043D\\u0430\\u0447\\u0430\\u0442\\u044C \\u0438\\u043C\\u0435\\u0442\\u044C \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u044B \\u0441 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0435\\u043C\\u043E\\u0441\\u0442\\u044C\\u044E. \\u041A\\u043E\\u0441\\u0432\\u0435\\u043D\\u043D\\u043E \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043B\\u044E\\u0431\\u043E\\u0439 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0439 \\u043D\\u0435 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u0435\\u0442\\u0441\\u044F \\u0438\\u043B\\u0438 \\u043D\\u0435 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u0435\\u0442\\u0441\\u044F \\u0432\\u043E\\u0432\\u0440\\u0435\\u043C\\u044F, \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u0442\\u044C \\u0432 \\u043E\\u043F\\u0442\\u0438\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0440\\u0435\\u0436\\u0438\\u043C\\u0435 \\u0438 \\u0441\\u0442\\u0430\\u043D\\u0435\\u0442 \\u043D\\u0435\\u044D\\u043A\\u043E\\u043D\\u043E\\u043C\\u0438\\u0447\\u043D\\u044B\\u043C.\"}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u043E\\u0431 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0438 \\u0438 \\u0440\\u0435\\u043C\\u043E\\u043D\\u0442\\u0435 GWM, \\u043F\\u043E\\u0436\\u0430\\u043B\\u0443\\u0439\\u0441\\u0442\\u0430, \\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0432\\u0430\\u0448\\u0438\\u043C \\u043C\\u0435\\u0441\\u0442\\u043D\\u044B\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u043E\\u043C GWM \\u0441\\u0435\\u0433\\u043E\\u0434\\u043D\\u044F \\u0438 \\u043E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441\",' ',/*#__PURE__*/_jsx(Link,{to:\"/owners/vehicle-reference-table\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u0435\\u0439 \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \"})}),\"\\u043F\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0434\\u043B\\u044F \\u043A\\u043E\\u043D\\u043A\\u0440\\u0435\\u0442\\u043D\\u044B\\u0445 \\u0438\\u043D\\u0442\\u0435\\u0440\\u0432\\u0430\\u043B\\u043E\\u0432 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F.\"]})]})]})})]})})})});};export default Service;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "Link", "AOS", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Service", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "underText", "redLine", "flexContent", "src", "alt", "banner", "textContent", "to"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Service/Service.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/962_OWNERS_S3.webp';\nimport { Link } from 'react-router-dom';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nimport styles from '../../owners.module.css';\n\nconst Service = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false,\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>СЕРВИС И ОБСЛУЖИВАНИЕ</strong>\n                  </h1>\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Позаботьтесь о своем драгоценном автомобиле, всегда\n                    обеспечивая его своевременное обслуживание и ремонт с\n                    использованием оригинальных деталей GWM и у дилера GWM.\n                  </span>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n                  <div\n                    className={styles.flexContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  >\n                    <img\n                      src={img}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <div\n                      className={styles.textContent}\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"300\"\n                    >\n                      <p>\n                        Все наши дилеры GWM имеют обученных и опытных\n                        специалистов, которые знают ваш автомобиль лучше всех и\n                        всегда следуют правильным процедурам и графикам\n                        обслуживания, используя специализированные инструменты и\n                        диагностическое оборудование от GWM.\n                      </p>\n                    </div>\n                  </div>\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"400\"\n                  >\n                    <p>\n                      Во время планового обслуживания и ремонта ваш автомобиль\n                      проверяется, а компоненты заменяются или обслуживаются\n                      (например, все фильтры, масло, свечи зажигания,\n                      охлаждающая жидкость) в правильное время и на нужном\n                      пробеге, чтобы гарантировать, что автомобиль продолжит\n                      экономично служить вам с надежностью и долговечностью\n                      компонентов. Кроме того, отсутствие или задержка\n                      обслуживания приводит к преждевременному износу\n                      компонентов и долгосрочному повреждению. Кроме того,\n                      автомобиль может быть отозван из-за безопасности,\n                      обновлений сервисных продуктов или других, таких как\n                      обновления программного обеспечения, и если автомобиль не\n                      будет представлен вовремя для обслуживания, он может выйти\n                      из строя или начать иметь проблемы с управляемостью.\n                      Косвенно также любой автомобиль, который не обслуживается\n                      или не обслуживается вовремя, не будет работать в\n                      оптимальном режиме и станет неэкономичным.\n                    </p>\n                    <p>\n                      Для получения дополнительной информации об обслуживании и\n                      ремонте GWM, пожалуйста, свяжитесь с вашим местным дилером\n                      GWM сегодня и ознакомьтесь с{' '}\n                      <Link to=\"/owners/vehicle-reference-table\">\n                        <strong>таблицей справочных данных </strong>\n                      </Link>\n                      по автомобилям для конкретных интервалов обслуживания\n                      модели автомобиля.\n                    </p>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Service;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,GAAG,KAAM,kDAAkD,CAClE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAEzB,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7C,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdK,GAAG,CAACW,IAAI,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,KACR,CAAC,CAAC,CAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAEvB,MAAM,CAACwB,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACN,OAAO,GAAE,CAAC,cACXM,IAAA,SAAMqB,SAAS,CAAEvB,MAAM,CAACyB,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAEvB,MAAM,CAAC0B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACpBpB,IAAA,WAAAoB,QAAA,CAAQ,sHAAqB,CAAQ,CAAC,CACpC,CAAC,cACLpB,IAAA,SACEqB,SAAS,CAAEvB,MAAM,CAAC2B,SAAU,CAC5B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAL,QAAA,CACrB,4zBAID,CAAM,CAAC,cACPpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAEvB,MAAM,CAAC4B,OAAQ,CACxB,CAAC,cACLxB,KAAA,QACEmB,SAAS,CAAEvB,MAAM,CAAC6B,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAP,QAAA,eAEpBpB,IAAA,QACE4B,GAAG,CAAEjC,GAAI,CACTkC,GAAG,CAAC,uGAAuB,CAC3BR,SAAS,CAAEvB,MAAM,CAACgC,MAAO,CAC1B,CAAC,cACF9B,IAAA,QACEqB,SAAS,CAAEvB,MAAM,CAACiC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAX,QAAA,cAEpBpB,IAAA,MAAAoB,QAAA,CAAG,ivCAMH,CAAG,CAAC,CACD,CAAC,EACH,CAAC,cACNlB,KAAA,QACEmB,SAAS,CAAEvB,MAAM,CAACiC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAX,QAAA,eAEpBpB,IAAA,MAAAoB,QAAA,CAAG,ylJAkBH,CAAG,CAAC,cACJlB,KAAA,MAAAkB,QAAA,EAAG,iuBAG2B,CAAC,GAAG,cAChCpB,IAAA,CAACJ,IAAI,EAACoC,EAAE,CAAC,iCAAiC,CAAAZ,QAAA,cACxCpB,IAAA,WAAAoB,QAAA,CAAQ,qJAA2B,CAAQ,CAAC,CACxC,CAAC,2YAGT,EAAG,CAAC,EACD,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAf,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}