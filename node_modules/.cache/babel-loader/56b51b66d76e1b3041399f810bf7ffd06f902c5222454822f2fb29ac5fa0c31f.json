{"ast": null, "code": "import React from'react';import styles from'./ErrorBoundary.module.css';/**\n * Error Boundary component to catch JavaScript errors anywhere in the child component tree\n * @class ErrorBoundary\n * @extends {React.Component}\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";class ErrorBoundary extends React.Component{constructor(props){super(props);/**\n   * Handle retry button click - reset error state\n   */this.handleRetry=()=>{this.setState({hasError:false,error:null,errorInfo:null,errorId:null});};/**\n   * Handle page reload\n   */this.handleReload=()=>{window.location.reload();};this.state={hasError:false,error:null,errorInfo:null,errorId:null};}/**\n   * Update state so the next render will show the fallback UI\n   * @param {Error} error - The error that was thrown\n   * @returns {Object} New state object\n   */static getDerivedStateFromError(error){return{hasError:true};}/**\n   * Log error details and update state with error information\n   * @param {Error} error - The error that was thrown\n   * @param {Object} errorInfo - Information about which component threw the error\n   */componentDidCatch(error,errorInfo){// Generate unique error ID for tracking\nconst errorId=\"error_\".concat(Date.now(),\"_\").concat(Math.random().toString(36).substr(2,9));// Log error details\nconsole.error('Error caught by boundary:',{errorId,error:error.message,stack:error.stack,componentStack:errorInfo.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href});// In production, you might want to send this to an error reporting service\nif(process.env.NODE_ENV==='production'){// Example: Send to error tracking service\n// logErrorToService({\n//   errorId,\n//   message: error.message,\n//   stack: error.stack,\n//   componentStack: errorInfo.componentStack,\n//   timestamp: new Date().toISOString(),\n//   userAgent: navigator.userAgent,\n//   url: window.location.href,\n// });\n}this.setState({error:error,errorInfo:errorInfo,errorId:errorId});}render(){if(this.state.hasError){// Custom fallback UI\nreturn/*#__PURE__*/_jsx(\"div\",{className:styles.errorBoundary,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.errorContainer,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.errorIcon,children:/*#__PURE__*/_jsxs(\"svg\",{width:\"64\",height:\"64\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"8\",x2:\"12\",y2:\"12\",stroke:\"currentColor\",strokeWidth:\"2\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"12\",y1:\"16\",x2:\"12.01\",y2:\"16\",stroke:\"currentColor\",strokeWidth:\"2\"})]})}),/*#__PURE__*/_jsx(\"h1\",{className:styles.errorTitle,children:\"\\u0427\\u0442\\u043E-\\u0442\\u043E \\u043F\\u043E\\u0448\\u043B\\u043E \\u043D\\u0435 \\u0442\\u0430\\u043A\"}),/*#__PURE__*/_jsx(\"p\",{className:styles.errorMessage,children:\"\\u041F\\u0440\\u043E\\u0438\\u0437\\u043E\\u0448\\u043B\\u0430 \\u043D\\u0435\\u043F\\u0440\\u0435\\u0434\\u0432\\u0438\\u0434\\u0435\\u043D\\u043D\\u0430\\u044F \\u043E\\u0448\\u0438\\u0431\\u043A\\u0430. \\u041C\\u044B \\u0443\\u0436\\u0435 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u0435\\u043C \\u043D\\u0430\\u0434 \\u0435\\u0451 \\u0438\\u0441\\u043F\\u0440\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435\\u043C.\"}),this.state.errorId&&/*#__PURE__*/_jsxs(\"p\",{className:styles.errorId,children:[\"ID \\u043E\\u0448\\u0438\\u0431\\u043A\\u0438: \",/*#__PURE__*/_jsx(\"code\",{children:this.state.errorId})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.errorActions,children:[/*#__PURE__*/_jsx(\"button\",{onClick:this.handleRetry,className:\"\".concat(styles.button,\" \").concat(styles.buttonPrimary),type:\"button\",children:\"\\u041F\\u043E\\u043F\\u0440\\u043E\\u0431\\u043E\\u0432\\u0430\\u0442\\u044C \\u0441\\u043D\\u043E\\u0432\\u0430\"}),/*#__PURE__*/_jsx(\"button\",{onClick:this.handleReload,className:\"\".concat(styles.button,\" \").concat(styles.buttonSecondary),type:\"button\",children:\"\\u041E\\u0431\\u043D\\u043E\\u0432\\u0438\\u0442\\u044C \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0443\"})]}),process.env.NODE_ENV==='development'&&this.state.error&&/*#__PURE__*/_jsxs(\"details\",{className:styles.errorDetails,children:[/*#__PURE__*/_jsx(\"summary\",{className:styles.errorDetailsSummary,children:\"\\u0414\\u0435\\u0442\\u0430\\u043B\\u0438 \\u043E\\u0448\\u0438\\u0431\\u043A\\u0438 (\\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u0432 \\u0440\\u0435\\u0436\\u0438\\u043C\\u0435 \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0438)\"}),/*#__PURE__*/_jsxs(\"div\",{className:styles.errorDetailsContent,children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u041E\\u0448\\u0438\\u0431\\u043A\\u0430:\"}),/*#__PURE__*/_jsx(\"pre\",{className:styles.errorStack,children:this.state.error.message}),/*#__PURE__*/_jsx(\"h3\",{children:\"Stack trace:\"}),/*#__PURE__*/_jsx(\"pre\",{className:styles.errorStack,children:this.state.error.stack}),this.state.errorInfo&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Component stack:\"}),/*#__PURE__*/_jsx(\"pre\",{className:styles.errorStack,children:this.state.errorInfo.componentStack})]})]})]})]})});}return this.props.children;}}export default ErrorBoundary;", "map": {"version": 3, "names": ["React", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "handleRetry", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "errorId", "handleReload", "window", "location", "reload", "state", "getDerivedStateFromError", "componentDidCatch", "concat", "Date", "now", "Math", "random", "toString", "substr", "console", "message", "stack", "componentStack", "timestamp", "toISOString", "userAgent", "navigator", "url", "href", "process", "env", "NODE_ENV", "render", "className", "errorBoundary", "children", "<PERSON><PERSON><PERSON><PERSON>", "errorIcon", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "stroke", "strokeWidth", "x1", "y1", "x2", "y2", "errorTitle", "errorMessage", "errorActions", "onClick", "button", "buttonPrimary", "type", "buttonSecondary", "errorDetails", "errorDetailsSummary", "errorDetailsContent", "errorStack"], "sources": ["/var/www/html/gwm.tj/src/components/ErrorBoundary/ErrorBoundary.jsx"], "sourcesContent": ["import React from 'react';\nimport styles from './ErrorBoundary.module.css';\n\n/**\n * Error Boundary component to catch JavaScript errors anywhere in the child component tree\n * @class ErrorBoundary\n * @extends {React.Component}\n */\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { \n      hasError: false, \n      error: null, \n      errorInfo: null,\n      errorId: null \n    };\n  }\n\n  /**\n   * Update state so the next render will show the fallback UI\n   * @param {Error} error - The error that was thrown\n   * @returns {Object} New state object\n   */\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  /**\n   * Log error details and update state with error information\n   * @param {Error} error - The error that was thrown\n   * @param {Object} errorInfo - Information about which component threw the error\n   */\n  componentDidCatch(error, errorInfo) {\n    // Generate unique error ID for tracking\n    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Log error details\n    console.error('Error caught by boundary:', {\n      errorId,\n      error: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n    });\n    \n    // In production, you might want to send this to an error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      // Example: Send to error tracking service\n      // logErrorToService({\n      //   errorId,\n      //   message: error.message,\n      //   stack: error.stack,\n      //   componentStack: errorInfo.componentStack,\n      //   timestamp: new Date().toISOString(),\n      //   userAgent: navigator.userAgent,\n      //   url: window.location.href,\n      // });\n    }\n    \n    this.setState({\n      error: error,\n      errorInfo: errorInfo,\n      errorId: errorId,\n    });\n  }\n\n  /**\n   * Handle retry button click - reset error state\n   */\n  handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null,\n    });\n  };\n\n  /**\n   * Handle page reload\n   */\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      return (\n        <div className={styles.errorBoundary}>\n          <div className={styles.errorContainer}>\n            <div className={styles.errorIcon}>\n              <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n              </svg>\n            </div>\n            \n            <h1 className={styles.errorTitle}>\n              Что-то пошло не так\n            </h1>\n            \n            <p className={styles.errorMessage}>\n              Произошла непредвиденная ошибка. Мы уже работаем над её исправлением.\n            </p>\n            \n            {this.state.errorId && (\n              <p className={styles.errorId}>\n                ID ошибки: <code>{this.state.errorId}</code>\n              </p>\n            )}\n            \n            <div className={styles.errorActions}>\n              <button \n                onClick={this.handleRetry}\n                className={`${styles.button} ${styles.buttonPrimary}`}\n                type=\"button\"\n              >\n                Попробовать снова\n              </button>\n              \n              <button \n                onClick={this.handleReload}\n                className={`${styles.button} ${styles.buttonSecondary}`}\n                type=\"button\"\n              >\n                Обновить страницу\n              </button>\n            </div>\n            \n            {/* Show error details in development */}\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className={styles.errorDetails}>\n                <summary className={styles.errorDetailsSummary}>\n                  Детали ошибки (только в режиме разработки)\n                </summary>\n                <div className={styles.errorDetailsContent}>\n                  <h3>Ошибка:</h3>\n                  <pre className={styles.errorStack}>\n                    {this.state.error.message}\n                  </pre>\n                  \n                  <h3>Stack trace:</h3>\n                  <pre className={styles.errorStack}>\n                    {this.state.error.stack}\n                  </pre>\n                  \n                  {this.state.errorInfo && (\n                    <>\n                      <h3>Component stack:</h3>\n                      <pre className={styles.errorStack}>\n                        {this.state.errorInfo.componentStack}\n                      </pre>\n                    </>\n                  )}\n                </div>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAE/C;AACA;AACA;AACA;AACA,GAJA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAKA,KAAM,CAAAC,aAAa,QAAS,CAAAR,KAAK,CAACS,SAAU,CAC1CC,WAAWA,CAACC,KAAK,CAAE,CACjB,KAAK,CAACA,KAAK,CAAC,CA2Dd;AACF;AACA,KAFE,KAGAC,WAAW,CAAG,IAAM,CAClB,IAAI,CAACC,QAAQ,CAAC,CACZC,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IACX,CAAC,CAAC,CACJ,CAAC,CAED;AACF;AACA,KAFE,KAGAC,YAAY,CAAG,IAAM,CACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CAAC,CA3EC,IAAI,CAACC,KAAK,CAAG,CACXR,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IACX,CAAC,CACH,CAEA;AACF;AACA;AACA;AACA,KACE,MAAO,CAAAM,wBAAwBA,CAACR,KAAK,CAAE,CACrC,MAAO,CAAED,QAAQ,CAAE,IAAK,CAAC,CAC3B,CAEA;AACF;AACA;AACA;AACA,KACEU,iBAAiBA,CAACT,KAAK,CAAEC,SAAS,CAAE,CAClC;AACA,KAAM,CAAAC,OAAO,UAAAQ,MAAA,CAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAAF,MAAA,CAAIG,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAEhF;AACAC,OAAO,CAACjB,KAAK,CAAC,2BAA2B,CAAE,CACzCE,OAAO,CACPF,KAAK,CAAEA,KAAK,CAACkB,OAAO,CACpBC,KAAK,CAAEnB,KAAK,CAACmB,KAAK,CAClBC,cAAc,CAAEnB,SAAS,CAACmB,cAAc,CACxCC,SAAS,CAAE,GAAI,CAAAV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC,CAAC,CACnCC,SAAS,CAAEC,SAAS,CAACD,SAAS,CAC9BE,GAAG,CAAErB,MAAM,CAACC,QAAQ,CAACqB,IACvB,CAAC,CAAC,CAEF;AACA,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,YAAY,CAAE,CACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CAGF,IAAI,CAAC/B,QAAQ,CAAC,CACZE,KAAK,CAAEA,KAAK,CACZC,SAAS,CAAEA,SAAS,CACpBC,OAAO,CAAEA,OACX,CAAC,CAAC,CACJ,CAqBA4B,MAAMA,CAAA,CAAG,CACP,GAAI,IAAI,CAACvB,KAAK,CAACR,QAAQ,CAAE,CACvB;AACA,mBACEX,IAAA,QAAK2C,SAAS,CAAE7C,MAAM,CAAC8C,aAAc,CAAAC,QAAA,cACnC3C,KAAA,QAAKyC,SAAS,CAAE7C,MAAM,CAACgD,cAAe,CAAAD,QAAA,eACpC7C,IAAA,QAAK2C,SAAS,CAAE7C,MAAM,CAACiD,SAAU,CAAAF,QAAA,cAC/B3C,KAAA,QAAK8C,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAAP,QAAA,eAC5F7C,IAAA,WAAQqD,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,cACtEzD,IAAA,SAAM0D,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACL,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,cAC5EzD,IAAA,SAAM0D,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,OAAO,CAACC,EAAE,CAAC,IAAI,CAACL,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAAC,CAAC,EAC7E,CAAC,CACH,CAAC,cAENzD,IAAA,OAAI2C,SAAS,CAAE7C,MAAM,CAACgE,UAAW,CAAAjB,QAAA,CAAC,gGAElC,CAAI,CAAC,cAEL7C,IAAA,MAAG2C,SAAS,CAAE7C,MAAM,CAACiE,YAAa,CAAAlB,QAAA,CAAC,8WAEnC,CAAG,CAAC,CAEH,IAAI,CAAC1B,KAAK,CAACL,OAAO,eACjBZ,KAAA,MAAGyC,SAAS,CAAE7C,MAAM,CAACgB,OAAQ,CAAA+B,QAAA,EAAC,2CACjB,cAAA7C,IAAA,SAAA6C,QAAA,CAAO,IAAI,CAAC1B,KAAK,CAACL,OAAO,CAAO,CAAC,EAC3C,CACJ,cAEDZ,KAAA,QAAKyC,SAAS,CAAE7C,MAAM,CAACkE,YAAa,CAAAnB,QAAA,eAClC7C,IAAA,WACEiE,OAAO,CAAE,IAAI,CAACxD,WAAY,CAC1BkC,SAAS,IAAArB,MAAA,CAAKxB,MAAM,CAACoE,MAAM,MAAA5C,MAAA,CAAIxB,MAAM,CAACqE,aAAa,CAAG,CACtDC,IAAI,CAAC,QAAQ,CAAAvB,QAAA,CACd,mGAED,CAAQ,CAAC,cAET7C,IAAA,WACEiE,OAAO,CAAE,IAAI,CAAClD,YAAa,CAC3B4B,SAAS,IAAArB,MAAA,CAAKxB,MAAM,CAACoE,MAAM,MAAA5C,MAAA,CAAIxB,MAAM,CAACuE,eAAe,CAAG,CACxDD,IAAI,CAAC,QAAQ,CAAAvB,QAAA,CACd,mGAED,CAAQ,CAAC,EACN,CAAC,CAGLN,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAI,IAAI,CAACtB,KAAK,CAACP,KAAK,eACzDV,KAAA,YAASyC,SAAS,CAAE7C,MAAM,CAACwE,YAAa,CAAAzB,QAAA,eACtC7C,IAAA,YAAS2C,SAAS,CAAE7C,MAAM,CAACyE,mBAAoB,CAAA1B,QAAA,CAAC,2NAEhD,CAAS,CAAC,cACV3C,KAAA,QAAKyC,SAAS,CAAE7C,MAAM,CAAC0E,mBAAoB,CAAA3B,QAAA,eACzC7C,IAAA,OAAA6C,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChB7C,IAAA,QAAK2C,SAAS,CAAE7C,MAAM,CAAC2E,UAAW,CAAA5B,QAAA,CAC/B,IAAI,CAAC1B,KAAK,CAACP,KAAK,CAACkB,OAAO,CACtB,CAAC,cAEN9B,IAAA,OAAA6C,QAAA,CAAI,cAAY,CAAI,CAAC,cACrB7C,IAAA,QAAK2C,SAAS,CAAE7C,MAAM,CAAC2E,UAAW,CAAA5B,QAAA,CAC/B,IAAI,CAAC1B,KAAK,CAACP,KAAK,CAACmB,KAAK,CACpB,CAAC,CAEL,IAAI,CAACZ,KAAK,CAACN,SAAS,eACnBX,KAAA,CAAAE,SAAA,EAAAyC,QAAA,eACE7C,IAAA,OAAA6C,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB7C,IAAA,QAAK2C,SAAS,CAAE7C,MAAM,CAAC2E,UAAW,CAAA5B,QAAA,CAC/B,IAAI,CAAC1B,KAAK,CAACN,SAAS,CAACmB,cAAc,CACjC,CAAC,EACN,CACH,EACE,CAAC,EACC,CACV,EACE,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,KAAI,CAACxB,KAAK,CAACqC,QAAQ,CAC5B,CACF,CAEA,cAAe,CAAAxC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}