{"ast": null, "code": "const isBrowser = typeof window !== \"undefined\";\nexport { isBrowser };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/is-browser.mjs"], "sourcesContent": ["const isBrowser = typeof window !== \"undefined\";\n\nexport { isBrowser };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;AAE/C,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}