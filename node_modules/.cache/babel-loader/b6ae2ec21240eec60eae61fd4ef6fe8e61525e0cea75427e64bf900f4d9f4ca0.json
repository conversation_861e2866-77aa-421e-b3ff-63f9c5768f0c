{"ast": null, "code": "import { g as getSlideTransformEl } from './utils.mjs';\nfunction effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\nexport { effectTarget as e };", "map": {"version": 3, "names": ["g", "getSlideTransformEl", "effect<PERSON>arget", "effectParams", "slideEl", "transformEl", "style", "backfaceVisibility", "e"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/shared/effect-target.mjs"], "sourcesContent": ["import { g as getSlideTransformEl } from './utils.mjs';\n\nfunction effectTarget(effectParams, slideEl) {\n  const transformEl = getSlideTransformEl(slideEl);\n  if (transformEl !== slideEl) {\n    transformEl.style.backfaceVisibility = 'hidden';\n    transformEl.style['-webkit-backface-visibility'] = 'hidden';\n  }\n  return transformEl;\n}\n\nexport { effectTarget as e };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,QAAQ,aAAa;AAEtD,SAASC,YAAYA,CAACC,YAAY,EAAEC,OAAO,EAAE;EAC3C,MAAMC,WAAW,GAAGJ,mBAAmB,CAACG,OAAO,CAAC;EAChD,IAAIC,WAAW,KAAKD,OAAO,EAAE;IAC3BC,WAAW,CAACC,KAAK,CAACC,kBAAkB,GAAG,QAAQ;IAC/CF,WAAW,CAACC,KAAK,CAAC,6BAA6B,CAAC,GAAG,QAAQ;EAC7D;EACA,OAAOD,WAAW;AACpB;AAEA,SAASH,YAAY,IAAIM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}