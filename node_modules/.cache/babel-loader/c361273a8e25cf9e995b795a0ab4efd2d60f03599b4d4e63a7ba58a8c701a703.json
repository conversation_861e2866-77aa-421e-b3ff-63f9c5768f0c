{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/Pages/Warranty/Warranty.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/2400_OWNERS_S1.webp';\nimport { Link } from 'react-router-dom';\nimport styles from '../../owners.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Warranty = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false // Анимация запускается только один раз\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.layout,\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: styles.main,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.mainContainer,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                \"data-aos\": \"fade-up\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u0413\\u0410\\u0420\\u0410\\u041D\\u0422\\u0418\\u042F \\u041E\\u0422 GWM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.underText,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"100\",\n                children: \"\\u0412\\u0441\\u0435 \\u043D\\u043E\\u0432\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 GWM \\u043F\\u043E\\u043A\\u0440\\u044B\\u0432\\u0430\\u044E\\u0442\\u0441\\u044F \\u0413\\u0410\\u0420\\u0410\\u041D\\u0422\\u0418\\u0415\\u0419 \\u041D\\u0410 \\u041D\\u041E\\u0412\\u042B\\u0419 \\u0410\\u0412\\u0422\\u041E\\u041C\\u041E\\u0411\\u0418\\u041B\\u042C \\u0434\\u043B\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"150\",\n                className: styles.redLine\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img,\n                alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                className: styles.banner,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.textContent,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"300\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u0435\\u0439 \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u043F\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0434\\u043B\\u044F \\u043A\\u043E\\u043D\\u043A\\u0440\\u0435\\u0442\\u043D\\u043E\\u0439 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u043E\\u0433\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u043E\\u0433\\u043E \\u043F\\u043E\\u043A\\u0440\\u044B\\u0442\\u0438\\u044F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u041E\\u0437\\u043D\\u0430\\u043A\\u043E\\u043C\\u044C\\u0442\\u0435\\u0441\\u044C \\u0441\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/owners/vehicle-reference-table\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0442\\u0430\\u0431\\u043B\\u0438\\u0446\\u0435\\u0439 \\u0441\\u043F\\u0440\\u0430\\u0432\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 23\n                  }, this), \"\\u043F\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0434\\u043B\\u044F \\u043A\\u043E\\u043D\\u043A\\u0440\\u0435\\u0442\\u043D\\u043E\\u0439 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u043E\\u0433\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0439\\u043D\\u043E\\u0433\\u043E \\u043F\\u043E\\u043A\\u0440\\u044B\\u0442\\u0438\\u044F.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0412\\u0430\\u0448\\u0430 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u044F GWM \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0438\\u0432\\u0430\\u0435\\u0442 \\u0432\\u0430\\u043C \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u0435 \\u0438 \\u0437\\u0430\\u0449\\u0438\\u0449\\u0430\\u0435\\u0442 \\u043E\\u0442 \\u043B\\u044E\\u0431\\u044B\\u0445 \\u043D\\u0435\\u043F\\u0440\\u0435\\u0434\\u0432\\u0438\\u0434\\u0435\\u043D\\u043D\\u044B\\u0445 \\u0440\\u0430\\u0441\\u0445\\u043E\\u0434\\u043E\\u0432 \\u0438\\u043B\\u0438 \\u0441\\u0431\\u043E\\u0435\\u0432 \\u0438\\u0437-\\u0437\\u0430 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u044B\\u0445 \\u0434\\u0435\\u0444\\u0435\\u043A\\u0442\\u043E\\u0432, \\u0434\\u0435\\u0444\\u0435\\u043A\\u0442\\u043D\\u044B\\u0445 \\u043C\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043B\\u043E\\u0432 \\u0438 \\u0438\\u0437\\u0433\\u043E\\u0442\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044F \\u043F\\u0440\\u0438 \\u043D\\u043E\\u0440\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u044D\\u043A\\u0441\\u043F\\u043B\\u0443\\u0430\\u0442\\u0430\\u0446\\u0438\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0438, \\u0437\\u0430 \\u0438\\u0441\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435\\u043C \\u043B\\u044E\\u0431\\u043E\\u0433\\u043E \\u0438\\u0437\\u043D\\u043E\\u0441\\u0430, \\u043F\\u043E\\u0432\\u0440\\u0435\\u0436\\u0434\\u0435\\u043D\\u0438\\u0439 \\u0432 \\u0440\\u0435\\u0437\\u0443\\u043B\\u044C\\u0442\\u0430\\u0442\\u0435 \\u043D\\u0435\\u0441\\u0447\\u0430\\u0441\\u0442\\u043D\\u044B\\u0445 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0435\\u0432 \\u0438\\u043B\\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u043C. \\u041F\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438 \\u043B\\u044E\\u0431\\u043E\\u0439 \\u043F\\u043E\\u0434\\u0442\\u0432\\u0435\\u0440\\u0436\\u0434\\u0435\\u043D\\u043D\\u044B\\u0439 \\u043D\\u0435\\u043E\\u0431\\u0445\\u043E\\u0434\\u0438\\u043C\\u044B\\u0439 \\u0440\\u0435\\u043C\\u043E\\u043D\\u0442 \\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u044F\\u0435\\u0442\\u0441\\u044F \\u0431\\u0435\\u0441\\u043F\\u043B\\u0430\\u0442\\u043D\\u043E \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0438\\u043C\\u044B\\u0445 \\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u0439, \\u043F\\u043E\\u0436\\u0430\\u043B\\u0443\\u0439\\u0441\\u0442\\u0430, \\u043E\\u0431\\u0440\\u0430\\u0442\\u0438\\u0442\\u0435\\u0441\\u044C \\u043A \\u0431\\u0443\\u043A\\u043B\\u0435\\u0442\\u0443 \\xAB\\u0420\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E \\u043F\\u043E \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E\\xBB,\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/contact\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043D\\u0430\\u043C\\u0438\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this), \".\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Warranty, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Warranty;\nexport default Warranty;\nvar _c;\n$RefreshReg$(_c, \"Warranty\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "Link", "styles", "AOS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Warranty", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "main", "mainContainer", "underText", "redLine", "src", "alt", "banner", "textContent", "to", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Warranty/Warranty.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/2400_OWNERS_S1.webp';\nimport { Link } from 'react-router-dom';\nimport styles from '../../owners.module.css';\n\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Warranty = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false, // Анимация запускается только один раз\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ГАРАНТИЯ ОТ GWM</strong>\n                  </h1>\n\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Все новые автомобили GWM покрываются ГАРАНТИЕЙ НА НОВЫЙ\n                    АВТОМОБИЛЬ для вашего спокойствия.\n                  </span>\n\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <img\n                    src={img}\n                    alt=\"Баннер владельцев GWM\"\n                    className={styles.banner}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  />\n\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"300\"\n                  >\n                    <p>\n                      Ознакомьтесь с таблицей справочных данных по автомобилям\n                      для конкретной модели автомобиля и применимого\n                      гарантийного покрытия.\n                    </p>\n                    <p>\n                      Ознакомьтесь с{' '}\n                      <Link to=\"/owners/vehicle-reference-table\">\n                        <strong>таблицей справочных данных </strong>\n                      </Link>\n                      по автомобилям для конкретной модели автомобиля и\n                      применимого гарантийного покрытия.\n                    </p>\n                    <p>\n                      Ваша гарантия GWM обеспечивает вам спокойствие и защищает\n                      от любых непредвиденных расходов или сбоев из-за\n                      производственных дефектов, дефектных материалов и\n                      изготовления при нормальной эксплуатации и обслуживании,\n                      за исключением любого износа, повреждений в результате\n                      несчастных случаев или обслуживания владельцем. По\n                      гарантии любой подтвержденный необходимый ремонт\n                      выполняется бесплатно для вас.\n                    </p>\n                    <p>\n                      Для получения дополнительной информации и применимых\n                      положений и условий, пожалуйста, обратитесь к буклету\n                      «Руководство по гарантии и обслуживанию»,{' '}\n                      <Link to=\"/contact\">\n                        <strong>свяжитесь с нами</strong>\n                      </Link>\n                      .\n                    </p>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Warranty;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,GAAG,MAAM,mDAAmD;AACnE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdM,GAAG,CAACS,IAAI,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,KAAK,CAAE;IACf,CAAC,CAAC;IAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBnB,OAAA;UAAKoB,SAAS,EAAEvB,MAAM,CAAC4B,MAAO;UAAAN,QAAA,gBAC5BnB,OAAA,CAACN,OAAO;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXxB,OAAA;YAAMoB,SAAS,EAAEvB,MAAM,CAAC6B,IAAK;YAAAP,QAAA,eAC3BnB,OAAA;cAAKoB,SAAS,EAAEvB,MAAM,CAAC8B,aAAc;cAAAR,QAAA,gBACnCnB,OAAA;gBAAI,YAAS,SAAS;gBAAAmB,QAAA,eACpBnB,OAAA;kBAAAmB,QAAA,EAAQ;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAELxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAAC+B,SAAU;gBAC5B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAT,QAAA,EACrB;cAGD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAEPxB,OAAA;gBACE,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBACpBoB,SAAS,EAAEvB,MAAM,CAACgC;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAELxB,OAAA;gBACE8B,GAAG,EAAEnC,GAAI;gBACToC,GAAG,EAAC,uGAAuB;gBAC3BX,SAAS,EAAEvB,MAAM,CAACmC,MAAO;gBACzB,YAAS,SAAS;gBAClB,kBAAe;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAEFxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAACoC,WAAY;gBAC9B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAd,QAAA,gBAEpBnB,OAAA;kBAAAmB,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAAmB,QAAA,GAAG,iFACa,EAAC,GAAG,eAClBnB,OAAA,CAACJ,IAAI;oBAACsC,EAAE,EAAC,iCAAiC;oBAAAf,QAAA,eACxCnB,OAAA;sBAAAmB,QAAA,EAAQ;oBAA2B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,0cAGT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAAmB,QAAA,EAAG;gBASH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAAmB,QAAA,GAAG,kxBAGwC,EAAC,GAAG,eAC7CnB,OAAA,CAACJ,IAAI;oBAACsC,EAAE,EAAC,UAAU;oBAAAf,QAAA,eACjBnB,OAAA;sBAAAmB,QAAA,EAAQ;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,KAET;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CA7GID,QAAQ;AAAAgC,EAAA,GAARhC,QAAQ;AA+Gd,eAAeA,QAAQ;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}