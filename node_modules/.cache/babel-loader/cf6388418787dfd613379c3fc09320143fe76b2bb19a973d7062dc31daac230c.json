{"ast": null, "code": "import { GroupAnimationWithThen } from 'motion-dom';\nimport { animateElements } from './animate-elements.mjs';\nconst createScopedWaapiAnimate = scope => {\n  function scopedAnimate(elementOrSelector, keyframes, options) {\n    return new GroupAnimationWithThen(animateElements(elementOrSelector, keyframes, options, scope));\n  }\n  return scopedAnimate;\n};\nconst animateMini = /*@__PURE__*/createScopedWaapiAnimate();\nexport { animateMini, createScopedWaapiAnimate };", "map": {"version": 3, "names": ["GroupAnimationWithThen", "animateElements", "createScopedWaapiAnimate", "scope", "scopedAnimate", "elementOrSelector", "keyframes", "options", "animateMini"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/animators/waapi/animate-style.mjs"], "sourcesContent": ["import { GroupAnimationWithThen } from 'motion-dom';\nimport { animateElements } from './animate-elements.mjs';\n\nconst createScopedWaapiAnimate = (scope) => {\n    function scopedAnimate(elementOrSelector, keyframes, options) {\n        return new GroupAnimationWithThen(animateElements(elementOrSelector, keyframes, options, scope));\n    }\n    return scopedAnimate;\n};\nconst animateMini = /*@__PURE__*/ createScopedWaapiAnimate();\n\nexport { animateMini, createScopedWaapiAnimate };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,YAAY;AACnD,SAASC,eAAe,QAAQ,wBAAwB;AAExD,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;EACxC,SAASC,aAAaA,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1D,OAAO,IAAIP,sBAAsB,CAACC,eAAe,CAACI,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEJ,KAAK,CAAC,CAAC;EACpG;EACA,OAAOC,aAAa;AACxB,CAAC;AACD,MAAMI,WAAW,GAAG,aAAcN,wBAAwB,CAAC,CAAC;AAE5D,SAASM,WAAW,EAAEN,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}