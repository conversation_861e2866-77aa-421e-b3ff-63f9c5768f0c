{"ast": null, "code": "import React,{useState,useCallback,useRef}from'react';import styles from'./form.module.css';import{useMask}from'@react-input/mask';import Notification from'../../../components/Notification/Notification';import{formRateLimiter,sanitizeAndValidateForm}from'../../../utils/validation';import{submitFeedback}from'../../../utils/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CallbackForm=_ref=>{let{formType='callback',title}=_ref;const[notification,setNotification]=useState({message:'',type:''});const[formErrors,setFormErrors]=useState({});const[isSubmitting,setIsSubmitting]=useState(false);const showNotification=useCallback(function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);},[]);const phoneRef=useMask({mask:'+992 ___-__-__-__',replacement:{_:/\\d/}});const formRef=useRef(null);const handleSubmit=async e=>{e.preventDefault();if(isSubmitting)return;if(!formRateLimiter.isAllowed()){showNotification('Слишком много попыток отправки. Подождите минуту.','error');return;}setIsSubmitting(true);setFormErrors({});const form=e.currentTarget;const formDataObj=new FormData(form);const data=Object.fromEntries(formDataObj.entries());data.consent=form.consent.checked;try{const{data:sanitizedData,errors,isValid}=sanitizeAndValidateForm(data);if(!isValid){setFormErrors(errors);showNotification('Пожалуйста, исправьте ошибки в форме','error');setIsSubmitting(false);return;}const payload={firstName:sanitizedData.firstName,lastName:sanitizedData.lastName,phone:sanitizedData.phone,topic:'',message:'Обратный звонок',consent:sanitizedData.consent,formType};await submitFeedback(payload);showNotification('Ваш запрос на обратный звонок отправлен!','success');form.reset();// сброс формы\nsetFormErrors({});}catch(error){showNotification(error.message,'error');}finally{setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),title&&/*#__PURE__*/_jsx(\"h2\",{className:styles.formTitle,children:title}),/*#__PURE__*/_jsxs(\"form\",{ref:formRef,onSubmit:handleSubmit,className:styles.form,noValidate:true,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.row,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"firstName\",className:styles.label,children:[\"\\u0418\\u043C\\u044F \",/*#__PURE__*/_jsx(\"span\",{className:styles.required,children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{id:\"firstName\",name:\"firstName\",type:\"text\",className:\"\".concat(styles.input,\" \").concat(formErrors.firstName?styles.inputError:''),required:true,minLength:2,maxLength:50,disabled:isSubmitting}),formErrors.firstName&&/*#__PURE__*/_jsx(\"div\",{className:styles.errorMessage,children:formErrors.firstName})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"lastName\",className:styles.label,children:\"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"input\",{id:\"lastName\",name:\"lastName\",type:\"text\",className:\"\".concat(styles.input,\" \").concat(formErrors.lastName?styles.inputError:''),minLength:2,maxLength:50,disabled:isSubmitting}),formErrors.lastName&&/*#__PURE__*/_jsx(\"div\",{className:styles.errorMessage,children:formErrors.lastName})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"phone\",className:styles.label,children:[\"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430 \",/*#__PURE__*/_jsx(\"span\",{className:styles.required,children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{id:\"phone\",name:\"phone\",type:\"tel\",placeholder:\"+992 XXX-XX-XX-XX\",className:\"\".concat(styles.input,\" \").concat(formErrors.phone?styles.inputError:''),required:true,ref:phoneRef,disabled:isSubmitting}),formErrors.phone&&/*#__PURE__*/_jsx(\"div\",{className:styles.errorMessage,children:formErrors.phone})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.checkboxGroup,children:/*#__PURE__*/_jsxs(\"label\",{className:styles.checkboxWrapper,children:[/*#__PURE__*/_jsx(\"input\",{id:\"consent\",name:\"consent\",type:\"checkbox\",required:true,className:styles.checkbox,disabled:isSubmitting}),/*#__PURE__*/_jsx(\"span\",{className:styles.checkboxLabel,children:\"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"})]})}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"\".concat(styles.button,\" \").concat(isSubmitting?styles.buttonLoading:''),disabled:isSubmitting,children:isSubmitting?'Отправка...':'Отправить'})]})]});};export default CallbackForm;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "styles", "useMask", "Notification", "formRateLimiter", "sanitizeAndValidateForm", "submitFeedback", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CallbackForm", "_ref", "formType", "title", "notification", "setNotification", "message", "type", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "arguments", "length", "undefined", "setTimeout", "phoneRef", "mask", "replacement", "_", "formRef", "handleSubmit", "e", "preventDefault", "isAllowed", "form", "currentTarget", "formDataObj", "FormData", "data", "Object", "fromEntries", "entries", "consent", "checked", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "firstName", "lastName", "phone", "topic", "reset", "error", "children", "className", "formTitle", "ref", "onSubmit", "noValidate", "row", "inputGroup", "htmlFor", "label", "required", "id", "name", "concat", "input", "inputError", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "disabled", "errorMessage", "placeholder", "checkboxGroup", "checkboxWrapper", "checkbox", "checkboxLabel", "button", "buttonLoading"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/Contact/ContactForm.jsx"], "sourcesContent": ["import React, { useState, useCallback, useRef } from 'react';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../components/Notification/Notification';\nimport {\n  formRateLimiter,\n  sanitizeAndValidateForm,\n} from '../../../utils/validation';\nimport { submitFeedback } from '../../../utils/api';\n\nconst CallbackForm = ({ formType = 'callback', title }) => {\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  const formRef = useRef(null);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (isSubmitting) return;\n\n    if (!formRateLimiter.isAllowed()) {\n      showNotification(\n        'Слишком много попыток отправки. Подождите минуту.',\n        'error'\n      );\n      return;\n    }\n\n    setIsSubmitting(true);\n    setFormErrors({});\n\n    const form = e.currentTarget;\n    const formDataObj = new FormData(form);\n    const data = Object.fromEntries(formDataObj.entries());\n    data.consent = form.consent.checked;\n\n    try {\n      const {\n        data: sanitizedData,\n        errors,\n        isValid,\n      } = sanitizeAndValidateForm(data);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        setIsSubmitting(false);\n        return;\n      }\n\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: '',\n        message: 'Обратный звонок',\n        consent: sanitizedData.consent,\n        formType,\n      };\n\n      await submitFeedback(payload);\n\n      showNotification('Ваш запрос на обратный звонок отправлен!', 'success');\n\n      form.reset(); // сброс формы\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {title && <h2 className={styles.formTitle}>{title}</h2>}\n      <form\n        ref={formRef}\n        onSubmit={handleSubmit}\n        className={styles.form}\n        noValidate\n      >\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"firstName\" className={styles.label}>\n              Имя <span className={styles.required}>*</span>\n            </label>\n            <input\n              id=\"firstName\"\n              name=\"firstName\"\n              type=\"text\"\n              className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n              required\n              minLength={2}\n              maxLength={50}\n              disabled={isSubmitting}\n            />\n            {formErrors.firstName && (\n              <div className={styles.errorMessage}>{formErrors.firstName}</div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"lastName\" className={styles.label}>\n              Фамилия\n            </label>\n            <input\n              id=\"lastName\"\n              name=\"lastName\"\n              type=\"text\"\n              className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n              minLength={2}\n              maxLength={50}\n              disabled={isSubmitting}\n            />\n            {formErrors.lastName && (\n              <div className={styles.errorMessage}>{formErrors.lastName}</div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.inputGroup}>\n          <label htmlFor=\"phone\" className={styles.label}>\n            Номер телефона <span className={styles.required}>*</span>\n          </label>\n          <input\n            id=\"phone\"\n            name=\"phone\"\n            type=\"tel\"\n            placeholder=\"+992 XXX-XX-XX-XX\"\n            className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n            required\n            ref={phoneRef}\n            disabled={isSubmitting}\n          />\n          {formErrors.phone && (\n            <div className={styles.errorMessage}>{formErrors.phone}</div>\n          )}\n        </div>\n\n        <div className={styles.checkboxGroup}>\n          <label className={styles.checkboxWrapper}>\n            <input\n              id=\"consent\"\n              name=\"consent\"\n              type=\"checkbox\"\n              required\n              className={styles.checkbox}\n              disabled={isSubmitting}\n            />\n            <span className={styles.checkboxLabel}>\n              Я даю согласие на обработку моих персональных данных\n            </span>\n          </label>\n        </div>\n\n        <button\n          type=\"submit\"\n          className={`${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`}\n          disabled={isSubmitting}\n        >\n          {isSubmitting ? 'Отправка...' : 'Отправить'}\n        </button>\n      </form>\n    </>\n  );\n};\n\nexport default CallbackForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CAC5D,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,mBAAmB,CAC3C,MAAO,CAAAC,YAAY,KAAM,+CAA+C,CACxE,OACEC,eAAe,CACfC,uBAAuB,KAClB,2BAA2B,CAClC,OAASC,cAAc,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpD,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAsC,IAArC,CAAEC,QAAQ,CAAG,UAAU,CAAEC,KAAM,CAAC,CAAAF,IAAA,CACpD,KAAM,CAACG,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAC,CAAEqB,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAC3E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAACyB,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAA2B,gBAAgB,CAAG1B,WAAW,CAAC,SAACoB,OAAO,CAAuB,IAArB,CAAAC,IAAI,CAAAM,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CAC7DR,eAAe,CAAC,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAC,CAClCS,UAAU,CAAC,IAAMX,eAAe,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAU,QAAQ,CAAG5B,OAAO,CAAC,CACvB6B,IAAI,CAAE,mBAAmB,CACzBC,WAAW,CAAE,CAAEC,CAAC,CAAE,IAAK,CACzB,CAAC,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGlC,MAAM,CAAC,IAAI,CAAC,CAE5B,KAAM,CAAAmC,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAId,YAAY,CAAE,OAElB,GAAI,CAACnB,eAAe,CAACkC,SAAS,CAAC,CAAC,CAAE,CAChCb,gBAAgB,CACd,mDAAmD,CACnD,OACF,CAAC,CACD,OACF,CAEAD,eAAe,CAAC,IAAI,CAAC,CACrBF,aAAa,CAAC,CAAC,CAAC,CAAC,CAEjB,KAAM,CAAAiB,IAAI,CAAGH,CAAC,CAACI,aAAa,CAC5B,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAC,QAAQ,CAACH,IAAI,CAAC,CACtC,KAAM,CAAAI,IAAI,CAAGC,MAAM,CAACC,WAAW,CAACJ,WAAW,CAACK,OAAO,CAAC,CAAC,CAAC,CACtDH,IAAI,CAACI,OAAO,CAAGR,IAAI,CAACQ,OAAO,CAACC,OAAO,CAEnC,GAAI,CACF,KAAM,CACJL,IAAI,CAAEM,aAAa,CACnBC,MAAM,CACNC,OACF,CAAC,CAAG9C,uBAAuB,CAACsC,IAAI,CAAC,CAEjC,GAAI,CAACQ,OAAO,CAAE,CACZ7B,aAAa,CAAC4B,MAAM,CAAC,CACrBzB,gBAAgB,CAAC,sCAAsC,CAAE,OAAO,CAAC,CACjED,eAAe,CAAC,KAAK,CAAC,CACtB,OACF,CAEA,KAAM,CAAA4B,OAAO,CAAG,CACdC,SAAS,CAAEJ,aAAa,CAACI,SAAS,CAClCC,QAAQ,CAAEL,aAAa,CAACK,QAAQ,CAChCC,KAAK,CAAEN,aAAa,CAACM,KAAK,CAC1BC,KAAK,CAAE,EAAE,CACTrC,OAAO,CAAE,iBAAiB,CAC1B4B,OAAO,CAAEE,aAAa,CAACF,OAAO,CAC9BhC,QACF,CAAC,CAED,KAAM,CAAAT,cAAc,CAAC8C,OAAO,CAAC,CAE7B3B,gBAAgB,CAAC,0CAA0C,CAAE,SAAS,CAAC,CAEvEc,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAE;AACdnC,aAAa,CAAC,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOoC,KAAK,CAAE,CACdjC,gBAAgB,CAACiC,KAAK,CAACvC,OAAO,CAAE,OAAO,CAAC,CAC1C,CAAC,OAAS,CACRK,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,mBACEd,KAAA,CAAAE,SAAA,EAAA+C,QAAA,eACEnD,IAAA,CAACL,YAAY,EAACgB,OAAO,CAAEF,YAAY,CAACE,OAAQ,CAACC,IAAI,CAAEH,YAAY,CAACG,IAAK,CAAE,CAAC,CACvEJ,KAAK,eAAIR,IAAA,OAAIoD,SAAS,CAAE3D,MAAM,CAAC4D,SAAU,CAAAF,QAAA,CAAE3C,KAAK,CAAK,CAAC,cACvDN,KAAA,SACEoD,GAAG,CAAE5B,OAAQ,CACb6B,QAAQ,CAAE5B,YAAa,CACvByB,SAAS,CAAE3D,MAAM,CAACsC,IAAK,CACvByB,UAAU,MAAAL,QAAA,eAEVjD,KAAA,QAAKkD,SAAS,CAAE3D,MAAM,CAACgE,GAAI,CAAAN,QAAA,eACzBjD,KAAA,QAAKkD,SAAS,CAAE3D,MAAM,CAACiE,UAAW,CAAAP,QAAA,eAChCjD,KAAA,UAAOyD,OAAO,CAAC,WAAW,CAACP,SAAS,CAAE3D,MAAM,CAACmE,KAAM,CAAAT,QAAA,EAAC,qBAC9C,cAAAnD,IAAA,SAAMoD,SAAS,CAAE3D,MAAM,CAACoE,QAAS,CAAAV,QAAA,CAAC,GAAC,CAAM,CAAC,EACzC,CAAC,cACRnD,IAAA,UACE8D,EAAE,CAAC,WAAW,CACdC,IAAI,CAAC,WAAW,CAChBnD,IAAI,CAAC,MAAM,CACXwC,SAAS,IAAAY,MAAA,CAAKvE,MAAM,CAACwE,KAAK,MAAAD,MAAA,CAAInD,UAAU,CAACgC,SAAS,CAAGpD,MAAM,CAACyE,UAAU,CAAG,EAAE,CAAG,CAC9EL,QAAQ,MACRM,SAAS,CAAE,CAAE,CACbC,SAAS,CAAE,EAAG,CACdC,QAAQ,CAAEtD,YAAa,CACxB,CAAC,CACDF,UAAU,CAACgC,SAAS,eACnB7C,IAAA,QAAKoD,SAAS,CAAE3D,MAAM,CAAC6E,YAAa,CAAAnB,QAAA,CAAEtC,UAAU,CAACgC,SAAS,CAAM,CACjE,EACE,CAAC,cAEN3C,KAAA,QAAKkD,SAAS,CAAE3D,MAAM,CAACiE,UAAW,CAAAP,QAAA,eAChCnD,IAAA,UAAO2D,OAAO,CAAC,UAAU,CAACP,SAAS,CAAE3D,MAAM,CAACmE,KAAM,CAAAT,QAAA,CAAC,4CAEnD,CAAO,CAAC,cACRnD,IAAA,UACE8D,EAAE,CAAC,UAAU,CACbC,IAAI,CAAC,UAAU,CACfnD,IAAI,CAAC,MAAM,CACXwC,SAAS,IAAAY,MAAA,CAAKvE,MAAM,CAACwE,KAAK,MAAAD,MAAA,CAAInD,UAAU,CAACiC,QAAQ,CAAGrD,MAAM,CAACyE,UAAU,CAAG,EAAE,CAAG,CAC7EC,SAAS,CAAE,CAAE,CACbC,SAAS,CAAE,EAAG,CACdC,QAAQ,CAAEtD,YAAa,CACxB,CAAC,CACDF,UAAU,CAACiC,QAAQ,eAClB9C,IAAA,QAAKoD,SAAS,CAAE3D,MAAM,CAAC6E,YAAa,CAAAnB,QAAA,CAAEtC,UAAU,CAACiC,QAAQ,CAAM,CAChE,EACE,CAAC,EACH,CAAC,cAEN5C,KAAA,QAAKkD,SAAS,CAAE3D,MAAM,CAACiE,UAAW,CAAAP,QAAA,eAChCjD,KAAA,UAAOyD,OAAO,CAAC,OAAO,CAACP,SAAS,CAAE3D,MAAM,CAACmE,KAAM,CAAAT,QAAA,EAAC,kFAC/B,cAAAnD,IAAA,SAAMoD,SAAS,CAAE3D,MAAM,CAACoE,QAAS,CAAAV,QAAA,CAAC,GAAC,CAAM,CAAC,EACpD,CAAC,cACRnD,IAAA,UACE8D,EAAE,CAAC,OAAO,CACVC,IAAI,CAAC,OAAO,CACZnD,IAAI,CAAC,KAAK,CACV2D,WAAW,CAAC,mBAAmB,CAC/BnB,SAAS,IAAAY,MAAA,CAAKvE,MAAM,CAACwE,KAAK,MAAAD,MAAA,CAAInD,UAAU,CAACkC,KAAK,CAAGtD,MAAM,CAACyE,UAAU,CAAG,EAAE,CAAG,CAC1EL,QAAQ,MACRP,GAAG,CAAEhC,QAAS,CACd+C,QAAQ,CAAEtD,YAAa,CACxB,CAAC,CACDF,UAAU,CAACkC,KAAK,eACf/C,IAAA,QAAKoD,SAAS,CAAE3D,MAAM,CAAC6E,YAAa,CAAAnB,QAAA,CAAEtC,UAAU,CAACkC,KAAK,CAAM,CAC7D,EACE,CAAC,cAEN/C,IAAA,QAAKoD,SAAS,CAAE3D,MAAM,CAAC+E,aAAc,CAAArB,QAAA,cACnCjD,KAAA,UAAOkD,SAAS,CAAE3D,MAAM,CAACgF,eAAgB,CAAAtB,QAAA,eACvCnD,IAAA,UACE8D,EAAE,CAAC,SAAS,CACZC,IAAI,CAAC,SAAS,CACdnD,IAAI,CAAC,UAAU,CACfiD,QAAQ,MACRT,SAAS,CAAE3D,MAAM,CAACiF,QAAS,CAC3BL,QAAQ,CAAEtD,YAAa,CACxB,CAAC,cACFf,IAAA,SAAMoD,SAAS,CAAE3D,MAAM,CAACkF,aAAc,CAAAxB,QAAA,CAAC,uRAEvC,CAAM,CAAC,EACF,CAAC,CACL,CAAC,cAENnD,IAAA,WACEY,IAAI,CAAC,QAAQ,CACbwC,SAAS,IAAAY,MAAA,CAAKvE,MAAM,CAACmF,MAAM,MAAAZ,MAAA,CAAIjD,YAAY,CAAGtB,MAAM,CAACoF,aAAa,CAAG,EAAE,CAAG,CAC1ER,QAAQ,CAAEtD,YAAa,CAAAoC,QAAA,CAEtBpC,YAAY,CAAG,aAAa,CAAG,WAAW,CACrC,CAAC,EACL,CAAC,EACP,CAAC,CAEP,CAAC,CAED,cAAe,CAAAV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}