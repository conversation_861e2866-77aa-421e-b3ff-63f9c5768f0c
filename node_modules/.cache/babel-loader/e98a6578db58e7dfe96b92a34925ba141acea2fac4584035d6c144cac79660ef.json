{"ast": null, "code": "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\nfunction EffectCards(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = rtl ? -swiper.translate : swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = \"translateX(\".concat(swiper.minTranslate(), \"px)\");\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = \"\".concat(-25 * subProgress * Math.abs(progress), \"%\");\n      }\n      if (progress < 0) {\n        // next\n        tX = \"calc(\".concat(tX, \"px \").concat(rtl ? '-' : '+', \" (\").concat(tXAdd * Math.abs(progress), \"%))\");\n      } else if (progress > 0) {\n        // prev\n        tX = \"calc(\".concat(tX, \"px \").concat(rtl ? '-' : '+', \" (-\").concat(tXAdd * Math.abs(progress), \"%))\");\n      } else {\n        tX = \"\".concat(tX, \"px\");\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? \"\".concat(1 + (1 - scale) * progress) : \"\".concat(1 - (1 - scale) * progress);\n\n      /* eslint-disable */\n      const transform = \"\\n        translate3d(\".concat(tX, \", \").concat(tY, \", \").concat(tZ, \"px)\\n        rotateZ(\").concat(params.rotate ? rtl ? -rotate : rotate : 0, \"deg)\\n        scale(\").concat(scaleString, \")\\n      \");\n      /* eslint-enable */\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow('cards', slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = \"\".concat(duration, \"ms\");\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = \"\".concat(duration, \"ms\");\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      _loopSwapReset: false,\n      watchSlidesProgress: true,\n      loopAdditionalSlides: swiper.params.cardsEffect.rotate ? 3 : 2,\n      centeredSlides: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\nexport { EffectCards as default };", "map": {"version": 3, "names": ["c", "createShadow", "e", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "g", "getSlideTransformEl", "EffectCards", "_ref", "swiper", "extendParams", "on", "cardsEffect", "slideShadows", "rotate", "perSlideRotate", "perSlideOffset", "setTranslate", "slides", "activeIndex", "rtlTranslate", "rtl", "params", "startTranslate", "isTouched", "touchEventsData", "currentTranslate", "translate", "i", "length", "slideEl", "slideProgress", "progress", "Math", "min", "max", "offset", "swiperSlideOffset", "centeredSlides", "cssMode", "wrapperEl", "style", "transform", "concat", "minTranslate", "tX", "tY", "tZ", "abs", "scale", "tXAdd", "slideIndex", "virtual", "enabled", "from", "isSwipeToNext", "isSwipeToPrev", "subProgress", "isHorizontal", "prevY", "scaleString", "shadowEl", "querySelector", "opacity", "zIndex", "round", "targetEl", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "effect", "perspective", "overwriteParams", "_loopSwapReset", "watchSlidesProgress", "loopAdditionalSlides", "virtualTranslate", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/modules/effect-cards.mjs"], "sourcesContent": ["import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\n\nfunction EffectCards(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = rtl ? -swiper.translate : swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n\n      /* eslint-disable */\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rtl ? -rotate : rotate : 0}deg)\n        scale(${scaleString})\n      `;\n      /* eslint-enable */\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow('cards', slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      _loopSwapReset: false,\n      watchSlidesProgress: true,\n      loopAdditionalSlides: swiper.params.cardsEffect.rotate ? 3 : 2,\n      centeredSlides: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectCards as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,UAAU,QAAQ,2BAA2B;AAC3D,SAASD,CAAC,IAAIE,YAAY,QAAQ,6BAA6B;AAC/D,SAASF,CAAC,IAAIG,0BAA0B,QAAQ,6CAA6C;AAC7F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE9D,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,WAAW,EAAE;MACXC,YAAY,EAAE,IAAI;MAClBC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC,MAAM;MACNC,WAAW;MACXC,YAAY,EAAEC;IAChB,CAAC,GAAGZ,MAAM;IACV,MAAMa,MAAM,GAAGb,MAAM,CAACa,MAAM,CAACV,WAAW;IACxC,MAAM;MACJW,cAAc;MACdC;IACF,CAAC,GAAGf,MAAM,CAACgB,eAAe;IAC1B,MAAMC,gBAAgB,GAAGL,GAAG,GAAG,CAACZ,MAAM,CAACkB,SAAS,GAAGlB,MAAM,CAACkB,SAAS;IACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,CAACW,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAME,OAAO,GAAGZ,MAAM,CAACU,CAAC,CAAC;MACzB,MAAMG,aAAa,GAAGD,OAAO,CAACE,QAAQ;MACtC,MAAMA,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD,IAAIK,MAAM,GAAGN,OAAO,CAACO,iBAAiB;MACtC,IAAI5B,MAAM,CAACa,MAAM,CAACgB,cAAc,IAAI,CAAC7B,MAAM,CAACa,MAAM,CAACiB,OAAO,EAAE;QAC1D9B,MAAM,CAAC+B,SAAS,CAACC,KAAK,CAACC,SAAS,iBAAAC,MAAA,CAAiBlC,MAAM,CAACmC,YAAY,CAAC,CAAC,QAAK;MAC7E;MACA,IAAInC,MAAM,CAACa,MAAM,CAACgB,cAAc,IAAI7B,MAAM,CAACa,MAAM,CAACiB,OAAO,EAAE;QACzDH,MAAM,IAAIlB,MAAM,CAAC,CAAC,CAAC,CAACmB,iBAAiB;MACvC;MACA,IAAIQ,EAAE,GAAGpC,MAAM,CAACa,MAAM,CAACiB,OAAO,GAAG,CAACH,MAAM,GAAG3B,MAAM,CAACkB,SAAS,GAAG,CAACS,MAAM;MACrE,IAAIU,EAAE,GAAG,CAAC;MACV,MAAMC,EAAE,GAAG,CAAC,GAAG,GAAGd,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC;MACpC,IAAIiB,KAAK,GAAG,CAAC;MACb,IAAInC,MAAM,GAAG,CAACQ,MAAM,CAACP,cAAc,GAAGiB,QAAQ;MAC9C,IAAIkB,KAAK,GAAG5B,MAAM,CAACN,cAAc,GAAGiB,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,GAAG,IAAI;MAC7D,MAAMmB,UAAU,GAAG1C,MAAM,CAAC2C,OAAO,IAAI3C,MAAM,CAACa,MAAM,CAAC8B,OAAO,CAACC,OAAO,GAAG5C,MAAM,CAAC2C,OAAO,CAACE,IAAI,GAAG1B,CAAC,GAAGA,CAAC;MAChG,MAAM2B,aAAa,GAAG,CAACJ,UAAU,KAAKhC,WAAW,IAAIgC,UAAU,KAAKhC,WAAW,GAAG,CAAC,KAAKa,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,KAAKR,SAAS,IAAIf,MAAM,CAACa,MAAM,CAACiB,OAAO,CAAC,IAAIb,gBAAgB,GAAGH,cAAc;MACjM,MAAMiC,aAAa,GAAG,CAACL,UAAU,KAAKhC,WAAW,IAAIgC,UAAU,KAAKhC,WAAW,GAAG,CAAC,KAAKa,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,KAAKR,SAAS,IAAIf,MAAM,CAACa,MAAM,CAACiB,OAAO,CAAC,IAAIb,gBAAgB,GAAGH,cAAc;MAClM,IAAIgC,aAAa,IAAIC,aAAa,EAAE;QAClC,MAAMC,WAAW,GAAG,CAAC,CAAC,GAAGxB,IAAI,CAACe,GAAG,CAAC,CAACf,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG;QAC3ElB,MAAM,IAAI,CAAC,EAAE,GAAGkB,QAAQ,GAAGyB,WAAW;QACtCR,KAAK,IAAI,CAAC,GAAG,GAAGQ,WAAW;QAC3BP,KAAK,IAAI,EAAE,GAAGO,WAAW;QACzBX,EAAE,MAAAH,MAAA,CAAM,CAAC,EAAE,GAAGc,WAAW,GAAGxB,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,MAAG;MACnD;MACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChB;QACAa,EAAE,WAAAF,MAAA,CAAWE,EAAE,SAAAF,MAAA,CAAMtB,GAAG,GAAG,GAAG,GAAG,GAAG,QAAAsB,MAAA,CAAKO,KAAK,GAAGjB,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,QAAK;MAC1E,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;QACvB;QACAa,EAAE,WAAAF,MAAA,CAAWE,EAAE,SAAAF,MAAA,CAAMtB,GAAG,GAAG,GAAG,GAAG,GAAG,SAAAsB,MAAA,CAAMO,KAAK,GAAGjB,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,QAAK;MAC3E,CAAC,MAAM;QACLa,EAAE,MAAAF,MAAA,CAAME,EAAE,OAAI;MAChB;MACA,IAAI,CAACpC,MAAM,CAACiD,YAAY,CAAC,CAAC,EAAE;QAC1B,MAAMC,KAAK,GAAGb,EAAE;QAChBA,EAAE,GAAGD,EAAE;QACPA,EAAE,GAAGc,KAAK;MACZ;MACA,MAAMC,WAAW,GAAG5B,QAAQ,GAAG,CAAC,MAAAW,MAAA,CAAM,CAAC,GAAG,CAAC,CAAC,GAAGM,KAAK,IAAIjB,QAAQ,OAAAW,MAAA,CAAQ,CAAC,GAAG,CAAC,CAAC,GAAGM,KAAK,IAAIjB,QAAQ,CAAE;;MAEpG;MACA,MAAMU,SAAS,4BAAAC,MAAA,CACCE,EAAE,QAAAF,MAAA,CAAKG,EAAE,QAAAH,MAAA,CAAKI,EAAE,2BAAAJ,MAAA,CACpBrB,MAAM,CAACR,MAAM,GAAGO,GAAG,GAAG,CAACP,MAAM,GAAGA,MAAM,GAAG,CAAC,0BAAA6B,MAAA,CAC5CiB,WAAW,cACpB;MACD;;MAEA,IAAItC,MAAM,CAACT,YAAY,EAAE;QACvB;QACA,IAAIgD,QAAQ,GAAG/B,OAAO,CAACgC,aAAa,CAAC,sBAAsB,CAAC;QAC5D,IAAI,CAACD,QAAQ,EAAE;UACbA,QAAQ,GAAG7D,YAAY,CAAC,OAAO,EAAE8B,OAAO,CAAC;QAC3C;QACA,IAAI+B,QAAQ,EAAEA,QAAQ,CAACpB,KAAK,CAACsB,OAAO,GAAG9B,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAACF,IAAI,CAACe,GAAG,CAAChB,QAAQ,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACnG;MACAF,OAAO,CAACW,KAAK,CAACuB,MAAM,GAAG,CAAC/B,IAAI,CAACe,GAAG,CAACf,IAAI,CAACgC,KAAK,CAAClC,aAAa,CAAC,CAAC,GAAGb,MAAM,CAACW,MAAM;MAC3E,MAAMqC,QAAQ,GAAG/D,YAAY,CAACmB,MAAM,EAAEQ,OAAO,CAAC;MAC9CoC,QAAQ,CAACzB,KAAK,CAACC,SAAS,GAAGA,SAAS;IACtC;EACF,CAAC;EACD,MAAMyB,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG5D,MAAM,CAACS,MAAM,CAACoD,GAAG,CAACxC,OAAO,IAAIxB,mBAAmB,CAACwB,OAAO,CAAC,CAAC;IACpFuC,iBAAiB,CAACE,OAAO,CAACC,EAAE,IAAI;MAC9BA,EAAE,CAAC/B,KAAK,CAACgC,kBAAkB,MAAA9B,MAAA,CAAMyB,QAAQ,OAAI;MAC7CI,EAAE,CAACE,gBAAgB,CAAC,sBAAsB,CAAC,CAACH,OAAO,CAACV,QAAQ,IAAI;QAC9DA,QAAQ,CAACpB,KAAK,CAACgC,kBAAkB,MAAA9B,MAAA,CAAMyB,QAAQ,OAAI;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IACFhE,0BAA0B,CAAC;MACzBK,MAAM;MACN2D,QAAQ;MACRC;IACF,CAAC,CAAC;EACJ,CAAC;EACDnE,UAAU,CAAC;IACTyE,MAAM,EAAE,OAAO;IACflE,MAAM;IACNE,EAAE;IACFM,YAAY;IACZkD,aAAa;IACbS,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,cAAc,EAAE,KAAK;MACrBC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAEvE,MAAM,CAACa,MAAM,CAACV,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC;MAC9DwB,cAAc,EAAE,IAAI;MACpB2C,gBAAgB,EAAE,CAACxE,MAAM,CAACa,MAAM,CAACiB;IACnC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAAShC,WAAW,IAAI2E,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}