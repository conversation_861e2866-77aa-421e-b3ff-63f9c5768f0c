{"ast": null, "code": "import _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transitionEnd\", \"transition\"];\nimport { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nfunction makeState(_ref, props, context, presenceContext) {\n  let {\n    scrapeMotionValuesFromProps,\n    createRenderState\n  } = _ref;\n  const state = {\n    latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n    renderState: createRenderState()\n  };\n  return state;\n}\nconst makeUseVisualState = config => (props, isStatic) => {\n  const context = useContext(MotionContext);\n  const presenceContext = useContext(PresenceContext);\n  const make = () => makeState(config, props, context, presenceContext);\n  return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n  const values = {};\n  const motionValues = scrapeMotionValues(props, {});\n  for (const key in motionValues) {\n    values[key] = resolveMotionValue(motionValues[key]);\n  }\n  let {\n    initial,\n    animate\n  } = props;\n  const isControllingVariants$1 = isControllingVariants(props);\n  const isVariantNode$1 = isVariantNode(props);\n  if (context && isVariantNode$1 && !isControllingVariants$1 && props.inherit !== false) {\n    if (initial === undefined) initial = context.initial;\n    if (animate === undefined) animate = context.animate;\n  }\n  let isInitialAnimationBlocked = presenceContext ? presenceContext.initial === false : false;\n  isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n  const variantToSet = isInitialAnimationBlocked ? animate : initial;\n  if (variantToSet && typeof variantToSet !== \"boolean\" && !isAnimationControls(variantToSet)) {\n    const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n    for (let i = 0; i < list.length; i++) {\n      const resolved = resolveVariantFromProps(props, list[i]);\n      if (resolved) {\n        const {\n            transitionEnd,\n            transition\n          } = resolved,\n          target = _objectWithoutProperties(resolved, _excluded);\n        for (const key in target) {\n          let valueTarget = target[key];\n          if (Array.isArray(valueTarget)) {\n            /**\n             * Take final keyframe if the initial animation is blocked because\n             * we want to initialise at the end of that blocked animation.\n             */\n            const index = isInitialAnimationBlocked ? valueTarget.length - 1 : 0;\n            valueTarget = valueTarget[index];\n          }\n          if (valueTarget !== null) {\n            values[key] = valueTarget;\n          }\n        }\n        for (const key in transitionEnd) {\n          values[key] = transitionEnd[key];\n        }\n      }\n    }\n  }\n  return values;\n}\nexport { makeUseVisualState };", "map": {"version": 3, "names": ["useContext", "isAnimationControls", "MotionContext", "PresenceContext", "isControllingVariants", "isVariantNode", "resolveVariantFromProps", "useConstant", "resolveMotionValue", "makeState", "_ref", "props", "context", "presenceContext", "scrapeMotionValuesFromProps", "createRenderState", "state", "latestValues", "makeLatestValues", "renderState", "makeUseVisualState", "config", "isStatic", "make", "scrapeMotionValues", "values", "motionValues", "key", "initial", "animate", "isControllingVariants$1", "isVariantNode$1", "inherit", "undefined", "isInitialAnimationBlocked", "variantToSet", "list", "Array", "isArray", "i", "length", "resolved", "transitionEnd", "transition", "target", "_objectWithoutProperties", "_excluded", "valueTarget", "index"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = resolveVariantFromProps(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,gDAAgD;AACrG,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,4CAA4C;AAE/E,SAASC,SAASA,CAAAC,IAAA,EAAsDC,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAE;EAAA,IAAtF;IAAEC,2BAA2B;IAAEC;EAAmB,CAAC,GAAAL,IAAA;EAClE,MAAMM,KAAK,GAAG;IACVC,YAAY,EAAEC,gBAAgB,CAACP,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEC,2BAA2B,CAAC;IAC5FK,WAAW,EAAEJ,iBAAiB,CAAC;EACnC,CAAC;EACD,OAAOC,KAAK;AAChB;AACA,MAAMI,kBAAkB,GAAIC,MAAM,IAAK,CAACV,KAAK,EAAEW,QAAQ,KAAK;EACxD,MAAMV,OAAO,GAAGZ,UAAU,CAACE,aAAa,CAAC;EACzC,MAAMW,eAAe,GAAGb,UAAU,CAACG,eAAe,CAAC;EACnD,MAAMoB,IAAI,GAAGA,CAAA,KAAMd,SAAS,CAACY,MAAM,EAAEV,KAAK,EAAEC,OAAO,EAAEC,eAAe,CAAC;EACrE,OAAOS,QAAQ,GAAGC,IAAI,CAAC,CAAC,GAAGhB,WAAW,CAACgB,IAAI,CAAC;AAChD,CAAC;AACD,SAASL,gBAAgBA,CAACP,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEW,kBAAkB,EAAE;EAC3E,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAGF,kBAAkB,CAACb,KAAK,EAAE,CAAC,CAAC,CAAC;EAClD,KAAK,MAAMgB,GAAG,IAAID,YAAY,EAAE;IAC5BD,MAAM,CAACE,GAAG,CAAC,GAAGnB,kBAAkB,CAACkB,YAAY,CAACC,GAAG,CAAC,CAAC;EACvD;EACA,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGlB,KAAK;EAChC,MAAMmB,uBAAuB,GAAG1B,qBAAqB,CAACO,KAAK,CAAC;EAC5D,MAAMoB,eAAe,GAAG1B,aAAa,CAACM,KAAK,CAAC;EAC5C,IAAIC,OAAO,IACPmB,eAAe,IACf,CAACD,uBAAuB,IACxBnB,KAAK,CAACqB,OAAO,KAAK,KAAK,EAAE;IACzB,IAAIJ,OAAO,KAAKK,SAAS,EACrBL,OAAO,GAAGhB,OAAO,CAACgB,OAAO;IAC7B,IAAIC,OAAO,KAAKI,SAAS,EACrBJ,OAAO,GAAGjB,OAAO,CAACiB,OAAO;EACjC;EACA,IAAIK,yBAAyB,GAAGrB,eAAe,GACzCA,eAAe,CAACe,OAAO,KAAK,KAAK,GACjC,KAAK;EACXM,yBAAyB,GAAGA,yBAAyB,IAAIN,OAAO,KAAK,KAAK;EAC1E,MAAMO,YAAY,GAAGD,yBAAyB,GAAGL,OAAO,GAAGD,OAAO;EAClE,IAAIO,YAAY,IACZ,OAAOA,YAAY,KAAK,SAAS,IACjC,CAAClC,mBAAmB,CAACkC,YAAY,CAAC,EAAE;IACpC,MAAMC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IACxE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,MAAME,QAAQ,GAAGnC,uBAAuB,CAACK,KAAK,EAAEyB,IAAI,CAACG,CAAC,CAAC,CAAC;MACxD,IAAIE,QAAQ,EAAE;QACV,MAAM;YAAEC,aAAa;YAAEC;UAAsB,CAAC,GAAGF,QAAQ;UAAnBG,MAAM,GAAAC,wBAAA,CAAKJ,QAAQ,EAAAK,SAAA;QACzD,KAAK,MAAMnB,GAAG,IAAIiB,MAAM,EAAE;UACtB,IAAIG,WAAW,GAAGH,MAAM,CAACjB,GAAG,CAAC;UAC7B,IAAIU,KAAK,CAACC,OAAO,CAACS,WAAW,CAAC,EAAE;YAC5B;AACxB;AACA;AACA;YACwB,MAAMC,KAAK,GAAGd,yBAAyB,GACjCa,WAAW,CAACP,MAAM,GAAG,CAAC,GACtB,CAAC;YACPO,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC;UACpC;UACA,IAAID,WAAW,KAAK,IAAI,EAAE;YACtBtB,MAAM,CAACE,GAAG,CAAC,GAAGoB,WAAW;UAC7B;QACJ;QACA,KAAK,MAAMpB,GAAG,IAAIe,aAAa,EAAE;UAC7BjB,MAAM,CAACE,GAAG,CAAC,GAAGe,aAAa,CAACf,GAAG,CAAC;QACpC;MACJ;IACJ;EACJ;EACA,OAAOF,MAAM;AACjB;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}