{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import styles from'./ImageCard.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ImageCard=()=>{return/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"div\",{className:styles.card,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.content,children:[/*#__PURE__*/_jsx(\"h3\",{children:\"GWM \\u0423\\u0425\\u041E\\u0414\"}),/*#__PURE__*/_jsx(Link,{to:\"/owners\",children:/*#__PURE__*/_jsx(\"button\",{className:\"button-white\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0423\\u0437\\u043D\\u0430\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435\"})})})]})})})});};export default ImageCard;", "map": {"version": 3, "names": ["React", "Link", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "ImageCard", "className", "section", "children", "card", "content", "to"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/Image-card/ImageCard.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './ImageCard.module.css';\n\nconst ImageCard = () => {\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.card}>\n          <div className={styles.content}>\n            <h3>GWM УХОД</h3>\n            <Link to=\"/owners\">\n              <button className=\"button-white\">\n                <span>Узнать больше</span>\n              </button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ImageCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,MAAM,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,mBACEH,IAAA,YAASI,SAAS,CAAEN,MAAM,CAACO,OAAQ,CAAAC,QAAA,cACjCN,IAAA,QAAKI,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBN,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACS,IAAK,CAAAD,QAAA,cAC1BJ,KAAA,QAAKE,SAAS,CAAEN,MAAM,CAACU,OAAQ,CAAAF,QAAA,eAC7BN,IAAA,OAAAM,QAAA,CAAI,8BAAQ,CAAI,CAAC,cACjBN,IAAA,CAACH,IAAI,EAACY,EAAE,CAAC,SAAS,CAAAH,QAAA,cAChBN,IAAA,WAAQI,SAAS,CAAC,cAAc,CAAAE,QAAA,cAC9BN,IAAA,SAAAM,QAAA,CAAM,2EAAa,CAAM,CAAC,CACpB,CAAC,CACL,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}