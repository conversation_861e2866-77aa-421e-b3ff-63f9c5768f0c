{"ast": null, "code": "import DOMPurify from 'dompurify';\n\n/**\n * Sanitizes user input to prevent XSS attacks\n * @param {string} input - The input string to sanitize\n * @returns {string} - The sanitized string\n */\nexport const sanitizeInput = input => {\n  if (typeof input !== 'string') return input;\n  return DOMPurify.sanitize(input.trim());\n};\n\n/**\n * Validates form data and returns validation errors\n * @param {Object} formData - The form data to validate\n * @returns {Object} - Object containing validation errors\n */\nexport const validateForm = formData => {\n  const errors = {};\n\n  // First name validation\n  if (!formData.firstName || formData.firstName.trim().length < 2) {\n    errors.firstName = 'Имя должно содержать минимум 2 символа';\n  } else if (formData.firstName.trim().length > 50) {\n    errors.firstName = 'Имя не должно превышать 50 символов';\n  } else if (!/^[а-яёА-ЯЁa-zA-Z\\s-]+$/.test(formData.firstName.trim())) {\n    errors.firstName = 'Имя может содержать только буквы, пробелы и дефисы';\n  }\n\n  // Last name validation (optional but if provided, validate)\n  if (formData.lastName && formData.lastName.trim().length > 0) {\n    if (formData.lastName.trim().length < 2) {\n      errors.lastName = 'Фамилия должна содержать минимум 2 символа';\n    } else if (formData.lastName.trim().length > 50) {\n      errors.lastName = 'Фамилия не должна превышать 50 символов';\n    } else if (!/^[а-яёА-ЯЁa-zA-Z\\s-]+$/.test(formData.lastName.trim())) {\n      errors.lastName = 'Фамилия может содержать только буквы, пробелы и дефисы';\n    }\n  }\n\n  // Phone validation\n  const phoneRegex = /^\\+992\\s\\d{3}-\\d{2}-\\d{2}-\\d{2}$/;\n  if (!formData.phone || !phoneRegex.test(formData.phone)) {\n    errors.phone = 'Неверный формат номера телефона. Используйте формат: +992 XXX-XX-XX-XX';\n  }\n\n  // Message validation (prevent script injection)\n  if (formData.message && formData.message.trim().length > 0) {\n    if (formData.message.trim().length > 1000) {\n      errors.message = 'Сообщение не должно превышать 1000 символов';\n    }\n    // Check for potentially dangerous content\n    if (/<script|javascript:|on\\w+=/i.test(formData.message)) {\n      errors.message = 'Недопустимые символы в сообщении';\n    }\n  }\n\n  // Topic validation\n  if (!formData.topic || !formData.topic.value) {\n    errors.topic = 'Пожалуйста, выберите модель автомобиля';\n  }\n\n  // Consent validation\n  if (!formData.consent) {\n    errors.consent = 'Необходимо дать согласие на обработку персональных данных';\n  }\n  return errors;\n};\n\n/**\n * Validates email format\n * @param {string} email - Email to validate\n * @returns {boolean} - True if email is valid\n */\nexport const validateEmail = email => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n/**\n * Validates phone number format for Tajikistan\n * @param {string} phone - Phone number to validate\n * @returns {boolean} - True if phone is valid\n */\nexport const validatePhone = phone => {\n  const phoneRegex = /^\\+992\\s\\d{3}-\\d{2}-\\d{2}-\\d{2}$/;\n  return phoneRegex.test(phone);\n};\n\n/**\n * Sanitizes and validates form data\n * @param {Object} formData - Raw form data\n * @returns {Object} - Object with sanitized data and validation errors\n */\nexport const sanitizeAndValidateForm = formData => {\n  // Sanitize all string inputs\n  const sanitizedData = {\n    firstName: sanitizeInput(formData.firstName),\n    lastName: sanitizeInput(formData.lastName),\n    phone: sanitizeInput(formData.phone),\n    message: sanitizeInput(formData.message),\n    topic: formData.topic,\n    // This is an object from react-select\n    consent: Boolean(formData.consent)\n  };\n\n  // Validate the sanitized data\n  const errors = validateForm(sanitizedData);\n  return {\n    data: sanitizedData,\n    errors,\n    isValid: Object.keys(errors).length === 0\n  };\n};\n\n/**\n * Rate limiting helper - tracks API calls per user\n */\nclass RateLimiter {\n  constructor(maxRequests = 5, windowMs = 60000) {\n    // 5 requests per minute\n    this.maxRequests = maxRequests;\n    this.windowMs = windowMs;\n    this.requests = new Map();\n  }\n  isAllowed(identifier = 'default') {\n    const now = Date.now();\n    const userRequests = this.requests.get(identifier) || [];\n\n    // Remove old requests outside the window\n    const validRequests = userRequests.filter(time => now - time < this.windowMs);\n    if (validRequests.length >= this.maxRequests) {\n      return false;\n    }\n\n    // Add current request\n    validRequests.push(now);\n    this.requests.set(identifier, validRequests);\n    return true;\n  }\n  getRemainingRequests(identifier = 'default') {\n    const now = Date.now();\n    const userRequests = this.requests.get(identifier) || [];\n    const validRequests = userRequests.filter(time => now - time < this.windowMs);\n    return Math.max(0, this.maxRequests - validRequests.length);\n  }\n}\nexport const formRateLimiter = new RateLimiter(3, 60000); // 3 form submissions per minute", "map": {"version": 3, "names": ["DOMPurify", "sanitizeInput", "input", "sanitize", "trim", "validateForm", "formData", "errors", "firstName", "length", "test", "lastName", "phoneRegex", "phone", "message", "topic", "value", "consent", "validateEmail", "email", "emailRegex", "validatePhone", "sanitizeAndValidateForm", "sanitizedData", "Boolean", "data", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "RateLimiter", "constructor", "maxRequests", "windowMs", "requests", "Map", "isAllowed", "identifier", "now", "Date", "userRequests", "get", "validRequests", "filter", "time", "push", "set", "getRemainingRequests", "Math", "max", "formRateLimiter"], "sources": ["/var/www/html/gwm.tj/src/utils/validation.js"], "sourcesContent": ["import DOMPurify from 'dompurify';\n\n/**\n * Sanitizes user input to prevent XSS attacks\n * @param {string} input - The input string to sanitize\n * @returns {string} - The sanitized string\n */\nexport const sanitizeInput = (input) => {\n  if (typeof input !== 'string') return input;\n  return DOMPurify.sanitize(input.trim());\n};\n\n/**\n * Validates form data and returns validation errors\n * @param {Object} formData - The form data to validate\n * @returns {Object} - Object containing validation errors\n */\nexport const validateForm = (formData) => {\n  const errors = {};\n\n  // First name validation\n  if (!formData.firstName || formData.firstName.trim().length < 2) {\n    errors.firstName = 'Имя должно содержать минимум 2 символа';\n  } else if (formData.firstName.trim().length > 50) {\n    errors.firstName = 'Имя не должно превышать 50 символов';\n  } else if (!/^[а-яёА-ЯЁa-zA-Z\\s-]+$/.test(formData.firstName.trim())) {\n    errors.firstName = 'Имя может содержать только буквы, пробелы и дефисы';\n  }\n\n  // Last name validation (optional but if provided, validate)\n  if (formData.lastName && formData.lastName.trim().length > 0) {\n    if (formData.lastName.trim().length < 2) {\n      errors.lastName = 'Фамилия должна содержать минимум 2 символа';\n    } else if (formData.lastName.trim().length > 50) {\n      errors.lastName = 'Фамилия не должна превышать 50 символов';\n    } else if (!/^[а-яёА-ЯЁa-zA-Z\\s-]+$/.test(formData.lastName.trim())) {\n      errors.lastName = 'Фамилия может содержать только буквы, пробелы и дефисы';\n    }\n  }\n\n  // Phone validation\n  const phoneRegex = /^\\+992\\s\\d{3}-\\d{2}-\\d{2}-\\d{2}$/;\n  if (!formData.phone || !phoneRegex.test(formData.phone)) {\n    errors.phone = 'Неверный формат номера телефона. Используйте формат: +992 XXX-XX-XX-XX';\n  }\n\n  // Message validation (prevent script injection)\n  if (formData.message && formData.message.trim().length > 0) {\n    if (formData.message.trim().length > 1000) {\n      errors.message = 'Сообщение не должно превышать 1000 символов';\n    }\n    // Check for potentially dangerous content\n    if (/<script|javascript:|on\\w+=/i.test(formData.message)) {\n      errors.message = 'Недопустимые символы в сообщении';\n    }\n  }\n\n  // Topic validation\n  if (!formData.topic || !formData.topic.value) {\n    errors.topic = 'Пожалуйста, выберите модель автомобиля';\n  }\n\n  // Consent validation\n  if (!formData.consent) {\n    errors.consent = 'Необходимо дать согласие на обработку персональных данных';\n  }\n\n  return errors;\n};\n\n/**\n * Validates email format\n * @param {string} email - Email to validate\n * @returns {boolean} - True if email is valid\n */\nexport const validateEmail = (email) => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n/**\n * Validates phone number format for Tajikistan\n * @param {string} phone - Phone number to validate\n * @returns {boolean} - True if phone is valid\n */\nexport const validatePhone = (phone) => {\n  const phoneRegex = /^\\+992\\s\\d{3}-\\d{2}-\\d{2}-\\d{2}$/;\n  return phoneRegex.test(phone);\n};\n\n/**\n * Sanitizes and validates form data\n * @param {Object} formData - Raw form data\n * @returns {Object} - Object with sanitized data and validation errors\n */\nexport const sanitizeAndValidateForm = (formData) => {\n  // Sanitize all string inputs\n  const sanitizedData = {\n    firstName: sanitizeInput(formData.firstName),\n    lastName: sanitizeInput(formData.lastName),\n    phone: sanitizeInput(formData.phone),\n    message: sanitizeInput(formData.message),\n    topic: formData.topic, // This is an object from react-select\n    consent: Boolean(formData.consent),\n  };\n\n  // Validate the sanitized data\n  const errors = validateForm(sanitizedData);\n\n  return {\n    data: sanitizedData,\n    errors,\n    isValid: Object.keys(errors).length === 0,\n  };\n};\n\n/**\n * Rate limiting helper - tracks API calls per user\n */\nclass RateLimiter {\n  constructor(maxRequests = 5, windowMs = 60000) { // 5 requests per minute\n    this.maxRequests = maxRequests;\n    this.windowMs = windowMs;\n    this.requests = new Map();\n  }\n\n  isAllowed(identifier = 'default') {\n    const now = Date.now();\n    const userRequests = this.requests.get(identifier) || [];\n\n    // Remove old requests outside the window\n    const validRequests = userRequests.filter(time => now - time < this.windowMs);\n\n    if (validRequests.length >= this.maxRequests) {\n      return false;\n    }\n\n    // Add current request\n    validRequests.push(now);\n    this.requests.set(identifier, validRequests);\n\n    return true;\n  }\n\n  getRemainingRequests(identifier = 'default') {\n    const now = Date.now();\n    const userRequests = this.requests.get(identifier) || [];\n    const validRequests = userRequests.filter(time => now - time < this.windowMs);\n\n    return Math.max(0, this.maxRequests - validRequests.length);\n  }\n}\n\nexport const formRateLimiter = new RateLimiter(3, 60000); // 3 form submissions per minute\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,WAAW;;AAEjC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAIC,KAAK,IAAK;EACtC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAC3C,OAAOF,SAAS,CAACG,QAAQ,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EACxC,MAAMC,MAAM,GAAG,CAAC,CAAC;;EAEjB;EACA,IAAI,CAACD,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;IAC/DF,MAAM,CAACC,SAAS,GAAG,wCAAwC;EAC7D,CAAC,MAAM,IAAIF,QAAQ,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,EAAE,EAAE;IAChDF,MAAM,CAACC,SAAS,GAAG,qCAAqC;EAC1D,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAACE,IAAI,CAACJ,QAAQ,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE;IACpEG,MAAM,CAACC,SAAS,GAAG,oDAAoD;EACzE;;EAEA;EACA,IAAIF,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACK,QAAQ,CAACP,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;IAC5D,IAAIH,QAAQ,CAACK,QAAQ,CAACP,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;MACvCF,MAAM,CAACI,QAAQ,GAAG,4CAA4C;IAChE,CAAC,MAAM,IAAIL,QAAQ,CAACK,QAAQ,CAACP,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,EAAE,EAAE;MAC/CF,MAAM,CAACI,QAAQ,GAAG,yCAAyC;IAC7D,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAACD,IAAI,CAACJ,QAAQ,CAACK,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC,EAAE;MACnEG,MAAM,CAACI,QAAQ,GAAG,wDAAwD;IAC5E;EACF;;EAEA;EACA,MAAMC,UAAU,GAAG,kCAAkC;EACrD,IAAI,CAACN,QAAQ,CAACO,KAAK,IAAI,CAACD,UAAU,CAACF,IAAI,CAACJ,QAAQ,CAACO,KAAK,CAAC,EAAE;IACvDN,MAAM,CAACM,KAAK,GAAG,wEAAwE;EACzF;;EAEA;EACA,IAAIP,QAAQ,CAACQ,OAAO,IAAIR,QAAQ,CAACQ,OAAO,CAACV,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;IAC1D,IAAIH,QAAQ,CAACQ,OAAO,CAACV,IAAI,CAAC,CAAC,CAACK,MAAM,GAAG,IAAI,EAAE;MACzCF,MAAM,CAACO,OAAO,GAAG,6CAA6C;IAChE;IACA;IACA,IAAI,6BAA6B,CAACJ,IAAI,CAACJ,QAAQ,CAACQ,OAAO,CAAC,EAAE;MACxDP,MAAM,CAACO,OAAO,GAAG,kCAAkC;IACrD;EACF;;EAEA;EACA,IAAI,CAACR,QAAQ,CAACS,KAAK,IAAI,CAACT,QAAQ,CAACS,KAAK,CAACC,KAAK,EAAE;IAC5CT,MAAM,CAACQ,KAAK,GAAG,wCAAwC;EACzD;;EAEA;EACA,IAAI,CAACT,QAAQ,CAACW,OAAO,EAAE;IACrBV,MAAM,CAACU,OAAO,GAAG,2DAA2D;EAC9E;EAEA,OAAOV,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACV,IAAI,CAACS,KAAK,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAIR,KAAK,IAAK;EACtC,MAAMD,UAAU,GAAG,kCAAkC;EACrD,OAAOA,UAAU,CAACF,IAAI,CAACG,KAAK,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,uBAAuB,GAAIhB,QAAQ,IAAK;EACnD;EACA,MAAMiB,aAAa,GAAG;IACpBf,SAAS,EAAEP,aAAa,CAACK,QAAQ,CAACE,SAAS,CAAC;IAC5CG,QAAQ,EAAEV,aAAa,CAACK,QAAQ,CAACK,QAAQ,CAAC;IAC1CE,KAAK,EAAEZ,aAAa,CAACK,QAAQ,CAACO,KAAK,CAAC;IACpCC,OAAO,EAAEb,aAAa,CAACK,QAAQ,CAACQ,OAAO,CAAC;IACxCC,KAAK,EAAET,QAAQ,CAACS,KAAK;IAAE;IACvBE,OAAO,EAAEO,OAAO,CAAClB,QAAQ,CAACW,OAAO;EACnC,CAAC;;EAED;EACA,MAAMV,MAAM,GAAGF,YAAY,CAACkB,aAAa,CAAC;EAE1C,OAAO;IACLE,IAAI,EAAEF,aAAa;IACnBhB,MAAM;IACNmB,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACrB,MAAM,CAAC,CAACE,MAAM,KAAK;EAC1C,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,MAAMoB,WAAW,CAAC;EAChBC,WAAWA,CAACC,WAAW,GAAG,CAAC,EAAEC,QAAQ,GAAG,KAAK,EAAE;IAAE;IAC/C,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;EAEAC,SAASA,CAACC,UAAU,GAAG,SAAS,EAAE;IAChC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,YAAY,GAAG,IAAI,CAACN,QAAQ,CAACO,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;;IAExD;IACA,MAAMK,aAAa,GAAGF,YAAY,CAACG,MAAM,CAACC,IAAI,IAAIN,GAAG,GAAGM,IAAI,GAAG,IAAI,CAACX,QAAQ,CAAC;IAE7E,IAAIS,aAAa,CAAChC,MAAM,IAAI,IAAI,CAACsB,WAAW,EAAE;MAC5C,OAAO,KAAK;IACd;;IAEA;IACAU,aAAa,CAACG,IAAI,CAACP,GAAG,CAAC;IACvB,IAAI,CAACJ,QAAQ,CAACY,GAAG,CAACT,UAAU,EAAEK,aAAa,CAAC;IAE5C,OAAO,IAAI;EACb;EAEAK,oBAAoBA,CAACV,UAAU,GAAG,SAAS,EAAE;IAC3C,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,YAAY,GAAG,IAAI,CAACN,QAAQ,CAACO,GAAG,CAACJ,UAAU,CAAC,IAAI,EAAE;IACxD,MAAMK,aAAa,GAAGF,YAAY,CAACG,MAAM,CAACC,IAAI,IAAIN,GAAG,GAAGM,IAAI,GAAG,IAAI,CAACX,QAAQ,CAAC;IAE7E,OAAOe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjB,WAAW,GAAGU,aAAa,CAAChC,MAAM,CAAC;EAC7D;AACF;AAEA,OAAO,MAAMwC,eAAe,GAAG,IAAIpB,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}