{"ast": null, "code": "import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nfunction getComputedStyle(element) {\n  return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"html\";\n    this.renderInstance = renderHTML;\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      return this.projection?.isProjecting ? defaultTransformValue(key) : readTransformValue(instance, key);\n    } else {\n      const computedStyle = getComputedStyle(instance);\n      const value = (isCSSVariableName(key) ? computedStyle.getPropertyValue(key) : computedStyle[key]) || 0;\n      return typeof value === \"string\" ? value.trim() : value;\n    }\n  }\n  measureInstanceViewportBox(instance, {\n    transformPagePoint\n  }) {\n    return measureViewportBox(instance, transformPagePoint);\n  }\n  build(renderState, latestValues, props) {\n    buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n  }\n  scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n  }\n}\nexport { HTMLVisualElement, getComputedStyle };", "map": {"version": 3, "names": ["transformProps", "defaultTransformValue", "readTransformValue", "isCSSVariableName", "measureViewportBox", "DOMVisualElement", "buildHTMLStyles", "renderHTML", "scrapeMotionValuesFromProps", "getComputedStyle", "element", "window", "HTMLVisualElement", "constructor", "arguments", "type", "renderInstance", "readValueFromInstance", "instance", "key", "has", "projection", "isProjecting", "computedStyle", "value", "getPropertyValue", "trim", "measureInstanceViewportBox", "transformPagePoint", "build", "renderState", "latestValues", "props", "transformTemplate", "prevProps", "visualElement"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs"], "sourcesContent": ["import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? defaultTransformValue(key)\n                : readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,YAAY;AACzG,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,OAAOC,MAAM,CAACF,gBAAgB,CAACC,OAAO,CAAC;AAC3C;AACA,MAAME,iBAAiB,SAASP,gBAAgB,CAAC;EAC7CQ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,cAAc,GAAGT,UAAU;EACpC;EACAU,qBAAqBA,CAACC,QAAQ,EAAEC,GAAG,EAAE;IACjC,IAAInB,cAAc,CAACoB,GAAG,CAACD,GAAG,CAAC,EAAE;MACzB,OAAO,IAAI,CAACE,UAAU,EAAEC,YAAY,GAC9BrB,qBAAqB,CAACkB,GAAG,CAAC,GAC1BjB,kBAAkB,CAACgB,QAAQ,EAAEC,GAAG,CAAC;IAC3C,CAAC,MACI;MACD,MAAMI,aAAa,GAAGd,gBAAgB,CAACS,QAAQ,CAAC;MAChD,MAAMM,KAAK,GAAG,CAACrB,iBAAiB,CAACgB,GAAG,CAAC,GAC/BI,aAAa,CAACE,gBAAgB,CAACN,GAAG,CAAC,GACnCI,aAAa,CAACJ,GAAG,CAAC,KAAK,CAAC;MAC9B,OAAO,OAAOK,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACE,IAAI,CAAC,CAAC,GAAGF,KAAK;IAC3D;EACJ;EACAG,0BAA0BA,CAACT,QAAQ,EAAE;IAAEU;EAAmB,CAAC,EAAE;IACzD,OAAOxB,kBAAkB,CAACc,QAAQ,EAAEU,kBAAkB,CAAC;EAC3D;EACAC,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAE;IACpC1B,eAAe,CAACwB,WAAW,EAAEC,YAAY,EAAEC,KAAK,CAACC,iBAAiB,CAAC;EACvE;EACAzB,2BAA2BA,CAACwB,KAAK,EAAEE,SAAS,EAAEC,aAAa,EAAE;IACzD,OAAO3B,2BAA2B,CAACwB,KAAK,EAAEE,SAAS,EAAEC,aAAa,CAAC;EACvE;AACJ;AAEA,SAASvB,iBAAiB,EAAEH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}