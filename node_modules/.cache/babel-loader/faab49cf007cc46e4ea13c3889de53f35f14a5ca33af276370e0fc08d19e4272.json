{"ast": null, "code": "import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n  const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n  for (const key in props) {\n    if (isMotionValue(props[key]) || isMotionValue(prevProps[key])) {\n      const targetKey = transformPropOrder.indexOf(key) !== -1 ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1) : key;\n      newValues[targetKey] = props[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isMotionValue", "transformPropOrder", "scrapeMotionValuesFromProps", "scrapeMotionValuesFromProps$1", "props", "prevProps", "visualElement", "newValues", "key", "<PERSON><PERSON><PERSON>", "indexOf", "char<PERSON>t", "toUpperCase", "substring"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n    for (const key in props) {\n        if (isMotionValue(props[key]) ||\n            isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,kBAAkB,QAAQ,YAAY;AAC9D,SAASC,2BAA2B,IAAIC,6BAA6B,QAAQ,2CAA2C;AAExH,SAASD,2BAA2BA,CAACE,KAAK,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAClE,MAAMC,SAAS,GAAGJ,6BAA6B,CAACC,KAAK,EAAEC,SAAS,EAAEC,aAAa,CAAC;EAChF,KAAK,MAAME,GAAG,IAAIJ,KAAK,EAAE;IACrB,IAAIJ,aAAa,CAACI,KAAK,CAACI,GAAG,CAAC,CAAC,IACzBR,aAAa,CAACK,SAAS,CAACG,GAAG,CAAC,CAAC,EAAE;MAC/B,MAAMC,SAAS,GAAGR,kBAAkB,CAACS,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,GAClD,MAAM,GAAGA,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAC,GACvDL,GAAG;MACTD,SAAS,CAACE,SAAS,CAAC,GAAGL,KAAK,CAACI,GAAG,CAAC;IACrC;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASL,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}