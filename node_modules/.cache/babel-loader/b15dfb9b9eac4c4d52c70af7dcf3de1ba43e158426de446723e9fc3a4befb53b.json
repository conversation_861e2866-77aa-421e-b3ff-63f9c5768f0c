{"ast": null, "code": "import React,{useRef,useState,useEffect}from'react';import{Link}from'react-router-dom';import{Swiper,SwiperSlide}from'swiper/react';import{Autoplay}from'swiper/modules';import{BsPlayFill,BsPauseFill}from'react-icons/bs';import'swiper/css';import SkeletonSlide from'./SkeletonSlide';import styles from'./header.module.css';// CSS-модуль\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SLIDE_DURATION=5000;const API_URL='https://api.gwm.tj/api/v1/sliders';const HeroSliderGWM=()=>{const swiperRef=useRef(null);const[slides,setSlides]=useState([]);const[activeIndex,setActiveIndex]=useState(0);const[paused,setPaused]=useState(false);const[isMobile,setIsMobile]=useState(window.innerWidth<760);useEffect(()=>{const handleResize=()=>{setIsMobile(window.innerWidth<760);};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[]);useEffect(()=>{const fetchSlides=async()=>{try{const response=await fetch(API_URL);const data=await response.json();setSlides(Array.isArray(data.sliders)?data.sliders:[]);}catch(error){setSlides([]);}};fetchSlides();},[]);const handlePauseToggle=()=>{var _swiperRef$current;const swiper=(_swiperRef$current=swiperRef.current)===null||_swiperRef$current===void 0?void 0:_swiperRef$current.swiper;if(!swiper)return;paused?swiper.autoplay.start():swiper.autoplay.stop();setPaused(!paused);};return/*#__PURE__*/_jsx(\"header\",{className:styles.heroSlider,children:slides.length>0?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Swiper,{ref:swiperRef,modules:[Autoplay],autoplay:{delay:SLIDE_DURATION,disableOnInteraction:false},loop:true,onSlideChange:swiper=>setActiveIndex(swiper.realIndex),children:slides.map(slide=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsx(\"div\",{className:styles.slide,style:{backgroundImage:\"url(\".concat(isMobile?slide.image_mobile:slide.image,\")\")},children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.slideContent,children:[slide.new&&/*#__PURE__*/_jsx(\"div\",{className:styles.innerNew,children:slide.new}),slide.sale&&/*#__PURE__*/_jsx(\"div\",{className:styles.innerSele,children:slide.sale}),slide.title&&/*#__PURE__*/_jsx(\"h1\",{children:slide.title}),slide.description&&/*#__PURE__*/_jsx(\"div\",{className:styles.description,children:slide.description}),slide.link&&/*#__PURE__*/_jsx(Link,{to:\"/\".concat(slide.link),children:/*#__PURE__*/_jsx(\"button\",{className:\"button-white\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0423\\u0437\\u043D\\u0430\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435\"})})})]})})})},slide.id))}),/*#__PURE__*/_jsxs(\"div\",{className:styles.progressContainer,children:[slides.map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.dot,\" \").concat(i===activeIndex?styles.active:''),children:i===activeIndex&&!paused&&/*#__PURE__*/_jsx(\"div\",{className:styles.dotFill,style:{animationDuration:\"\".concat(SLIDE_DURATION,\"ms\")}})},i)),/*#__PURE__*/_jsx(\"button\",{className:styles.pauseBtn,onClick:handlePauseToggle,\"aria-label\":paused?'Воспроизвести слайдер':'Поставить на паузу',role:\"button\",children:paused?/*#__PURE__*/_jsx(BsPlayFill,{size:20}):/*#__PURE__*/_jsx(BsPauseFill,{size:20})})]})]}):/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(SkeletonSlide,{})})});};export default HeroSliderGWM;", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "Link", "Swiper", "SwiperSlide", "Autoplay", "BsPlayFill", "BsPauseFill", "SkeletonSlide", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SLIDE_DURATION", "API_URL", "HeroSliderGWM", "swiperRef", "slides", "setSlides", "activeIndex", "setActiveIndex", "paused", "setPaused", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "fetchSlides", "response", "fetch", "data", "json", "Array", "isArray", "sliders", "error", "handlePauseToggle", "_swiperRef$current", "swiper", "current", "autoplay", "start", "stop", "className", "<PERSON><PERSON><PERSON><PERSON>", "children", "length", "ref", "modules", "delay", "disableOnInteraction", "loop", "onSlideChange", "realIndex", "map", "slide", "style", "backgroundImage", "concat", "image_mobile", "image", "slideContent", "new", "innerNew", "sale", "innerSele", "title", "description", "link", "to", "id", "progressContainer", "_", "i", "dot", "active", "dotFill", "animationDuration", "pauseBtn", "onClick", "role", "size"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/header-slider/HeroSliderGWM.jsx"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Autoplay } from 'swiper/modules';\nimport { BsPlayFill, BsPauseFill } from 'react-icons/bs';\nimport 'swiper/css';\nimport SkeletonSlide from './SkeletonSlide';\n\nimport styles from './header.module.css'; // CSS-модуль\n\nconst SLIDE_DURATION = 5000;\nconst API_URL = 'https://api.gwm.tj/api/v1/sliders';\n\nconst HeroSliderGWM = () => {\n  const swiperRef = useRef(null);\n  const [slides, setSlides] = useState([]);\n  const [activeIndex, setActiveIndex] = useState(0);\n  const [paused, setPaused] = useState(false);\n\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 760);\n\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 760);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  useEffect(() => {\n    const fetchSlides = async () => {\n      try {\n        const response = await fetch(API_URL);\n        const data = await response.json();\n        setSlides(Array.isArray(data.sliders) ? data.sliders : []);\n      } catch (error) {\n        setSlides([]);\n      }\n    };\n\n    fetchSlides();\n  }, []);\n\n  const handlePauseToggle = () => {\n    const swiper = swiperRef.current?.swiper;\n    if (!swiper) return;\n\n    paused ? swiper.autoplay.start() : swiper.autoplay.stop();\n    setPaused(!paused);\n  };\n\n  return (\n    <header className={styles.heroSlider}>\n      {slides.length > 0 ? (\n        <>\n          <Swiper\n            ref={swiperRef}\n            modules={[Autoplay]}\n            autoplay={{ delay: SLIDE_DURATION, disableOnInteraction: false }}\n            loop\n            onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}\n          >\n            {slides.map((slide) => (\n              <SwiperSlide key={slide.id}>\n                <div\n                  className={styles.slide}\n                  style={{\n                    backgroundImage: `url(${isMobile ? slide.image_mobile : slide.image\n                      })`,\n                  }}\n                >\n                  <div className=\"container\">\n                    <div className={styles.slideContent}>\n                      {slide.new && (\n                        <div className={styles.innerNew}>{slide.new}</div>\n                      )}\n                      {slide.sale && (\n                        <div className={styles.innerSele}>{slide.sale}</div>\n                      )}\n                      {slide.title && <h1>{slide.title}</h1>}\n                      {slide.description && (\n                        <div className={styles.description}>\n                          {slide.description}\n                        </div>\n                      )}\n                      {slide.link && (\n                        <Link to={`/${slide.link}`}>\n                          <button className=\"button-white\">\n                            <span>Узнать больше</span>\n                          </button>\n                        </Link>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </SwiperSlide>\n            ))}\n          </Swiper>\n\n          <div className={styles.progressContainer}>\n            {slides.map((_, i) => (\n              <div\n                key={i}\n                className={`${styles.dot} ${i === activeIndex ? styles.active : ''\n                  }`}\n              >\n                {i === activeIndex && !paused && (\n                  <div\n                    className={styles.dotFill}\n                    style={{ animationDuration: `${SLIDE_DURATION}ms` }}\n                  />\n                )}\n              </div>\n            ))}\n            <button\n              className={styles.pauseBtn}\n              onClick={handlePauseToggle}\n              aria-label={\n                paused ? 'Воспроизвести слайдер' : 'Поставить на паузу'\n              }\n              role=\"button\"\n            >\n              {paused ? <BsPlayFill size={20} /> : <BsPauseFill size={20} />}\n            </button>\n          </div>\n        </>\n      ) : (\n        <>\n          <SkeletonSlide />\n        </>\n      )}\n    </header>\n  );\n};\n\nexport default HeroSliderGWM;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,MAAM,CAAEC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,UAAU,CAAEC,WAAW,KAAQ,gBAAgB,CACxD,MAAO,YAAY,CACnB,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAE3C,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1C,KAAM,CAAAC,cAAc,CAAG,IAAI,CAC3B,KAAM,CAAAC,OAAO,CAAG,mCAAmC,CAEnD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,SAAS,CAAGpB,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAACqB,MAAM,CAAEC,SAAS,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACwB,MAAM,CAAEC,SAAS,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAAC0B,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAC4B,MAAM,CAACC,UAAU,CAAG,GAAG,CAAC,CAEjE5B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6B,YAAY,CAAGA,CAAA,GAAM,CACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,CAAG,GAAG,CAAC,CACtC,CAAC,CAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,CAAED,YAAY,CAAC,CAC/C,MAAO,IAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CACjE,CAAC,CAAE,EAAE,CAAC,CAEN7B,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAClB,OAAO,CAAC,CACrC,KAAM,CAAAmB,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClChB,SAAS,CAACiB,KAAK,CAACC,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC,CAAGJ,IAAI,CAACI,OAAO,CAAG,EAAE,CAAC,CAC5D,CAAE,MAAOC,KAAK,CAAE,CACdpB,SAAS,CAAC,EAAE,CAAC,CACf,CACF,CAAC,CAEDY,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,iBAAiB,CAAGA,CAAA,GAAM,KAAAC,kBAAA,CAC9B,KAAM,CAAAC,MAAM,EAAAD,kBAAA,CAAGxB,SAAS,CAAC0B,OAAO,UAAAF,kBAAA,iBAAjBA,kBAAA,CAAmBC,MAAM,CACxC,GAAI,CAACA,MAAM,CAAE,OAEbpB,MAAM,CAAGoB,MAAM,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAGH,MAAM,CAACE,QAAQ,CAACE,IAAI,CAAC,CAAC,CACzDvB,SAAS,CAAC,CAACD,MAAM,CAAC,CACpB,CAAC,CAED,mBACEb,IAAA,WAAQsC,SAAS,CAAExC,MAAM,CAACyC,UAAW,CAAAC,QAAA,CAClC/B,MAAM,CAACgC,MAAM,CAAG,CAAC,cAChBvC,KAAA,CAAAE,SAAA,EAAAoC,QAAA,eACExC,IAAA,CAACR,MAAM,EACLkD,GAAG,CAAElC,SAAU,CACfmC,OAAO,CAAE,CAACjD,QAAQ,CAAE,CACpByC,QAAQ,CAAE,CAAES,KAAK,CAAEvC,cAAc,CAAEwC,oBAAoB,CAAE,KAAM,CAAE,CACjEC,IAAI,MACJC,aAAa,CAAGd,MAAM,EAAKrB,cAAc,CAACqB,MAAM,CAACe,SAAS,CAAE,CAAAR,QAAA,CAE3D/B,MAAM,CAACwC,GAAG,CAAEC,KAAK,eAChBlD,IAAA,CAACP,WAAW,EAAA+C,QAAA,cACVxC,IAAA,QACEsC,SAAS,CAAExC,MAAM,CAACoD,KAAM,CACxBC,KAAK,CAAE,CACLC,eAAe,QAAAC,MAAA,CAAStC,QAAQ,CAAGmC,KAAK,CAACI,YAAY,CAAGJ,KAAK,CAACK,KAAK,KAErE,CAAE,CAAAf,QAAA,cAEFxC,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBtC,KAAA,QAAKoC,SAAS,CAAExC,MAAM,CAAC0D,YAAa,CAAAhB,QAAA,EACjCU,KAAK,CAACO,GAAG,eACRzD,IAAA,QAAKsC,SAAS,CAAExC,MAAM,CAAC4D,QAAS,CAAAlB,QAAA,CAAEU,KAAK,CAACO,GAAG,CAAM,CAClD,CACAP,KAAK,CAACS,IAAI,eACT3D,IAAA,QAAKsC,SAAS,CAAExC,MAAM,CAAC8D,SAAU,CAAApB,QAAA,CAAEU,KAAK,CAACS,IAAI,CAAM,CACpD,CACAT,KAAK,CAACW,KAAK,eAAI7D,IAAA,OAAAwC,QAAA,CAAKU,KAAK,CAACW,KAAK,CAAK,CAAC,CACrCX,KAAK,CAACY,WAAW,eAChB9D,IAAA,QAAKsC,SAAS,CAAExC,MAAM,CAACgE,WAAY,CAAAtB,QAAA,CAChCU,KAAK,CAACY,WAAW,CACf,CACN,CACAZ,KAAK,CAACa,IAAI,eACT/D,IAAA,CAACT,IAAI,EAACyE,EAAE,KAAAX,MAAA,CAAMH,KAAK,CAACa,IAAI,CAAG,CAAAvB,QAAA,cACzBxC,IAAA,WAAQsC,SAAS,CAAC,cAAc,CAAAE,QAAA,cAC9BxC,IAAA,SAAAwC,QAAA,CAAM,2EAAa,CAAM,CAAC,CACpB,CAAC,CACL,CACP,EACE,CAAC,CACH,CAAC,CACH,CAAC,EA/BUU,KAAK,CAACe,EAgCX,CACd,CAAC,CACI,CAAC,cAET/D,KAAA,QAAKoC,SAAS,CAAExC,MAAM,CAACoE,iBAAkB,CAAA1B,QAAA,EACtC/B,MAAM,CAACwC,GAAG,CAAC,CAACkB,CAAC,CAAEC,CAAC,gBACfpE,IAAA,QAEEsC,SAAS,IAAAe,MAAA,CAAKvD,MAAM,CAACuE,GAAG,MAAAhB,MAAA,CAAIe,CAAC,GAAKzD,WAAW,CAAGb,MAAM,CAACwE,MAAM,CAAG,EAAE,CAC7D,CAAA9B,QAAA,CAEJ4B,CAAC,GAAKzD,WAAW,EAAI,CAACE,MAAM,eAC3Bb,IAAA,QACEsC,SAAS,CAAExC,MAAM,CAACyE,OAAQ,CAC1BpB,KAAK,CAAE,CAAEqB,iBAAiB,IAAAnB,MAAA,CAAKhD,cAAc,MAAK,CAAE,CACrD,CACF,EATI+D,CAUF,CACN,CAAC,cACFpE,IAAA,WACEsC,SAAS,CAAExC,MAAM,CAAC2E,QAAS,CAC3BC,OAAO,CAAE3C,iBAAkB,CAC3B,aACElB,MAAM,CAAG,uBAAuB,CAAG,oBACpC,CACD8D,IAAI,CAAC,QAAQ,CAAAnC,QAAA,CAEZ3B,MAAM,cAAGb,IAAA,CAACL,UAAU,EAACiF,IAAI,CAAE,EAAG,CAAE,CAAC,cAAG5E,IAAA,CAACJ,WAAW,EAACgF,IAAI,CAAE,EAAG,CAAE,CAAC,CACxD,CAAC,EACN,CAAC,EACN,CAAC,cAEH5E,IAAA,CAAAI,SAAA,EAAAoC,QAAA,cACExC,IAAA,CAACH,aAAa,GAAE,CAAC,CACjB,CACH,CACK,CAAC,CAEb,CAAC,CAED,cAAe,CAAAU,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}