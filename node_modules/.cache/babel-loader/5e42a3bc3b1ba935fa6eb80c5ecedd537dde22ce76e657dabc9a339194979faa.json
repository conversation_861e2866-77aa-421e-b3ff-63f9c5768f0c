{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/components/form/OwnersForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../../components/Notification/Notification';\nimport { sanitizeAndValidateForm, formRateLimiter } from '../../../../utils/validation';\nimport { submitFeedback } from '../../../../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst options = [{\n  value: 'вопрос',\n  label: 'Вопрос'\n}, {\n  value: 'жалоба',\n  label: 'Жалоба'\n}, {\n  value: 'предложение',\n  label: 'Предложение'\n}, {\n  value: 'Обслуживание и ремонт',\n  label: 'Обслуживание и ремонт'\n}, {\n  value: 'Послепродажное обслуживание',\n  label: 'Послепродажное обслуживание'\n}, {\n  value: 'Другое',\n  label: 'Другое'\n}];\nconst OwnersForm = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false\n  });\n  const [notification, setNotification] = useState({\n    message: '',\n    type: ''\n  });\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification({\n      message: '',\n      type: ''\n    }), 3000);\n  }, []);\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: {\n      _: /\\d/\n    }\n  });\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSelectChange = selectedOption => {\n    setFormData(prev => ({\n      ...prev,\n      topic: selectedOption\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification('Слишком много попыток отправки. Подождите минуту.', 'error');\n      return;\n    }\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n    try {\n      var _sanitizedData$topic;\n      // Validate and sanitize form data\n      const {\n        data: sanitizedData,\n        errors,\n        isValid\n      } = sanitizeAndValidateForm(formData);\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: ((_sanitizedData$topic = sanitizedData.topic) === null || _sanitizedData$topic === void 0 ? void 0 : _sanitizedData$topic.value) || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: 'owners' // Specify this is an owners form\n      };\n\n      // Submit form using the same API as the main form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.', 'success');\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false\n      });\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Notification, {\n      message: notification.message,\n      type: notification.type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), ' ', /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: styles.form,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"firstName\",\n          placeholder: \"\\u0418\\u043C\\u044F *\",\n          className: `${styles.input} ${formErrors.firstName ? styles.inputError : ''}`,\n          value: formData.firstName,\n          required: true,\n          onChange: handleChange,\n          minLength: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"lastName\",\n          placeholder: \"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\",\n          className: `${styles.input} ${formErrors.lastName ? styles.inputError : ''}`,\n          value: formData.lastName,\n          onChange: handleChange,\n          minLength: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"tel\",\n          name: \"phone\",\n          placeholder: \"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430 *\",\n          className: `${styles.input} ${formErrors.phone ? styles.inputError : ''}`,\n          value: formData.phone,\n          required: true,\n          ref: phoneRef,\n          onChange: handleChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"message\",\n          placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0412\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435\",\n          className: `${styles.input} ${formErrors.message ? styles.inputError : ''}`,\n          value: formData.message,\n          onChange: handleChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.selectWrapper,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            required: true,\n            classNamePrefix: \"react-select\",\n            options: options,\n            placeholder: \"\\u0422\\u0435\\u043C\\u0430 *\",\n            onChange: handleSelectChange,\n            value: formData.topic,\n            isSearchable: false,\n            styles: {\n              control: (provided, state) => ({\n                ...provided,\n                width: '100%',\n                paddingLeft: '8px',\n                paddingRight: '0px',\n                height: '58px',\n                borderRadius: '2px',\n                border: `1px solid ${state.isFocused ? '#d7000f' : '#8e8e93'}`,\n                fontSize: '16px',\n                boxShadow: 'none',\n                backgroundColor: '#fff',\n                transition: 'all 0.2s ease',\n                '&:hover': {\n                  borderColor: state.isFocused ? '#d7000f' : '#8e8e93'\n                }\n              }),\n              container: provided => ({\n                ...provided,\n                width: '100%'\n              }),\n              placeholder: provided => ({\n                ...provided,\n                color: '#888'\n              }),\n              singleValue: provided => ({\n                ...provided,\n                color: '#000'\n              }),\n              indicatorsContainer: provided => ({\n                ...provided,\n                paddingRight: '10px'\n              }),\n              dropdownIndicator: provided => ({\n                ...provided,\n                color: '#8e8e93'\n              }),\n              menu: provided => ({\n                ...provided,\n                border: '1px solid #8e8e93',\n                borderRadius: '2px',\n                boxShadow: 'none',\n                marginTop: '4px',\n                backgroundColor: '#fff',\n                zIndex: 9999,\n                position: 'absolute'\n              }),\n              menuList: provided => ({\n                ...provided,\n                padding: 0\n              }),\n              option: (provided, state) => ({\n                ...provided,\n                padding: '14px 16px',\n                fontSize: '14px',\n                backgroundColor: state.isFocused ? '#000' : '#fff',\n                color: state.isFocused ? '#fff' : '#000',\n                cursor: 'pointer',\n                '&:active': {\n                  backgroundColor: '#000'\n                }\n              })\n            },\n            menuPortalTarget: document.body\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: styles.checkboxWrapper,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          name: \"consent\",\n          required: true,\n          checked: formData.consent,\n          onChange: handleChange,\n          className: styles.checkbox\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0434\\u043B\\u044F \\u043F\\u0440\\u043E\\u0432\\u0435\\u0440\\u043A\\u0438/\\u043F\\u043E\\u0434\\u0442\\u0432\\u0435\\u0440\\u0436\\u0434\\u0435\\u043D\\u0438\\u044F \\u043D\\u0435\\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043A\\u0430\\u043C\\u043F\\u0430\\u043D\\u0438\\u0439 \\u043F\\u043E \\u043C\\u043E\\u0438\\u043C \\u0442\\u0440\\u0430\\u043D\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u043C \\u0441\\u0440\\u0435\\u0434\\u0441\\u0442\\u0432\\u0430\\u043C. *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: styles.button,\n        disabled: btnLoading || isSubmitting,\n        children: btnLoading ? 'Отправка...' : 'Отправить'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(OwnersForm, \"LjCFByFt+lUbKs1zazevGuHNLAQ=\", false, function () {\n  return [useMask];\n});\n_c = OwnersForm;\nexport default OwnersForm;\nvar _c;\n$RefreshReg$(_c, \"OwnersForm\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Select", "styles", "useMask", "Notification", "sanitizeAndValidateForm", "formRateLimiter", "submitFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "options", "value", "label", "OwnersForm", "_s", "formData", "setFormData", "firstName", "lastName", "phone", "topic", "message", "consent", "notification", "setNotification", "type", "btnLoading", "setBtnLoading", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "setTimeout", "phoneRef", "mask", "replacement", "_", "handleChange", "e", "name", "checked", "target", "prev", "handleSelectChange", "selectedOption", "handleSubmit", "preventDefault", "isAllowed", "_sanitizedData$topic", "data", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "formType", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "className", "form", "row", "placeholder", "input", "inputError", "required", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "ref", "selectWrapper", "classNamePrefix", "isSearchable", "control", "provided", "state", "width", "paddingLeft", "paddingRight", "height", "borderRadius", "border", "isFocused", "fontSize", "boxShadow", "backgroundColor", "transition", "borderColor", "container", "color", "singleValue", "indicatorsContainer", "dropdownIndicator", "menu", "marginTop", "zIndex", "position", "menuList", "padding", "option", "cursor", "menuPortalTarget", "document", "body", "checkboxWrapper", "checkbox", "button", "disabled", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/components/form/OwnersForm.jsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../../components/Notification/Notification';\nimport { sanitizeAndValidateForm, formRateLimiter } from '../../../../utils/validation';\nimport { submitFeedback } from '../../../../utils/api';\n\nconst options = [\n  { value: 'вопрос', label: 'Вопрос' },\n  { value: 'жалоба', label: 'Жалоба' },\n  { value: 'предложение', label: 'Предложение' },\n  { value: 'Обслуживание и ремонт', label: 'Обслуживание и ремонт' },\n  {\n    value: 'Послепродажное обслуживание',\n    label: 'Послепродажное обслуживание',\n  },\n  {\n    value: 'Другое',\n    label: 'Другое',\n  },\n];\n\nconst OwnersForm = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false,\n  });\n\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n  };\n\n  const handleSelectChange = (selectedOption) => {\n    setFormData((prev) => ({\n      ...prev,\n      topic: selectedOption,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification('Слишком много попыток отправки. Подождите минуту.', 'error');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n\n    try {\n      // Validate and sanitize form data\n      const { data: sanitizedData, errors, isValid } = sanitizeAndValidateForm(formData);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: sanitizedData.topic?.value || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: 'owners', // Specify this is an owners form\n      };\n\n      // Submit form using the same API as the main form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.', 'success');\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false,\n      });\n      setFormErrors({});\n\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />{' '}\n      <form onSubmit={handleSubmit} className={styles.form}>\n        <div className={styles.row}>\n          <input\n            type=\"text\"\n            name=\"firstName\"\n            placeholder=\"Имя *\"\n            className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n            value={formData.firstName}\n            required\n            onChange={handleChange}\n            minLength=\"3\"\n          />\n          <input\n            type=\"text\"\n            name=\"lastName\"\n            placeholder=\"Фамилия\"\n            className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n            value={formData.lastName}\n            onChange={handleChange}\n            minLength=\"3\"\n          />\n        </div>\n        <div className={styles.row}>\n          <input\n            type=\"tel\"\n            name=\"phone\"\n            placeholder=\"Номер мобильного телефона *\"\n            className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n            value={formData.phone}\n            required\n            ref={phoneRef}\n            onChange={handleChange}\n          />\n          <input\n            type=\"text\"\n            name=\"message\"\n            placeholder=\"Введите Ваше сообщение\"\n            className={`${styles.input} ${formErrors.message ? styles.inputError : ''}`}\n            value={formData.message}\n            onChange={handleChange}\n          />\n        </div>\n        <div className={styles.row}>\n          <div className={styles.selectWrapper}>\n            <Select\n            required\n              classNamePrefix=\"react-select\"\n              options={options}\n              placeholder=\"Тема *\"\n              onChange={handleSelectChange}\n              value={formData.topic}\n              isSearchable={false}\n              styles={{\n                control: (provided, state) => ({\n                  ...provided,\n                  width: '100%',\n                  paddingLeft: '8px',\n                  paddingRight: '0px',\n                  height: '58px',\n                  borderRadius: '2px',\n                  border: `1px solid ${\n                    state.isFocused ? '#d7000f' : '#8e8e93'\n                  }`,\n                  fontSize: '16px',\n                  boxShadow: 'none',\n                  backgroundColor: '#fff',\n\n                  transition: 'all 0.2s ease',\n                  '&:hover': {\n                    borderColor: state.isFocused ? '#d7000f' : '#8e8e93',\n                  },\n                }),\n                container: (provided) => ({\n                  ...provided,\n                  width: '100%',\n                }),\n                placeholder: (provided) => ({\n                  ...provided,\n                  color: '#888',\n                }),\n                singleValue: (provided) => ({\n                  ...provided,\n                  color: '#000',\n                }),\n                indicatorsContainer: (provided) => ({\n                  ...provided,\n                  paddingRight: '10px',\n                }),\n                dropdownIndicator: (provided) => ({\n                  ...provided,\n                  color: '#8e8e93',\n                }),\n                menu: (provided) => ({\n                  ...provided,\n                  border: '1px solid #8e8e93',\n                  borderRadius: '2px',\n                  boxShadow: 'none',\n                  marginTop: '4px',\n                  backgroundColor: '#fff',\n                  zIndex: 9999,\n                  position: 'absolute',\n                }),\n                menuList: (provided) => ({\n                  ...provided,\n                  padding: 0,\n                }),\n                option: (provided, state) => ({\n                  ...provided,\n                  padding: '14px 16px',\n                  fontSize: '14px',\n                  backgroundColor: state.isFocused ? '#000' : '#fff',\n                  color: state.isFocused ? '#fff' : '#000',\n                  cursor: 'pointer',\n                  '&:active': {\n                    backgroundColor: '#000',\n                  },\n                }),\n              }}\n              menuPortalTarget={document.body}\n            />\n          </div>\n        </div>\n        <label className={styles.checkboxWrapper}>\n          <input\n            type=\"checkbox\"\n            name=\"consent\"\n            required\n            checked={formData.consent}\n            onChange={handleChange}\n            className={styles.checkbox}\n          />\n          <span>\n            Я даю согласие на обработку моих персональных данных для\n            проверки/подтверждения невыполненных кампаний по моим транспортным\n            средствам. *\n          </span>\n        </label>\n        <button type=\"submit\" className={styles.button} disabled={btnLoading || isSubmitting}>\n          {btnLoading ? 'Отправка...' : 'Отправить'}\n        </button>\n      </form>\n    </>\n  );\n};\n\nexport default OwnersForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,kDAAkD;AAC3E,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,8BAA8B;AACvF,SAASC,cAAc,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,OAAO,GAAG,CACd;EAAEC,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC9C;EAAED,KAAK,EAAE,uBAAuB;EAAEC,KAAK,EAAE;AAAwB,CAAC,EAClE;EACED,KAAK,EAAE,6BAA6B;EACpCC,KAAK,EAAE;AACT,CAAC,EACD;EACED,KAAK,EAAE,QAAQ;EACfC,KAAK,EAAE;AACT,CAAC,CACF;AAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC;IAAEwB,OAAO,EAAE,EAAE;IAAEI,IAAI,EAAE;EAAG,CAAC,CAAC;EAC3E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;AACF;AACA;AACA;AACA;EACE,MAAMmC,gBAAgB,GAAGlC,WAAW,CAAC,CAACuB,OAAO,EAAEI,IAAI,GAAG,SAAS,KAAK;IAClED,eAAe,CAAC;MAAEH,OAAO;MAAEI;IAAK,CAAC,CAAC;IAClCQ,UAAU,CAAC,MAAMT,eAAe,CAAC;MAAEH,OAAO,EAAE,EAAE;MAAEI,IAAI,EAAE;IAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,QAAQ,GAAGjC,OAAO,CAAC;IACvBkC,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;MAAEC,CAAC,EAAE;IAAK;EACzB,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAE7B,KAAK;MAAEc,IAAI;MAAEgB;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/C1B,WAAW,CAAE2B,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGf,IAAI,KAAK,UAAU,GAAGgB,OAAO,GAAG9B;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiC,kBAAkB,GAAIC,cAAc,IAAK;IAC7C7B,WAAW,CAAE2B,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvB,KAAK,EAAEyB;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;;IAElB;IACA,IAAIjB,YAAY,EAAE;;IAElB;IACA,IAAI,CAAC1B,eAAe,CAAC4C,SAAS,CAAC,CAAC,EAAE;MAChChB,gBAAgB,CAAC,mDAAmD,EAAE,OAAO,CAAC;MAC9E;IACF;IAEAD,eAAe,CAAC,IAAI,CAAC;IACrBJ,aAAa,CAAC,IAAI,CAAC;IACnBE,aAAa,CAAC,CAAC,CAAC,CAAC;IAEjB,IAAI;MAAA,IAAAoB,oBAAA;MACF;MACA,MAAM;QAAEC,IAAI,EAAEC,aAAa;QAAEC,MAAM;QAAEC;MAAQ,CAAC,GAAGlD,uBAAuB,CAACY,QAAQ,CAAC;MAElF,IAAI,CAACsC,OAAO,EAAE;QACZxB,aAAa,CAACuB,MAAM,CAAC;QACrBpB,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,CAAC;QACjE;MACF;;MAEA;MACA,MAAMsB,OAAO,GAAG;QACdrC,SAAS,EAAEkC,aAAa,CAAClC,SAAS;QAClCC,QAAQ,EAAEiC,aAAa,CAACjC,QAAQ;QAChCC,KAAK,EAAEgC,aAAa,CAAChC,KAAK;QAC1BC,KAAK,EAAE,EAAA6B,oBAAA,GAAAE,aAAa,CAAC/B,KAAK,cAAA6B,oBAAA,uBAAnBA,oBAAA,CAAqBtC,KAAK,KAAI,EAAE;QACvCU,OAAO,EAAE8B,aAAa,CAAC9B,OAAO;QAC9BC,OAAO,EAAE6B,aAAa,CAAC7B,OAAO;QAC9BiC,QAAQ,EAAE,QAAQ,CAAE;MACtB,CAAC;;MAED;MACA,MAAMlD,cAAc,CAACiD,OAAO,CAAC;;MAE7B;MACAtB,gBAAgB,CAAC,iEAAiE,EAAE,SAAS,CAAC;;MAE9F;MACAhB,WAAW,CAAC;QACVC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;MACX,CAAC,CAAC;MACFO,aAAa,CAAC,CAAC,CAAC,CAAC;IAEnB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdxB,gBAAgB,CAACwB,KAAK,CAACnC,OAAO,EAAE,OAAO,CAAC;IAC1C,CAAC,SAAS;MACRM,aAAa,CAAC,KAAK,CAAC;MACpBI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACExB,OAAA,CAAAE,SAAA;IAAAgD,QAAA,gBACElD,OAAA,CAACL,YAAY;MAACmB,OAAO,EAAEE,YAAY,CAACF,OAAQ;MAACI,IAAI,EAAEF,YAAY,CAACE;IAAK;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAAC,GAAG,eAC5EtD,OAAA;MAAMuD,QAAQ,EAAEhB,YAAa;MAACiB,SAAS,EAAE/D,MAAM,CAACgE,IAAK;MAAAP,QAAA,gBACnDlD,OAAA;QAAKwD,SAAS,EAAE/D,MAAM,CAACiE,GAAI;QAAAR,QAAA,gBACzBlD,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXe,IAAI,EAAC,WAAW;UAChB0B,WAAW,EAAC,sBAAO;UACnBH,SAAS,EAAE,GAAG/D,MAAM,CAACmE,KAAK,IAAIvC,UAAU,CAACX,SAAS,GAAGjB,MAAM,CAACoE,UAAU,GAAG,EAAE,EAAG;UAC9EzD,KAAK,EAAEI,QAAQ,CAACE,SAAU;UAC1BoD,QAAQ;UACRC,QAAQ,EAAEhC,YAAa;UACvBiC,SAAS,EAAC;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACFtD,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXe,IAAI,EAAC,UAAU;UACf0B,WAAW,EAAC,4CAAS;UACrBH,SAAS,EAAE,GAAG/D,MAAM,CAACmE,KAAK,IAAIvC,UAAU,CAACV,QAAQ,GAAGlB,MAAM,CAACoE,UAAU,GAAG,EAAE,EAAG;UAC7EzD,KAAK,EAAEI,QAAQ,CAACG,QAAS;UACzBoD,QAAQ,EAAEhC,YAAa;UACvBiC,SAAS,EAAC;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtD,OAAA;QAAKwD,SAAS,EAAE/D,MAAM,CAACiE,GAAI;QAAAR,QAAA,gBACzBlD,OAAA;UACEkB,IAAI,EAAC,KAAK;UACVe,IAAI,EAAC,OAAO;UACZ0B,WAAW,EAAC,gJAA6B;UACzCH,SAAS,EAAE,GAAG/D,MAAM,CAACmE,KAAK,IAAIvC,UAAU,CAACT,KAAK,GAAGnB,MAAM,CAACoE,UAAU,GAAG,EAAE,EAAG;UAC1EzD,KAAK,EAAEI,QAAQ,CAACI,KAAM;UACtBkD,QAAQ;UACRG,GAAG,EAAEtC,QAAS;UACdoC,QAAQ,EAAEhC;QAAa;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFtD,OAAA;UACEkB,IAAI,EAAC,MAAM;UACXe,IAAI,EAAC,SAAS;UACd0B,WAAW,EAAC,4HAAwB;UACpCH,SAAS,EAAE,GAAG/D,MAAM,CAACmE,KAAK,IAAIvC,UAAU,CAACP,OAAO,GAAGrB,MAAM,CAACoE,UAAU,GAAG,EAAE,EAAG;UAC5EzD,KAAK,EAAEI,QAAQ,CAACM,OAAQ;UACxBiD,QAAQ,EAAEhC;QAAa;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtD,OAAA;QAAKwD,SAAS,EAAE/D,MAAM,CAACiE,GAAI;QAAAR,QAAA,eACzBlD,OAAA;UAAKwD,SAAS,EAAE/D,MAAM,CAACyE,aAAc;UAAAhB,QAAA,eACnClD,OAAA,CAACR,MAAM;YACPsE,QAAQ;YACNK,eAAe,EAAC,cAAc;YAC9BhE,OAAO,EAAEA,OAAQ;YACjBwD,WAAW,EAAC,4BAAQ;YACpBI,QAAQ,EAAE1B,kBAAmB;YAC7BjC,KAAK,EAAEI,QAAQ,CAACK,KAAM;YACtBuD,YAAY,EAAE,KAAM;YACpB3E,MAAM,EAAE;cACN4E,OAAO,EAAEA,CAACC,QAAQ,EAAEC,KAAK,MAAM;gBAC7B,GAAGD,QAAQ;gBACXE,KAAK,EAAE,MAAM;gBACbC,WAAW,EAAE,KAAK;gBAClBC,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,KAAK;gBACnBC,MAAM,EAAE,aACNN,KAAK,CAACO,SAAS,GAAG,SAAS,GAAG,SAAS,EACvC;gBACFC,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,eAAe,EAAE,MAAM;gBAEvBC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACTC,WAAW,EAAEZ,KAAK,CAACO,SAAS,GAAG,SAAS,GAAG;gBAC7C;cACF,CAAC,CAAC;cACFM,SAAS,EAAGd,QAAQ,KAAM;gBACxB,GAAGA,QAAQ;gBACXE,KAAK,EAAE;cACT,CAAC,CAAC;cACFb,WAAW,EAAGW,QAAQ,KAAM;gBAC1B,GAAGA,QAAQ;gBACXe,KAAK,EAAE;cACT,CAAC,CAAC;cACFC,WAAW,EAAGhB,QAAQ,KAAM;gBAC1B,GAAGA,QAAQ;gBACXe,KAAK,EAAE;cACT,CAAC,CAAC;cACFE,mBAAmB,EAAGjB,QAAQ,KAAM;gBAClC,GAAGA,QAAQ;gBACXI,YAAY,EAAE;cAChB,CAAC,CAAC;cACFc,iBAAiB,EAAGlB,QAAQ,KAAM;gBAChC,GAAGA,QAAQ;gBACXe,KAAK,EAAE;cACT,CAAC,CAAC;cACFI,IAAI,EAAGnB,QAAQ,KAAM;gBACnB,GAAGA,QAAQ;gBACXO,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,KAAK;gBACnBI,SAAS,EAAE,MAAM;gBACjBU,SAAS,EAAE,KAAK;gBAChBT,eAAe,EAAE,MAAM;gBACvBU,MAAM,EAAE,IAAI;gBACZC,QAAQ,EAAE;cACZ,CAAC,CAAC;cACFC,QAAQ,EAAGvB,QAAQ,KAAM;gBACvB,GAAGA,QAAQ;gBACXwB,OAAO,EAAE;cACX,CAAC,CAAC;cACFC,MAAM,EAAEA,CAACzB,QAAQ,EAAEC,KAAK,MAAM;gBAC5B,GAAGD,QAAQ;gBACXwB,OAAO,EAAE,WAAW;gBACpBf,QAAQ,EAAE,MAAM;gBAChBE,eAAe,EAAEV,KAAK,CAACO,SAAS,GAAG,MAAM,GAAG,MAAM;gBAClDO,KAAK,EAAEd,KAAK,CAACO,SAAS,GAAG,MAAM,GAAG,MAAM;gBACxCkB,MAAM,EAAE,SAAS;gBACjB,UAAU,EAAE;kBACVf,eAAe,EAAE;gBACnB;cACF,CAAC;YACH,CAAE;YACFgB,gBAAgB,EAAEC,QAAQ,CAACC;UAAK;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtD,OAAA;QAAOwD,SAAS,EAAE/D,MAAM,CAAC2G,eAAgB;QAAAlD,QAAA,gBACvClD,OAAA;UACEkB,IAAI,EAAC,UAAU;UACfe,IAAI,EAAC,SAAS;UACd6B,QAAQ;UACR5B,OAAO,EAAE1B,QAAQ,CAACO,OAAQ;UAC1BgD,QAAQ,EAAEhC,YAAa;UACvByB,SAAS,EAAE/D,MAAM,CAAC4G;QAAS;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFtD,OAAA;UAAAkD,QAAA,EAAM;QAIN;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACRtD,OAAA;QAAQkB,IAAI,EAAC,QAAQ;QAACsC,SAAS,EAAE/D,MAAM,CAAC6G,MAAO;QAACC,QAAQ,EAAEpF,UAAU,IAAII,YAAa;QAAA2B,QAAA,EAClF/B,UAAU,GAAG,aAAa,GAAG;MAAW;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAAC/C,EAAA,CA7PID,UAAU;EAAA,QAyBGZ,OAAO;AAAA;AAAA8G,EAAA,GAzBpBlG,UAAU;AA+PhB,eAAeA,UAAU;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}