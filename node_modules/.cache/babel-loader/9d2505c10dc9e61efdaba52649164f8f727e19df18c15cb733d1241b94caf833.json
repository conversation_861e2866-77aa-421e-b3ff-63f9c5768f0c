{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { animations } from '../../motion/features/animations.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domMin = _objectSpread({\n  renderer: createDomVisualElement\n}, animations);\nexport { domMin };", "map": {"version": 3, "names": ["animations", "createDomVisualElement", "dom<PERSON>in", "_objectSpread", "renderer"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/features-min.mjs"], "sourcesContent": ["import { animations } from '../../motion/features/animations.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domMin = {\n    renderer: createDomVisualElement,\n    ...animations,\n};\n\nexport { domMin };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,sCAAsC;AACjE,SAASC,sBAAsB,QAAQ,6BAA6B;;AAEpE;AACA;AACA;AACA,MAAMC,MAAM,GAAAC,aAAA;EACRC,QAAQ,EAAEH;AAAsB,GAC7BD,UAAU,CAChB;AAED,SAASE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}