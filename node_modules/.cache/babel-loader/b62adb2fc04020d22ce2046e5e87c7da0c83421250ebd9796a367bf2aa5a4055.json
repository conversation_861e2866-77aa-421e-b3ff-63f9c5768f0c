{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/Owners.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styles from './owners.module.css';\nimport Sidebar from './components/sidebar/Sidebar';\nimport img from '../../asset/imgs/owners/BANNER-HOME-FAMILIA_1800x600.webp';\nimport OwnersForm from './components/form/OwnersForm';\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Owners = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.layout,\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: styles.main,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.mainContainer,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                \"data-aos\": \"fade-up\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u0412\\u041B\\u0410\\u0414\\u0415\\u041B\\u042C\\u0426\\u0410\\u041C GWM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"100\",\n                className: styles.redLine\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img,\n                alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                className: styles.banner,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"150\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.textContent,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"150\",\n                  children: \"\\u041C\\u044B \\u0433\\u043E\\u0440\\u0434\\u0438\\u043C\\u0441\\u044F \\u0442\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043E\\u0440\\u0438\\u0435\\u043D\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u044B \\u043D\\u0430 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430, \\u0438 \\u0441\\u0447\\u0438\\u0442\\u0430\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043C\\u043E\\u043C\\u0435\\u043D\\u0442, \\u043A\\u043E\\u0433\\u0434\\u0430 \\u0432\\u044B \\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0438\\u0442\\u0435\\u0441\\u044C \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u043C \\u0441\\u0432\\u043E\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F, \\u2014 \\u044D\\u0442\\u043E \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u043D\\u0430\\u0447\\u0430\\u043B\\u043E \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043F\\u0443\\u0442\\u0438 \\u043A \\u043E\\u043F\\u044B\\u0442\\u0443 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u043C. \\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u043C\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u043C \\u043A\\u043E\\u043C\\u043F\\u043B\\u0435\\u043A\\u0441\\u043D\\u0443\\u044E \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0443 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u043C\\u0441\\u044F \\u0441\\u0434\\u0435\\u043B\\u0430\\u0442\\u044C \\u0432\\u0441\\u0451 \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0435, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u0442\\u044C, \\u0447\\u0442\\u043E \\u0432\\u0441\\u0435 \\u043D\\u0430\\u0448\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u044B \\u043F\\u043E\\u043B\\u0443\\u0447\\u0430\\u0442 \\u043D\\u0430\\u0438\\u043B\\u0443\\u0447\\u0448\\u0443\\u044E \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C \\u0432 \\u043B\\u044E\\u0431\\u043E\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"200\",\n                  children: [\"\\u041C\\u044B \\u0441\\u0442\\u0440\\u0430\\u0441\\u0442\\u043D\\u043E \\u0441\\u043E\\u0441\\u0440\\u0435\\u0434\\u043E\\u0442\\u043E\\u0447\\u0435\\u043D\\u044B \\u043D\\u0430 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430\\u0445 \\u0438 \\u0438\\u0445 \\u043E\\u043F\\u044B\\u0442\\u0435 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F, \\u0433\\u0434\\u0435 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"GWM CARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 37\n                  }, this), \" \\u2014 \\u044D\\u0442\\u043E \\u043D\\u0430\\u0448 \\u0431\\u0440\\u0435\\u043D\\u0434 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0439 \\u0444\\u043E\\u0440\\u043C\\u0438\\u0440\\u0443\\u0435\\u0442 \\u043D\\u0430\\u0448\\u0443 \\u0441\\u0442\\u0440\\u0443\\u043A\\u0442\\u0443\\u0440\\u0443 \\u043E\\u043F\\u044B\\u0442\\u0430 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u043C \\u0434\\u043B\\u044F \\u0443\\u043A\\u0440\\u0435\\u043F\\u043B\\u0435\\u043D\\u0438\\u044F \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430 \\u0438 \\u0434\\u0438\\u0444\\u0444\\u0435\\u0440\\u0435\\u043D\\u0446\\u0438\\u0430\\u0446\\u0438\\u0438 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u0442\\u0432 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439 \\u0438 \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(OwnersForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Owners, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Owners;\nexport default Owners;\nvar _c;\n$RefreshReg$(_c, \"Owners\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "Sidebar", "img", "OwnersForm", "AOS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Owners", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "main", "mainContainer", "redLine", "src", "alt", "banner", "textContent", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Owners.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './owners.module.css';\nimport Sidebar from './components/sidebar/Sidebar';\nimport img from '../../asset/imgs/owners/BANNER-HOME-FAMILIA_1800x600.webp';\nimport OwnersForm from './components/form/OwnersForm';\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Owners = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                     <strong>ВЛАДЕЛЬЦАМ GWM</strong>\n                  </h1>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                    className={styles.redLine}\n                  ></i>\n                  <img\n                    src={img}\n                    alt=\"Баннер владельцев GWM\"\n                    className={styles.banner}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                  />\n                  <div className={styles.textContent}>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                      Мы гордимся тем, что ориентированы на клиента, и считаем,\n                      что момент, когда вы становитесь владельцем своего\n                      автомобиля, — это только начало вашего пути к опыту\n                      владения брендом. Для полного спокойствия мы предлагаем\n                      комплексную поддержку послепродажного обслуживания и\n                      всегда стремимся сделать всё возможное, чтобы\n                      гарантировать, что все наши клиенты получат наилучшую\n                      помощь в любое время.\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Мы страстно сосредоточены на наших клиентах и их опыте\n                      владения, где <strong>GWM CARE</strong> — это наш бренд\n                      послепродажного обслуживания, который формирует нашу\n                      структуру опыта владения клиентом для укрепления\n                      обслуживания, бренда и дифференциации наших обязательств\n                      послепродажного обслуживания, ценностных предложений и\n                      спокойствия владения.\n                    </p>\n                  </div>\n                  <OwnersForm />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Owners;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,GAAG,MAAM,2DAA2D;AAC3E,OAAOC,UAAU,MAAM,8BAA8B;AACrD;AACA,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdM,GAAG,CAACS,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;IAEzC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBnB,OAAA;UAAKoB,SAAS,EAAE1B,MAAM,CAAC+B,MAAO;UAAAN,QAAA,gBAC5BnB,OAAA,CAACL,OAAO;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXxB,OAAA;YAAMoB,SAAS,EAAE1B,MAAM,CAACgC,IAAK;YAAAP,QAAA,eAC3BnB,OAAA;cAAKoB,SAAS,EAAE1B,MAAM,CAACiC,aAAc;cAAAR,QAAA,gBACnCnB,OAAA;gBAAI,YAAS,SAAS;gBAAAmB,QAAA,eACnBnB,OAAA;kBAAAmB,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACLxB,OAAA;gBACE,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBACpBoB,SAAS,EAAE1B,MAAM,CAACkC;cAAQ;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACLxB,OAAA;gBACE6B,GAAG,EAAEjC,GAAI;gBACTkC,GAAG,EAAC,uGAAuB;gBAC3BV,SAAS,EAAE1B,MAAM,CAACqC,MAAO;gBACzB,YAAS,SAAS;gBAClB,kBAAe;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACFxB,OAAA;gBAAKoB,SAAS,EAAE1B,MAAM,CAACsC,WAAY;gBAAAb,QAAA,gBACjCnB,OAAA;kBAAG,YAAS,SAAS;kBAAC,kBAAe,KAAK;kBAAAmB,QAAA,EAAC;gBAS3C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAG,YAAS,SAAS;kBAAC,kBAAe,KAAK;kBAAAmB,QAAA,GAAC,oWAE3B,eAAAnB,OAAA;oBAAAmB,QAAA,EAAQ;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,40CAMzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxB,OAAA,CAACH,UAAU;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CA9EID,MAAM;AAAA8B,EAAA,GAAN9B,MAAM;AAgFZ,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}