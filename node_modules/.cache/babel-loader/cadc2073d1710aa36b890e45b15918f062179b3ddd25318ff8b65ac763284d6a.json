{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Book-a-test-drive/Book-a-test-dieve.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { FaMapMarkerAlt, FaClock, FaPhoneAlt } from 'react-icons/fa';\nimport Form from '../../components/Form/Form';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BookTestDrive = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            \"data-aos\": \"fade-up\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u0417\\u0430\\u043F\\u0438\\u0441\\u0430\\u0442\\u044C\\u0441\\u044F \\u043D\\u0430 \\u0422\\u0415\\u0421\\u0422-\\u0414\\u0420\\u0410\\u0419\\u0412\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            \"data-aos\": \"fade-up\",\n            \"data-aos-delay\": \"100\",\n            className: \"redLine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u0413\\u043E\\u0442\\u043E\\u0432\\u044B \\u043B\\u0438 \\u0432\\u044B \\u0438\\u0441\\u043F\\u044B\\u0442\\u0430\\u0442\\u044C \\u043E\\u0441\\u0442\\u0440\\u044B\\u0435 \\u043E\\u0449\\u0443\\u0449\\u0435\\u043D\\u0438\\u044F \\u043E\\u0442 \\u043B\\u0438\\u043D\\u0435\\u0439\\u043A\\u0438 GWM? \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 69\n          }, this), \"\\u0417\\u0430\\u0431\\u0440\\u043E\\u043D\\u0438\\u0440\\u0443\\u0439\\u0442\\u0435 \\u0442\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432 \\u0443\\u0436\\u0435 \\u0441\\u0435\\u0433\\u043E\\u0434\\u043D\\u044F, \\u0441\\u044F\\u0434\\u044C\\u0442\\u0435 \\u0437\\u0430 \\u0440\\u0443\\u043B\\u044C \\u043E\\u0434\\u043D\\u043E\\u0433\\u043E \\u0438\\u043B\\u0438 \\u043D\\u0435\\u0441\\u043A\\u043E\\u043B\\u044C\\u043A\\u0438\\u0445 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0438 \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0439\\u0442\\u0435\\u0441\\u044C \\u0432 \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0435\\u0435 \\u043F\\u0440\\u0438\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435 \\u0441 GWM.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          formType: \"test-drive\",\n          title: \"\\u0417\\u0430\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435 \\u0444\\u043E\\u0440\\u043C\\u0443 \\u0434\\u043B\\u044F \\u0437\\u0430\\u043F\\u0438\\u0441\\u0438 \\u043D\\u0430 \\u0442\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(BookTestDrive, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = BookTestDrive;\nexport default BookTestDrive;\nvar _c;\n$RefreshReg$(_c, \"BookTestDrive\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "AOS", "FaMapMarkerAlt", "FaClock", "FaPhoneAlt", "Form", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BookTestDrive", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formType", "title", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Book-a-test-drive/Book-a-test-dieve.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { FaMapMarkerAlt, FaClock, FaPhoneAlt } from 'react-icons/fa';\nimport Form from '../../components/Form/Form';\n\nconst BookTestDrive = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\">\n                <strong>Записаться на ТЕСТ-ДРАЙВ</strong>\n              </h1>\n              <i\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n                className=\"redLine\"\n              ></i>\n            </div>\n\n            <p>\n              Готовы ли вы испытать острые ощущения от линейки GWM? <br />\n              Забронируйте тест-драйв уже сегодня, сядьте за руль одного или\n              нескольких наших автомобилей и отправляйтесь в следующее\n              приключение с GWM.\n            </p>\n            <br />\n            <Form\n              formType=\"test-drive\"\n              title=\"Заполните форму для записи на тест-драйв\"\n            />\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default BookTestDrive;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AACzB,SAASC,cAAc,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AACpE,OAAOC,IAAI,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdE,GAAG,CAACa,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBnB,OAAA;UAAKoB,SAAS,EAAC,SAAS;UAAAD,QAAA,gBACtBnB,OAAA;YAAIoB,SAAS,EAAC,OAAO;YAAC,YAAS,SAAS;YAAAD,QAAA,eACtCnB,OAAA;cAAAmB,QAAA,EAAQ;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACLxB,OAAA;YACE,YAAS,SAAS;YAClB,kBAAe,KAAK;YACpBoB,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENxB,OAAA;UAAAmB,QAAA,GAAG,qQACqD,eAAAnB,OAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,wsBAI9D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxB,OAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxB,OAAA,CAACF,IAAI;UACH2B,QAAQ,EAAC,YAAY;UACrBC,KAAK,EAAC;QAA0C;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CArDID,aAAa;AAAAwB,EAAA,GAAbxB,aAAa;AAuDnB,eAAeA,aAAa;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}