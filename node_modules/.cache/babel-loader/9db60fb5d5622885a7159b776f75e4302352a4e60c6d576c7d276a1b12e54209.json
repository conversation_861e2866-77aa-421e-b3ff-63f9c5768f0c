{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/components/header-slider/HeroSliderGWM.jsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Autoplay } from 'swiper/modules';\nimport { BsPlayFill, BsPauseFill } from 'react-icons/bs';\nimport 'swiper/css';\nimport SkeletonSlide from './SkeletonSlide';\nimport styles from './header.module.css'; // CSS-модуль\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SLIDE_DURATION = 5000;\nconst API_URL = 'https://api.gwm.tj/api/v1/sliders';\nconst HeroSliderGWM = () => {\n  _s();\n  const swiperRef = useRef(null);\n  const [slides, setSlides] = useState([]);\n  const [activeIndex, setActiveIndex] = useState(0);\n  const [paused, setPaused] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 760);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 760);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  useEffect(() => {\n    const fetchSlides = async () => {\n      try {\n        const response = await fetch(API_URL);\n        const data = await response.json();\n        setSlides(Array.isArray(data.sliders) ? data.sliders : []);\n      } catch (error) {\n        setSlides([]);\n      }\n    };\n    fetchSlides();\n  }, []);\n  const handlePauseToggle = () => {\n    var _swiperRef$current;\n    const swiper = (_swiperRef$current = swiperRef.current) === null || _swiperRef$current === void 0 ? void 0 : _swiperRef$current.swiper;\n    if (!swiper) return;\n    paused ? swiper.autoplay.start() : swiper.autoplay.stop();\n    setPaused(!paused);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: styles.heroSlider,\n    children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Swiper, {\n        ref: swiperRef,\n        modules: [Autoplay],\n        autoplay: {\n          delay: SLIDE_DURATION,\n          disableOnInteraction: false\n        },\n        loop: true,\n        onSlideChange: swiper => setActiveIndex(swiper.realIndex),\n        children: slides.map(slide => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.slide,\n            style: {\n              backgroundImage: `url(${isMobile ? slide.image_mobile : slide.image})`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.slideContent,\n                children: [slide.new && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.innerNew,\n                  children: slide.new\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 25\n                }, this), slide.sale && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.innerSele,\n                  children: slide.sale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 25\n                }, this), slide.title && /*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: slide.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 39\n                }, this), slide.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.description,\n                  children: slide.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 25\n                }, this), slide.link && /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/${slide.link}`,\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"button-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u0423\\u0437\\u043D\\u0430\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 90,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 17\n          }, this)\n        }, slide.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.progressContainer,\n        children: [slides.map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.dot} ${i === activeIndex ? styles.active : ''}`,\n          children: i === activeIndex && !paused && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.dotFill,\n            style: {\n              animationDuration: `${SLIDE_DURATION}ms`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 19\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: styles.pauseBtn,\n          onClick: handlePauseToggle,\n          \"aria-label\": paused ? 'Воспроизвести слайдер' : 'Поставить на паузу',\n          role: \"button\",\n          children: paused ? /*#__PURE__*/_jsxDEV(BsPlayFill, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this) : /*#__PURE__*/_jsxDEV(BsPauseFill, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(SkeletonSlide, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this)\n    }, void 0, false)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(HeroSliderGWM, \"YCmMRqVeXYshw9itOc8TU0uSZVM=\");\n_c = HeroSliderGWM;\nexport default HeroSliderGWM;\nvar _c;\n$RefreshReg$(_c, \"HeroSliderGWM\");", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "Link", "Swiper", "SwiperSlide", "Autoplay", "BsPlayFill", "BsPauseFill", "SkeletonSlide", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SLIDE_DURATION", "API_URL", "HeroSliderGWM", "_s", "swiperRef", "slides", "setSlides", "activeIndex", "setActiveIndex", "paused", "setPaused", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "fetchSlides", "response", "fetch", "data", "json", "Array", "isArray", "sliders", "error", "handlePauseToggle", "_swiperRef$current", "swiper", "current", "autoplay", "start", "stop", "className", "<PERSON><PERSON><PERSON><PERSON>", "children", "length", "ref", "modules", "delay", "disableOnInteraction", "loop", "onSlideChange", "realIndex", "map", "slide", "style", "backgroundImage", "image_mobile", "image", "slideContent", "new", "innerNew", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sale", "innerSele", "title", "description", "link", "to", "id", "progressContainer", "_", "i", "dot", "active", "dotFill", "animationDuration", "pauseBtn", "onClick", "role", "size", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/header-slider/HeroSliderGWM.jsx"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Autoplay } from 'swiper/modules';\nimport { BsPlayFill, BsPauseFill } from 'react-icons/bs';\nimport 'swiper/css';\nimport SkeletonSlide from './SkeletonSlide';\n\nimport styles from './header.module.css'; // CSS-модуль\n\nconst SLIDE_DURATION = 5000;\nconst API_URL = 'https://api.gwm.tj/api/v1/sliders';\n\nconst HeroSliderGWM = () => {\n  const swiperRef = useRef(null);\n  const [slides, setSlides] = useState([]);\n  const [activeIndex, setActiveIndex] = useState(0);\n  const [paused, setPaused] = useState(false);\n\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 760);\n\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 760);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  useEffect(() => {\n    const fetchSlides = async () => {\n      try {\n        const response = await fetch(API_URL);\n        const data = await response.json();\n        setSlides(Array.isArray(data.sliders) ? data.sliders : []);\n      } catch (error) {\n        setSlides([]);\n      }\n    };\n\n    fetchSlides();\n  }, []);\n\n  const handlePauseToggle = () => {\n    const swiper = swiperRef.current?.swiper;\n    if (!swiper) return;\n\n    paused ? swiper.autoplay.start() : swiper.autoplay.stop();\n    setPaused(!paused);\n  };\n\n  return (\n    <header className={styles.heroSlider}>\n      {slides.length > 0 ? (\n        <>\n          <Swiper\n            ref={swiperRef}\n            modules={[Autoplay]}\n            autoplay={{ delay: SLIDE_DURATION, disableOnInteraction: false }}\n            loop\n            onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}\n          >\n            {slides.map((slide) => (\n              <SwiperSlide key={slide.id}>\n                <div\n                  className={styles.slide}\n                  style={{\n                    backgroundImage: `url(${isMobile ? slide.image_mobile : slide.image\n                      })`,\n                  }}\n                >\n                  <div className=\"container\">\n                    <div className={styles.slideContent}>\n                      {slide.new && (\n                        <div className={styles.innerNew}>{slide.new}</div>\n                      )}\n                      {slide.sale && (\n                        <div className={styles.innerSele}>{slide.sale}</div>\n                      )}\n                      {slide.title && <h1>{slide.title}</h1>}\n                      {slide.description && (\n                        <div className={styles.description}>\n                          {slide.description}\n                        </div>\n                      )}\n                      {slide.link && (\n                        <Link to={`/${slide.link}`}>\n                          <button className=\"button-white\">\n                            <span>Узнать больше</span>\n                          </button>\n                        </Link>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </SwiperSlide>\n            ))}\n          </Swiper>\n\n          <div className={styles.progressContainer}>\n            {slides.map((_, i) => (\n              <div\n                key={i}\n                className={`${styles.dot} ${i === activeIndex ? styles.active : ''\n                  }`}\n              >\n                {i === activeIndex && !paused && (\n                  <div\n                    className={styles.dotFill}\n                    style={{ animationDuration: `${SLIDE_DURATION}ms` }}\n                  />\n                )}\n              </div>\n            ))}\n            <button\n              className={styles.pauseBtn}\n              onClick={handlePauseToggle}\n              aria-label={\n                paused ? 'Воспроизвести слайдер' : 'Поставить на паузу'\n              }\n              role=\"button\"\n            >\n              {paused ? <BsPlayFill size={20} /> : <BsPauseFill size={20} />}\n            </button>\n          </div>\n        </>\n      ) : (\n        <>\n          <SkeletonSlide />\n        </>\n      )}\n    </header>\n  );\n};\n\nexport default HeroSliderGWM;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;AACxD,OAAO,YAAY;AACnB,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAOC,MAAM,MAAM,qBAAqB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,cAAc,GAAG,IAAI;AAC3B,MAAMC,OAAO,GAAG,mCAAmC;AAEnD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;EAEjE3B,SAAS,CAAC,MAAM;IACd,MAAM4B,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IACtC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN5B,SAAS,CAAC,MAAM;IACd,MAAM+B,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACnB,OAAO,CAAC;QACrC,MAAMoB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClChB,SAAS,CAACiB,KAAK,CAACC,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC,GAAGJ,IAAI,CAACI,OAAO,GAAG,EAAE,CAAC;MAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdpB,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC;IAEDY,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA;IAC9B,MAAMC,MAAM,IAAAD,kBAAA,GAAGxB,SAAS,CAAC0B,OAAO,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBC,MAAM;IACxC,IAAI,CAACA,MAAM,EAAE;IAEbpB,MAAM,GAAGoB,MAAM,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC,GAAGH,MAAM,CAACE,QAAQ,CAACE,IAAI,CAAC,CAAC;IACzDvB,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,oBACEZ,OAAA;IAAQqC,SAAS,EAAEvC,MAAM,CAACwC,UAAW;IAAAC,QAAA,EAClC/B,MAAM,CAACgC,MAAM,GAAG,CAAC,gBAChBxC,OAAA,CAAAE,SAAA;MAAAqC,QAAA,gBACEvC,OAAA,CAACR,MAAM;QACLiD,GAAG,EAAElC,SAAU;QACfmC,OAAO,EAAE,CAAChD,QAAQ,CAAE;QACpBwC,QAAQ,EAAE;UAAES,KAAK,EAAExC,cAAc;UAAEyC,oBAAoB,EAAE;QAAM,CAAE;QACjEC,IAAI;QACJC,aAAa,EAAGd,MAAM,IAAKrB,cAAc,CAACqB,MAAM,CAACe,SAAS,CAAE;QAAAR,QAAA,EAE3D/B,MAAM,CAACwC,GAAG,CAAEC,KAAK,iBAChBjD,OAAA,CAACP,WAAW;UAAA8C,QAAA,eACVvC,OAAA;YACEqC,SAAS,EAAEvC,MAAM,CAACmD,KAAM;YACxBC,KAAK,EAAE;cACLC,eAAe,EAAE,OAAOrC,QAAQ,GAAGmC,KAAK,CAACG,YAAY,GAAGH,KAAK,CAACI,KAAK;YAErE,CAAE;YAAAd,QAAA,eAEFvC,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAE,QAAA,eACxBvC,OAAA;gBAAKqC,SAAS,EAAEvC,MAAM,CAACwD,YAAa;gBAAAf,QAAA,GACjCU,KAAK,CAACM,GAAG,iBACRvD,OAAA;kBAAKqC,SAAS,EAAEvC,MAAM,CAAC0D,QAAS;kBAAAjB,QAAA,EAAEU,KAAK,CAACM;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAClD,EACAX,KAAK,CAACY,IAAI,iBACT7D,OAAA;kBAAKqC,SAAS,EAAEvC,MAAM,CAACgE,SAAU;kBAAAvB,QAAA,EAAEU,KAAK,CAACY;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACpD,EACAX,KAAK,CAACc,KAAK,iBAAI/D,OAAA;kBAAAuC,QAAA,EAAKU,KAAK,CAACc;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACrCX,KAAK,CAACe,WAAW,iBAChBhE,OAAA;kBAAKqC,SAAS,EAAEvC,MAAM,CAACkE,WAAY;kBAAAzB,QAAA,EAChCU,KAAK,CAACe;gBAAW;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,EACAX,KAAK,CAACgB,IAAI,iBACTjE,OAAA,CAACT,IAAI;kBAAC2E,EAAE,EAAE,IAAIjB,KAAK,CAACgB,IAAI,EAAG;kBAAA1B,QAAA,eACzBvC,OAAA;oBAAQqC,SAAS,EAAC,cAAc;oBAAAE,QAAA,eAC9BvC,OAAA;sBAAAuC,QAAA,EAAM;oBAAa;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA/BUX,KAAK,CAACkB,EAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCb,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAET5D,OAAA;QAAKqC,SAAS,EAAEvC,MAAM,CAACsE,iBAAkB;QAAA7B,QAAA,GACtC/B,MAAM,CAACwC,GAAG,CAAC,CAACqB,CAAC,EAAEC,CAAC,kBACftE,OAAA;UAEEqC,SAAS,EAAE,GAAGvC,MAAM,CAACyE,GAAG,IAAID,CAAC,KAAK5D,WAAW,GAAGZ,MAAM,CAAC0E,MAAM,GAAG,EAAE,EAC7D;UAAAjC,QAAA,EAEJ+B,CAAC,KAAK5D,WAAW,IAAI,CAACE,MAAM,iBAC3BZ,OAAA;YACEqC,SAAS,EAAEvC,MAAM,CAAC2E,OAAQ;YAC1BvB,KAAK,EAAE;cAAEwB,iBAAiB,EAAE,GAAGvE,cAAc;YAAK;UAAE;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QACF,GATIU,CAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUH,CACN,CAAC,eACF5D,OAAA;UACEqC,SAAS,EAAEvC,MAAM,CAAC6E,QAAS;UAC3BC,OAAO,EAAE9C,iBAAkB;UAC3B,cACElB,MAAM,GAAG,uBAAuB,GAAG,oBACpC;UACDiE,IAAI,EAAC,QAAQ;UAAAtC,QAAA,EAEZ3B,MAAM,gBAAGZ,OAAA,CAACL,UAAU;YAACmF,IAAI,EAAE;UAAG;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG5D,OAAA,CAACJ,WAAW;YAACkF,IAAI,EAAE;UAAG;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CAAC,gBAEH5D,OAAA,CAAAE,SAAA;MAAAqC,QAAA,eACEvC,OAAA,CAACH,aAAa;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC,gBACjB;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACtD,EAAA,CAzHID,aAAa;AAAA0E,EAAA,GAAb1E,aAAa;AA2HnB,eAAeA,aAAa;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}