{"ast": null, "code": "function buildProjectionTransform(delta, treeScale, latestTransform) {\n  let transform = \"\";\n  /**\n   * The translations we use to calculate are always relative to the viewport coordinate space.\n   * But when we apply scales, we also scale the coordinate space of an element and its children.\n   * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n   * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n   */\n  const xTranslate = delta.x.translate / treeScale.x;\n  const yTranslate = delta.y.translate / treeScale.y;\n  const zTranslate = latestTransform?.z || 0;\n  if (xTranslate || yTranslate || zTranslate) {\n    transform = `translate3d(${xTranslate}px, ${yTranslate}px, ${zTranslate}px) `;\n  }\n  /**\n   * Apply scale correction for the tree transform.\n   * This will apply scale to the screen-orientated axes.\n   */\n  if (treeScale.x !== 1 || treeScale.y !== 1) {\n    transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n  }\n  if (latestTransform) {\n    const {\n      transformPerspective,\n      rotate,\n      rotateX,\n      rotateY,\n      skewX,\n      skewY\n    } = latestTransform;\n    if (transformPerspective) transform = `perspective(${transformPerspective}px) ${transform}`;\n    if (rotate) transform += `rotate(${rotate}deg) `;\n    if (rotateX) transform += `rotateX(${rotateX}deg) `;\n    if (rotateY) transform += `rotateY(${rotateY}deg) `;\n    if (skewX) transform += `skewX(${skewX}deg) `;\n    if (skewY) transform += `skewY(${skewY}deg) `;\n  }\n  /**\n   * Apply scale to match the size of the element to the size we want it.\n   * This will apply scale to the element-orientated axes.\n   */\n  const elementScaleX = delta.x.scale * treeScale.x;\n  const elementScaleY = delta.y.scale * treeScale.y;\n  if (elementScaleX !== 1 || elementScaleY !== 1) {\n    transform += `scale(${elementScaleX}, ${elementScaleY})`;\n  }\n  return transform || \"none\";\n}\nexport { buildProjectionTransform };", "map": {"version": 3, "names": ["buildProjectionTransform", "delta", "treeScale", "latestTransform", "transform", "xTranslate", "x", "translate", "yTranslate", "y", "zTranslate", "z", "transformPerspective", "rotate", "rotateX", "rotateY", "skewX", "skewY", "elementScaleX", "scale", "elementScaleY"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/projection/styles/transform.mjs"], "sourcesContent": ["function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    const zTranslate = latestTransform?.z || 0;\n    if (xTranslate || yTranslate || zTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, ${zTranslate}px) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { transformPerspective, rotate, rotateX, rotateY, skewX, skewY } = latestTransform;\n        if (transformPerspective)\n            transform = `perspective(${transformPerspective}px) ${transform}`;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n        if (skewX)\n            transform += `skewX(${skewX}deg) `;\n        if (skewY)\n            transform += `skewY(${skewY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n"], "mappings": "AAAA,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAE;EACjE,IAAIC,SAAS,GAAG,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,UAAU,GAAGJ,KAAK,CAACK,CAAC,CAACC,SAAS,GAAGL,SAAS,CAACI,CAAC;EAClD,MAAME,UAAU,GAAGP,KAAK,CAACQ,CAAC,CAACF,SAAS,GAAGL,SAAS,CAACO,CAAC;EAClD,MAAMC,UAAU,GAAGP,eAAe,EAAEQ,CAAC,IAAI,CAAC;EAC1C,IAAIN,UAAU,IAAIG,UAAU,IAAIE,UAAU,EAAE;IACxCN,SAAS,GAAG,eAAeC,UAAU,OAAOG,UAAU,OAAOE,UAAU,MAAM;EACjF;EACA;AACJ;AACA;AACA;EACI,IAAIR,SAAS,CAACI,CAAC,KAAK,CAAC,IAAIJ,SAAS,CAACO,CAAC,KAAK,CAAC,EAAE;IACxCL,SAAS,IAAI,SAAS,CAAC,GAAGF,SAAS,CAACI,CAAC,KAAK,CAAC,GAAGJ,SAAS,CAACO,CAAC,IAAI;EACjE;EACA,IAAIN,eAAe,EAAE;IACjB,MAAM;MAAES,oBAAoB;MAAEC,MAAM;MAAEC,OAAO;MAAEC,OAAO;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGd,eAAe;IACxF,IAAIS,oBAAoB,EACpBR,SAAS,GAAG,eAAeQ,oBAAoB,OAAOR,SAAS,EAAE;IACrE,IAAIS,MAAM,EACNT,SAAS,IAAI,UAAUS,MAAM,OAAO;IACxC,IAAIC,OAAO,EACPV,SAAS,IAAI,WAAWU,OAAO,OAAO;IAC1C,IAAIC,OAAO,EACPX,SAAS,IAAI,WAAWW,OAAO,OAAO;IAC1C,IAAIC,KAAK,EACLZ,SAAS,IAAI,SAASY,KAAK,OAAO;IACtC,IAAIC,KAAK,EACLb,SAAS,IAAI,SAASa,KAAK,OAAO;EAC1C;EACA;AACJ;AACA;AACA;EACI,MAAMC,aAAa,GAAGjB,KAAK,CAACK,CAAC,CAACa,KAAK,GAAGjB,SAAS,CAACI,CAAC;EACjD,MAAMc,aAAa,GAAGnB,KAAK,CAACQ,CAAC,CAACU,KAAK,GAAGjB,SAAS,CAACO,CAAC;EACjD,IAAIS,aAAa,KAAK,CAAC,IAAIE,aAAa,KAAK,CAAC,EAAE;IAC5ChB,SAAS,IAAI,SAASc,aAAa,KAAKE,aAAa,GAAG;EAC5D;EACA,OAAOhB,SAAS,IAAI,MAAM;AAC9B;AAEA,SAASJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}