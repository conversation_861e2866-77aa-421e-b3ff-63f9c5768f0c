{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/Home.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\n// components\nimport HeroSliderGWM from './components/header-slider/HeroSliderGWM';\nimport OurModels from './components/Models/OurModels';\nimport ImageCard from './components/Image-card/ImageCard';\nimport ToolBar from '../../components/ToolBar/ToolBar';\nimport VideoCard from './components/video-card/VideoCard';\nimport SimpleCard from '../../components/SimpleCard/SimpleCard';\nimport Form from '../../components/Form/Form';\nimport SEO from '../../hooks/useSEO';\nimport seoData from '../../data/seoData';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SEO, {\n      ...seoData.home\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(HeroSliderGWM, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(OurModels, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ImageCard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ToolBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(VideoCard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(SimpleCard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          formType: \"inquiry\",\n          title: \"\\u0417\\u0430\\u043F\\u0440\\u043E\\u0441\\u0438\\u0442\\u0435 \\u0441\\u0435\\u0433\\u043E\\u0434\\u043D\\u044F!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Home, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["useEffect", "useState", "HeroSliderGWM", "OurModels", "ImageCard", "<PERSON><PERSON><PERSON><PERSON>", "VideoCard", "SimpleCard", "Form", "SEO", "seoData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "loading", "setLoading", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "home", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "formType", "title", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/Home.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\n// components\nimport HeroSliderGWM from './components/header-slider/HeroSliderGWM';\nimport OurModels from './components/Models/OurModels';\nimport ImageCard from './components/Image-card/ImageCard';\nimport ToolBar from '../../components/ToolBar/ToolBar';\nimport VideoCard from './components/video-card/VideoCard';\nimport SimpleCard from '../../components/SimpleCard/SimpleCard';\nimport Form from '../../components/Form/Form';\nimport SEO from '../../hooks/useSEO';\nimport seoData from '../../data/seoData';\n\nconst Home = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      <SEO {...seoData.home} />\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <main>\n          <HeroSliderGWM />\n          <OurModels />\n          <ImageCard />\n          <ToolBar />\n          <VideoCard />\n          <SimpleCard />\n          <section className=\"container\">\n            <Form\n              formType=\"inquiry\"\n              title=\"Запросите сегодня!\"\n            />\n          </section>\n        </main>\n      )}\n    </>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C;AACA,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,UAAU,MAAM,wCAAwC;AAC/D,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAOC,OAAO,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdmB,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;IAEzC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BR,UAAU,CAAC,KAAK,CAAC;MACjBG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAAc,QAAA,gBACEhB,OAAA,CAACH,GAAG;MAAA,GAAKC,OAAO,CAACmB;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EACxBhB,OAAO,gBACNL,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAN,QAAA,eAC5BhB,OAAA;QAAKsB,SAAS,EAAC;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENrB,OAAA;MAAAgB,QAAA,gBACEhB,OAAA,CAACV,aAAa;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjBrB,OAAA,CAACT,SAAS;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbrB,OAAA,CAACR,SAAS;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbrB,OAAA,CAACP,OAAO;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXrB,OAAA,CAACN,SAAS;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACbrB,OAAA,CAACL,UAAU;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACdrB,OAAA;QAASsB,SAAS,EAAC,WAAW;QAAAN,QAAA,eAC5BhB,OAAA,CAACJ,IAAI;UACH2B,QAAQ,EAAC,SAAS;UAClBC,KAAK,EAAC;QAAoB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACP;EAAA,eACD,CAAC;AAEP,CAAC;AAACjB,EAAA,CA3CID,IAAI;AAAAsB,EAAA,GAAJtB,IAAI;AA6CV,eAAeA,IAAI;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}