{"ast": null, "code": "import { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\nfunction handoffOptimizedAppearAnimation(elementId, valueName, frame) {\n  var _window$MotionHandoff, _window2;\n  const storeId = appearStoreId(elementId, valueName);\n  const optimisedAnimation = appearAnimationStore.get(storeId);\n  if (!optimisedAnimation) {\n    return null;\n  }\n  const {\n    animation,\n    startTime\n  } = optimisedAnimation;\n  function cancelAnimation() {\n    var _window$MotionCancelO, _window;\n    (_window$MotionCancelO = (_window = window).MotionCancelOptimisedAnimation) === null || _window$MotionCancelO === void 0 || _window$MotionCancelO.call(_window, elementId, valueName, frame);\n  }\n  /**\n   * We can cancel the animation once it's finished now that we've synced\n   * with Motion.\n   *\n   * Prefer onfinish over finished as onfinish is backwards compatible with\n   * older browsers.\n   */\n  animation.onfinish = cancelAnimation;\n  if (startTime === null || (_window$MotionHandoff = (_window2 = window).MotionHandoffIsComplete) !== null && _window$MotionHandoff !== void 0 && _window$MotionHandoff.call(_window2, elementId)) {\n    /**\n     * If the startTime is null, this animation is the Paint Ready detection animation\n     * and we can cancel it immediately without handoff.\n     *\n     * Or if we've already handed off the animation then we're now interrupting it.\n     * In which case we need to cancel it.\n     */\n    cancelAnimation();\n    return null;\n  } else {\n    return startTime;\n  }\n}\nexport { handoffOptimizedAppearAnimation };", "map": {"version": 3, "names": ["appearAnimationStore", "appearStoreId", "handoffOptimizedAppearAnimation", "elementId", "valueName", "frame", "_window$MotionHandoff", "_window2", "storeId", "optimisedAnimation", "get", "animation", "startTime", "cancelAnimation", "_window$MotionCancelO", "_window", "window", "MotionCancelOptimisedAnimation", "call", "onfinish", "MotionHandoffIsComplete"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/optimized-appear/handoff.mjs"], "sourcesContent": ["import { appearAnimationStore } from './store.mjs';\nimport { appearStoreId } from './store-id.mjs';\n\nfunction handoffOptimizedAppearAnimation(elementId, valueName, frame) {\n    const storeId = appearStoreId(elementId, valueName);\n    const optimisedAnimation = appearAnimationStore.get(storeId);\n    if (!optimisedAnimation) {\n        return null;\n    }\n    const { animation, startTime } = optimisedAnimation;\n    function cancelAnimation() {\n        window.MotionCancelOptimisedAnimation?.(elementId, valueName, frame);\n    }\n    /**\n     * We can cancel the animation once it's finished now that we've synced\n     * with <PERSON>.\n     *\n     * Prefer onfinish over finished as onfinish is backwards compatible with\n     * older browsers.\n     */\n    animation.onfinish = cancelAnimation;\n    if (startTime === null || window.MotionHandoffIsComplete?.(elementId)) {\n        /**\n         * If the startTime is null, this animation is the Paint Ready detection animation\n         * and we can cancel it immediately without handoff.\n         *\n         * Or if we've already handed off the animation then we're now interrupting it.\n         * In which case we need to cancel it.\n         */\n        cancelAnimation();\n        return null;\n    }\n    else {\n        return startTime;\n    }\n}\n\nexport { handoffOptimizedAppearAnimation };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,aAAa;AAClD,SAASC,aAAa,QAAQ,gBAAgB;AAE9C,SAASC,+BAA+BA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAAA,IAAAC,qBAAA,EAAAC,QAAA;EAClE,MAAMC,OAAO,GAAGP,aAAa,CAACE,SAAS,EAAEC,SAAS,CAAC;EACnD,MAAMK,kBAAkB,GAAGT,oBAAoB,CAACU,GAAG,CAACF,OAAO,CAAC;EAC5D,IAAI,CAACC,kBAAkB,EAAE;IACrB,OAAO,IAAI;EACf;EACA,MAAM;IAAEE,SAAS;IAAEC;EAAU,CAAC,GAAGH,kBAAkB;EACnD,SAASI,eAAeA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,OAAA;IACvB,CAAAD,qBAAA,IAAAC,OAAA,GAAAC,MAAM,EAACC,8BAA8B,cAAAH,qBAAA,eAArCA,qBAAA,CAAAI,IAAA,CAAAH,OAAA,EAAwCZ,SAAS,EAAEC,SAAS,EAAEC,KAAK,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,SAAS,CAACQ,QAAQ,GAAGN,eAAe;EACpC,IAAID,SAAS,KAAK,IAAI,KAAAN,qBAAA,GAAI,CAAAC,QAAA,GAAAS,MAAM,EAACI,uBAAuB,cAAAd,qBAAA,eAA9BA,qBAAA,CAAAY,IAAA,CAAAX,QAAA,EAAiCJ,SAAS,CAAC,EAAE;IACnE;AACR;AACA;AACA;AACA;AACA;AACA;IACQU,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC,MACI;IACD,OAAOD,SAAS;EACpB;AACJ;AAEA,SAASV,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}