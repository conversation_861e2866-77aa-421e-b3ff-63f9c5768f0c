{"ast": null, "code": "import{useEffect,useState}from'react';import styles from'./noPage.module.css';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const NoPage=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{window.scrollTo(0,0);document.body.style.overflow='hidden';// Отключаем скролл при загрузке\nconst timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';// Возвращаем скролл после загрузки\n},300);// 1 секунда для имитации загрузки\nreturn()=>{clearTimeout(timer);document.body.style.overflow='visible';// На случай размонтирования компонента\n};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"section\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.box,children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"404\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"underText \",children:\"\\u0421\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430 \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"}),/*#__PURE__*/_jsx(\"i\",{className:\"redLine\"}),/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"button-black\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0413\\u043B\\u0430\\u0432\\u043D\\u0430\\u044F \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430\"})})]})})})});};export default NoPage;", "map": {"version": 3, "names": ["useEffect", "useState", "styles", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "NoPage", "loading", "setLoading", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "box", "to"], "sources": ["/var/www/html/gwm.tj/src/pages/NoPage/NoPage.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport styles from './noPage.module.css';\nimport { Link } from 'react-router-dom';\n\nconst NoPage = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <section className=\"container\">\n            <div className={styles.box}>\n              <h1 className=\"title\">\n                <strong>404</strong>\n              </h1>\n              <p className=\"underText \">Страница не найдена</p>\n              <i className=\"redLine\"></i>\n\n              <Link to=\"/\" className=\"button-black\">\n                <span>Главная страница</span>\n              </Link>\n            </div>\n          </section>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default NoPage;\n"], "mappings": "AAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExC,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACda,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAAE;AAEzC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BR,UAAU,CAAC,KAAK,CAAC,CACjBG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEb,IAAA,CAAAI,SAAA,EAAAa,QAAA,CACGX,OAAO,cACNN,IAAA,QAAKkB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BjB,IAAA,QAAKkB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENlB,IAAA,QAAKkB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBjB,IAAA,YAASkB,SAAS,CAAC,WAAW,CAAAD,QAAA,cAC5Bf,KAAA,QAAKgB,SAAS,CAAErB,MAAM,CAACsB,GAAI,CAAAF,QAAA,eACzBjB,IAAA,OAAIkB,SAAS,CAAC,OAAO,CAAAD,QAAA,cACnBjB,IAAA,WAAAiB,QAAA,CAAQ,KAAG,CAAQ,CAAC,CAClB,CAAC,cACLjB,IAAA,MAAGkB,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,0GAAmB,CAAG,CAAC,cACjDjB,IAAA,MAAGkB,SAAS,CAAC,SAAS,CAAI,CAAC,cAE3BlB,IAAA,CAACF,IAAI,EAACsB,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,cAAc,CAAAD,QAAA,cACnCjB,IAAA,SAAAiB,QAAA,CAAM,6FAAgB,CAAM,CAAC,CACzB,CAAC,EACJ,CAAC,CACC,CAAC,CACP,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAZ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}