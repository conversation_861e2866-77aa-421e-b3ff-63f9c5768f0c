{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/NoPage/NoPage.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport styles from './noPage.module.css';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NoPage = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.box,\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"404\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"underText \",\n            children: \"\\u0421\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430 \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"redLine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"button-black\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0413\\u043B\\u0430\\u0432\\u043D\\u0430\\u044F \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(NoPage, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = NoPage;\nexport default NoPage;\nvar _c;\n$RefreshReg$(_c, \"NoPage\");", "map": {"version": 3, "names": ["useEffect", "useState", "styles", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NoPage", "_s", "loading", "setLoading", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "box", "to", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/NoPage/NoPage.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport styles from './noPage.module.css';\nimport { Link } from 'react-router-dom';\n\nconst NoPage = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <section className=\"container\">\n            <div className={styles.box}>\n              <h1 className=\"title\">\n                <strong>404</strong>\n              </h1>\n              <p className=\"underText \">Страница не найдена</p>\n              <i className=\"redLine\"></i>\n\n              <Link to=\"/\" className=\"button-black\">\n                <span>Главная страница</span>\n              </Link>\n            </div>\n          </section>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default NoPage;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdY,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;IAEzC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BR,UAAU,CAAC,KAAK,CAAC;MACjBG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAAc,QAAA,EACGX,OAAO,gBACNL,OAAA;MAAKiB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BhB,OAAA;QAAKiB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENrB,OAAA;MAAKiB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBhB,OAAA;QAASiB,SAAS,EAAC,WAAW;QAAAD,QAAA,eAC5BhB,OAAA;UAAKiB,SAAS,EAAEpB,MAAM,CAACyB,GAAI;UAAAN,QAAA,gBACzBhB,OAAA;YAAIiB,SAAS,EAAC,OAAO;YAAAD,QAAA,eACnBhB,OAAA;cAAAgB,QAAA,EAAQ;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACLrB,OAAA;YAAGiB,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjDrB,OAAA;YAAGiB,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3BrB,OAAA,CAACF,IAAI;YAACyB,EAAE,EAAC,GAAG;YAACN,SAAS,EAAC,cAAc;YAAAD,QAAA,eACnChB,OAAA;cAAAgB,QAAA,EAAM;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EACN,gBACD,CAAC;AAEP,CAAC;AAACjB,EAAA,CA3CID,MAAM;AAAAqB,EAAA,GAANrB,MAAM;AA6CZ,eAAeA,MAAM;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}