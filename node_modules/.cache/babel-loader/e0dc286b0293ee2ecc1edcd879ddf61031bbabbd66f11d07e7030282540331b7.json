{"ast": null, "code": "import { stepsOrder } from './order.mjs';\nimport { frame, cancelFrame } from './frame.mjs';\n\n/**\n * @deprecated\n *\n * Import as `frame` instead.\n */\nconst sync = frame;\n/**\n * @deprecated\n *\n * Use cancelFrame(callback) instead.\n */\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = process => cancelFrame(process);\n  return acc;\n}, {});\nexport { cancelSync, sync };", "map": {"version": 3, "names": ["stepsOrder", "frame", "cancelFrame", "sync", "cancelSync", "reduce", "acc", "key", "process"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs"], "sourcesContent": ["import { stepsOrder } from './order.mjs';\nimport { frame, cancelFrame } from './frame.mjs';\n\n/**\n * @deprecated\n *\n * Import as `frame` instead.\n */\nconst sync = frame;\n/**\n * @deprecated\n *\n * Use cancelFrame(callback) instead.\n */\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = (process) => cancelFrame(process);\n    return acc;\n}, {});\n\nexport { cancelSync, sync };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,KAAK,EAAEC,WAAW,QAAQ,aAAa;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAGF,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA,MAAMG,UAAU,GAAGJ,UAAU,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC/CD,GAAG,CAACC,GAAG,CAAC,GAAIC,OAAO,IAAKN,WAAW,CAACM,OAAO,CAAC;EAC5C,OAAOF,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,SAASF,UAAU,EAAED,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}