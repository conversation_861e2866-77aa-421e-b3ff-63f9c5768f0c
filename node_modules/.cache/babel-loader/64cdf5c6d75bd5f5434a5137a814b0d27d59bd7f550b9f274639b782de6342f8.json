{"ast": null, "code": "import { e as extend, p as paramsList, i as isObject, n as needsNavigation, a as needsPagination, b as needsScrollbar } from './update-swiper.mjs';\nimport { d as defaults } from './swiper-core.mjs';\nfunction getParams(obj, splitEvents) {\n  if (obj === void 0) {\n    obj = {};\n  }\n  if (splitEvents === void 0) {\n    splitEvents = true;\n  }\n  const params = {\n    on: {}\n  };\n  const events = {};\n  const passedParams = {};\n  extend(params, defaults);\n  params._emitClasses = true;\n  params.init = false;\n  const rest = {};\n  const allowedParams = paramsList.map(key => key.replace(/_/, ''));\n  const plainObj = Object.assign({}, obj);\n  Object.keys(plainObj).forEach(key => {\n    if (typeof obj[key] === 'undefined') return;\n    if (allowedParams.indexOf(key) >= 0) {\n      if (isObject(obj[key])) {\n        params[key] = {};\n        passedParams[key] = {};\n        extend(params[key], obj[key]);\n        extend(passedParams[key], obj[key]);\n      } else {\n        params[key] = obj[key];\n        passedParams[key] = obj[key];\n      }\n    } else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n      if (splitEvents) {\n        events[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      } else {\n        params.on[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      }\n    } else {\n      rest[key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach(key => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n  return {\n    params,\n    passedParams,\n    rest,\n    events\n  };\n}\nfunction mountSwiper(_ref, swiperParams) {\n  let {\n    el,\n    nextEl,\n    prevEl,\n    paginationEl,\n    scrollbarEl,\n    swiper\n  } = _ref;\n  if (needsNavigation(swiperParams) && nextEl && prevEl) {\n    swiper.params.navigation.nextEl = nextEl;\n    swiper.originalParams.navigation.nextEl = nextEl;\n    swiper.params.navigation.prevEl = prevEl;\n    swiper.originalParams.navigation.prevEl = prevEl;\n  }\n  if (needsPagination(swiperParams) && paginationEl) {\n    swiper.params.pagination.el = paginationEl;\n    swiper.originalParams.pagination.el = paginationEl;\n  }\n  if (needsScrollbar(swiperParams) && scrollbarEl) {\n    swiper.params.scrollbar.el = scrollbarEl;\n    swiper.originalParams.scrollbar.el = scrollbarEl;\n  }\n  swiper.init(el);\n}\nfunction getChangedParams(swiperParams, oldParams, children, oldChildren, getKey) {\n  const keys = [];\n  if (!oldParams) return keys;\n  const addKey = key => {\n    if (keys.indexOf(key) < 0) keys.push(key);\n  };\n  if (children && oldChildren) {\n    const oldChildrenKeys = oldChildren.map(getKey);\n    const childrenKeys = children.map(getKey);\n    if (oldChildrenKeys.join('') !== childrenKeys.join('')) addKey('children');\n    if (oldChildren.length !== children.length) addKey('children');\n  }\n  const watchParams = paramsList.filter(key => key[0] === '_').map(key => key.replace(/_/, ''));\n  watchParams.forEach(key => {\n    if (key in swiperParams && key in oldParams) {\n      if (isObject(swiperParams[key]) && isObject(oldParams[key])) {\n        const newKeys = Object.keys(swiperParams[key]);\n        const oldKeys = Object.keys(oldParams[key]);\n        if (newKeys.length !== oldKeys.length) {\n          addKey(key);\n        } else {\n          newKeys.forEach(newKey => {\n            if (swiperParams[key][newKey] !== oldParams[key][newKey]) {\n              addKey(key);\n            }\n          });\n          oldKeys.forEach(oldKey => {\n            if (swiperParams[key][oldKey] !== oldParams[key][oldKey]) addKey(key);\n          });\n        }\n      } else if (swiperParams[key] !== oldParams[key]) {\n        addKey(key);\n      }\n    }\n  });\n  return keys;\n}\nconst updateOnVirtualData = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params.virtual || swiper.params.virtual && !swiper.params.virtual.enabled) return;\n  swiper.updateSlides();\n  swiper.updateProgress();\n  swiper.updateSlidesClasses();\n  if (swiper.parallax && swiper.params.parallax && swiper.params.parallax.enabled) {\n    swiper.parallax.setTranslate();\n  }\n};\nexport { getChangedParams as a, getParams as g, mountSwiper as m, updateOnVirtualData as u };", "map": {"version": 3, "names": ["e", "extend", "p", "paramsList", "i", "isObject", "n", "needsNavigation", "a", "needsPagination", "b", "needsScrollbar", "d", "defaults", "getParams", "obj", "splitEvents", "params", "on", "events", "passedParams", "_emitClasses", "init", "rest", "allowedParams", "map", "key", "replace", "plainObj", "Object", "assign", "keys", "for<PERSON>ach", "indexOf", "search", "toLowerCase", "substr", "mountSwiper", "_ref", "swiperParams", "el", "nextEl", "prevEl", "paginationEl", "scrollbarEl", "swiper", "navigation", "originalParams", "pagination", "scrollbar", "getChangedParams", "oldParams", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children<PERSON>eys", "join", "length", "watchParams", "filter", "newKeys", "oldKeys", "new<PERSON>ey", "<PERSON><PERSON><PERSON>", "updateOnVirtualData", "destroyed", "virtual", "enabled", "updateSlides", "updateProgress", "updateSlidesClasses", "parallax", "setTranslate", "g", "m", "u"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/shared/update-on-virtual-data.mjs"], "sourcesContent": ["import { e as extend, p as paramsList, i as isObject, n as needsNavigation, a as needsPagination, b as needsScrollbar } from './update-swiper.mjs';\nimport { d as defaults } from './swiper-core.mjs';\n\nfunction getParams(obj, splitEvents) {\n  if (obj === void 0) {\n    obj = {};\n  }\n  if (splitEvents === void 0) {\n    splitEvents = true;\n  }\n  const params = {\n    on: {}\n  };\n  const events = {};\n  const passedParams = {};\n  extend(params, defaults);\n  params._emitClasses = true;\n  params.init = false;\n  const rest = {};\n  const allowedParams = paramsList.map(key => key.replace(/_/, ''));\n  const plainObj = Object.assign({}, obj);\n  Object.keys(plainObj).forEach(key => {\n    if (typeof obj[key] === 'undefined') return;\n    if (allowedParams.indexOf(key) >= 0) {\n      if (isObject(obj[key])) {\n        params[key] = {};\n        passedParams[key] = {};\n        extend(params[key], obj[key]);\n        extend(passedParams[key], obj[key]);\n      } else {\n        params[key] = obj[key];\n        passedParams[key] = obj[key];\n      }\n    } else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n      if (splitEvents) {\n        events[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      } else {\n        params.on[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      }\n    } else {\n      rest[key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach(key => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n  return {\n    params,\n    passedParams,\n    rest,\n    events\n  };\n}\n\nfunction mountSwiper(_ref, swiperParams) {\n  let {\n    el,\n    nextEl,\n    prevEl,\n    paginationEl,\n    scrollbarEl,\n    swiper\n  } = _ref;\n  if (needsNavigation(swiperParams) && nextEl && prevEl) {\n    swiper.params.navigation.nextEl = nextEl;\n    swiper.originalParams.navigation.nextEl = nextEl;\n    swiper.params.navigation.prevEl = prevEl;\n    swiper.originalParams.navigation.prevEl = prevEl;\n  }\n  if (needsPagination(swiperParams) && paginationEl) {\n    swiper.params.pagination.el = paginationEl;\n    swiper.originalParams.pagination.el = paginationEl;\n  }\n  if (needsScrollbar(swiperParams) && scrollbarEl) {\n    swiper.params.scrollbar.el = scrollbarEl;\n    swiper.originalParams.scrollbar.el = scrollbarEl;\n  }\n  swiper.init(el);\n}\n\nfunction getChangedParams(swiperParams, oldParams, children, oldChildren, getKey) {\n  const keys = [];\n  if (!oldParams) return keys;\n  const addKey = key => {\n    if (keys.indexOf(key) < 0) keys.push(key);\n  };\n  if (children && oldChildren) {\n    const oldChildrenKeys = oldChildren.map(getKey);\n    const childrenKeys = children.map(getKey);\n    if (oldChildrenKeys.join('') !== childrenKeys.join('')) addKey('children');\n    if (oldChildren.length !== children.length) addKey('children');\n  }\n  const watchParams = paramsList.filter(key => key[0] === '_').map(key => key.replace(/_/, ''));\n  watchParams.forEach(key => {\n    if (key in swiperParams && key in oldParams) {\n      if (isObject(swiperParams[key]) && isObject(oldParams[key])) {\n        const newKeys = Object.keys(swiperParams[key]);\n        const oldKeys = Object.keys(oldParams[key]);\n        if (newKeys.length !== oldKeys.length) {\n          addKey(key);\n        } else {\n          newKeys.forEach(newKey => {\n            if (swiperParams[key][newKey] !== oldParams[key][newKey]) {\n              addKey(key);\n            }\n          });\n          oldKeys.forEach(oldKey => {\n            if (swiperParams[key][oldKey] !== oldParams[key][oldKey]) addKey(key);\n          });\n        }\n      } else if (swiperParams[key] !== oldParams[key]) {\n        addKey(key);\n      }\n    }\n  });\n  return keys;\n}\n\nconst updateOnVirtualData = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params.virtual || swiper.params.virtual && !swiper.params.virtual.enabled) return;\n  swiper.updateSlides();\n  swiper.updateProgress();\n  swiper.updateSlidesClasses();\n  if (swiper.parallax && swiper.params.parallax && swiper.params.parallax.enabled) {\n    swiper.parallax.setTranslate();\n  }\n};\n\nexport { getChangedParams as a, getParams as g, mountSwiper as m, updateOnVirtualData as u };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAClJ,SAASC,CAAC,IAAIC,QAAQ,QAAQ,mBAAmB;AAEjD,SAASC,SAASA,CAACC,GAAG,EAAEC,WAAW,EAAE;EACnC,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,CAAC,CAAC;EACV;EACA,IAAIC,WAAW,KAAK,KAAK,CAAC,EAAE;IAC1BA,WAAW,GAAG,IAAI;EACpB;EACA,MAAMC,MAAM,GAAG;IACbC,EAAE,EAAE,CAAC;EACP,CAAC;EACD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAG,CAAC,CAAC;EACvBnB,MAAM,CAACgB,MAAM,EAAEJ,QAAQ,CAAC;EACxBI,MAAM,CAACI,YAAY,GAAG,IAAI;EAC1BJ,MAAM,CAACK,IAAI,GAAG,KAAK;EACnB,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,aAAa,GAAGrB,UAAU,CAACsB,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACjE,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,GAAG,CAAC;EACvCc,MAAM,CAACE,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACN,GAAG,IAAI;IACnC,IAAI,OAAOX,GAAG,CAACW,GAAG,CAAC,KAAK,WAAW,EAAE;IACrC,IAAIF,aAAa,CAACS,OAAO,CAACP,GAAG,CAAC,IAAI,CAAC,EAAE;MACnC,IAAIrB,QAAQ,CAACU,GAAG,CAACW,GAAG,CAAC,CAAC,EAAE;QACtBT,MAAM,CAACS,GAAG,CAAC,GAAG,CAAC,CAAC;QAChBN,YAAY,CAACM,GAAG,CAAC,GAAG,CAAC,CAAC;QACtBzB,MAAM,CAACgB,MAAM,CAACS,GAAG,CAAC,EAAEX,GAAG,CAACW,GAAG,CAAC,CAAC;QAC7BzB,MAAM,CAACmB,YAAY,CAACM,GAAG,CAAC,EAAEX,GAAG,CAACW,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLT,MAAM,CAACS,GAAG,CAAC,GAAGX,GAAG,CAACW,GAAG,CAAC;QACtBN,YAAY,CAACM,GAAG,CAAC,GAAGX,GAAG,CAACW,GAAG,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIA,GAAG,CAACQ,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,OAAOnB,GAAG,CAACW,GAAG,CAAC,KAAK,UAAU,EAAE;MACxE,IAAIV,WAAW,EAAE;QACfG,MAAM,CAAC,GAAGO,GAAG,CAAC,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,GAAGT,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGrB,GAAG,CAACW,GAAG,CAAC;MAC9D,CAAC,MAAM;QACLT,MAAM,CAACC,EAAE,CAAC,GAAGQ,GAAG,CAAC,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC,GAAGT,GAAG,CAACU,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGrB,GAAG,CAACW,GAAG,CAAC;MACjE;IACF,CAAC,MAAM;MACLH,IAAI,CAACG,GAAG,CAAC,GAAGX,GAAG,CAACW,GAAG,CAAC;IACtB;EACF,CAAC,CAAC;EACF,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACM,OAAO,CAACN,GAAG,IAAI;IACvD,IAAIT,MAAM,CAACS,GAAG,CAAC,KAAK,IAAI,EAAET,MAAM,CAACS,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAIT,MAAM,CAACS,GAAG,CAAC,KAAK,KAAK,EAAE,OAAOT,MAAM,CAACS,GAAG,CAAC;EAC/C,CAAC,CAAC;EACF,OAAO;IACLT,MAAM;IACNG,YAAY;IACZG,IAAI;IACJJ;EACF,CAAC;AACH;AAEA,SAASkB,WAAWA,CAACC,IAAI,EAAEC,YAAY,EAAE;EACvC,IAAI;IACFC,EAAE;IACFC,MAAM;IACNC,MAAM;IACNC,YAAY;IACZC,WAAW;IACXC;EACF,CAAC,GAAGP,IAAI;EACR,IAAI/B,eAAe,CAACgC,YAAY,CAAC,IAAIE,MAAM,IAAIC,MAAM,EAAE;IACrDG,MAAM,CAAC5B,MAAM,CAAC6B,UAAU,CAACL,MAAM,GAAGA,MAAM;IACxCI,MAAM,CAACE,cAAc,CAACD,UAAU,CAACL,MAAM,GAAGA,MAAM;IAChDI,MAAM,CAAC5B,MAAM,CAAC6B,UAAU,CAACJ,MAAM,GAAGA,MAAM;IACxCG,MAAM,CAACE,cAAc,CAACD,UAAU,CAACJ,MAAM,GAAGA,MAAM;EAClD;EACA,IAAIjC,eAAe,CAAC8B,YAAY,CAAC,IAAII,YAAY,EAAE;IACjDE,MAAM,CAAC5B,MAAM,CAAC+B,UAAU,CAACR,EAAE,GAAGG,YAAY;IAC1CE,MAAM,CAACE,cAAc,CAACC,UAAU,CAACR,EAAE,GAAGG,YAAY;EACpD;EACA,IAAIhC,cAAc,CAAC4B,YAAY,CAAC,IAAIK,WAAW,EAAE;IAC/CC,MAAM,CAAC5B,MAAM,CAACgC,SAAS,CAACT,EAAE,GAAGI,WAAW;IACxCC,MAAM,CAACE,cAAc,CAACE,SAAS,CAACT,EAAE,GAAGI,WAAW;EAClD;EACAC,MAAM,CAACvB,IAAI,CAACkB,EAAE,CAAC;AACjB;AAEA,SAASU,gBAAgBA,CAACX,YAAY,EAAEY,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAE;EAChF,MAAMvB,IAAI,GAAG,EAAE;EACf,IAAI,CAACoB,SAAS,EAAE,OAAOpB,IAAI;EAC3B,MAAMwB,MAAM,GAAG7B,GAAG,IAAI;IACpB,IAAIK,IAAI,CAACE,OAAO,CAACP,GAAG,CAAC,GAAG,CAAC,EAAEK,IAAI,CAACyB,IAAI,CAAC9B,GAAG,CAAC;EAC3C,CAAC;EACD,IAAI0B,QAAQ,IAAIC,WAAW,EAAE;IAC3B,MAAMI,eAAe,GAAGJ,WAAW,CAAC5B,GAAG,CAAC6B,MAAM,CAAC;IAC/C,MAAMI,YAAY,GAAGN,QAAQ,CAAC3B,GAAG,CAAC6B,MAAM,CAAC;IACzC,IAAIG,eAAe,CAACE,IAAI,CAAC,EAAE,CAAC,KAAKD,YAAY,CAACC,IAAI,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAAC,UAAU,CAAC;IAC1E,IAAIF,WAAW,CAACO,MAAM,KAAKR,QAAQ,CAACQ,MAAM,EAAEL,MAAM,CAAC,UAAU,CAAC;EAChE;EACA,MAAMM,WAAW,GAAG1D,UAAU,CAAC2D,MAAM,CAACpC,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EAC7FkC,WAAW,CAAC7B,OAAO,CAACN,GAAG,IAAI;IACzB,IAAIA,GAAG,IAAIa,YAAY,IAAIb,GAAG,IAAIyB,SAAS,EAAE;MAC3C,IAAI9C,QAAQ,CAACkC,YAAY,CAACb,GAAG,CAAC,CAAC,IAAIrB,QAAQ,CAAC8C,SAAS,CAACzB,GAAG,CAAC,CAAC,EAAE;QAC3D,MAAMqC,OAAO,GAAGlC,MAAM,CAACE,IAAI,CAACQ,YAAY,CAACb,GAAG,CAAC,CAAC;QAC9C,MAAMsC,OAAO,GAAGnC,MAAM,CAACE,IAAI,CAACoB,SAAS,CAACzB,GAAG,CAAC,CAAC;QAC3C,IAAIqC,OAAO,CAACH,MAAM,KAAKI,OAAO,CAACJ,MAAM,EAAE;UACrCL,MAAM,CAAC7B,GAAG,CAAC;QACb,CAAC,MAAM;UACLqC,OAAO,CAAC/B,OAAO,CAACiC,MAAM,IAAI;YACxB,IAAI1B,YAAY,CAACb,GAAG,CAAC,CAACuC,MAAM,CAAC,KAAKd,SAAS,CAACzB,GAAG,CAAC,CAACuC,MAAM,CAAC,EAAE;cACxDV,MAAM,CAAC7B,GAAG,CAAC;YACb;UACF,CAAC,CAAC;UACFsC,OAAO,CAAChC,OAAO,CAACkC,MAAM,IAAI;YACxB,IAAI3B,YAAY,CAACb,GAAG,CAAC,CAACwC,MAAM,CAAC,KAAKf,SAAS,CAACzB,GAAG,CAAC,CAACwC,MAAM,CAAC,EAAEX,MAAM,CAAC7B,GAAG,CAAC;UACvE,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAIa,YAAY,CAACb,GAAG,CAAC,KAAKyB,SAAS,CAACzB,GAAG,CAAC,EAAE;QAC/C6B,MAAM,CAAC7B,GAAG,CAAC;MACb;IACF;EACF,CAAC,CAAC;EACF,OAAOK,IAAI;AACb;AAEA,MAAMoC,mBAAmB,GAAGtB,MAAM,IAAI;EACpC,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACuB,SAAS,IAAI,CAACvB,MAAM,CAAC5B,MAAM,CAACoD,OAAO,IAAIxB,MAAM,CAAC5B,MAAM,CAACoD,OAAO,IAAI,CAACxB,MAAM,CAAC5B,MAAM,CAACoD,OAAO,CAACC,OAAO,EAAE;EACtHzB,MAAM,CAAC0B,YAAY,CAAC,CAAC;EACrB1B,MAAM,CAAC2B,cAAc,CAAC,CAAC;EACvB3B,MAAM,CAAC4B,mBAAmB,CAAC,CAAC;EAC5B,IAAI5B,MAAM,CAAC6B,QAAQ,IAAI7B,MAAM,CAAC5B,MAAM,CAACyD,QAAQ,IAAI7B,MAAM,CAAC5B,MAAM,CAACyD,QAAQ,CAACJ,OAAO,EAAE;IAC/EzB,MAAM,CAAC6B,QAAQ,CAACC,YAAY,CAAC,CAAC;EAChC;AACF,CAAC;AAED,SAASzB,gBAAgB,IAAI1C,CAAC,EAAEM,SAAS,IAAI8D,CAAC,EAAEvC,WAAW,IAAIwC,CAAC,EAAEV,mBAAmB,IAAIW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}