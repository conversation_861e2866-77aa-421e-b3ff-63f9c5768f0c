{"ast": null, "code": "import{menuData}from'../../../../asset/data/ownersMenu';import{Link,useLocation}from'react-router-dom';import styles from'./sidebar.module.css';import{useState}from'react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Sidebar=()=>{const location=useLocation();const[isFilterOpen,setIsFilterOpen]=useState(false);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"aside\",{className:styles.sidebar,children:/*#__PURE__*/_jsx(\"div\",{className:styles.nav,children:/*#__PURE__*/_jsx(\"div\",{className:styles.links,children:menuData.map(item=>{const isActiveLink=location.pathname===item.url;return/*#__PURE__*/_jsx(Link,{to:item.url,className:isActiveLink?styles.active:undefined,children:item.title},item.id);})})})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.filterBar,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.filterSelected,onClick:()=>setIsFilterOpen(prev=>!prev),children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0412\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0430\\u043C GML\"}),/*#__PURE__*/_jsx(\"div\",{className:isFilterOpen?styles.iconMinus:styles.iconPlus,children:isFilterOpen?'-':'+'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.filterDropdownContainer,\" \").concat(isFilterOpen?styles.show:''),children:/*#__PURE__*/_jsx(\"div\",{className:styles.filterList,children:/*#__PURE__*/_jsx(\"div\",{className:styles.filterItem,children:/*#__PURE__*/_jsx(\"div\",{className:styles.setting,children:/*#__PURE__*/_jsx(\"div\",{className:styles.links,children:menuData.map(item=>{const isActiveLink=location.pathname===item.url;return/*#__PURE__*/_jsx(Link,{to:item.url,className:isActiveLink?styles.active:'undifined',children:item.title},item.id);})})})})})})]})]});};export default Sidebar;", "map": {"version": 3, "names": ["menuData", "Link", "useLocation", "styles", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Sidebar", "location", "isFilterOpen", "setIsFilterOpen", "children", "className", "sidebar", "nav", "links", "map", "item", "isActiveLink", "pathname", "url", "to", "active", "undefined", "title", "id", "filterBar", "filterSelected", "onClick", "prev", "iconMinus", "iconPlus", "concat", "filterDropdownContainer", "show", "filterList", "filterItem", "setting"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/components/sidebar/Sidebar.jsx"], "sourcesContent": ["import { menuData } from '../../../../asset/data/ownersMenu';\nimport { Link, useLocation } from 'react-router-dom';\nimport styles from './sidebar.module.css';\nimport { useState } from 'react';\n\nconst Sidebar = () => {\n  const location = useLocation();\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n\n  return (\n    <>\n      <aside className={styles.sidebar}>\n        <div className={styles.nav}>\n          <div className={styles.links}>\n            {menuData.map((item) => {\n              const isActiveLink = location.pathname === item.url;\n\n              return (\n                <Link\n                  to={item.url}\n                  key={item.id}\n                  className={isActiveLink ? styles.active : undefined}\n                >\n                  {item.title}\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </aside>\n      {/* mobile filter bar  */}\n      <div className={styles.filterBar}>\n        <div\n          className={styles.filterSelected}\n          onClick={() => setIsFilterOpen((prev) => !prev)}\n        >\n          <span>Владельцам GML</span>\n          <div className={isFilterOpen ? styles.iconMinus : styles.iconPlus}>\n            {isFilterOpen ? '-' : '+'}\n          </div>\n        </div>\n\n        <div\n          className={`${styles.filterDropdownContainer} ${isFilterOpen ? styles.show : ''\n            }`}\n        >\n          <div className={styles.filterList}>\n            <div className={styles.filterItem}>\n              <div className={styles.setting}>\n                <div className={styles.links}>\n                  {menuData.map((item) => {\n                    const isActiveLink = location.pathname === item.url;\n\n                    return (\n                      <Link\n                        to={item.url}\n                        key={item.id}\n                        className={isActiveLink ? styles.active : 'undifined'}\n                      >\n                        {item.title}\n                      </Link>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "AAAA,OAASA,QAAQ,KAAQ,mCAAmC,CAC5D,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,OAASC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjC,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACW,YAAY,CAAEC,eAAe,CAAC,CAAGV,QAAQ,CAAC,KAAK,CAAC,CAEvD,mBACEI,KAAA,CAAAE,SAAA,EAAAK,QAAA,eACET,IAAA,UAAOU,SAAS,CAAEb,MAAM,CAACc,OAAQ,CAAAF,QAAA,cAC/BT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACe,GAAI,CAAAH,QAAA,cACzBT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACgB,KAAM,CAAAJ,QAAA,CAC1Bf,QAAQ,CAACoB,GAAG,CAAEC,IAAI,EAAK,CACtB,KAAM,CAAAC,YAAY,CAAGV,QAAQ,CAACW,QAAQ,GAAKF,IAAI,CAACG,GAAG,CAEnD,mBACElB,IAAA,CAACL,IAAI,EACHwB,EAAE,CAAEJ,IAAI,CAACG,GAAI,CAEbR,SAAS,CAAEM,YAAY,CAAGnB,MAAM,CAACuB,MAAM,CAAGC,SAAU,CAAAZ,QAAA,CAEnDM,IAAI,CAACO,KAAK,EAHNP,IAAI,CAACQ,EAIN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,CACH,CAAC,CACD,CAAC,cAERrB,KAAA,QAAKQ,SAAS,CAAEb,MAAM,CAAC2B,SAAU,CAAAf,QAAA,eAC/BP,KAAA,QACEQ,SAAS,CAAEb,MAAM,CAAC4B,cAAe,CACjCC,OAAO,CAAEA,CAAA,GAAMlB,eAAe,CAAEmB,IAAI,EAAK,CAACA,IAAI,CAAE,CAAAlB,QAAA,eAEhDT,IAAA,SAAAS,QAAA,CAAM,kEAAc,CAAM,CAAC,cAC3BT,IAAA,QAAKU,SAAS,CAAEH,YAAY,CAAGV,MAAM,CAAC+B,SAAS,CAAG/B,MAAM,CAACgC,QAAS,CAAApB,QAAA,CAC/DF,YAAY,CAAG,GAAG,CAAG,GAAG,CACtB,CAAC,EACH,CAAC,cAENP,IAAA,QACEU,SAAS,IAAAoB,MAAA,CAAKjC,MAAM,CAACkC,uBAAuB,MAAAD,MAAA,CAAIvB,YAAY,CAAGV,MAAM,CAACmC,IAAI,CAAG,EAAE,CAC1E,CAAAvB,QAAA,cAELT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACoC,UAAW,CAAAxB,QAAA,cAChCT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACqC,UAAW,CAAAzB,QAAA,cAChCT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACsC,OAAQ,CAAA1B,QAAA,cAC7BT,IAAA,QAAKU,SAAS,CAAEb,MAAM,CAACgB,KAAM,CAAAJ,QAAA,CAC1Bf,QAAQ,CAACoB,GAAG,CAAEC,IAAI,EAAK,CACtB,KAAM,CAAAC,YAAY,CAAGV,QAAQ,CAACW,QAAQ,GAAKF,IAAI,CAACG,GAAG,CAEnD,mBACElB,IAAA,CAACL,IAAI,EACHwB,EAAE,CAAEJ,IAAI,CAACG,GAAI,CAEbR,SAAS,CAAEM,YAAY,CAAGnB,MAAM,CAACuB,MAAM,CAAG,WAAY,CAAAX,QAAA,CAErDM,IAAI,CAACO,KAAK,EAHNP,IAAI,CAACQ,EAIN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAlB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}