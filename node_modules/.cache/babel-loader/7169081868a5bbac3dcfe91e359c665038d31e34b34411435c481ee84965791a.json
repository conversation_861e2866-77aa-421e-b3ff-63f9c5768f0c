{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { animations } from '../../motion/features/animations.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domAnimation = _objectSpread(_objectSpread({\n  renderer: createDomVisualElement\n}, animations), gestureAnimations);\nexport { domAnimation };", "map": {"version": 3, "names": ["animations", "gestureAnimations", "createDomVisualElement", "domAnimation", "_objectSpread", "renderer"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs"], "sourcesContent": ["import { animations } from '../../motion/features/animations.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domAnimation = {\n    renderer: createDomVisualElement,\n    ...animations,\n    ...gestureAnimations,\n};\n\nexport { domAnimation };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,sCAAsC;AACjE,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,sBAAsB,QAAQ,6BAA6B;;AAEpE;AACA;AACA;AACA,MAAMC,YAAY,GAAAC,aAAA,CAAAA,aAAA;EACdC,QAAQ,EAAEH;AAAsB,GAC7BF,UAAU,GACVC,iBAAiB,CACvB;AAED,SAASE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}