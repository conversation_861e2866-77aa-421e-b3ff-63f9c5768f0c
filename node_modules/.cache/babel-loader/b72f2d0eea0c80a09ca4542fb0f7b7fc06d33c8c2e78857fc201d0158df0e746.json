{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';// import { initPerformanceMonitoring, registerServiceWorker } from './utils/performance';\n// import { initAccessibility, addHighContrastSupport } from './utils/accessibility';\n// Initialize performance monitoring\n// initPerformanceMonitoring();\n// Initialize accessibility features\n// initAccessibility();\n// addHighContrastSupport();\n// Register service worker for caching\n// registerServiceWorker();\nimport{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["/var/www/html/gwm.tj/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n// import { initPerformanceMonitoring, registerServiceWorker } from './utils/performance';\n// import { initAccessibility, addHighContrastSupport } from './utils/accessibility';\n\n// Initialize performance monitoring\n// initPerformanceMonitoring();\n\n// Initialize accessibility features\n// initAccessibility();\n// addHighContrastSupport();\n\n// Register service worker for caching\n// registerServiceWorker();\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBAEA,KAAM,CAAAC,IAAI,CAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACJ,KAAK,CAACU,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACF,GAAG,GAAE,CAAC,CACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}