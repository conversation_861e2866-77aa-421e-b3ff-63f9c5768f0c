{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/components/Image-card/ImageCard.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './ImageCard.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageCard = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: styles.section,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.card,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.content,\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"GWM \\u0423\\u0425\\u041E\\u0414\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/owners\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"button-white\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0423\\u0437\\u043D\\u0430\\u0442\\u044C \\u0431\\u043E\\u043B\\u044C\\u0448\\u0435\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 14,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = ImageCard;\nexport default ImageCard;\nvar _c;\n$RefreshReg$(_c, \"ImageCard\");", "map": {"version": 3, "names": ["React", "Link", "styles", "jsxDEV", "_jsxDEV", "ImageCard", "className", "section", "children", "card", "content", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/Image-card/ImageCard.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './ImageCard.module.css';\n\nconst ImageCard = () => {\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.card}>\n          <div className={styles.content}>\n            <h3>GWM УХОД</h3>\n            <Link to=\"/owners\">\n              <button className=\"button-white\">\n                <span>Узнать больше</span>\n              </button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ImageCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACED,OAAA;IAASE,SAAS,EAAEJ,MAAM,CAACK,OAAQ;IAAAC,QAAA,eACjCJ,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAE,QAAA,eACxBJ,OAAA;QAAKE,SAAS,EAAEJ,MAAM,CAACO,IAAK;QAAAD,QAAA,eAC1BJ,OAAA;UAAKE,SAAS,EAAEJ,MAAM,CAACQ,OAAQ;UAAAF,QAAA,gBAC7BJ,OAAA;YAAAI,QAAA,EAAI;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBV,OAAA,CAACH,IAAI;YAACc,EAAE,EAAC,SAAS;YAAAP,QAAA,eAChBJ,OAAA;cAAQE,SAAS,EAAC,cAAc;cAAAE,QAAA,eAC9BJ,OAAA;gBAAAI,QAAA,EAAM;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACE,EAAA,GAjBIX,SAAS;AAmBf,eAAeA,SAAS;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}