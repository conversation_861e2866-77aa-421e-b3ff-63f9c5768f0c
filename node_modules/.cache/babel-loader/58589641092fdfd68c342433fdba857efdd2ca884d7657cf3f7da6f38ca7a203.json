{"ast": null, "code": "import { resolveElements, isSVGElement } from 'motion-dom';\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n  if (borderBoxSize) {\n    const {\n      inlineSize,\n      blockSize\n    } = borderBoxSize[0];\n    return {\n      width: inlineSize,\n      height: blockSize\n    };\n  } else if (isSVGElement(target) && \"getBBox\" in target) {\n    return target.getBBox();\n  } else {\n    return {\n      width: target.offsetWidth,\n      height: target.offsetHeight\n    };\n  }\n}\nfunction notifyTarget({\n  target,\n  contentRect,\n  borderBoxSize\n}) {\n  resizeHandlers.get(target)?.forEach(handler => {\n    handler({\n      target,\n      contentSize: contentRect,\n      get size() {\n        return getElementSize(target, borderBoxSize);\n      }\n    });\n  });\n}\nfunction notifyAll(entries) {\n  entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n  if (typeof ResizeObserver === \"undefined\") return;\n  observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n  if (!observer) createResizeObserver();\n  const elements = resolveElements(target);\n  elements.forEach(element => {\n    let elementHandlers = resizeHandlers.get(element);\n    if (!elementHandlers) {\n      elementHandlers = new Set();\n      resizeHandlers.set(element, elementHandlers);\n    }\n    elementHandlers.add(handler);\n    observer?.observe(element);\n  });\n  return () => {\n    elements.forEach(element => {\n      const elementHandlers = resizeHandlers.get(element);\n      elementHandlers?.delete(handler);\n      if (!elementHandlers?.size) {\n        observer?.unobserve(element);\n      }\n    });\n  };\n}\nexport { resizeElement };", "map": {"version": 3, "names": ["resolveElements", "isSVGElement", "resizeHandlers", "WeakMap", "observer", "getElementSize", "target", "borderBoxSize", "inlineSize", "blockSize", "width", "height", "getBBox", "offsetWidth", "offsetHeight", "notify<PERSON><PERSON><PERSON>", "contentRect", "get", "for<PERSON>ach", "handler", "contentSize", "size", "notifyAll", "entries", "createResizeObserver", "ResizeObserver", "resizeElement", "elements", "element", "elementHandlers", "Set", "set", "add", "observe", "delete", "unobserve"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs"], "sourcesContent": ["import { resolveElements, isSVGElement } from 'motion-dom';\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (isSVGElement(target) && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = resolveElements(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\nexport { resizeElement };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,YAAY,QAAQ,YAAY;AAE1D,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AACpC,IAAIC,QAAQ;AACZ,SAASC,cAAcA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAC3C,IAAIA,aAAa,EAAE;IACf,MAAM;MAAEC,UAAU;MAAEC;IAAU,CAAC,GAAGF,aAAa,CAAC,CAAC,CAAC;IAClD,OAAO;MAAEG,KAAK,EAAEF,UAAU;MAAEG,MAAM,EAAEF;IAAU,CAAC;EACnD,CAAC,MACI,IAAIR,YAAY,CAACK,MAAM,CAAC,IAAI,SAAS,IAAIA,MAAM,EAAE;IAClD,OAAOA,MAAM,CAACM,OAAO,CAAC,CAAC;EAC3B,CAAC,MACI;IACD,OAAO;MACHF,KAAK,EAAEJ,MAAM,CAACO,WAAW;MACzBF,MAAM,EAAEL,MAAM,CAACQ;IACnB,CAAC;EACL;AACJ;AACA,SAASC,YAAYA,CAAC;EAAET,MAAM;EAAEU,WAAW;EAAET;AAAe,CAAC,EAAE;EAC3DL,cAAc,CAACe,GAAG,CAACX,MAAM,CAAC,EAAEY,OAAO,CAAEC,OAAO,IAAK;IAC7CA,OAAO,CAAC;MACJb,MAAM;MACNc,WAAW,EAAEJ,WAAW;MACxB,IAAIK,IAAIA,CAAA,EAAG;QACP,OAAOhB,cAAc,CAACC,MAAM,EAAEC,aAAa,CAAC;MAChD;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASe,SAASA,CAACC,OAAO,EAAE;EACxBA,OAAO,CAACL,OAAO,CAACH,YAAY,CAAC;AACjC;AACA,SAASS,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,cAAc,KAAK,WAAW,EACrC;EACJrB,QAAQ,GAAG,IAAIqB,cAAc,CAACH,SAAS,CAAC;AAC5C;AACA,SAASI,aAAaA,CAACpB,MAAM,EAAEa,OAAO,EAAE;EACpC,IAAI,CAACf,QAAQ,EACToB,oBAAoB,CAAC,CAAC;EAC1B,MAAMG,QAAQ,GAAG3B,eAAe,CAACM,MAAM,CAAC;EACxCqB,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAC1B,IAAIC,eAAe,GAAG3B,cAAc,CAACe,GAAG,CAACW,OAAO,CAAC;IACjD,IAAI,CAACC,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B5B,cAAc,CAAC6B,GAAG,CAACH,OAAO,EAAEC,eAAe,CAAC;IAChD;IACAA,eAAe,CAACG,GAAG,CAACb,OAAO,CAAC;IAC5Bf,QAAQ,EAAE6B,OAAO,CAACL,OAAO,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,MAAM;IACTD,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;MAC1B,MAAMC,eAAe,GAAG3B,cAAc,CAACe,GAAG,CAACW,OAAO,CAAC;MACnDC,eAAe,EAAEK,MAAM,CAACf,OAAO,CAAC;MAChC,IAAI,CAACU,eAAe,EAAER,IAAI,EAAE;QACxBjB,QAAQ,EAAE+B,SAAS,CAACP,OAAO,CAAC;MAChC;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}