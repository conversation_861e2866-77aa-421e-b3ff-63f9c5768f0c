{"ast": null, "code": "const springDefaults = {\n  // Default spring physics\n  stiffness: 100,\n  damping: 10,\n  mass: 1.0,\n  velocity: 0.0,\n  // Default duration/bounce-based options\n  duration: 800,\n  // in ms\n  bounce: 0.3,\n  visualDuration: 0.3,\n  // in seconds\n  // Rest thresholds\n  restSpeed: {\n    granular: 0.01,\n    default: 2\n  },\n  restDelta: {\n    granular: 0.005,\n    default: 0.5\n  },\n  // Limits\n  minDuration: 0.01,\n  // in seconds\n  maxDuration: 10.0,\n  // in seconds\n  minDamping: 0.05,\n  maxDamping: 1\n};\nexport { springDefaults };", "map": {"version": 3, "names": ["springDefaults", "stiffness", "damping", "mass", "velocity", "duration", "bounce", "visualDuration", "restSpeed", "granular", "default", "restDelta", "minDuration", "maxDuration", "minDamping", "maxDamping"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/generators/spring/defaults.mjs"], "sourcesContent": ["const springDefaults = {\n    // Default spring physics\n    stiffness: 100,\n    damping: 10,\n    mass: 1.0,\n    velocity: 0.0,\n    // Default duration/bounce-based options\n    duration: 800, // in ms\n    bounce: 0.3,\n    visualDuration: 0.3, // in seconds\n    // Rest thresholds\n    restSpeed: {\n        granular: 0.01,\n        default: 2,\n    },\n    restDelta: {\n        granular: 0.005,\n        default: 0.5,\n    },\n    // Limits\n    minDuration: 0.01, // in seconds\n    maxDuration: 10.0, // in seconds\n    minDamping: 0.05,\n    maxDamping: 1,\n};\n\nexport { springDefaults };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACnB;EACAC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAE,EAAE;EACXC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE,GAAG;EACb;EACAC,QAAQ,EAAE,GAAG;EAAE;EACfC,MAAM,EAAE,GAAG;EACXC,cAAc,EAAE,GAAG;EAAE;EACrB;EACAC,SAAS,EAAE;IACPC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACb,CAAC;EACDC,SAAS,EAAE;IACPF,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACb,CAAC;EACD;EACAE,WAAW,EAAE,IAAI;EAAE;EACnBC,WAAW,EAAE,IAAI;EAAE;EACnBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;AAChB,CAAC;AAED,SAASf,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}