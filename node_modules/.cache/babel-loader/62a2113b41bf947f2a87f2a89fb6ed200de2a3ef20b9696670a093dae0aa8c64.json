{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Home/components/Models/FilterSlide/FilterSwiper.jsx\";\nimport React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterSwiper = ({\n  activeModel,\n  setActiveModel,\n  cars\n}) => {\n  const count = cars.length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.carsModelsList,\n    children: /*#__PURE__*/_jsxDEV(Swiper, {\n      breakpoints: {\n        320: {\n          slidesPerView: 2,\n          spaceBetween: 0\n        },\n        580: {\n          slidesPerView: 2.5,\n          spaceBetween: 0\n        },\n        750: {\n          slidesPerView: 4.5,\n          spaceBetween: 0\n        },\n        860: {\n          slidesPerView: 4.5,\n          spaceBetween: 0\n        },\n        1160: {\n          slidesPerView: 5.5,\n          spaceBetween: 0\n        },\n        1460: {\n          slidesPerView: 6.5,\n          spaceBetween: 0\n        }\n      },\n      spaceBetween: 10,\n      grabCursor: true,\n      className: \"mySwiper\",\n      children: [cars.map(car => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.btn} ${activeModel === car.id ? styles.active : ''}`,\n          onClick: () => setActiveModel(car.id),\n          children: car.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this)\n      }, car.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.activeBar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = FilterSwiper;\nexport default FilterSwiper;\nvar _c;\n$RefreshReg$(_c, \"FilterSwiper\");", "map": {"version": 3, "names": ["React", "Swiper", "SwiperSlide", "styles", "jsxDEV", "_jsxDEV", "FilterSwiper", "activeModel", "setActiveModel", "cars", "count", "length", "className", "carsModelsList", "children", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "grabCursor", "map", "car", "btn", "id", "active", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "activeBar", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/Models/FilterSlide/FilterSwiper.jsx"], "sourcesContent": ["import React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\n\nconst FilterSwiper = ({ activeModel, setActiveModel, cars }) => {\n  const count = cars.length;\n\n  return (\n    <div className={styles.carsModelsList}>\n      <Swiper\n        breakpoints={{\n          320: {\n            slidesPerView: 2,\n            spaceBetween: 0,\n          },\n          580: {\n            slidesPerView: 2.5,\n            spaceBetween: 0,\n          },\n          750: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          860: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          1160: {\n            slidesPerView: 5.5,\n            spaceBetween: 0,\n          },\n          1460: {\n            slidesPerView: 6.5,\n            spaceBetween: 0,\n          },\n        }}\n        spaceBetween={10}\n        grabCursor={true}\n        className=\"mySwiper\"\n      >\n        {cars.map((car) => (\n          <SwiperSlide key={car.id}>\n            <div\n              className={`${styles.btn} ${\n                activeModel === car.id ? styles.active : ''\n              }`}\n              onClick={() => setActiveModel(car.id)}\n            >\n              {car.title}\n            </div>\n          </SwiperSlide>\n        ))}\n        <div className={styles.activeBar}></div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default FilterSwiper;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD;AACA,OAAO,YAAY;AACnB,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,cAAc;EAAEC;AAAK,CAAC,KAAK;EAC9D,MAAMC,KAAK,GAAGD,IAAI,CAACE,MAAM;EAEzB,oBACEN,OAAA;IAAKO,SAAS,EAAET,MAAM,CAACU,cAAe;IAAAC,QAAA,eACpCT,OAAA,CAACJ,MAAM;MACLc,WAAW,EAAE;QACX,GAAG,EAAE;UACHC,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,IAAI,EAAE;UACJD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,IAAI,EAAE;UACJD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB;MACF,CAAE;MACFA,YAAY,EAAE,EAAG;MACjBC,UAAU,EAAE,IAAK;MACjBN,SAAS,EAAC,UAAU;MAAAE,QAAA,GAEnBL,IAAI,CAACU,GAAG,CAAEC,GAAG,iBACZf,OAAA,CAACH,WAAW;QAAAY,QAAA,eACVT,OAAA;UACEO,SAAS,EAAE,GAAGT,MAAM,CAACkB,GAAG,IACtBd,WAAW,KAAKa,GAAG,CAACE,EAAE,GAAGnB,MAAM,CAACoB,MAAM,GAAG,EAAE,EAC1C;UACHC,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAACY,GAAG,CAACE,EAAE,CAAE;UAAAR,QAAA,EAErCM,GAAG,CAACK;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC,GARUT,GAAG,CAACE,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASX,CACd,CAAC,eACFxB,OAAA;QAAKO,SAAS,EAAET,MAAM,CAAC2B;MAAU;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GApDIzB,YAAY;AAsDlB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}