{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'./history.module.css';import AOS from'aos';import'aos/dist/aos.css';// swiper\nimport{Swiper,SwiperSlide}from'swiper/react';import{Navigation}from'swiper/modules';import'swiper/css';import'swiper/css/navigation';// imgs\nimport img_1 from'../../../../asset/imgs/history/1.webp';import img_2 from'../../../../asset/imgs/history/2.webp';import img_3 from'../../../../asset/imgs/history/history-25.webp';import img_4 from'../../../../asset/imgs/history/history-24-2.webp';import img_5 from'../../../../asset/imgs/history/history-24.webp';import img_6 from'../../../../asset/imgs/history/history-24-1.webp';import img_7 from'../../../../asset/imgs/history/history-23.webp';import img_8 from'../../../../asset/imgs/history/history-22.webp';import img_9 from'../../../../asset/imgs/history/history-21.webp';import img_10 from'../../../../asset/imgs/history/20.webp';import img_11 from'../../../../asset/imgs/history/2015.webp';import img_12 from'../../../../asset/imgs/history/2014.webp';import img_13 from'../../../../asset/imgs/history/2011.webp';import img_14 from'../../../../asset/imgs/history/2006.webp';import img_15 from'../../../../asset/imgs/history/2002.webp';import img_16 from'../../../../asset/imgs/history/1999.webp';import img_17 from'../../../../asset/imgs/history/1993.webp';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const data=[{id:1,date:'2024 | Март',desc:'В марте 2024 года GWM официально представлена в Таджикистане',img:img_1},{id:2,date:'2025 | Февраль',desc:'GWM P300 будет запущен 26 февраля 2025 г.',img:img_2},{id:3,date:'2025 | Январь',desc:'HAVAL H7 будет запущен в январе 2025 года',img:img_3},{id:4,date:'2024 | Август',desc:'GWM P500 и TANK 500 будут запущены в августе 2024 года',img:img_4},{id:5,date:'2024 | Июнь',desc:'HAVAL JOLION PRO выйдет в июне 2024 года',img:img_5},{id:6,date:'2024 | Февраль',desc:'GWM TANK 300 будет запущен в феврале 2024 года.',img:img_6},{id:7,date:'2023 | Ноябрь',desc:'GWM ORA03 будет запущен в ноябре 2023 года.',img:img_7},{id:8,date:'2023 | Сентябрь',desc:'HAVAL H6 GT выйшел в сентябре 2023 года.',img:img_8},{id:9,date:'2021 | Апрель',desc:'HAVAL JOLION был запущен в апреле 2021 года.',img:img_9},{id:10,date:'2020',desc:'В 2020 году компания GWM увеличила объем продаж по сравнению с рынком, реализовав за год 1 111 598 автомобилей, выполнив 109% от цели и вновь став национальным чемпионом по продажам внедорожников и пикапов.',img:img_10},{id:11,date:'2015 - 2019',desc:'HAVAL вышел на рынки Австралии, Южной Африки, Евразии, Эквадора, Чили, Саудовской Аравии и других стран.',img:img_11},{id:12,date:'2014 | Ноябрь',desc:'GWM подчеркнула свою стратегию внедорожников запуском моделей HAVAL H9, H1 и H2, завершив линейку внедорожников HAVAL. Бренд представил 8 моделей HAVAL на Пекинской международной автомобильной выставке в апреле. К октябрю продажи HAVAL H2 превысили 10 000 единиц за один месяц, войдя в пятерку самых продаваемых внедорожников.',img:img_12},{id:13,date:'2011 | Ноябрь',desc:'HAVAL H6, «национальный легендарный автомобиль», официально представлен по всему миру.',img:img_13},{id:14,date:'2006 | Июль',desc:'GWM завоевала звание «500 лучших производителей машин Китая»',img:img_14},{id:15,date:'2002 | Май',desc:'GWM выпустила Safe SUV, первый экономичный внедорожник в Китае. Автомобиль вошел в тройку лидеров на китайском рынке внедорожников, создав «Безопасный феномен».',img:img_15},{id:16,date:'1999 | Октябрь',desc:'Годовой объем производства GWM превысил 10 000 единиц, и было выпущено пять моделей печей с четырьмя шасси и различными техническими характеристиками. GWM стала профессиональным производителем печей с самым большим ассортиментом в Китае.',img:img_16},{id:17,date:'1993 | Март',desc:'GWM выпустила первый автомобиль',img:img_17}];const History=()=>{const[loading,setLoading]=useState(true);const[activeIndex,setActiveIndex]=useState(0);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0418\\u0441\\u0442\\u043E\\u0440\\u0438\\u044F GWM\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:\"redLine\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:\"GWM \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u0442\\u0441\\u044F \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0438\\u0442\\u044C \\u043D\\u043E\\u0432\\u0435\\u0439\\u0448\\u0438\\u0435 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0438 \\u044D\\u043A\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438 \\u0447\\u0438\\u0441\\u0442\\u044B\\u0435 \\u0443\\u0441\\u043B\\u0443\\u0433\\u0438 \\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043D\\u0430\\u0448\\u0438\\u043C \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430\\u043C \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443 \\u0438 \\u0443\\u0441\\u043A\\u043E\\u0440\\u0438\\u0442\\u044C \\u043D\\u0430\\u0448\\u0443 \\u0442\\u0440\\u0430\\u043D\\u0441\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044E \\u0432 \\u0433\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u0443\\u044E \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u0443\\u044E \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0443\\u044E \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044E. \\u041D\\u0430\\u0448 \\u043A\\u043B\\u044E\\u0447\\u0435\\u0432\\u043E\\u0439 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u043E\\u0445\\u0432\\u0430\\u0442\\u044B\\u0432\\u0430\\u0435\\u0442 \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435, \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435, \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E, \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u0443 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0442\\u0440\\u0430\\u043D\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u0445 \\u0441\\u0440\\u0435\\u0434\\u0441\\u0442\\u0432 \\u0438 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u043E\\u0432. \\u041C\\u044B \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0441\\u0434\\u0435\\u043B\\u0430\\u043B\\u0438 \\u0441\\u0442\\u0440\\u0430\\u0442\\u0435\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u0435 \\u0438\\u043D\\u0432\\u0435\\u0441\\u0442\\u0438\\u0446\\u0438\\u0438 \\u0432 \\u0432\\u043E\\u0434\\u043E\\u0440\\u043E\\u0434\\u043D\\u0443\\u044E \\u0438 \\u0441\\u043E\\u043B\\u043D\\u0435\\u0447\\u043D\\u0443\\u044E \\u044D\\u043D\\u0435\\u0440\\u0433\\u0438\\u044E. \\u041C\\u0435\\u0436\\u0434\\u0443 \\u0442\\u0435\\u043C, GWM \\u0441\\u043E\\u0441\\u0440\\u0435\\u0434\\u043E\\u0442\\u043E\\u0447\\u0438\\u0442\\u0441\\u044F \\u043D\\u0430 \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0435 \\u0438 \\u043F\\u0440\\u0438\\u043C\\u0435\\u043D\\u0435\\u043D\\u0438\\u0438 Connectivity, \\u0430\\u0432\\u0442\\u043E\\u043D\\u043E\\u043C\\u043D\\u043E\\u0433\\u043E \\u0432\\u043E\\u0436\\u0434\\u0435\\u043D\\u0438\\u044F \\u0438 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0447\\u0438\\u043F\\u043E\\u0432.\"})]}),/*#__PURE__*/_jsxs(\"section\",{className:styles.sliderSection,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.progressBarWrapper,children:/*#__PURE__*/_jsx(\"div\",{className:styles.progressBar,style:{width:\"\".concat((activeIndex+1)/data.length*100,\"%\")}})}),/*#__PURE__*/_jsx(Swiper,{modules:[Navigation],navigation:true,spaceBetween:50,slidesPerView:1,loop:false,onSlideChange:swiper=>setActiveIndex(swiper.activeIndex),children:data.map(item=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsxs(\"div\",{className:styles.slideContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.leftImage,children:/*#__PURE__*/_jsx(\"img\",{src:item.img,alt:item.date})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.rightText,children:[/*#__PURE__*/_jsx(\"h3\",{children:item.date}),/*#__PURE__*/_jsx(\"p\",{children:item.desc})]})]})},item.id))})]})]})})});};export default History;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "AOS", "Swiper", "SwiperSlide", "Navigation", "img_1", "img_2", "img_3", "img_4", "img_5", "img_6", "img_7", "img_8", "img_9", "img_10", "img_11", "img_12", "img_13", "img_14", "img_15", "img_16", "img_17", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "data", "id", "date", "desc", "img", "History", "loading", "setLoading", "activeIndex", "setActiveIndex", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "sliderSection", "progressBarWrapper", "progressBar", "width", "concat", "length", "modules", "navigation", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "loop", "onSlideChange", "swiper", "map", "item", "slideContent", "leftImage", "src", "alt", "rightText"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/About/History/History.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './history.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation } from 'swiper/modules';\n\nimport 'swiper/css';\nimport 'swiper/css/navigation';\n\n// imgs\nimport img_1 from '../../../../asset/imgs/history/1.webp';\nimport img_2 from '../../../../asset/imgs/history/2.webp';\nimport img_3 from '../../../../asset/imgs/history/history-25.webp';\nimport img_4 from '../../../../asset/imgs/history/history-24-2.webp';\nimport img_5 from '../../../../asset/imgs/history/history-24.webp';\nimport img_6 from '../../../../asset/imgs/history/history-24-1.webp';\nimport img_7 from '../../../../asset/imgs/history/history-23.webp';\nimport img_8 from '../../../../asset/imgs/history/history-22.webp';\nimport img_9 from '../../../../asset/imgs/history/history-21.webp';\nimport img_10 from '../../../../asset/imgs/history/20.webp';\nimport img_11 from '../../../../asset/imgs/history/2015.webp';\nimport img_12 from '../../../../asset/imgs/history/2014.webp';\nimport img_13 from '../../../../asset/imgs/history/2011.webp';\nimport img_14 from '../../../../asset/imgs/history/2006.webp';\nimport img_15 from '../../../../asset/imgs/history/2002.webp';\nimport img_16 from '../../../../asset/imgs/history/1999.webp';\nimport img_17 from '../../../../asset/imgs/history/1993.webp';\n\nconst data = [\n  {\n    id: 1,\n    date: '2024 | Март',\n    desc: 'В марте 2024 года GWM официально представлена в Таджикистане',\n    img: img_1,\n  },\n  {\n    id: 2,\n    date: '2025 | Февраль',\n    desc: 'GWM P300 будет запущен 26 февраля 2025 г.',\n    img: img_2,\n  },\n  {\n    id: 3,\n    date: '2025 | Январь',\n    desc: 'HAVAL H7 будет запущен в январе 2025 года',\n    img: img_3,\n  },\n  {\n    id: 4,\n    date: '2024 | Август',\n    desc: 'GWM P500 и TANK 500 будут запущены в августе 2024 года',\n    img: img_4,\n  },\n  {\n    id: 5,\n    date: '2024 | Июнь',\n    desc: 'HAVAL JOLION PRO выйдет в июне 2024 года',\n    img: img_5,\n  },\n  {\n    id: 6,\n    date: '2024 | Февраль',\n    desc: 'GWM TANK 300 будет запущен в феврале 2024 года.',\n    img: img_6,\n  },\n  {\n    id: 7,\n    date: '2023 | Ноябрь',\n    desc: 'GWM ORA03 будет запущен в ноябре 2023 года.',\n    img: img_7,\n  },\n  {\n    id: 8,\n    date: '2023 | Сентябрь',\n    desc: 'HAVAL H6 GT выйшел в сентябре 2023 года.',\n    img: img_8,\n  },\n  {\n    id: 9,\n    date: '2021 | Апрель',\n    desc: 'HAVAL JOLION был запущен в апреле 2021 года.',\n    img: img_9,\n  },\n  {\n    id: 10,\n    date: '2020',\n    desc: 'В 2020 году компания GWM увеличила объем продаж по сравнению с рынком, реализовав за год 1 111 598 автомобилей, выполнив 109% от цели и вновь став национальным чемпионом по продажам внедорожников и пикапов.',\n    img: img_10,\n  },\n  {\n    id: 11,\n    date: '2015 - 2019',\n    desc: 'HAVAL вышел на рынки Австралии, Южной Африки, Евразии, Эквадора, Чили, Саудовской Аравии и других стран.',\n    img: img_11,\n  },\n  {\n    id: 12,\n    date: '2014 | Ноябрь',\n    desc: 'GWM подчеркнула свою стратегию внедорожников запуском моделей HAVAL H9, H1 и H2, завершив линейку внедорожников HAVAL. Бренд представил 8 моделей HAVAL на Пекинской международной автомобильной выставке в апреле. К октябрю продажи HAVAL H2 превысили 10 000 единиц за один месяц, войдя в пятерку самых продаваемых внедорожников.',\n    img: img_12,\n  },\n  {\n    id: 13,\n    date: '2011 | Ноябрь',\n    desc: 'HAVAL H6, «национальный легендарный автомобиль», официально представлен по всему миру.',\n    img: img_13,\n  },\n  {\n    id: 14,\n    date: '2006 | Июль',\n    desc: 'GWM завоевала звание «500 лучших производителей машин Китая»',\n    img: img_14,\n  },\n  {\n    id: 15,\n    date: '2002 | Май',\n    desc: 'GWM выпустила Safe SUV, первый экономичный внедорожник в Китае. Автомобиль вошел в тройку лидеров на китайском рынке внедорожников, создав «Безопасный феномен».',\n    img: img_15,\n  },\n  {\n    id: 16,\n    date: '1999 | Октябрь',\n    desc: 'Годовой объем производства GWM превысил 10 000 единиц, и было выпущено пять моделей печей с четырьмя шасси и различными техническими характеристиками. GWM стала профессиональным производителем печей с самым большим ассортиментом в Китае.',\n    img: img_16,\n  },\n  {\n    id: 17,\n    date: '1993 | Март',\n    desc: 'GWM выпустила первый автомобиль',\n    img: img_17,\n  },\n];\n\nconst History = () => {\n  const [loading, setLoading] = useState(true);\n  const [activeIndex, setActiveIndex] = useState(0);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"100\">\n                <strong>История GWM</strong>\n              </h1>\n              <i data-aos=\"fade-up\" data-aos-delay=\"150\" className=\"redLine\" />\n              <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                GWM стремится предложить новейшие интеллектуальные и\n                экологически чистые услуги мобильности нашим клиентам по всему\n                миру и ускорить нашу трансформацию в глобальную интеллектуальную\n                технологическую компанию. Наш ключевой бизнес охватывает\n                проектирование, проектирование, производство, продажу и\n                обслуживание автомобильных транспортных средств и компонентов.\n                Мы также сделали стратегические инвестиции в водородную и\n                солнечную энергию. Между тем, GWM сосредоточится на разработке и\n                применении Connectivity, автономного вождения и автомобильных\n                чипов.\n              </p>\n            </div>\n\n            <section className={styles.sliderSection}>\n              <div className={styles.progressBarWrapper}>\n                <div\n                  className={styles.progressBar}\n                  style={{\n                    width: `${((activeIndex + 1) / data.length) * 100}%`,\n                  }}\n                />\n              </div>\n\n              <Swiper\n                modules={[Navigation]}\n                navigation\n                spaceBetween={50}\n                slidesPerView={1}\n                loop={false}\n                onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}\n              >\n                {data.map((item) => (\n                  <SwiperSlide key={item.id}>\n                    <div className={styles.slideContent}>\n                      <div className={styles.leftImage}>\n                        <img src={item.img} alt={item.date} />\n                      </div>\n                      <div className={styles.rightText}>\n                        <h3>{item.date}</h3>\n                        <p>{item.desc}</p>\n                      </div>\n                    </div>\n                  </SwiperSlide>\n                ))}\n              </Swiper>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default History;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAEzB;AACA,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD,OAASC,UAAU,KAAQ,gBAAgB,CAE3C,MAAO,YAAY,CACnB,MAAO,uBAAuB,CAE9B;AACA,MAAO,CAAAC,KAAK,KAAM,uCAAuC,CACzD,MAAO,CAAAC,KAAK,KAAM,uCAAuC,CACzD,MAAO,CAAAC,KAAK,KAAM,gDAAgD,CAClE,MAAO,CAAAC,KAAK,KAAM,kDAAkD,CACpE,MAAO,CAAAC,KAAK,KAAM,gDAAgD,CAClE,MAAO,CAAAC,KAAK,KAAM,kDAAkD,CACpE,MAAO,CAAAC,KAAK,KAAM,gDAAgD,CAClE,MAAO,CAAAC,KAAK,KAAM,gDAAgD,CAClE,MAAO,CAAAC,KAAK,KAAM,gDAAgD,CAClE,MAAO,CAAAC,MAAM,KAAM,wCAAwC,CAC3D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAC7D,MAAO,CAAAC,MAAM,KAAM,0CAA0C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE9D,KAAM,CAAAC,IAAI,CAAG,CACX,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,8DAA8D,CACpEC,GAAG,CAAE3B,KACP,CAAC,CACD,CACEwB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,2CAA2C,CACjDC,GAAG,CAAE1B,KACP,CAAC,CACD,CACEuB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,2CAA2C,CACjDC,GAAG,CAAEzB,KACP,CAAC,CACD,CACEsB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,wDAAwD,CAC9DC,GAAG,CAAExB,KACP,CAAC,CACD,CACEqB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,0CAA0C,CAChDC,GAAG,CAAEvB,KACP,CAAC,CACD,CACEoB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,iDAAiD,CACvDC,GAAG,CAAEtB,KACP,CAAC,CACD,CACEmB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,6CAA6C,CACnDC,GAAG,CAAErB,KACP,CAAC,CACD,CACEkB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,iBAAiB,CACvBC,IAAI,CAAE,0CAA0C,CAChDC,GAAG,CAAEpB,KACP,CAAC,CACD,CACEiB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,8CAA8C,CACpDC,GAAG,CAAEnB,KACP,CAAC,CACD,CACEgB,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,gNAAgN,CACtNC,GAAG,CAAElB,MACP,CAAC,CACD,CACEe,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,0GAA0G,CAChHC,GAAG,CAAEjB,MACP,CAAC,CACD,CACEc,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,wUAAwU,CAC9UC,GAAG,CAAEhB,MACP,CAAC,CACD,CACEa,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,eAAe,CACrBC,IAAI,CAAE,wFAAwF,CAC9FC,GAAG,CAAEf,MACP,CAAC,CACD,CACEY,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,8DAA8D,CACpEC,GAAG,CAAEd,MACP,CAAC,CACD,CACEW,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,kKAAkK,CACxKC,GAAG,CAAEb,MACP,CAAC,CACD,CACEU,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,+OAA+O,CACrPC,GAAG,CAAEZ,MACP,CAAC,CACD,CACES,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,aAAa,CACnBC,IAAI,CAAE,iCAAiC,CACvCC,GAAG,CAAEX,MACP,CAAC,CACF,CAED,KAAM,CAAAY,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,CAAC,CAAC,CAEjDD,SAAS,CAAC,IAAM,CACdG,GAAG,CAACqC,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7Bb,UAAU,CAAC,KAAK,CAAC,CACjBQ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEvB,IAAA,CAAAI,SAAA,EAAAuB,QAAA,CACGhB,OAAO,cACNX,IAAA,QAAK4B,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5B3B,IAAA,QAAK4B,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAEN5B,IAAA,QAAK4B,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBzB,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBzB,KAAA,QAAK0B,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtB3B,IAAA,OAAI4B,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAD,QAAA,cAC3D3B,IAAA,WAAA2B,QAAA,CAAQ,gDAAW,CAAQ,CAAC,CAC1B,CAAC,cACL3B,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,cACjE5B,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAA2B,QAAA,CAAC,6xFAW3C,CAAG,CAAC,EACD,CAAC,cAENzB,KAAA,YAAS0B,SAAS,CAAEnD,MAAM,CAACoD,aAAc,CAAAF,QAAA,eACvC3B,IAAA,QAAK4B,SAAS,CAAEnD,MAAM,CAACqD,kBAAmB,CAAAH,QAAA,cACxC3B,IAAA,QACE4B,SAAS,CAAEnD,MAAM,CAACsD,WAAY,CAC9BT,KAAK,CAAE,CACLU,KAAK,IAAAC,MAAA,CAAM,CAACpB,WAAW,CAAG,CAAC,EAAIR,IAAI,CAAC6B,MAAM,CAAI,GAAG,KACnD,CAAE,CACH,CAAC,CACC,CAAC,cAENlC,IAAA,CAACrB,MAAM,EACLwD,OAAO,CAAE,CAACtD,UAAU,CAAE,CACtBuD,UAAU,MACVC,YAAY,CAAE,EAAG,CACjBC,aAAa,CAAE,CAAE,CACjBC,IAAI,CAAE,KAAM,CACZC,aAAa,CAAGC,MAAM,EAAK3B,cAAc,CAAC2B,MAAM,CAAC5B,WAAW,CAAE,CAAAc,QAAA,CAE7DtB,IAAI,CAACqC,GAAG,CAAEC,IAAI,eACb3C,IAAA,CAACpB,WAAW,EAAA+C,QAAA,cACVzB,KAAA,QAAK0B,SAAS,CAAEnD,MAAM,CAACmE,YAAa,CAAAjB,QAAA,eAClC3B,IAAA,QAAK4B,SAAS,CAAEnD,MAAM,CAACoE,SAAU,CAAAlB,QAAA,cAC/B3B,IAAA,QAAK8C,GAAG,CAAEH,IAAI,CAAClC,GAAI,CAACsC,GAAG,CAAEJ,IAAI,CAACpC,IAAK,CAAE,CAAC,CACnC,CAAC,cACNL,KAAA,QAAK0B,SAAS,CAAEnD,MAAM,CAACuE,SAAU,CAAArB,QAAA,eAC/B3B,IAAA,OAAA2B,QAAA,CAAKgB,IAAI,CAACpC,IAAI,CAAK,CAAC,cACpBP,IAAA,MAAA2B,QAAA,CAAIgB,IAAI,CAACnC,IAAI,CAAI,CAAC,EACf,CAAC,EACH,CAAC,EATUmC,IAAI,CAACrC,EAUV,CACd,CAAC,CACI,CAAC,EACF,CAAC,EACP,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}