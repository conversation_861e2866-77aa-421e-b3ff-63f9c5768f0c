{"ast": null, "code": "function appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\nfunction prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\nfunction addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\nfunction removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\nfunction removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\nfunction Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}\nexport { Manipulation as default };", "map": {"version": 3, "names": ["appendSlide", "slides", "swiper", "params", "slidesEl", "loop", "loop<PERSON><PERSON><PERSON>", "appendElement", "slideEl", "tempDOM", "document", "createElement", "innerHTML", "append", "children", "i", "length", "recalcSlides", "loopCreate", "observer", "isElement", "update", "prependSlide", "activeIndex", "newActiveIndex", "prependElement", "prepend", "slideTo", "addSlide", "index", "activeIndexBuffer", "loopedSlides", "baseLength", "slidesBuffer", "currentSlide", "remove", "unshift", "removeSlide", "slidesIndexes", "indexToRemove", "Math", "max", "removeAllSlides", "push", "Manipulation", "_ref", "Object", "assign", "bind", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/modules/manipulation.mjs"], "sourcesContent": ["function appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.append(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\n\nfunction prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      tempDOM.innerHTML = slideEl;\n      slidesEl.prepend(tempDOM.children[0]);\n      tempDOM.innerHTML = '';\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n\nfunction addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n\nfunction Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}\n\nexport { Manipulation as default };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,MAAM,EAAE;EAC3B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAIC,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,MAAMC,aAAa,GAAGC,OAAO,IAAI;IAC/B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,OAAO,CAACG,SAAS,GAAGJ,OAAO;MAC3BJ,QAAQ,CAACS,MAAM,CAACJ,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpCL,OAAO,CAACG,SAAS,GAAG,EAAE;IACxB,CAAC,MAAM;MACLR,QAAQ,CAACS,MAAM,CAACL,OAAO,CAAC;IAC1B;EACF,CAAC;EACD,IAAI,OAAOP,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACe,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAId,MAAM,CAACc,CAAC,CAAC,EAAER,aAAa,CAACN,MAAM,CAACc,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,MAAM;IACLR,aAAa,CAACN,MAAM,CAAC;EACvB;EACAC,MAAM,CAACe,YAAY,CAAC,CAAC;EACrB,IAAId,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACgB,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,CAAC,CAAC;EACjB;AACF;AAEA,SAASC,YAAYA,CAACrB,MAAM,EAAE;EAC5B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNoB,WAAW;IACXnB;EACF,CAAC,GAAGF,MAAM;EACV,IAAIC,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,IAAIkB,cAAc,GAAGD,WAAW,GAAG,CAAC;EACpC,MAAME,cAAc,GAAGjB,OAAO,IAAI;IAChC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CF,OAAO,CAACG,SAAS,GAAGJ,OAAO;MAC3BJ,QAAQ,CAACsB,OAAO,CAACjB,OAAO,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrCL,OAAO,CAACG,SAAS,GAAG,EAAE;IACxB,CAAC,MAAM;MACLR,QAAQ,CAACsB,OAAO,CAAClB,OAAO,CAAC;IAC3B;EACF,CAAC;EACD,IAAI,OAAOP,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACe,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAId,MAAM,CAACc,CAAC,CAAC,EAAEU,cAAc,CAACxB,MAAM,CAACc,CAAC,CAAC,CAAC;IAC1C;IACAS,cAAc,GAAGD,WAAW,GAAGtB,MAAM,CAACe,MAAM;EAC9C,CAAC,MAAM;IACLS,cAAc,CAACxB,MAAM,CAAC;EACxB;EACAC,MAAM,CAACe,YAAY,CAAC,CAAC;EACrB,IAAId,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACgB,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,CAAC,CAAC;EACjB;EACAnB,MAAM,CAACyB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;AAC1C;AAEA,SAASI,QAAQA,CAACC,KAAK,EAAE5B,MAAM,EAAE;EAC/B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNoB,WAAW;IACXnB;EACF,CAAC,GAAGF,MAAM;EACV,IAAI4B,iBAAiB,GAAGP,WAAW;EACnC,IAAIpB,MAAM,CAACE,IAAI,EAAE;IACfyB,iBAAiB,IAAI5B,MAAM,CAAC6B,YAAY;IACxC7B,MAAM,CAACI,WAAW,CAAC,CAAC;IACpBJ,MAAM,CAACe,YAAY,CAAC,CAAC;EACvB;EACA,MAAMe,UAAU,GAAG9B,MAAM,CAACD,MAAM,CAACe,MAAM;EACvC,IAAIa,KAAK,IAAI,CAAC,EAAE;IACd3B,MAAM,CAACoB,YAAY,CAACrB,MAAM,CAAC;IAC3B;EACF;EACA,IAAI4B,KAAK,IAAIG,UAAU,EAAE;IACvB9B,MAAM,CAACF,WAAW,CAACC,MAAM,CAAC;IAC1B;EACF;EACA,IAAIuB,cAAc,GAAGM,iBAAiB,GAAGD,KAAK,GAAGC,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;EAC1F,MAAMG,YAAY,GAAG,EAAE;EACvB,KAAK,IAAIlB,CAAC,GAAGiB,UAAU,GAAG,CAAC,EAAEjB,CAAC,IAAIc,KAAK,EAAEd,CAAC,IAAI,CAAC,EAAE;IAC/C,MAAMmB,YAAY,GAAGhC,MAAM,CAACD,MAAM,CAACc,CAAC,CAAC;IACrCmB,YAAY,CAACC,MAAM,CAAC,CAAC;IACrBF,YAAY,CAACG,OAAO,CAACF,YAAY,CAAC;EACpC;EACA,IAAI,OAAOjC,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,CAACe,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAId,MAAM,CAACc,CAAC,CAAC,EAAEX,QAAQ,CAACS,MAAM,CAACZ,MAAM,CAACc,CAAC,CAAC,CAAC;IAC3C;IACAS,cAAc,GAAGM,iBAAiB,GAAGD,KAAK,GAAGC,iBAAiB,GAAG7B,MAAM,CAACe,MAAM,GAAGc,iBAAiB;EACpG,CAAC,MAAM;IACL1B,QAAQ,CAACS,MAAM,CAACZ,MAAM,CAAC;EACzB;EACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,YAAY,CAACjB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC/CX,QAAQ,CAACS,MAAM,CAACoB,YAAY,CAAClB,CAAC,CAAC,CAAC;EAClC;EACAb,MAAM,CAACe,YAAY,CAAC,CAAC;EACrB,IAAId,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACgB,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,CAAC,CAAC;EACjB;EACA,IAAIlB,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACyB,OAAO,CAACH,cAAc,GAAGtB,MAAM,CAAC6B,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACL7B,MAAM,CAACyB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF;AAEA,SAASa,WAAWA,CAACC,aAAa,EAAE;EAClC,MAAMpC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNoB;EACF,CAAC,GAAGrB,MAAM;EACV,IAAI4B,iBAAiB,GAAGP,WAAW;EACnC,IAAIpB,MAAM,CAACE,IAAI,EAAE;IACfyB,iBAAiB,IAAI5B,MAAM,CAAC6B,YAAY;IACxC7B,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,IAAIkB,cAAc,GAAGM,iBAAiB;EACtC,IAAIS,aAAa;EACjB,IAAI,OAAOD,aAAa,KAAK,QAAQ,IAAI,QAAQ,IAAIA,aAAa,EAAE;IAClE,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,aAAa,CAACtB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAChDwB,aAAa,GAAGD,aAAa,CAACvB,CAAC,CAAC;MAChC,IAAIb,MAAM,CAACD,MAAM,CAACsC,aAAa,CAAC,EAAErC,MAAM,CAACD,MAAM,CAACsC,aAAa,CAAC,CAACJ,MAAM,CAAC,CAAC;MACvE,IAAII,aAAa,GAAGf,cAAc,EAAEA,cAAc,IAAI,CAAC;IACzD;IACAA,cAAc,GAAGgB,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLe,aAAa,GAAGD,aAAa;IAC7B,IAAIpC,MAAM,CAACD,MAAM,CAACsC,aAAa,CAAC,EAAErC,MAAM,CAACD,MAAM,CAACsC,aAAa,CAAC,CAACJ,MAAM,CAAC,CAAC;IACvE,IAAII,aAAa,GAAGf,cAAc,EAAEA,cAAc,IAAI,CAAC;IACvDA,cAAc,GAAGgB,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,CAAC,CAAC;EAC9C;EACAtB,MAAM,CAACe,YAAY,CAAC,CAAC;EACrB,IAAId,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACgB,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACf,MAAM,CAACgB,QAAQ,IAAIjB,MAAM,CAACkB,SAAS,EAAE;IACxClB,MAAM,CAACmB,MAAM,CAAC,CAAC;EACjB;EACA,IAAIlB,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACyB,OAAO,CAACH,cAAc,GAAGtB,MAAM,CAAC6B,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACL7B,MAAM,CAACyB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF;AAEA,SAASkB,eAAeA,CAAA,EAAG;EACzB,MAAMxC,MAAM,GAAG,IAAI;EACnB,MAAMoC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACD,MAAM,CAACe,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChDuB,aAAa,CAACK,IAAI,CAAC5B,CAAC,CAAC;EACvB;EACAb,MAAM,CAACmC,WAAW,CAACC,aAAa,CAAC;AACnC;AAEA,SAASM,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACF3C;EACF,CAAC,GAAG2C,IAAI;EACRC,MAAM,CAACC,MAAM,CAAC7C,MAAM,EAAE;IACpBF,WAAW,EAAEA,WAAW,CAACgD,IAAI,CAAC9C,MAAM,CAAC;IACrCoB,YAAY,EAAEA,YAAY,CAAC0B,IAAI,CAAC9C,MAAM,CAAC;IACvC0B,QAAQ,EAAEA,QAAQ,CAACoB,IAAI,CAAC9C,MAAM,CAAC;IAC/BmC,WAAW,EAAEA,WAAW,CAACW,IAAI,CAAC9C,MAAM,CAAC;IACrCwC,eAAe,EAAEA,eAAe,CAACM,IAAI,CAAC9C,MAAM;EAC9C,CAAC,CAAC;AACJ;AAEA,SAAS0C,YAAY,IAAIK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}