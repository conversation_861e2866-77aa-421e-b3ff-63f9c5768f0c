{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useParams}from'react-router-dom';import styles from'./offerPage.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OfferPage=()=>{const{id}=useParams();// id — это slug\nconst[newsItem,setNewsItem]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{window.scrollTo(0,0);document.body.style.overflow='hidden';const fetchNews=async()=>{try{var _json$promo;const res=await fetch('https://api.gwm.tj/api/v1/promo');const json=await res.json();const found=(_json$promo=json.promo)===null||_json$promo===void 0?void 0:_json$promo.find(item=>item.slug===id);setNewsItem(found||null);}catch(err){}finally{setLoading(false);document.body.style.overflow='visible';}};fetchNews();return()=>{document.body.style.overflow='visible';};},[id]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})});}if(!newsItem){return/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:\"\\u041F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u041F\\u0440\\u043E\\u0432\\u0435\\u0440\\u044C\\u0442\\u0435 \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C \\u0441\\u0441\\u044B\\u043B\\u043A\\u0438 \\u0438\\u043B\\u0438 \\u0432\\u0435\\u0440\\u043D\\u0438\\u0442\\u0435\\u0441\\u044C \\u043D\\u0430\\u0437\\u0430\\u0434.\"})]})})});}return/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:newsItem.title}),/*#__PURE__*/_jsx(\"i\",{className:\"redLine\"}),/*#__PURE__*/_jsx(\"h3\",{className:styles.date,children:new Date(newsItem.updated_at).toLocaleDateString('ru-RU')}),/*#__PURE__*/_jsx(\"div\",{className:styles.imageWrapper,children:/*#__PURE__*/_jsx(\"img\",{src:newsItem.preview,alt:newsItem.title})}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"div\",{className:styles.text,dangerouslySetInnerHTML:{__html:newsItem.content}}),/*#__PURE__*/_jsx(\"br\",{})]})})});};export default OfferPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "OfferPage", "id", "newsItem", "setNewsItem", "loading", "setLoading", "window", "scrollTo", "document", "body", "style", "overflow", "fetchNews", "_json$promo", "res", "fetch", "json", "found", "promo", "find", "item", "slug", "err", "className", "children", "title", "date", "Date", "updated_at", "toLocaleDateString", "imageWrapper", "src", "preview", "alt", "text", "dangerouslySetInnerHTML", "__html", "content"], "sources": ["/var/www/html/gwm.tj/src/pages/Offer/OfferPage/OfferPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styles from './offerPage.module.css';\n\nconst OfferPage = () => {\n  const { id } = useParams(); // id — это slug\n  const [newsItem, setNewsItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const fetchNews = async () => {\n      try {\n        const res = await fetch('https://api.gwm.tj/api/v1/promo');\n        const json = await res.json();\n        const found = json.promo?.find((item) => item.slug === id);\n        setNewsItem(found || null);\n      } catch (err) {\n      } finally {\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n      }\n    };\n\n    fetchNews();\n\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, [id]);\n\n  if (loading) {\n    return (\n      <div className=\"loaderWrapper\">\n        <div className=\"loaderPage\"></div>\n      </div>\n    );\n  }\n\n  if (!newsItem) {\n    return (\n      <div className=\"topmenu\">\n        <div className=\"container\">\n          <div className=\"content\">\n            <h1 className=\"title\">Предложение не найдена</h1>\n            <p>Проверьте правильность ссылки или вернитесь назад.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"topmenu\">\n      <div className=\"container\">\n        <div className=\"content\">\n          <h1 className=\"title\">{newsItem.title}</h1>\n          <i className=\"redLine\"></i>\n          <h3 className={styles.date}>\n            {new Date(newsItem.updated_at).toLocaleDateString('ru-RU')}\n          </h3>\n\n          <div className={styles.imageWrapper}>\n            <img src={newsItem.preview} alt={newsItem.title} />\n          </div>\n          <br />\n          <div\n            className={styles.text}\n            dangerouslySetInnerHTML={{ __html: newsItem.content }}\n          />\n          <br />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default OfferPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,MAAO,CAAAC,MAAM,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,EAAG,CAAC,CAAGP,SAAS,CAAC,CAAC,CAAE;AAC5B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdc,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,KAAAC,WAAA,CACF,KAAM,CAAAC,GAAG,CAAG,KAAM,CAAAC,KAAK,CAAC,iCAAiC,CAAC,CAC1D,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAF,GAAG,CAACE,IAAI,CAAC,CAAC,CAC7B,KAAM,CAAAC,KAAK,EAAAJ,WAAA,CAAGG,IAAI,CAACE,KAAK,UAAAL,WAAA,iBAAVA,WAAA,CAAYM,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACC,IAAI,GAAKpB,EAAE,CAAC,CAC1DE,WAAW,CAACc,KAAK,EAAI,IAAI,CAAC,CAC5B,CAAE,MAAOK,GAAG,CAAE,CACd,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACjBG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CACF,CAAC,CAEDC,SAAS,CAAC,CAAC,CAEX,MAAO,IAAM,CACXJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,CAACV,EAAE,CAAC,CAAC,CAER,GAAIG,OAAO,CAAE,CACX,mBACEP,IAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3B,IAAA,QAAK0B,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,CAEV,CAEA,GAAI,CAACrB,QAAQ,CAAE,CACb,mBACEL,IAAA,QAAK0B,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB3B,IAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,KAAA,QAAKwB,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB3B,IAAA,OAAI0B,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,4HAAsB,CAAI,CAAC,cACjD3B,IAAA,MAAA2B,QAAA,CAAG,gRAAkD,CAAG,CAAC,EACtD,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACE3B,IAAA,QAAK0B,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB3B,IAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,KAAA,QAAKwB,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB3B,IAAA,OAAI0B,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAEtB,QAAQ,CAACuB,KAAK,CAAK,CAAC,cAC3C5B,IAAA,MAAG0B,SAAS,CAAC,SAAS,CAAI,CAAC,cAC3B1B,IAAA,OAAI0B,SAAS,CAAE5B,MAAM,CAAC+B,IAAK,CAAAF,QAAA,CACxB,GAAI,CAAAG,IAAI,CAACzB,QAAQ,CAAC0B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CACxD,CAAC,cAELhC,IAAA,QAAK0B,SAAS,CAAE5B,MAAM,CAACmC,YAAa,CAAAN,QAAA,cAClC3B,IAAA,QAAKkC,GAAG,CAAE7B,QAAQ,CAAC8B,OAAQ,CAACC,GAAG,CAAE/B,QAAQ,CAACuB,KAAM,CAAE,CAAC,CAChD,CAAC,cACN5B,IAAA,QAAK,CAAC,cACNA,IAAA,QACE0B,SAAS,CAAE5B,MAAM,CAACuC,IAAK,CACvBC,uBAAuB,CAAE,CAAEC,MAAM,CAAElC,QAAQ,CAACmC,OAAQ,CAAE,CACvD,CAAC,cACFxC,IAAA,QAAK,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}