{"ast": null, "code": "import{Outlet}from'react-router-dom';import Navbar from'./Navbar/Navbar';// nav\nimport Footer from'./Footer/Footer';// footer\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=()=>{return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(\"main\",{children:/*#__PURE__*/_jsx(Outlet,{})}),/*#__PURE__*/_jsx(Footer,{})]});};export default Layout;", "map": {"version": 3, "names": ["Outlet", "<PERSON><PERSON><PERSON>", "Footer", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Layout", "children"], "sources": ["/var/www/html/gwm.tj/src/layout/Layout.jsx"], "sourcesContent": ["import { Outlet } from 'react-router-dom';\nimport Navbar from './Navbar/Navbar'; // nav\nimport Footer from './Footer/Footer'; // footer\n\nconst Layout = () => {\n  return (\n    <>\n      <Navbar />\n      <main>\n        <Outlet />\n      </main>\n      <Footer />\n    </>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,kBAAkB,CACzC,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CAAE;AACtC,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CAAE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtC,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACED,KAAA,CAAAF,SAAA,EAAAI,QAAA,eACEN,IAAA,CAACH,MAAM,GAAE,CAAC,cACVG,IAAA,SAAAM,QAAA,cACEN,IAAA,CAACJ,MAAM,GAAE,CAAC,CACN,CAAC,cACPI,IAAA,CAACF,MAAM,GAAE,CAAC,EACV,CAAC,CAEP,CAAC,CAED,cAAe,CAAAO,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}