{"ast": null, "code": "function filterViewAnimations(animation) {\n  var _effect$pseudoElement;\n  const {\n    effect\n  } = animation;\n  if (!effect) return false;\n  return effect.target === document.documentElement && ((_effect$pseudoElement = effect.pseudoElement) === null || _effect$pseudoElement === void 0 ? void 0 : _effect$pseudoElement.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n  return document.getAnimations().filter(filterViewAnimations);\n}\nexport { getViewAnimations };", "map": {"version": 3, "names": ["filterViewAnimations", "animation", "_effect$pseudoElement", "effect", "target", "document", "documentElement", "pseudoElement", "startsWith", "getViewAnimations", "getAnimations", "filter"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs"], "sourcesContent": ["function filterViewAnimations(animation) {\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        effect.pseudoElement?.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\nexport { getViewAnimations };\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,SAAS,EAAE;EAAA,IAAAC,qBAAA;EACrC,MAAM;IAAEC;EAAO,CAAC,GAAGF,SAAS;EAC5B,IAAI,CAACE,MAAM,EACP,OAAO,KAAK;EAChB,OAAQA,MAAM,CAACC,MAAM,KAAKC,QAAQ,CAACC,eAAe,MAAAJ,qBAAA,GAC9CC,MAAM,CAACI,aAAa,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,UAAU,CAAC,mBAAmB,CAAC;AAC7D;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOJ,QAAQ,CAACK,aAAa,CAAC,CAAC,CAACC,MAAM,CAACX,oBAAoB,CAAC;AAChE;AAEA,SAASS,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}