{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Offer/OfferPage/OfferPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styles from './offerPage.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OfferPage = () => {\n  _s();\n  const {\n    id\n  } = useParams(); // id — это slug\n  const [newsItem, setNewsItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const fetchNews = async () => {\n      try {\n        var _json$promo;\n        const res = await fetch('https://api.gwm.tj/api/v1/promo');\n        const json = await res.json();\n        const found = (_json$promo = json.promo) === null || _json$promo === void 0 ? void 0 : _json$promo.find(item => item.slug === id);\n        setNewsItem(found || null);\n      } catch (err) {} finally {\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n      }\n    };\n    fetchNews();\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, [id]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n  if (!newsItem) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: \"\\u041F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u041F\\u0440\\u043E\\u0432\\u0435\\u0440\\u044C\\u0442\\u0435 \\u043F\\u0440\\u0430\\u0432\\u0438\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C \\u0441\\u0441\\u044B\\u043B\\u043A\\u0438 \\u0438\\u043B\\u0438 \\u0432\\u0435\\u0440\\u043D\\u0438\\u0442\\u0435\\u0441\\u044C \\u043D\\u0430\\u0437\\u0430\\u0434.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"topmenu\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: newsItem.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"redLine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: styles.date,\n          children: new Date(newsItem.updated_at).toLocaleDateString('ru-RU')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.imageWrapper,\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: newsItem.preview,\n            alt: newsItem.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.text,\n          dangerouslySetInnerHTML: {\n            __html: newsItem.content\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(OfferPage, \"GNjqvi4Kf1OGt1Fy/K2w85rS6J0=\", false, function () {\n  return [useParams];\n});\n_c = OfferPage;\nexport default OfferPage;\nvar _c;\n$RefreshReg$(_c, \"OfferPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "styles", "jsxDEV", "_jsxDEV", "OfferPage", "_s", "id", "newsItem", "setNewsItem", "loading", "setLoading", "window", "scrollTo", "document", "body", "style", "overflow", "fetchNews", "_json$promo", "res", "fetch", "json", "found", "promo", "find", "item", "slug", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "date", "Date", "updated_at", "toLocaleDateString", "imageWrapper", "src", "preview", "alt", "text", "dangerouslySetInnerHTML", "__html", "content", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Offer/OfferPage/OfferPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useParams } from 'react-router-dom';\nimport styles from './offerPage.module.css';\n\nconst OfferPage = () => {\n  const { id } = useParams(); // id — это slug\n  const [newsItem, setNewsItem] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const fetchNews = async () => {\n      try {\n        const res = await fetch('https://api.gwm.tj/api/v1/promo');\n        const json = await res.json();\n        const found = json.promo?.find((item) => item.slug === id);\n        setNewsItem(found || null);\n      } catch (err) {\n      } finally {\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n      }\n    };\n\n    fetchNews();\n\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, [id]);\n\n  if (loading) {\n    return (\n      <div className=\"loaderWrapper\">\n        <div className=\"loaderPage\"></div>\n      </div>\n    );\n  }\n\n  if (!newsItem) {\n    return (\n      <div className=\"topmenu\">\n        <div className=\"container\">\n          <div className=\"content\">\n            <h1 className=\"title\">Предложение не найдена</h1>\n            <p>Проверьте правильность ссылки или вернитесь назад.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"topmenu\">\n      <div className=\"container\">\n        <div className=\"content\">\n          <h1 className=\"title\">{newsItem.title}</h1>\n          <i className=\"redLine\"></i>\n          <h3 className={styles.date}>\n            {new Date(newsItem.updated_at).toLocaleDateString('ru-RU')}\n          </h3>\n\n          <div className={styles.imageWrapper}>\n            <img src={newsItem.preview} alt={newsItem.title} />\n          </div>\n          <br />\n          <div\n            className={styles.text}\n            dangerouslySetInnerHTML={{ __html: newsItem.content }}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default OfferPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,MAAM,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAG,CAAC,GAAGN,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACda,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QAAA,IAAAC,WAAA;QACF,MAAMC,GAAG,GAAG,MAAMC,KAAK,CAAC,iCAAiC,CAAC;QAC1D,MAAMC,IAAI,GAAG,MAAMF,GAAG,CAACE,IAAI,CAAC,CAAC;QAC7B,MAAMC,KAAK,IAAAJ,WAAA,GAAGG,IAAI,CAACE,KAAK,cAAAL,WAAA,uBAAVA,WAAA,CAAYM,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAKpB,EAAE,CAAC;QAC1DE,WAAW,CAACc,KAAK,IAAI,IAAI,CAAC;MAC5B,CAAC,CAAC,OAAOK,GAAG,EAAE,CACd,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;QACjBG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;MAC1C;IACF,CAAC;IAEDC,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,CAACV,EAAE,CAAC,CAAC;EAER,IAAIG,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKyB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B1B,OAAA;QAAKyB,SAAS,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAEV;EAEA,IAAI,CAAC1B,QAAQ,EAAE;IACb,oBACEJ,OAAA;MAAKyB,SAAS,EAAC,SAAS;MAAAC,QAAA,eACtB1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1B,OAAA;UAAKyB,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtB1B,OAAA;YAAIyB,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD9B,OAAA;YAAA0B,QAAA,EAAG;UAAkD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9B,OAAA;IAAKyB,SAAS,EAAC,SAAS;IAAAC,QAAA,eACtB1B,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB1B,OAAA;QAAKyB,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtB1B,OAAA;UAAIyB,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAEtB,QAAQ,CAAC2B;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3C9B,OAAA;UAAGyB,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B9B,OAAA;UAAIyB,SAAS,EAAE3B,MAAM,CAACkC,IAAK;UAAAN,QAAA,EACxB,IAAIO,IAAI,CAAC7B,QAAQ,CAAC8B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEL9B,OAAA;UAAKyB,SAAS,EAAE3B,MAAM,CAACsC,YAAa;UAAAV,QAAA,eAClC1B,OAAA;YAAKqC,GAAG,EAAEjC,QAAQ,CAACkC,OAAQ;YAACC,GAAG,EAAEnC,QAAQ,CAAC2B;UAAM;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN9B,OAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9B,OAAA;UACEyB,SAAS,EAAE3B,MAAM,CAAC0C,IAAK;UACvBC,uBAAuB,EAAE;YAAEC,MAAM,EAAEtC,QAAQ,CAACuC;UAAQ;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAxEID,SAAS;EAAA,QACEJ,SAAS;AAAA;AAAA+C,EAAA,GADpB3C,SAAS;AA0Ef,eAAeA,SAAS;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}