{"ast": null, "code": "import _objectSpread from\"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useCallback}from'react';import Select from'react-select';import styles from'./form.module.css';import{useMask}from'@react-input/mask';import Notification from'../../../../components/Notification/Notification';import{sanitizeAndValidateForm,formRateLimiter}from'../../../../utils/validation';import{submitFeedback}from'../../../../utils/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const options=[{value:'вопрос',label:'Вопрос'},{value:'жалоба',label:'Жалоба'},{value:'предложение',label:'Предложение'},{value:'Обслуживание и ремонт',label:'Обслуживание и ремонт'},{value:'Послепродажное обслуживание',label:'Послепродажное обслуживание'},{value:'Другое',label:'Другое'}];const OwnersForm=()=>{const[formData,setFormData]=useState({firstName:'',lastName:'',phone:'',topic:null,message:'',consent:false});const[notification,setNotification]=useState({message:'',type:''});const[btnLoading,setBtnLoading]=useState(false);const[formErrors,setFormErrors]=useState({});const[isSubmitting,setIsSubmitting]=useState(false);/**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */const showNotification=useCallback(function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);},[]);const phoneRef=useMask({mask:'+992 ___-__-__-__',replacement:{_:/\\d/}});const handleChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));};const handleSelectChange=selectedOption=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{topic:selectedOption}));};const handleSubmit=async e=>{e.preventDefault();// Prevent double submission\nif(isSubmitting)return;// Check rate limiting\nif(!formRateLimiter.isAllowed()){showNotification('Слишком много попыток отправки. Подождите минуту.','error');return;}setIsSubmitting(true);setBtnLoading(true);setFormErrors({});try{var _sanitizedData$topic;// Validate and sanitize form data\nconst{data:sanitizedData,errors,isValid}=sanitizeAndValidateForm(formData);if(!isValid){setFormErrors(errors);showNotification('Пожалуйста, исправьте ошибки в форме','error');return;}// Prepare payload for API\nconst payload={firstName:sanitizedData.firstName,lastName:sanitizedData.lastName,phone:sanitizedData.phone,topic:((_sanitizedData$topic=sanitizedData.topic)===null||_sanitizedData$topic===void 0?void 0:_sanitizedData$topic.value)||'',message:sanitizedData.message,consent:sanitizedData.consent,formType:'owners'// Specify this is an owners form\n};// Submit form using the same API as the main form\nawait submitFeedback(payload);// Success\nshowNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.','success');// Reset form\nsetFormData({firstName:'',lastName:'',phone:'',topic:null,message:'',consent:false});setFormErrors({});}catch(error){showNotification(error.message,'error');}finally{setBtnLoading(false);setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),' ',/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:styles.form,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.row,children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"firstName\",placeholder:\"\\u0418\\u043C\\u044F *\",className:\"\".concat(styles.input,\" \").concat(formErrors.firstName?styles.inputError:''),value:formData.firstName,required:true,onChange:handleChange,minLength:\"3\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"lastName\",placeholder:\"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\",className:\"\".concat(styles.input,\" \").concat(formErrors.lastName?styles.inputError:''),value:formData.lastName,onChange:handleChange,minLength:\"3\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.row,children:[/*#__PURE__*/_jsx(\"input\",{type:\"tel\",name:\"phone\",placeholder:\"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430 *\",className:\"\".concat(styles.input,\" \").concat(formErrors.phone?styles.inputError:''),value:formData.phone,required:true,ref:phoneRef,onChange:handleChange}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"message\",placeholder:\"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0412\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435\",className:\"\".concat(styles.input,\" \").concat(formErrors.message?styles.inputError:''),value:formData.message,onChange:handleChange})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.row,children:/*#__PURE__*/_jsx(\"div\",{className:styles.selectWrapper,children:/*#__PURE__*/_jsx(Select,{required:true,classNamePrefix:\"react-select\",options:options,placeholder:\"\\u0422\\u0435\\u043C\\u0430 *\",onChange:handleSelectChange,value:formData.topic,isSearchable:false,styles:{control:(provided,state)=>_objectSpread(_objectSpread({},provided),{},{width:'100%',paddingLeft:'8px',paddingRight:'0px',height:'58px',borderRadius:'2px',border:\"1px solid \".concat(state.isFocused?'#d7000f':'#8e8e93'),fontSize:'16px',boxShadow:'none',backgroundColor:'#fff',transition:'all 0.2s ease','&:hover':{borderColor:state.isFocused?'#d7000f':'#8e8e93'}}),container:provided=>_objectSpread(_objectSpread({},provided),{},{width:'100%'}),placeholder:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#888'}),singleValue:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#000'}),indicatorsContainer:provided=>_objectSpread(_objectSpread({},provided),{},{paddingRight:'10px'}),dropdownIndicator:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#8e8e93'}),menu:provided=>_objectSpread(_objectSpread({},provided),{},{border:'1px solid #8e8e93',borderRadius:'2px',boxShadow:'none',marginTop:'4px',backgroundColor:'#fff',zIndex:9999,position:'absolute'}),menuList:provided=>_objectSpread(_objectSpread({},provided),{},{padding:0}),option:(provided,state)=>_objectSpread(_objectSpread({},provided),{},{padding:'14px 16px',fontSize:'14px',backgroundColor:state.isFocused?'#000':'#fff',color:state.isFocused?'#fff':'#000',cursor:'pointer','&:active':{backgroundColor:'#000'}})},menuPortalTarget:document.body})})}),/*#__PURE__*/_jsxs(\"label\",{className:styles.checkboxWrapper,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"consent\",required:true,checked:formData.consent,onChange:handleChange,className:styles.checkbox}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445 \\u0434\\u043B\\u044F \\u043F\\u0440\\u043E\\u0432\\u0435\\u0440\\u043A\\u0438/\\u043F\\u043E\\u0434\\u0442\\u0432\\u0435\\u0440\\u0436\\u0434\\u0435\\u043D\\u0438\\u044F \\u043D\\u0435\\u0432\\u044B\\u043F\\u043E\\u043B\\u043D\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043A\\u0430\\u043C\\u043F\\u0430\\u043D\\u0438\\u0439 \\u043F\\u043E \\u043C\\u043E\\u0438\\u043C \\u0442\\u0440\\u0430\\u043D\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u043C \\u0441\\u0440\\u0435\\u0434\\u0441\\u0442\\u0432\\u0430\\u043C. *\"})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:styles.button,disabled:btnLoading||isSubmitting,children:btnLoading?'Отправка...':'Отправить'})]})]});};export default OwnersForm;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Select", "styles", "useMask", "Notification", "sanitizeAndValidateForm", "formRateLimiter", "submitFeedback", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "options", "value", "label", "OwnersForm", "formData", "setFormData", "firstName", "lastName", "phone", "topic", "message", "consent", "notification", "setNotification", "type", "btnLoading", "setBtnLoading", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "arguments", "length", "undefined", "setTimeout", "phoneRef", "mask", "replacement", "_", "handleChange", "e", "name", "checked", "target", "prev", "_objectSpread", "handleSelectChange", "selectedOption", "handleSubmit", "preventDefault", "isAllowed", "_sanitizedData$topic", "data", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "formType", "error", "children", "onSubmit", "className", "form", "row", "placeholder", "concat", "input", "inputError", "required", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "ref", "selectWrapper", "classNamePrefix", "isSearchable", "control", "provided", "state", "width", "paddingLeft", "paddingRight", "height", "borderRadius", "border", "isFocused", "fontSize", "boxShadow", "backgroundColor", "transition", "borderColor", "container", "color", "singleValue", "indicatorsContainer", "dropdownIndicator", "menu", "marginTop", "zIndex", "position", "menuList", "padding", "option", "cursor", "menuPortalTarget", "document", "body", "checkboxWrapper", "checkbox", "button", "disabled"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/components/form/OwnersForm.jsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../../../components/Notification/Notification';\nimport { sanitizeAndValidateForm, formRateLimiter } from '../../../../utils/validation';\nimport { submitFeedback } from '../../../../utils/api';\n\nconst options = [\n  { value: 'вопрос', label: 'Вопрос' },\n  { value: 'жалоба', label: 'Жалоба' },\n  { value: 'предложение', label: 'Предложение' },\n  { value: 'Обслуживание и ремонт', label: 'Обслуживание и ремонт' },\n  {\n    value: 'Послепродажное обслуживание',\n    label: 'Послепродажное обслуживание',\n  },\n  {\n    value: 'Другое',\n    label: 'Другое',\n  },\n];\n\nconst OwnersForm = () => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false,\n  });\n\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n  };\n\n  const handleSelectChange = (selectedOption) => {\n    setFormData((prev) => ({\n      ...prev,\n      topic: selectedOption,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification('Слишком много попыток отправки. Подождите минуту.', 'error');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n\n    try {\n      // Validate and sanitize form data\n      const { data: sanitizedData, errors, isValid } = sanitizeAndValidateForm(formData);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: sanitizedData.topic?.value || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: 'owners', // Specify this is an owners form\n      };\n\n      // Submit form using the same API as the main form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.', 'success');\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false,\n      });\n      setFormErrors({});\n\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />{' '}\n      <form onSubmit={handleSubmit} className={styles.form}>\n        <div className={styles.row}>\n          <input\n            type=\"text\"\n            name=\"firstName\"\n            placeholder=\"Имя *\"\n            className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n            value={formData.firstName}\n            required\n            onChange={handleChange}\n            minLength=\"3\"\n          />\n          <input\n            type=\"text\"\n            name=\"lastName\"\n            placeholder=\"Фамилия\"\n            className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n            value={formData.lastName}\n            onChange={handleChange}\n            minLength=\"3\"\n          />\n        </div>\n        <div className={styles.row}>\n          <input\n            type=\"tel\"\n            name=\"phone\"\n            placeholder=\"Номер мобильного телефона *\"\n            className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n            value={formData.phone}\n            required\n            ref={phoneRef}\n            onChange={handleChange}\n          />\n          <input\n            type=\"text\"\n            name=\"message\"\n            placeholder=\"Введите Ваше сообщение\"\n            className={`${styles.input} ${formErrors.message ? styles.inputError : ''}`}\n            value={formData.message}\n            onChange={handleChange}\n          />\n        </div>\n        <div className={styles.row}>\n          <div className={styles.selectWrapper}>\n            <Select\n            required\n              classNamePrefix=\"react-select\"\n              options={options}\n              placeholder=\"Тема *\"\n              onChange={handleSelectChange}\n              value={formData.topic}\n              isSearchable={false}\n              styles={{\n                control: (provided, state) => ({\n                  ...provided,\n                  width: '100%',\n                  paddingLeft: '8px',\n                  paddingRight: '0px',\n                  height: '58px',\n                  borderRadius: '2px',\n                  border: `1px solid ${\n                    state.isFocused ? '#d7000f' : '#8e8e93'\n                  }`,\n                  fontSize: '16px',\n                  boxShadow: 'none',\n                  backgroundColor: '#fff',\n\n                  transition: 'all 0.2s ease',\n                  '&:hover': {\n                    borderColor: state.isFocused ? '#d7000f' : '#8e8e93',\n                  },\n                }),\n                container: (provided) => ({\n                  ...provided,\n                  width: '100%',\n                }),\n                placeholder: (provided) => ({\n                  ...provided,\n                  color: '#888',\n                }),\n                singleValue: (provided) => ({\n                  ...provided,\n                  color: '#000',\n                }),\n                indicatorsContainer: (provided) => ({\n                  ...provided,\n                  paddingRight: '10px',\n                }),\n                dropdownIndicator: (provided) => ({\n                  ...provided,\n                  color: '#8e8e93',\n                }),\n                menu: (provided) => ({\n                  ...provided,\n                  border: '1px solid #8e8e93',\n                  borderRadius: '2px',\n                  boxShadow: 'none',\n                  marginTop: '4px',\n                  backgroundColor: '#fff',\n                  zIndex: 9999,\n                  position: 'absolute',\n                }),\n                menuList: (provided) => ({\n                  ...provided,\n                  padding: 0,\n                }),\n                option: (provided, state) => ({\n                  ...provided,\n                  padding: '14px 16px',\n                  fontSize: '14px',\n                  backgroundColor: state.isFocused ? '#000' : '#fff',\n                  color: state.isFocused ? '#fff' : '#000',\n                  cursor: 'pointer',\n                  '&:active': {\n                    backgroundColor: '#000',\n                  },\n                }),\n              }}\n              menuPortalTarget={document.body}\n            />\n          </div>\n        </div>\n        <label className={styles.checkboxWrapper}>\n          <input\n            type=\"checkbox\"\n            name=\"consent\"\n            required\n            checked={formData.consent}\n            onChange={handleChange}\n            className={styles.checkbox}\n          />\n          <span>\n            Я даю согласие на обработку моих персональных данных для\n            проверки/подтверждения невыполненных кампаний по моим транспортным\n            средствам. *\n          </span>\n        </label>\n        <button type=\"submit\" className={styles.button} disabled={btnLoading || isSubmitting}>\n          {btnLoading ? 'Отправка...' : 'Отправить'}\n        </button>\n      </form>\n    </>\n  );\n};\n\nexport default OwnersForm;\n"], "mappings": "yGAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACpD,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,mBAAmB,CAC3C,MAAO,CAAAC,YAAY,KAAM,kDAAkD,CAC3E,OAASC,uBAAuB,CAAEC,eAAe,KAAQ,8BAA8B,CACvF,OAASC,cAAc,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,OAAO,CAAG,CACd,CAAEC,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,QAAS,CAAC,CACpC,CAAED,KAAK,CAAE,aAAa,CAAEC,KAAK,CAAE,aAAc,CAAC,CAC9C,CAAED,KAAK,CAAE,uBAAuB,CAAEC,KAAK,CAAE,uBAAwB,CAAC,CAClE,CACED,KAAK,CAAE,6BAA6B,CACpCC,KAAK,CAAE,6BACT,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,QACT,CAAC,CACF,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,CACvCqB,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CAEF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,CAAEyB,OAAO,CAAE,EAAE,CAAEI,IAAI,CAAE,EAAG,CAAC,CAAC,CAC3E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgC,UAAU,CAAEC,aAAa,CAAC,CAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACF;AACA;AACA;AACA,KACE,KAAM,CAAAoC,gBAAgB,CAAGnC,WAAW,CAAC,SAACwB,OAAO,CAAuB,IAArB,CAAAI,IAAI,CAAAQ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CAC7DT,eAAe,CAAC,CAAEH,OAAO,CAAEI,IAAK,CAAC,CAAC,CAClCW,UAAU,CAAC,IAAMZ,eAAe,CAAC,CAAEH,OAAO,CAAE,EAAE,CAAEI,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,QAAQ,CAAGrC,OAAO,CAAC,CACvBsC,IAAI,CAAE,mBAAmB,CACzBC,WAAW,CAAE,CAAEC,CAAC,CAAE,IAAK,CACzB,CAAC,CAAC,CAEF,KAAM,CAAAC,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAEC,IAAI,CAAE/B,KAAK,CAAEa,IAAI,CAAEmB,OAAQ,CAAC,CAAGF,CAAC,CAACG,MAAM,CAC/C7B,WAAW,CAAE8B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACP,CAACH,IAAI,EAAGlB,IAAI,GAAK,UAAU,CAAGmB,OAAO,CAAGhC,KAAK,EAC7C,CAAC,CACL,CAAC,CAED,KAAM,CAAAoC,kBAAkB,CAAIC,cAAc,EAAK,CAC7CjC,WAAW,CAAE8B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACP1B,KAAK,CAAE6B,cAAc,EACrB,CAAC,CACL,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAR,CAAC,EAAK,CAChCA,CAAC,CAACS,cAAc,CAAC,CAAC,CAElB;AACA,GAAIrB,YAAY,CAAE,OAElB;AACA,GAAI,CAAC3B,eAAe,CAACiD,SAAS,CAAC,CAAC,CAAE,CAChCpB,gBAAgB,CAAC,mDAAmD,CAAE,OAAO,CAAC,CAC9E,OACF,CAEAD,eAAe,CAAC,IAAI,CAAC,CACrBJ,aAAa,CAAC,IAAI,CAAC,CACnBE,aAAa,CAAC,CAAC,CAAC,CAAC,CAEjB,GAAI,KAAAwB,oBAAA,CACF;AACA,KAAM,CAAEC,IAAI,CAAEC,aAAa,CAAEC,MAAM,CAAEC,OAAQ,CAAC,CAAGvD,uBAAuB,CAACa,QAAQ,CAAC,CAElF,GAAI,CAAC0C,OAAO,CAAE,CACZ5B,aAAa,CAAC2B,MAAM,CAAC,CACrBxB,gBAAgB,CAAC,sCAAsC,CAAE,OAAO,CAAC,CACjE,OACF,CAEA;AACA,KAAM,CAAA0B,OAAO,CAAG,CACdzC,SAAS,CAAEsC,aAAa,CAACtC,SAAS,CAClCC,QAAQ,CAAEqC,aAAa,CAACrC,QAAQ,CAChCC,KAAK,CAAEoC,aAAa,CAACpC,KAAK,CAC1BC,KAAK,CAAE,EAAAiC,oBAAA,CAAAE,aAAa,CAACnC,KAAK,UAAAiC,oBAAA,iBAAnBA,oBAAA,CAAqBzC,KAAK,GAAI,EAAE,CACvCS,OAAO,CAAEkC,aAAa,CAAClC,OAAO,CAC9BC,OAAO,CAAEiC,aAAa,CAACjC,OAAO,CAC9BqC,QAAQ,CAAE,QAAU;AACtB,CAAC,CAED;AACA,KAAM,CAAAvD,cAAc,CAACsD,OAAO,CAAC,CAE7B;AACA1B,gBAAgB,CAAC,iEAAiE,CAAE,SAAS,CAAC,CAE9F;AACAhB,WAAW,CAAC,CACVC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CACFO,aAAa,CAAC,CAAC,CAAC,CAAC,CAEnB,CAAE,MAAO+B,KAAK,CAAE,CACd5B,gBAAgB,CAAC4B,KAAK,CAACvC,OAAO,CAAE,OAAO,CAAC,CAC1C,CAAC,OAAS,CACRM,aAAa,CAAC,KAAK,CAAC,CACpBI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,mBACEvB,KAAA,CAAAE,SAAA,EAAAmD,QAAA,eACEvD,IAAA,CAACL,YAAY,EAACoB,OAAO,CAAEE,YAAY,CAACF,OAAQ,CAACI,IAAI,CAAEF,YAAY,CAACE,IAAK,CAAE,CAAC,CAAC,GAAG,cAC5EjB,KAAA,SAAMsD,QAAQ,CAAEZ,YAAa,CAACa,SAAS,CAAEhE,MAAM,CAACiE,IAAK,CAAAH,QAAA,eACnDrD,KAAA,QAAKuD,SAAS,CAAEhE,MAAM,CAACkE,GAAI,CAAAJ,QAAA,eACzBvD,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXkB,IAAI,CAAC,WAAW,CAChBuB,WAAW,CAAC,sBAAO,CACnBH,SAAS,IAAAI,MAAA,CAAKpE,MAAM,CAACqE,KAAK,MAAAD,MAAA,CAAIvC,UAAU,CAACX,SAAS,CAAGlB,MAAM,CAACsE,UAAU,CAAG,EAAE,CAAG,CAC9EzD,KAAK,CAAEG,QAAQ,CAACE,SAAU,CAC1BqD,QAAQ,MACRC,QAAQ,CAAE9B,YAAa,CACvB+B,SAAS,CAAC,GAAG,CACd,CAAC,cACFlE,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXkB,IAAI,CAAC,UAAU,CACfuB,WAAW,CAAC,4CAAS,CACrBH,SAAS,IAAAI,MAAA,CAAKpE,MAAM,CAACqE,KAAK,MAAAD,MAAA,CAAIvC,UAAU,CAACV,QAAQ,CAAGnB,MAAM,CAACsE,UAAU,CAAG,EAAE,CAAG,CAC7EzD,KAAK,CAAEG,QAAQ,CAACG,QAAS,CACzBqD,QAAQ,CAAE9B,YAAa,CACvB+B,SAAS,CAAC,GAAG,CACd,CAAC,EACC,CAAC,cACNhE,KAAA,QAAKuD,SAAS,CAAEhE,MAAM,CAACkE,GAAI,CAAAJ,QAAA,eACzBvD,IAAA,UACEmB,IAAI,CAAC,KAAK,CACVkB,IAAI,CAAC,OAAO,CACZuB,WAAW,CAAC,gJAA6B,CACzCH,SAAS,IAAAI,MAAA,CAAKpE,MAAM,CAACqE,KAAK,MAAAD,MAAA,CAAIvC,UAAU,CAACT,KAAK,CAAGpB,MAAM,CAACsE,UAAU,CAAG,EAAE,CAAG,CAC1EzD,KAAK,CAAEG,QAAQ,CAACI,KAAM,CACtBmD,QAAQ,MACRG,GAAG,CAAEpC,QAAS,CACdkC,QAAQ,CAAE9B,YAAa,CACxB,CAAC,cACFnC,IAAA,UACEmB,IAAI,CAAC,MAAM,CACXkB,IAAI,CAAC,SAAS,CACduB,WAAW,CAAC,4HAAwB,CACpCH,SAAS,IAAAI,MAAA,CAAKpE,MAAM,CAACqE,KAAK,MAAAD,MAAA,CAAIvC,UAAU,CAACP,OAAO,CAAGtB,MAAM,CAACsE,UAAU,CAAG,EAAE,CAAG,CAC5EzD,KAAK,CAAEG,QAAQ,CAACM,OAAQ,CACxBkD,QAAQ,CAAE9B,YAAa,CACxB,CAAC,EACC,CAAC,cACNnC,IAAA,QAAKyD,SAAS,CAAEhE,MAAM,CAACkE,GAAI,CAAAJ,QAAA,cACzBvD,IAAA,QAAKyD,SAAS,CAAEhE,MAAM,CAAC2E,aAAc,CAAAb,QAAA,cACnCvD,IAAA,CAACR,MAAM,EACPwE,QAAQ,MACNK,eAAe,CAAC,cAAc,CAC9BhE,OAAO,CAAEA,OAAQ,CACjBuD,WAAW,CAAC,4BAAQ,CACpBK,QAAQ,CAAEvB,kBAAmB,CAC7BpC,KAAK,CAAEG,QAAQ,CAACK,KAAM,CACtBwD,YAAY,CAAE,KAAM,CACpB7E,MAAM,CAAE,CACN8E,OAAO,CAAEA,CAACC,QAAQ,CAAEC,KAAK,GAAAhC,aAAA,CAAAA,aAAA,IACpB+B,QAAQ,MACXE,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,KAAK,CAClBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,cAAAlB,MAAA,CACJY,KAAK,CAACO,SAAS,CAAG,SAAS,CAAG,SAAS,CACvC,CACFC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,MAAM,CACjBC,eAAe,CAAE,MAAM,CAEvBC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTC,WAAW,CAAEZ,KAAK,CAACO,SAAS,CAAG,SAAS,CAAG,SAC7C,CAAC,EACD,CACFM,SAAS,CAAGd,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACf+B,QAAQ,MACXE,KAAK,CAAE,MAAM,EACb,CACFd,WAAW,CAAGY,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACjB+B,QAAQ,MACXe,KAAK,CAAE,MAAM,EACb,CACFC,WAAW,CAAGhB,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACjB+B,QAAQ,MACXe,KAAK,CAAE,MAAM,EACb,CACFE,mBAAmB,CAAGjB,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACzB+B,QAAQ,MACXI,YAAY,CAAE,MAAM,EACpB,CACFc,iBAAiB,CAAGlB,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACvB+B,QAAQ,MACXe,KAAK,CAAE,SAAS,EAChB,CACFI,IAAI,CAAGnB,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACV+B,QAAQ,MACXO,MAAM,CAAE,mBAAmB,CAC3BD,YAAY,CAAE,KAAK,CACnBI,SAAS,CAAE,MAAM,CACjBU,SAAS,CAAE,KAAK,CAChBT,eAAe,CAAE,MAAM,CACvBU,MAAM,CAAE,IAAI,CACZC,QAAQ,CAAE,UAAU,EACpB,CACFC,QAAQ,CAAGvB,QAAQ,EAAA/B,aAAA,CAAAA,aAAA,IACd+B,QAAQ,MACXwB,OAAO,CAAE,CAAC,EACV,CACFC,MAAM,CAAEA,CAACzB,QAAQ,CAAEC,KAAK,GAAAhC,aAAA,CAAAA,aAAA,IACnB+B,QAAQ,MACXwB,OAAO,CAAE,WAAW,CACpBf,QAAQ,CAAE,MAAM,CAChBE,eAAe,CAAEV,KAAK,CAACO,SAAS,CAAG,MAAM,CAAG,MAAM,CAClDO,KAAK,CAAEd,KAAK,CAACO,SAAS,CAAG,MAAM,CAAG,MAAM,CACxCkB,MAAM,CAAE,SAAS,CACjB,UAAU,CAAE,CACVf,eAAe,CAAE,MACnB,CAAC,EAEL,CAAE,CACFgB,gBAAgB,CAAEC,QAAQ,CAACC,IAAK,CACjC,CAAC,CACC,CAAC,CACH,CAAC,cACNnG,KAAA,UAAOuD,SAAS,CAAEhE,MAAM,CAAC6G,eAAgB,CAAA/C,QAAA,eACvCvD,IAAA,UACEmB,IAAI,CAAC,UAAU,CACfkB,IAAI,CAAC,SAAS,CACd2B,QAAQ,MACR1B,OAAO,CAAE7B,QAAQ,CAACO,OAAQ,CAC1BiD,QAAQ,CAAE9B,YAAa,CACvBsB,SAAS,CAAEhE,MAAM,CAAC8G,QAAS,CAC5B,CAAC,cACFvG,IAAA,SAAAuD,QAAA,CAAM,mtBAIN,CAAM,CAAC,EACF,CAAC,cACRvD,IAAA,WAAQmB,IAAI,CAAC,QAAQ,CAACsC,SAAS,CAAEhE,MAAM,CAAC+G,MAAO,CAACC,QAAQ,CAAErF,UAAU,EAAII,YAAa,CAAA+B,QAAA,CAClFnC,UAAU,CAAG,aAAa,CAAG,WAAW,CACnC,CAAC,EACL,CAAC,EACP,CAAC,CAEP,CAAC,CAED,cAAe,CAAAZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}