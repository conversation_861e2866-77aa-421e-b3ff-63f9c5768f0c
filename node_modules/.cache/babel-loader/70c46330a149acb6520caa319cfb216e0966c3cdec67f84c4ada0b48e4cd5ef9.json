{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/Pages/Accessories/Accessories.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styles from '../../owners.module.css';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport PartsList from '../../components/PartsList/PartsList';\n\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Accessories = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.layout,\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: styles.main,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.mainContainer,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                \"data-aos\": \"fade-up\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u041E\\u0420\\u0418\\u0413\\u0418\\u041D\\u0410\\u041B\\u042C\\u041D\\u042B\\u0415 \\u0410\\u041A\\u0421\\u0415\\u0421\\u0421\\u0423\\u0410\\u0420\\u042B GWM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"100\",\n                className: styles.redLine\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.textContent,\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"150\",\n                  children: \"\\u041F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043D\\u043E\\u0432\\u043E\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F GWM \\u0441\\u0442\\u0430\\u043B\\u0430 \\u043F\\u0440\\u043E\\u0449\\u0435, \\u0447\\u0435\\u043C \\u043A\\u043E\\u0433\\u0434\\u0430-\\u043B\\u0438\\u0431\\u043E, \\u0441 \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u0430\\u043C\\u0438 GWM!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"200\",\n                  children: \"\\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u044B \\u0434\\u043B\\u044F \\u0443\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u044F \\u0441\\u0442\\u0438\\u043B\\u044F, \\u043F\\u043E\\u0434\\u0433\\u043E\\u043D\\u043A\\u0438, \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0438 \\u043E\\u043F\\u0442\\u0438\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0440\\u0430\\u0431\\u043E\\u0442\\u044B. \\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u0438\\u0432\\u0430\\u044E\\u0442 \\u0438 \\u043F\\u043E\\u0432\\u044B\\u0448\\u0430\\u044E\\u0442 \\u043E\\u0431\\u0449\\u0443\\u044E \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F. \\u041D\\u0435 \\u0438\\u0434\\u0438\\u0442\\u0435 \\u043D\\u0430 \\u043A\\u043E\\u043C\\u043F\\u0440\\u043E\\u043C\\u0438\\u0441\\u0441 \\u043E\\u0442\\u043D\\u043E\\u0441\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u043D\\u0430\\u0441\\u0442\\u0430\\u0438\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0438 \\u0443\\u0441\\u0442\\u0430\\u043D\\u0430\\u0432\\u043B\\u0438\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u0430 \\u0441\\u0432\\u043E\\u0439 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B GWM!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"200\",\n                  children: \"\\u041F\\u0440\\u043E\\u0441\\u0442\\u043E \\u0432\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u0438\\u0437 \\u043D\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0441\\u0441\\u043E\\u0440\\u0442\\u0438\\u043C\\u0435\\u043D\\u0442\\u0430 \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043A\\u0441\\u0435\\u0441\\u0441\\u0443\\u0430\\u0440\\u044B GWM \\u0434\\u043B\\u044F \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PartsList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Accessories, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Accessories;\nexport default Accessories;\nvar _c;\n$RefreshReg$(_c, \"Accessories\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "Sidebar", "PartsList", "AOS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Accessories", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "main", "mainContainer", "redLine", "textContent", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Accessories/Accessories.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from '../../owners.module.css';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport PartsList from '../../components/PartsList/PartsList';\n\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Accessories = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                     <strong>ОРИГИНАЛЬНЫЕ АКСЕССУАРЫ GWM</strong>\n                  </h1>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <div className={styles.textContent}>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                      Персонализация вашего нового автомобиля GWM стала проще,\n                      чем когда-либо, с оригинальными аксессуарами GWM!\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Оригинальные аксессуары специально разработаны для\n                      улучшения стиля, подгонки, функциональности и оптимальной\n                      работы. Оригинальные аксессуары поддерживают и повышают\n                      общую ценность вашего автомобиля. Не идите на компромисс\n                      относительно качества и всегда настаивайте и\n                      устанавливайте на свой автомобиль только оригинальные\n                      аксессуары GWM!\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Просто выберите из нашего ассортимента оригинальные\n                      аксессуары GWM для вашего автомобиля!\n                    </p>\n                  </div>\n                  {/* <OwnersForm /> */}\n                  <PartsList />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Accessories;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,SAAS,MAAM,sCAAsC;;AAE5D;AACA,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdK,GAAG,CAACS,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;IAEzC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBnB,OAAA;UAAKoB,SAAS,EAAEzB,MAAM,CAAC8B,MAAO;UAAAN,QAAA,gBAC5BnB,OAAA,CAACJ,OAAO;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXxB,OAAA;YAAMoB,SAAS,EAAEzB,MAAM,CAAC+B,IAAK;YAAAP,QAAA,eAC3BnB,OAAA;cAAKoB,SAAS,EAAEzB,MAAM,CAACgC,aAAc;cAAAR,QAAA,gBACnCnB,OAAA;gBAAI,YAAS,SAAS;gBAAAmB,QAAA,eACnBnB,OAAA;kBAAAmB,QAAA,EAAQ;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACLxB,OAAA;gBACE,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBACpBoB,SAAS,EAAEzB,MAAM,CAACiC;cAAQ;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAELxB,OAAA;gBAAKoB,SAAS,EAAEzB,MAAM,CAACkC,WAAY;gBAAAV,QAAA,gBACjCnB,OAAA;kBAAG,YAAS,SAAS;kBAAC,kBAAe,KAAK;kBAAAmB,QAAA,EAAC;gBAG3C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAG,YAAS,SAAS;kBAAC,kBAAe,KAAK;kBAAAmB,QAAA,EAAC;gBAQ3C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAG,YAAS,SAAS;kBAAC,kBAAe,KAAK;kBAAAmB,QAAA,EAAC;gBAG3C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENxB,OAAA,CAACH,SAAS;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CAvEID,WAAW;AAAA2B,EAAA,GAAX3B,WAAW;AAyEjB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}