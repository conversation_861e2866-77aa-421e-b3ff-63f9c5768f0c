{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElement, h as elementIndex, m as makeElementsArray } from '../shared/utils.mjs';\nfunction A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      containerRole: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n      scrollOnFocus: true\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  let preventFocusHandler;\n  let focusTargetSlideEl;\n  let visibilityChangedTimestamp = new Date().getTime();\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n  function getRandomNumber(size) {\n    if (size === void 0) {\n      size = 16;\n    }\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {\n      const prevEls = makeElementsArray(swiper.navigation.prevEl);\n      const nextEls = makeElementsArray(swiper.navigation.nextEl);\n      if (nextEls.includes(targetEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          notify(params.lastSlideMessage);\n        } else {\n          notify(params.nextSlideMessage);\n        }\n      }\n      if (prevEls.includes(targetEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          notify(params.firstSlideMessage);\n        } else {\n          notify(params.prevSlideMessage);\n        }\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = e => {\n    if (focusTargetSlideEl && focusTargetSlideEl !== e.target && !focusTargetSlideEl.contains(e.target)) {\n      preventFocusHandler = true;\n    }\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    preventFocusHandler = false;\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const onVisibilityChange = e => {\n    visibilityChangedTimestamp = new Date().getTime();\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked || !swiper.params.a11y.scrollOnFocus) return;\n    if (new Date().getTime() - visibilityChangedTimestamp < 100) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    focusTargetSlideEl = slideEl;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    requestAnimationFrame(() => {\n      if (preventFocusHandler) return;\n      if (swiper.params.loop) {\n        swiper.slideToLoop(parseInt(slideEl.getAttribute('data-swiper-slide-index')), 0);\n      } else {\n        swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n      }\n      preventFocusHandler = false;\n    });\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n    if (params.containerRole) {\n      addElRole(containerEl, params.containerRole);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n    // Tab focus\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('focus', handleFocus, true);\n      swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n      swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n    }\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\nexport { A11y as default };", "map": {"version": 3, "names": ["g", "getDocument", "c", "classesToSelector", "createElement", "h", "elementIndex", "m", "makeElementsArray", "A11y", "_ref", "swiper", "extendParams", "on", "a11y", "enabled", "notificationClass", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "slideLabelMessage", "containerMessage", "containerRoleDescriptionMessage", "containerRole", "itemRoleDescriptionMessage", "slideRole", "id", "scrollOnFocus", "clicked", "liveRegion", "preventFocus<PERSON><PERSON>ler", "focusTargetSlideEl", "visibilityChangedTimestamp", "Date", "getTime", "notify", "message", "notification", "length", "innerHTML", "getRandomNumber", "size", "randomChar", "Math", "round", "random", "toString", "repeat", "replace", "makeElFocusable", "el", "for<PERSON>ach", "subEl", "setAttribute", "makeElNotFocusable", "addElRole", "role", "addElRoleDescription", "description", "addElControls", "controls", "addElLabel", "label", "addElId", "addElLive", "live", "disableEl", "enableEl", "onEnterOrSpaceKey", "e", "keyCode", "params", "targetEl", "target", "pagination", "contains", "matches", "bulletClass", "navigation", "prevEl", "nextEl", "prevEls", "nextEls", "includes", "isEnd", "loop", "slideNext", "isBeginning", "slidePrev", "click", "updateNavigation", "rewind", "hasPagination", "bullets", "hasClickablePagination", "clickable", "updatePagination", "bulletEl", "renderBullet", "bulletActiveClass", "removeAttribute", "initNavEl", "wrapperId", "tagName", "addEventListener", "handlePointerDown", "handlePointerUp", "requestAnimationFrame", "destroyed", "onVisibilityChange", "handleFocus", "slideEl", "closest", "slideClass", "slides", "isActive", "indexOf", "activeIndex", "isVisible", "watchSlidesProgress", "visibleSlides", "sourceCapabilities", "firesTouchEvents", "isHorizontal", "scrollLeft", "scrollTop", "slideToLoop", "parseInt", "getAttribute", "slideTo", "initSlides", "<PERSON><PERSON><PERSON><PERSON>", "index", "slideIndex", "ariaLabelMessage", "init", "append", "containerEl", "wrapperEl", "autoplay", "paginationEl", "document", "destroy", "remove", "removeEventListener", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/modules/a11y.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElement, h as elementIndex, m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      containerRole: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n      scrollOnFocus: true\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  let preventFocusHandler;\n  let focusTargetSlideEl;\n  let visibilityChangedTimestamp = new Date().getTime();\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    notification.innerHTML = '';\n    notification.innerHTML = message;\n  }\n  function getRandomNumber(size) {\n    if (size === void 0) {\n      size = 16;\n    }\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {\n      const prevEls = makeElementsArray(swiper.navigation.prevEl);\n      const nextEls = makeElementsArray(swiper.navigation.nextEl);\n      if (nextEls.includes(targetEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          notify(params.lastSlideMessage);\n        } else {\n          notify(params.nextSlideMessage);\n        }\n      }\n      if (prevEls.includes(targetEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          notify(params.firstSlideMessage);\n        } else {\n          notify(params.prevSlideMessage);\n        }\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = e => {\n    if (focusTargetSlideEl && focusTargetSlideEl !== e.target && !focusTargetSlideEl.contains(e.target)) {\n      preventFocusHandler = true;\n    }\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    preventFocusHandler = false;\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const onVisibilityChange = e => {\n    visibilityChangedTimestamp = new Date().getTime();\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked || !swiper.params.a11y.scrollOnFocus) return;\n    if (new Date().getTime() - visibilityChangedTimestamp < 100) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    focusTargetSlideEl = slideEl;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    requestAnimationFrame(() => {\n      if (preventFocusHandler) return;\n      if (swiper.params.loop) {\n        swiper.slideToLoop(parseInt(slideEl.getAttribute('data-swiper-slide-index')), 0);\n      } else {\n        swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n      }\n      preventFocusHandler = false;\n    });\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n    if (params.containerRole) {\n      addElRole(containerEl, params.containerRole);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n    // Tab focus\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('focus', handleFocus, true);\n      swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n      swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n    }\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\n\nexport { A11y as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;AAC/D,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,mCAAmC;AAC1E,SAASD,CAAC,IAAIE,aAAa,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,qBAAqB;AAEnG,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,IAAI,EAAE;MACJC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,qBAAqB;MACxCC,gBAAgB,EAAE,gBAAgB;MAClCC,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,yBAAyB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,uBAAuB,EAAE,uBAAuB;MAChDC,iBAAiB,EAAE,8BAA8B;MACjDC,gBAAgB,EAAE,IAAI;MACtBC,+BAA+B,EAAE,IAAI;MACrCC,aAAa,EAAE,IAAI;MACnBC,0BAA0B,EAAE,IAAI;MAChCC,SAAS,EAAE,OAAO;MAClBC,EAAE,EAAE,IAAI;MACRC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACFlB,MAAM,CAACG,IAAI,GAAG;IACZgB,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,mBAAmB;EACvB,IAAIC,kBAAkB;EACtB,IAAIC,0BAA0B,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACrD,SAASC,MAAMA,CAACC,OAAO,EAAE;IACvB,MAAMC,YAAY,GAAGR,UAAU;IAC/B,IAAIQ,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;IAC/BD,YAAY,CAACE,SAAS,GAAG,EAAE;IAC3BF,YAAY,CAACE,SAAS,GAAGH,OAAO;EAClC;EACA,SAASI,eAAeA,CAACC,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,EAAE;IACX;IACA,MAAMC,UAAU,GAAGA,CAAA,KAAMC,IAAI,CAACC,KAAK,CAAC,EAAE,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACpE,OAAO,GAAG,CAACC,MAAM,CAACN,IAAI,CAAC,CAACO,OAAO,CAAC,IAAI,EAAEN,UAAU,CAAC;EACnD;EACA,SAASO,eAAeA,CAACC,EAAE,EAAE;IAC3BA,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,CAAC,CAAC;EACJ;EACA,SAASC,kBAAkBA,CAACJ,EAAE,EAAE;IAC9BA,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACJ;EACA,SAASE,SAASA,CAACL,EAAE,EAAEM,IAAI,EAAE;IAC3BN,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,MAAM,EAAEG,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EACA,SAASC,oBAAoBA,CAACP,EAAE,EAAEQ,WAAW,EAAE;IAC7CR,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,sBAAsB,EAAEK,WAAW,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAACT,EAAE,EAAEU,QAAQ,EAAE;IACnCV,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAEO,QAAQ,CAAC;IAC/C,CAAC,CAAC;EACJ;EACA,SAASC,UAAUA,CAACX,EAAE,EAAEY,KAAK,EAAE;IAC7BZ,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,YAAY,EAAES,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EACA,SAASC,OAAOA,CAACb,EAAE,EAAExB,EAAE,EAAE;IACvBwB,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,IAAI,EAAE3B,EAAE,CAAC;IAC9B,CAAC,CAAC;EACJ;EACA,SAASsC,SAASA,CAACd,EAAE,EAAEe,IAAI,EAAE;IAC3Bf,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,WAAW,EAAEY,IAAI,CAAC;IACvC,CAAC,CAAC;EACJ;EACA,SAASC,SAASA,CAAChB,EAAE,EAAE;IACrBA,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;IAC3C,CAAC,CAAC;EACJ;EACA,SAASc,QAAQA,CAACjB,EAAE,EAAE;IACpBA,EAAE,GAAG5C,iBAAiB,CAAC4C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC;IAC5C,CAAC,CAAC;EACJ;EACA,SAASe,iBAAiBA,CAACC,CAAC,EAAE;IAC5B,IAAIA,CAAC,CAACC,OAAO,KAAK,EAAE,IAAID,CAAC,CAACC,OAAO,KAAK,EAAE,EAAE;IAC1C,MAAMC,MAAM,GAAG9D,MAAM,CAAC8D,MAAM,CAAC3D,IAAI;IACjC,MAAM4D,QAAQ,GAAGH,CAAC,CAACI,MAAM;IACzB,IAAIhE,MAAM,CAACiE,UAAU,IAAIjE,MAAM,CAACiE,UAAU,CAACxB,EAAE,KAAKsB,QAAQ,KAAK/D,MAAM,CAACiE,UAAU,CAACxB,EAAE,IAAIzC,MAAM,CAACiE,UAAU,CAACxB,EAAE,CAACyB,QAAQ,CAACN,CAAC,CAACI,MAAM,CAAC,CAAC,EAAE;MAC/H,IAAI,CAACJ,CAAC,CAACI,MAAM,CAACG,OAAO,CAAC3E,iBAAiB,CAACQ,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;IAClF;IACA,IAAIpE,MAAM,CAACqE,UAAU,IAAIrE,MAAM,CAACqE,UAAU,CAACC,MAAM,IAAItE,MAAM,CAACqE,UAAU,CAACE,MAAM,EAAE;MAC7E,MAAMC,OAAO,GAAG3E,iBAAiB,CAACG,MAAM,CAACqE,UAAU,CAACC,MAAM,CAAC;MAC3D,MAAMG,OAAO,GAAG5E,iBAAiB,CAACG,MAAM,CAACqE,UAAU,CAACE,MAAM,CAAC;MAC3D,IAAIE,OAAO,CAACC,QAAQ,CAACX,QAAQ,CAAC,EAAE;QAC9B,IAAI,EAAE/D,MAAM,CAAC2E,KAAK,IAAI,CAAC3E,MAAM,CAAC8D,MAAM,CAACc,IAAI,CAAC,EAAE;UAC1C5E,MAAM,CAAC6E,SAAS,CAAC,CAAC;QACpB;QACA,IAAI7E,MAAM,CAAC2E,KAAK,EAAE;UAChBjD,MAAM,CAACoC,MAAM,CAACrD,gBAAgB,CAAC;QACjC,CAAC,MAAM;UACLiB,MAAM,CAACoC,MAAM,CAACvD,gBAAgB,CAAC;QACjC;MACF;MACA,IAAIiE,OAAO,CAACE,QAAQ,CAACX,QAAQ,CAAC,EAAE;QAC9B,IAAI,EAAE/D,MAAM,CAAC8E,WAAW,IAAI,CAAC9E,MAAM,CAAC8D,MAAM,CAACc,IAAI,CAAC,EAAE;UAChD5E,MAAM,CAAC+E,SAAS,CAAC,CAAC;QACpB;QACA,IAAI/E,MAAM,CAAC8E,WAAW,EAAE;UACtBpD,MAAM,CAACoC,MAAM,CAACtD,iBAAiB,CAAC;QAClC,CAAC,MAAM;UACLkB,MAAM,CAACoC,MAAM,CAACxD,gBAAgB,CAAC;QACjC;MACF;IACF;IACA,IAAIN,MAAM,CAACiE,UAAU,IAAIF,QAAQ,CAACI,OAAO,CAAC3E,iBAAiB,CAACQ,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;MAClGL,QAAQ,CAACiB,KAAK,CAAC,CAAC;IAClB;EACF;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAIjF,MAAM,CAAC8D,MAAM,CAACc,IAAI,IAAI5E,MAAM,CAAC8D,MAAM,CAACoB,MAAM,IAAI,CAAClF,MAAM,CAACqE,UAAU,EAAE;IACtE,MAAM;MACJE,MAAM;MACND;IACF,CAAC,GAAGtE,MAAM,CAACqE,UAAU;IACrB,IAAIC,MAAM,EAAE;MACV,IAAItE,MAAM,CAAC8E,WAAW,EAAE;QACtBrB,SAAS,CAACa,MAAM,CAAC;QACjBzB,kBAAkB,CAACyB,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLZ,QAAQ,CAACY,MAAM,CAAC;QAChB9B,eAAe,CAAC8B,MAAM,CAAC;MACzB;IACF;IACA,IAAIC,MAAM,EAAE;MACV,IAAIvE,MAAM,CAAC2E,KAAK,EAAE;QAChBlB,SAAS,CAACc,MAAM,CAAC;QACjB1B,kBAAkB,CAAC0B,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLb,QAAQ,CAACa,MAAM,CAAC;QAChB/B,eAAe,CAAC+B,MAAM,CAAC;MACzB;IACF;EACF;EACA,SAASY,aAAaA,CAAA,EAAG;IACvB,OAAOnF,MAAM,CAACiE,UAAU,IAAIjE,MAAM,CAACiE,UAAU,CAACmB,OAAO,IAAIpF,MAAM,CAACiE,UAAU,CAACmB,OAAO,CAACvD,MAAM;EAC3F;EACA,SAASwD,sBAAsBA,CAAA,EAAG;IAChC,OAAOF,aAAa,CAAC,CAAC,IAAInF,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACqB,SAAS;EAC9D;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,MAAMzB,MAAM,GAAG9D,MAAM,CAAC8D,MAAM,CAAC3D,IAAI;IACjC,IAAI,CAACgF,aAAa,CAAC,CAAC,EAAE;IACtBnF,MAAM,CAACiE,UAAU,CAACmB,OAAO,CAAC1C,OAAO,CAAC8C,QAAQ,IAAI;MAC5C,IAAIxF,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACqB,SAAS,EAAE;QACtC9C,eAAe,CAACgD,QAAQ,CAAC;QACzB,IAAI,CAACxF,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACwB,YAAY,EAAE;UAC1C3C,SAAS,CAAC0C,QAAQ,EAAE,QAAQ,CAAC;UAC7BpC,UAAU,CAACoC,QAAQ,EAAE1B,MAAM,CAACpD,uBAAuB,CAAC6B,OAAO,CAAC,eAAe,EAAE5C,YAAY,CAAC6F,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3G;MACF;MACA,IAAIA,QAAQ,CAACrB,OAAO,CAAC3E,iBAAiB,CAACQ,MAAM,CAAC8D,MAAM,CAACG,UAAU,CAACyB,iBAAiB,CAAC,CAAC,EAAE;QACnFF,QAAQ,CAAC5C,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;MAC/C,CAAC,MAAM;QACL4C,QAAQ,CAACG,eAAe,CAAC,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ;EACA,MAAMC,SAAS,GAAGA,CAACnD,EAAE,EAAEoD,SAAS,EAAElE,OAAO,KAAK;IAC5Ca,eAAe,CAACC,EAAE,CAAC;IACnB,IAAIA,EAAE,CAACqD,OAAO,KAAK,QAAQ,EAAE;MAC3BhD,SAAS,CAACL,EAAE,EAAE,QAAQ,CAAC;MACvBA,EAAE,CAACsD,gBAAgB,CAAC,SAAS,EAAEpC,iBAAiB,CAAC;IACnD;IACAP,UAAU,CAACX,EAAE,EAAEd,OAAO,CAAC;IACvBuB,aAAa,CAACT,EAAE,EAAEoD,SAAS,CAAC;EAC9B,CAAC;EACD,MAAMG,iBAAiB,GAAGpC,CAAC,IAAI;IAC7B,IAAItC,kBAAkB,IAAIA,kBAAkB,KAAKsC,CAAC,CAACI,MAAM,IAAI,CAAC1C,kBAAkB,CAAC4C,QAAQ,CAACN,CAAC,CAACI,MAAM,CAAC,EAAE;MACnG3C,mBAAmB,GAAG,IAAI;IAC5B;IACArB,MAAM,CAACG,IAAI,CAACgB,OAAO,GAAG,IAAI;EAC5B,CAAC;EACD,MAAM8E,eAAe,GAAGA,CAAA,KAAM;IAC5B5E,mBAAmB,GAAG,KAAK;IAC3B6E,qBAAqB,CAAC,MAAM;MAC1BA,qBAAqB,CAAC,MAAM;QAC1B,IAAI,CAAClG,MAAM,CAACmG,SAAS,EAAE;UACrBnG,MAAM,CAACG,IAAI,CAACgB,OAAO,GAAG,KAAK;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMiF,kBAAkB,GAAGxC,CAAC,IAAI;IAC9BrC,0BAA0B,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACnD,CAAC;EACD,MAAM4E,WAAW,GAAGzC,CAAC,IAAI;IACvB,IAAI5D,MAAM,CAACG,IAAI,CAACgB,OAAO,IAAI,CAACnB,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACe,aAAa,EAAE;IAC9D,IAAI,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGF,0BAA0B,GAAG,GAAG,EAAE;IAC7D,MAAM+E,OAAO,GAAG1C,CAAC,CAACI,MAAM,CAACuC,OAAO,CAAC,IAAIvG,MAAM,CAAC8D,MAAM,CAAC0C,UAAU,gBAAgB,CAAC;IAC9E,IAAI,CAACF,OAAO,IAAI,CAACtG,MAAM,CAACyG,MAAM,CAAC/B,QAAQ,CAAC4B,OAAO,CAAC,EAAE;IAClDhF,kBAAkB,GAAGgF,OAAO;IAC5B,MAAMI,QAAQ,GAAG1G,MAAM,CAACyG,MAAM,CAACE,OAAO,CAACL,OAAO,CAAC,KAAKtG,MAAM,CAAC4G,WAAW;IACtE,MAAMC,SAAS,GAAG7G,MAAM,CAAC8D,MAAM,CAACgD,mBAAmB,IAAI9G,MAAM,CAAC+G,aAAa,IAAI/G,MAAM,CAAC+G,aAAa,CAACrC,QAAQ,CAAC4B,OAAO,CAAC;IACrH,IAAII,QAAQ,IAAIG,SAAS,EAAE;IAC3B,IAAIjD,CAAC,CAACoD,kBAAkB,IAAIpD,CAAC,CAACoD,kBAAkB,CAACC,gBAAgB,EAAE;IACnE,IAAIjH,MAAM,CAACkH,YAAY,CAAC,CAAC,EAAE;MACzBlH,MAAM,CAACyC,EAAE,CAAC0E,UAAU,GAAG,CAAC;IAC1B,CAAC,MAAM;MACLnH,MAAM,CAACyC,EAAE,CAAC2E,SAAS,GAAG,CAAC;IACzB;IACAlB,qBAAqB,CAAC,MAAM;MAC1B,IAAI7E,mBAAmB,EAAE;MACzB,IAAIrB,MAAM,CAAC8D,MAAM,CAACc,IAAI,EAAE;QACtB5E,MAAM,CAACqH,WAAW,CAACC,QAAQ,CAAChB,OAAO,CAACiB,YAAY,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;MAClF,CAAC,MAAM;QACLvH,MAAM,CAACwH,OAAO,CAACxH,MAAM,CAACyG,MAAM,CAACE,OAAO,CAACL,OAAO,CAAC,EAAE,CAAC,CAAC;MACnD;MACAjF,mBAAmB,GAAG,KAAK;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD,MAAMoG,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAM3D,MAAM,GAAG9D,MAAM,CAAC8D,MAAM,CAAC3D,IAAI;IACjC,IAAI2D,MAAM,CAAC/C,0BAA0B,EAAE;MACrCiC,oBAAoB,CAAChD,MAAM,CAACyG,MAAM,EAAE3C,MAAM,CAAC/C,0BAA0B,CAAC;IACxE;IACA,IAAI+C,MAAM,CAAC9C,SAAS,EAAE;MACpB8B,SAAS,CAAC9C,MAAM,CAACyG,MAAM,EAAE3C,MAAM,CAAC9C,SAAS,CAAC;IAC5C;IACA,MAAM0G,YAAY,GAAG1H,MAAM,CAACyG,MAAM,CAAC5E,MAAM;IACzC,IAAIiC,MAAM,CAACnD,iBAAiB,EAAE;MAC5BX,MAAM,CAACyG,MAAM,CAAC/D,OAAO,CAAC,CAAC4D,OAAO,EAAEqB,KAAK,KAAK;QACxC,MAAMC,UAAU,GAAG5H,MAAM,CAAC8D,MAAM,CAACc,IAAI,GAAG0C,QAAQ,CAAChB,OAAO,CAACiB,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC,GAAGI,KAAK;QAC7G,MAAME,gBAAgB,GAAG/D,MAAM,CAACnD,iBAAiB,CAAC4B,OAAO,CAAC,eAAe,EAAEqF,UAAU,GAAG,CAAC,CAAC,CAACrF,OAAO,CAAC,sBAAsB,EAAEmF,YAAY,CAAC;QACxItE,UAAU,CAACkD,OAAO,EAAEuB,gBAAgB,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMC,IAAI,GAAGA,CAAA,KAAM;IACjB,MAAMhE,MAAM,GAAG9D,MAAM,CAAC8D,MAAM,CAAC3D,IAAI;IACjCH,MAAM,CAACyC,EAAE,CAACsF,MAAM,CAAC3G,UAAU,CAAC;;IAE5B;IACA,MAAM4G,WAAW,GAAGhI,MAAM,CAACyC,EAAE;IAC7B,IAAIqB,MAAM,CAACjD,+BAA+B,EAAE;MAC1CmC,oBAAoB,CAACgF,WAAW,EAAElE,MAAM,CAACjD,+BAA+B,CAAC;IAC3E;IACA,IAAIiD,MAAM,CAAClD,gBAAgB,EAAE;MAC3BwC,UAAU,CAAC4E,WAAW,EAAElE,MAAM,CAAClD,gBAAgB,CAAC;IAClD;IACA,IAAIkD,MAAM,CAAChD,aAAa,EAAE;MACxBgC,SAAS,CAACkF,WAAW,EAAElE,MAAM,CAAChD,aAAa,CAAC;IAC9C;;IAEA;IACA,MAAMmH,SAAS,GAAGjI,MAAM,CAACiI,SAAS;IAClC,MAAMpC,SAAS,GAAG/B,MAAM,CAAC7C,EAAE,IAAIgH,SAAS,CAACV,YAAY,CAAC,IAAI,CAAC,IAAI,kBAAkBxF,eAAe,CAAC,EAAE,CAAC,EAAE;IACtG,MAAMyB,IAAI,GAAGxD,MAAM,CAAC8D,MAAM,CAACoE,QAAQ,IAAIlI,MAAM,CAAC8D,MAAM,CAACoE,QAAQ,CAAC9H,OAAO,GAAG,KAAK,GAAG,QAAQ;IACxFkD,OAAO,CAAC2E,SAAS,EAAEpC,SAAS,CAAC;IAC7BtC,SAAS,CAAC0E,SAAS,EAAEzE,IAAI,CAAC;;IAE1B;IACAiE,UAAU,CAAC,CAAC;;IAEZ;IACA,IAAI;MACFlD,MAAM;MACND;IACF,CAAC,GAAGtE,MAAM,CAACqE,UAAU,GAAGrE,MAAM,CAACqE,UAAU,GAAG,CAAC,CAAC;IAC9CE,MAAM,GAAG1E,iBAAiB,CAAC0E,MAAM,CAAC;IAClCD,MAAM,GAAGzE,iBAAiB,CAACyE,MAAM,CAAC;IAClC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAC7B,OAAO,CAACD,EAAE,IAAImD,SAAS,CAACnD,EAAE,EAAEoD,SAAS,EAAE/B,MAAM,CAACvD,gBAAgB,CAAC,CAAC;IACzE;IACA,IAAI+D,MAAM,EAAE;MACVA,MAAM,CAAC5B,OAAO,CAACD,EAAE,IAAImD,SAAS,CAACnD,EAAE,EAAEoD,SAAS,EAAE/B,MAAM,CAACxD,gBAAgB,CAAC,CAAC;IACzE;;IAEA;IACA,IAAI+E,sBAAsB,CAAC,CAAC,EAAE;MAC5B,MAAM8C,YAAY,GAAGtI,iBAAiB,CAACG,MAAM,CAACiE,UAAU,CAACxB,EAAE,CAAC;MAC5D0F,YAAY,CAACzF,OAAO,CAACD,EAAE,IAAI;QACzBA,EAAE,CAACsD,gBAAgB,CAAC,SAAS,EAAEpC,iBAAiB,CAAC;MACnD,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMyE,QAAQ,GAAG9I,WAAW,CAAC,CAAC;IAC9B8I,QAAQ,CAACrC,gBAAgB,CAAC,kBAAkB,EAAEK,kBAAkB,CAAC;IACjEpG,MAAM,CAACyC,EAAE,CAACsD,gBAAgB,CAAC,OAAO,EAAEM,WAAW,EAAE,IAAI,CAAC;IACtDrG,MAAM,CAACyC,EAAE,CAACsD,gBAAgB,CAAC,OAAO,EAAEM,WAAW,EAAE,IAAI,CAAC;IACtDrG,MAAM,CAACyC,EAAE,CAACsD,gBAAgB,CAAC,aAAa,EAAEC,iBAAiB,EAAE,IAAI,CAAC;IAClEhG,MAAM,CAACyC,EAAE,CAACsD,gBAAgB,CAAC,WAAW,EAAEE,eAAe,EAAE,IAAI,CAAC;EAChE,CAAC;EACD,SAASoC,OAAOA,CAAA,EAAG;IACjB,IAAIjH,UAAU,EAAEA,UAAU,CAACkH,MAAM,CAAC,CAAC;IACnC,IAAI;MACF/D,MAAM;MACND;IACF,CAAC,GAAGtE,MAAM,CAACqE,UAAU,GAAGrE,MAAM,CAACqE,UAAU,GAAG,CAAC,CAAC;IAC9CE,MAAM,GAAG1E,iBAAiB,CAAC0E,MAAM,CAAC;IAClCD,MAAM,GAAGzE,iBAAiB,CAACyE,MAAM,CAAC;IAClC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAC7B,OAAO,CAACD,EAAE,IAAIA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC,CAAC;IAC5E;IACA,IAAIW,MAAM,EAAE;MACVA,MAAM,CAAC5B,OAAO,CAACD,EAAE,IAAIA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC,CAAC;IAC5E;;IAEA;IACA,IAAI0B,sBAAsB,CAAC,CAAC,EAAE;MAC5B,MAAM8C,YAAY,GAAGtI,iBAAiB,CAACG,MAAM,CAACiE,UAAU,CAACxB,EAAE,CAAC;MAC5D0F,YAAY,CAACzF,OAAO,CAACD,EAAE,IAAI;QACzBA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC;MACtD,CAAC,CAAC;IACJ;IACA,MAAMyE,QAAQ,GAAG9I,WAAW,CAAC,CAAC;IAC9B8I,QAAQ,CAACG,mBAAmB,CAAC,kBAAkB,EAAEnC,kBAAkB,CAAC;IACpE;IACA,IAAIpG,MAAM,CAACyC,EAAE,IAAI,OAAOzC,MAAM,CAACyC,EAAE,KAAK,QAAQ,EAAE;MAC9CzC,MAAM,CAACyC,EAAE,CAAC8F,mBAAmB,CAAC,OAAO,EAAElC,WAAW,EAAE,IAAI,CAAC;MACzDrG,MAAM,CAACyC,EAAE,CAAC8F,mBAAmB,CAAC,aAAa,EAAEvC,iBAAiB,EAAE,IAAI,CAAC;MACrEhG,MAAM,CAACyC,EAAE,CAAC8F,mBAAmB,CAAC,WAAW,EAAEtC,eAAe,EAAE,IAAI,CAAC;IACnE;EACF;EACA/F,EAAE,CAAC,YAAY,EAAE,MAAM;IACrBkB,UAAU,GAAG3B,aAAa,CAAC,MAAM,EAAEO,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACE,iBAAiB,CAAC;IACxEe,UAAU,CAACwB,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;IACjDxB,UAAU,CAACwB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAChD,CAAC,CAAC;EACF1C,EAAE,CAAC,WAAW,EAAE,MAAM;IACpB,IAAI,CAACF,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACC,OAAO,EAAE;IACjC0H,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;EACF5H,EAAE,CAAC,gEAAgE,EAAE,MAAM;IACzE,IAAI,CAACF,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACC,OAAO,EAAE;IACjCqH,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACFvH,EAAE,CAAC,uCAAuC,EAAE,MAAM;IAChD,IAAI,CAACF,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACC,OAAO,EAAE;IACjC6E,gBAAgB,CAAC,CAAC;EACpB,CAAC,CAAC;EACF/E,EAAE,CAAC,kBAAkB,EAAE,MAAM;IAC3B,IAAI,CAACF,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACC,OAAO,EAAE;IACjCmF,gBAAgB,CAAC,CAAC;EACpB,CAAC,CAAC;EACFrF,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAI,CAACF,MAAM,CAAC8D,MAAM,CAAC3D,IAAI,CAACC,OAAO,EAAE;IACjCiI,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;AACJ;AAEA,SAASvI,IAAI,IAAI0I,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}