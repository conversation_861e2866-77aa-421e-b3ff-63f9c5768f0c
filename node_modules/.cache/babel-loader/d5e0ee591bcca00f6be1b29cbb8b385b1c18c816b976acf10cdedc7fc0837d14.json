{"ast": null, "code": "/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && a instanceof Map && b instanceof Map) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done) if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done) if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n    if (hasSet && a instanceof Set && b instanceof Set) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done) if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (a[i] !== b[i]) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if ((error.message || '').match(/stack|recursion/i)) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["hasElementType", "Element", "hasMap", "Map", "hasSet", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "equal", "a", "b", "constructor", "length", "i", "keys", "Array", "isArray", "it", "size", "entries", "next", "done", "has", "value", "get", "RegExp", "source", "flags", "valueOf", "Object", "prototype", "toString", "hasOwnProperty", "call", "$$typeof", "module", "exports", "isEqual", "error", "message", "match", "console", "warn"], "sources": ["/var/www/html/gwm.tj/node_modules/react-fast-compare/index.js"], "sourcesContent": ["/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,cAAc,GAAG,OAAOC,OAAO,KAAK,WAAW;AACnD,IAAIC,MAAM,GAAG,OAAOC,GAAG,KAAK,UAAU;AACtC,IAAIC,MAAM,GAAG,OAAOC,GAAG,KAAK,UAAU;AACtC,IAAIC,cAAc,GAAG,OAAOC,WAAW,KAAK,UAAU,IAAI,CAAC,CAACA,WAAW,CAACC,MAAM;;AAE9E;;AAEA,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,IAAI;EAExB,IAAID,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IAC1D,IAAID,CAAC,CAACE,WAAW,KAAKD,CAAC,CAACC,WAAW,EAAE,OAAO,KAAK;IAEjD,IAAIC,MAAM,EAAEC,CAAC,EAAEC,IAAI;IACnB,IAAIC,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,EAAE;MACpBG,MAAM,GAAGH,CAAC,CAACG,MAAM;MACjB,IAAIA,MAAM,IAAIF,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;MACpC,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACL,KAAK,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACtC,OAAO,IAAI;IACb;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAII,EAAE;IACN,IAAIhB,MAAM,IAAKQ,CAAC,YAAYP,GAAI,IAAKQ,CAAC,YAAYR,GAAI,EAAE;MACtD,IAAIO,CAAC,CAACS,IAAI,KAAKR,CAAC,CAACQ,IAAI,EAAE,OAAO,KAAK;MACnCD,EAAE,GAAGR,CAAC,CAACU,OAAO,CAAC,CAAC;MAChB,OAAO,CAAC,CAACN,CAAC,GAAGI,EAAE,CAACG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAC1B,IAAI,CAACX,CAAC,CAACY,GAAG,CAACT,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACtCN,EAAE,GAAGR,CAAC,CAACU,OAAO,CAAC,CAAC;MAChB,OAAO,CAAC,CAACN,CAAC,GAAGI,EAAE,CAACG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAC1B,IAAI,CAACb,KAAK,CAACK,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,EAAEb,CAAC,CAACc,GAAG,CAACX,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACzD,OAAO,IAAI;IACb;IAEA,IAAIpB,MAAM,IAAKM,CAAC,YAAYL,GAAI,IAAKM,CAAC,YAAYN,GAAI,EAAE;MACtD,IAAIK,CAAC,CAACS,IAAI,KAAKR,CAAC,CAACQ,IAAI,EAAE,OAAO,KAAK;MACnCD,EAAE,GAAGR,CAAC,CAACU,OAAO,CAAC,CAAC;MAChB,OAAO,CAAC,CAACN,CAAC,GAAGI,EAAE,CAACG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAC1B,IAAI,CAACX,CAAC,CAACY,GAAG,CAACT,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACtC,OAAO,IAAI;IACb;IACA;;IAEA,IAAIlB,cAAc,IAAIC,WAAW,CAACC,MAAM,CAACE,CAAC,CAAC,IAAIH,WAAW,CAACC,MAAM,CAACG,CAAC,CAAC,EAAE;MACpEE,MAAM,GAAGH,CAAC,CAACG,MAAM;MACjB,IAAIA,MAAM,IAAIF,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;MACpC,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACxB,IAAIJ,CAAC,CAACI,CAAC,CAAC,KAAKH,CAAC,CAACG,CAAC,CAAC,EAAE,OAAO,KAAK;MACjC,OAAO,IAAI;IACb;IAEA,IAAIJ,CAAC,CAACE,WAAW,KAAKc,MAAM,EAAE,OAAOhB,CAAC,CAACiB,MAAM,KAAKhB,CAAC,CAACgB,MAAM,IAAIjB,CAAC,CAACkB,KAAK,KAAKjB,CAAC,CAACiB,KAAK;IACjF;IACA;IACA;IACA;IACA,IAAIlB,CAAC,CAACmB,OAAO,KAAKC,MAAM,CAACC,SAAS,CAACF,OAAO,IAAI,OAAOnB,CAAC,CAACmB,OAAO,KAAK,UAAU,IAAI,OAAOlB,CAAC,CAACkB,OAAO,KAAK,UAAU,EAAE,OAAOnB,CAAC,CAACmB,OAAO,CAAC,CAAC,KAAKlB,CAAC,CAACkB,OAAO,CAAC,CAAC;IACpJ,IAAInB,CAAC,CAACsB,QAAQ,KAAKF,MAAM,CAACC,SAAS,CAACC,QAAQ,IAAI,OAAOtB,CAAC,CAACsB,QAAQ,KAAK,UAAU,IAAI,OAAOrB,CAAC,CAACqB,QAAQ,KAAK,UAAU,EAAE,OAAOtB,CAAC,CAACsB,QAAQ,CAAC,CAAC,KAAKrB,CAAC,CAACqB,QAAQ,CAAC,CAAC;IAC1J;;IAEAjB,IAAI,GAAGe,MAAM,CAACf,IAAI,CAACL,CAAC,CAAC;IACrBG,MAAM,GAAGE,IAAI,CAACF,MAAM;IACpB,IAAIA,MAAM,KAAKiB,MAAM,CAACf,IAAI,CAACJ,CAAC,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;IAElD,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACgB,MAAM,CAACC,SAAS,CAACE,cAAc,CAACC,IAAI,CAACvB,CAAC,EAAEI,IAAI,CAACD,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IACrE;;IAEA;IACA;IACA,IAAId,cAAc,IAAIU,CAAC,YAAYT,OAAO,EAAE,OAAO,KAAK;;IAExD;IACA,KAAKa,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;MAC3B,IAAI,CAACC,IAAI,CAACD,CAAC,CAAC,KAAK,QAAQ,IAAIC,IAAI,CAACD,CAAC,CAAC,KAAK,KAAK,IAAIC,IAAI,CAACD,CAAC,CAAC,KAAK,KAAK,KAAKJ,CAAC,CAACyB,QAAQ,EAAE;QAClF;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;MACF;;MAEA;MACA,IAAI,CAAC1B,KAAK,CAACC,CAAC,CAACK,IAAI,CAACD,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IAClD;IACA;;IAEA;IACA,OAAO,IAAI;EACb;EAEA,OAAOJ,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;AAC3B;AACA;;AAEAyB,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAAC5B,CAAC,EAAEC,CAAC,EAAE;EACtC,IAAI;IACF,OAAOF,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC;EACpB,CAAC,CAAC,OAAO4B,KAAK,EAAE;IACd,IAAK,CAACA,KAAK,CAACC,OAAO,IAAI,EAAE,EAAEC,KAAK,CAAC,kBAAkB,CAAC,EAAG;MACrD;MACA;MACA;MACA;MACA;MACAC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;MAC9D,OAAO,KAAK;IACd;IACA;IACA,MAAMJ,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}