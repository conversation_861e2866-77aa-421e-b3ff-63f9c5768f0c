{"ast": null, "code": "import React from'react';// swiper\nimport{Swiper,SwiperSlide}from'swiper/react';// Import Swiper styles\nimport'swiper/css';import styles from'./filter.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterSwiper=_ref=>{let{activeModel,setActiveModel,cars}=_ref;const count=cars.length;return/*#__PURE__*/_jsx(\"div\",{className:styles.carsModelsList,children:/*#__PURE__*/_jsxs(Swiper,{breakpoints:{320:{slidesPerView:2,spaceBetween:0},580:{slidesPerView:2.5,spaceBetween:0},750:{slidesPerView:4.5,spaceBetween:0},860:{slidesPerView:4.5,spaceBetween:0},1160:{slidesPerView:5.5,spaceBetween:0},1460:{slidesPerView:6.5,spaceBetween:0}},spaceBetween:10,grabCursor:true,className:\"mySwiper\",children:[cars.map(car=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.btn,\" \").concat(activeModel===car.id?styles.active:''),onClick:()=>setActiveModel(car.id),children:car.title})},car.id)),/*#__PURE__*/_jsx(\"div\",{className:styles.activeBar})]})});};export default FilterSwiper;", "map": {"version": 3, "names": ["React", "Swiper", "SwiperSlide", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "FilterSwiper", "_ref", "activeModel", "setActiveModel", "cars", "count", "length", "className", "carsModelsList", "children", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "grabCursor", "map", "car", "concat", "btn", "id", "active", "onClick", "title", "activeBar"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/Models/FilterSlide/FilterSwiper.jsx"], "sourcesContent": ["import React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\n\nconst FilterSwiper = ({ activeModel, setActiveModel, cars }) => {\n  const count = cars.length;\n\n  return (\n    <div className={styles.carsModelsList}>\n      <Swiper\n        breakpoints={{\n          320: {\n            slidesPerView: 2,\n            spaceBetween: 0,\n          },\n          580: {\n            slidesPerView: 2.5,\n            spaceBetween: 0,\n          },\n          750: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          860: {\n            slidesPerView: 4.5,\n            spaceBetween: 0,\n          },\n          1160: {\n            slidesPerView: 5.5,\n            spaceBetween: 0,\n          },\n          1460: {\n            slidesPerView: 6.5,\n            spaceBetween: 0,\n          },\n        }}\n        spaceBetween={10}\n        grabCursor={true}\n        className=\"mySwiper\"\n      >\n        {cars.map((car) => (\n          <SwiperSlide key={car.id}>\n            <div\n              className={`${styles.btn} ${\n                activeModel === car.id ? styles.active : ''\n              }`}\n              onClick={() => setActiveModel(car.id)}\n            >\n              {car.title}\n            </div>\n          </SwiperSlide>\n        ))}\n        <div className={styles.activeBar}></div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default FilterSwiper;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB;AACA,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD;AACA,MAAO,YAAY,CACnB,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,WAAW,CAAEC,cAAc,CAAEC,IAAK,CAAC,CAAAH,IAAA,CACzD,KAAM,CAAAI,KAAK,CAAGD,IAAI,CAACE,MAAM,CAEzB,mBACET,IAAA,QAAKU,SAAS,CAAEZ,MAAM,CAACa,cAAe,CAAAC,QAAA,cACpCV,KAAA,CAACN,MAAM,EACLiB,WAAW,CAAE,CACX,GAAG,CAAE,CACHC,aAAa,CAAE,CAAC,CAChBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,GAAG,CAAE,CACHD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CAAC,CACD,IAAI,CAAE,CACJD,aAAa,CAAE,GAAG,CAClBC,YAAY,CAAE,CAChB,CACF,CAAE,CACFA,YAAY,CAAE,EAAG,CACjBC,UAAU,CAAE,IAAK,CACjBN,SAAS,CAAC,UAAU,CAAAE,QAAA,EAEnBL,IAAI,CAACU,GAAG,CAAEC,GAAG,eACZlB,IAAA,CAACH,WAAW,EAAAe,QAAA,cACVZ,IAAA,QACEU,SAAS,IAAAS,MAAA,CAAKrB,MAAM,CAACsB,GAAG,MAAAD,MAAA,CACtBd,WAAW,GAAKa,GAAG,CAACG,EAAE,CAAGvB,MAAM,CAACwB,MAAM,CAAG,EAAE,CAC1C,CACHC,OAAO,CAAEA,CAAA,GAAMjB,cAAc,CAACY,GAAG,CAACG,EAAE,CAAE,CAAAT,QAAA,CAErCM,GAAG,CAACM,KAAK,CACP,CAAC,EARUN,GAAG,CAACG,EAST,CACd,CAAC,cACFrB,IAAA,QAAKU,SAAS,CAAEZ,MAAM,CAAC2B,SAAU,CAAM,CAAC,EAClC,CAAC,CACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}