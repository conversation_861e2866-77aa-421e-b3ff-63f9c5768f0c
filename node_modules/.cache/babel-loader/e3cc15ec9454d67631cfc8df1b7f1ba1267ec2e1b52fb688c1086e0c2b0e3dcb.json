{"ast": null, "code": "import { attachSpring, isMotionValue } from 'motion-dom';\nimport { useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\nfunction useSpring(source, options = {}) {\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  const getFromSource = () => isMotionValue(source) ? source.get() : source;\n  // isStatic will never change, allowing early hooks return\n  if (isStatic) {\n    return useTransform(getFromSource);\n  }\n  const value = useMotionValue(getFromSource());\n  useInsertionEffect(() => {\n    return attachSpring(value, source, options);\n  }, [value, JSON.stringify(options)]);\n  return value;\n}\nexport { useSpring };", "map": {"version": 3, "names": ["attachSpring", "isMotionValue", "useContext", "useInsertionEffect", "MotionConfigContext", "useMotionValue", "useTransform", "useSpring", "source", "options", "isStatic", "getFromSource", "get", "value", "JSON", "stringify"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-spring.mjs"], "sourcesContent": ["import { attachSpring, isMotionValue } from 'motion-dom';\nimport { useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\n\nfunction useSpring(source, options = {}) {\n    const { isStatic } = useContext(MotionConfigContext);\n    const getFromSource = () => (isMotionValue(source) ? source.get() : source);\n    // isStatic will never change, allowing early hooks return\n    if (isStatic) {\n        return useTransform(getFromSource);\n    }\n    const value = useMotionValue(getFromSource());\n    useInsertionEffect(() => {\n        return attachSpring(value, source, options);\n    }, [value, JSON.stringify(options)]);\n    return value;\n}\n\nexport { useSpring };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,YAAY;AACxD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,OAAO;AACtD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,SAASA,CAACC,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACrC,MAAM;IAAEC;EAAS,CAAC,GAAGR,UAAU,CAACE,mBAAmB,CAAC;EACpD,MAAMO,aAAa,GAAGA,CAAA,KAAOV,aAAa,CAACO,MAAM,CAAC,GAAGA,MAAM,CAACI,GAAG,CAAC,CAAC,GAAGJ,MAAO;EAC3E;EACA,IAAIE,QAAQ,EAAE;IACV,OAAOJ,YAAY,CAACK,aAAa,CAAC;EACtC;EACA,MAAME,KAAK,GAAGR,cAAc,CAACM,aAAa,CAAC,CAAC,CAAC;EAC7CR,kBAAkB,CAAC,MAAM;IACrB,OAAOH,YAAY,CAACa,KAAK,EAAEL,MAAM,EAAEC,OAAO,CAAC;EAC/C,CAAC,EAAE,CAACI,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACN,OAAO,CAAC,CAAC,CAAC;EACpC,OAAOI,KAAK;AAChB;AAEA,SAASN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}