{"ast": null, "code": "\"use client\";\n\nexport { AnimatePresence } from './components/AnimatePresence/index.mjs';\nexport { LayoutGroup } from './components/LayoutGroup/index.mjs';\nexport { LazyMotion } from './components/LazyMotion/index.mjs';\nexport { MotionConfig } from './components/MotionConfig/index.mjs';\nexport { m } from './render/components/m/proxy.mjs';\nexport { motion } from './render/components/motion/proxy.mjs';\nexport { addPointerEvent } from './events/add-pointer-event.mjs';\nexport { addPointerInfo } from './events/event-info.mjs';\nexport { animations } from './motion/features/animations.mjs';\nexport { makeUseVisualState } from './motion/utils/use-visual-state.mjs';\nexport { calcLength } from './projection/geometry/delta-calc.mjs';\nexport { createBox } from './projection/geometry/models.mjs';\nexport { filterProps } from './render/dom/utils/filter-props.mjs';\nexport { isBrowser } from './utils/is-browser.mjs';\nexport { useForceUpdate } from './utils/use-force-update.mjs';\nexport { useIsomorphicLayoutEffect } from './utils/use-isomorphic-effect.mjs';\nexport { useUnmountEffect } from './utils/use-unmount-effect.mjs';\nexport { domAnimation } from './render/dom/features-animation.mjs';\nexport { domMax } from './render/dom/features-max.mjs';\nexport { domMin } from './render/dom/features-min.mjs';\nexport { useMotionValueEvent } from './utils/use-motion-value-event.mjs';\nexport { useElementScroll } from './value/scroll/use-element-scroll.mjs';\nexport { useViewportScroll } from './value/scroll/use-viewport-scroll.mjs';\nexport { useMotionTemplate } from './value/use-motion-template.mjs';\nexport { useMotionValue } from './value/use-motion-value.mjs';\nexport { useScroll } from './value/use-scroll.mjs';\nexport { useSpring } from './value/use-spring.mjs';\nexport { useTime } from './value/use-time.mjs';\nexport { useTransform } from './value/use-transform.mjs';\nexport { useVelocity } from './value/use-velocity.mjs';\nexport { useWillChange } from './value/use-will-change/index.mjs';\nexport { WillChangeMotionValue } from './value/use-will-change/WillChangeMotionValue.mjs';\nexport { resolveMotionValue } from './value/utils/resolve-motion-value.mjs';\nexport { useReducedMotion } from './utils/reduced-motion/use-reduced-motion.mjs';\nexport { useReducedMotionConfig } from './utils/reduced-motion/use-reduced-motion-config.mjs';\nexport * from 'motion-utils';\nexport { MotionGlobalConfig } from 'motion-utils';\nexport { animationControls } from './animation/hooks/animation-controls.mjs';\nexport { useAnimate } from './animation/hooks/use-animate.mjs';\nexport { useAnimateMini } from './animation/hooks/use-animate-style.mjs';\nexport { useAnimation, useAnimationControls } from './animation/hooks/use-animation.mjs';\nexport { animateVisualElement } from './animation/interfaces/visual-element.mjs';\nexport { useIsPresent, usePresence } from './components/AnimatePresence/use-presence.mjs';\nexport { usePresenceData } from './components/AnimatePresence/use-presence-data.mjs';\nexport { useDomEvent } from './events/use-dom-event.mjs';\nexport { DragControls, useDragControls } from './gestures/drag/use-drag-controls.mjs';\nexport { createRendererMotionComponent } from './motion/index.mjs';\nexport { isMotionComponent } from './motion/utils/is-motion-component.mjs';\nexport { unwrapMotionComponent } from './motion/utils/unwrap-motion-component.mjs';\nexport { isValidMotionProp } from './motion/utils/valid-prop.mjs';\nexport { addScaleCorrector } from './projection/styles/scale-correction.mjs';\nexport { useInstantLayoutTransition } from './projection/use-instant-layout-transition.mjs';\nexport { useResetProjection } from './projection/use-reset-projection.mjs';\nexport { buildTransform } from './render/html/utils/build-transform.mjs';\nexport { visualElementStore } from './render/store.mjs';\nexport { VisualElement } from './render/VisualElement.mjs';\nexport { useAnimationFrame } from './utils/use-animation-frame.mjs';\nexport { useCycle } from './utils/use-cycle.mjs';\nexport { useInView } from './utils/use-in-view.mjs';\nexport { disableInstantTransitions, useInstantTransition } from './utils/use-instant-transition.mjs';\nexport { optimizedAppearDataAttribute } from './animation/optimized-appear/data-id.mjs';\nexport { startOptimizedAppearAnimation } from './animation/optimized-appear/start.mjs';\nexport { LayoutGroupContext } from './context/LayoutGroupContext.mjs';\nexport { MotionConfigContext } from './context/MotionConfigContext.mjs';\nexport { MotionContext } from './context/MotionContext/index.mjs';\nexport { PresenceContext } from './context/PresenceContext.mjs';\nexport { SwitchLayoutGroupContext } from './context/SwitchLayoutGroupContext.mjs';\nexport { FlatTree } from './render/utils/flat-tree.mjs';\nexport { useAnimatedState as useDeprecatedAnimatedState } from './animation/hooks/use-animated-state.mjs';\nexport { AnimateSharedLayout } from './components/AnimateSharedLayout.mjs';\nexport { DeprecatedLayoutGroupContext } from './context/DeprecatedLayoutGroupContext.mjs';\nexport { useInvertedScale as useDeprecatedInvertedScale } from './value/use-inverted-scale.mjs';\nexport { delay } from './utils/delay.mjs';\nimport * as namespace from './components/Reorder/namespace.mjs';\nexport { namespace as Reorder };\nexport { animate, createScopedAnimate } from './animation/animate/index.mjs';\nexport { animateMini } from './animation/animators/waapi/animate-style.mjs';\nexport { scroll } from './render/dom/scroll/index.mjs';\nexport { scrollInfo } from './render/dom/scroll/track.mjs';\nexport { inView } from './render/dom/viewport/index.mjs';\nexport { stagger } from './animation/utils/stagger.mjs';\nexport { distance, distance2D } from './utils/distance.mjs';\nexport * from 'motion-dom';", "map": {"version": 3, "names": ["AnimatePresence", "LayoutGroup", "LazyMotion", "MotionConfig", "m", "motion", "addPointerEvent", "addPointerInfo", "animations", "makeUseVisualState", "calcLength", "createBox", "filterProps", "<PERSON><PERSON><PERSON><PERSON>", "useForceUpdate", "useIsomorphicLayoutEffect", "useUnmountEffect", "domAnimation", "domMax", "dom<PERSON>in", "useMotionValueEvent", "useElementScroll", "useViewportScroll", "useMotionTemplate", "useMotionValue", "useScroll", "useSpring", "useTime", "useTransform", "useVelocity", "useWillChange", "WillChangeMotionValue", "resolveMotionValue", "useReducedMotion", "useReducedMotionConfig", "MotionGlobalConfig", "animationControls", "useAnimate", "useAnimateMini", "useAnimation", "useAnimationControls", "animateVisualElement", "useIsPresent", "usePresence", "usePresenceData", "useDomEvent", "DragControls", "useDragControls", "createRendererMotionComponent", "isMotionComponent", "unwrapMotionComponent", "isValidMotionProp", "addScaleCorrector", "useInstantLayoutTransition", "useResetProjection", "buildTransform", "visualElementStore", "VisualElement", "useAnimationFrame", "useCycle", "useInView", "disableInstantTransitions", "useInstantTransition", "optimizedAppearDataAttribute", "startOptimizedAppearAnimation", "LayoutGroupContext", "MotionConfigContext", "MotionContext", "PresenceContext", "SwitchLayoutGroupContext", "FlatTree", "useAnimatedState", "useDeprecatedAnimatedState", "AnimateSharedLayout", "DeprecatedLayoutGroupContext", "useInvertedScale", "useDeprecatedInvertedScale", "delay", "namespace", "Reorder", "animate", "createScopedAnimate", "animateMini", "scroll", "scrollInfo", "inView", "stagger", "distance", "distance2D"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/index.mjs"], "sourcesContent": ["\"use client\";\nexport { AnimatePresence } from './components/AnimatePresence/index.mjs';\nexport { LayoutGroup } from './components/LayoutGroup/index.mjs';\nexport { LazyMotion } from './components/LazyMotion/index.mjs';\nexport { MotionConfig } from './components/MotionConfig/index.mjs';\nexport { m } from './render/components/m/proxy.mjs';\nexport { motion } from './render/components/motion/proxy.mjs';\nexport { addPointerEvent } from './events/add-pointer-event.mjs';\nexport { addPointerInfo } from './events/event-info.mjs';\nexport { animations } from './motion/features/animations.mjs';\nexport { makeUseVisualState } from './motion/utils/use-visual-state.mjs';\nexport { calcLength } from './projection/geometry/delta-calc.mjs';\nexport { createBox } from './projection/geometry/models.mjs';\nexport { filterProps } from './render/dom/utils/filter-props.mjs';\nexport { isBrowser } from './utils/is-browser.mjs';\nexport { useForceUpdate } from './utils/use-force-update.mjs';\nexport { useIsomorphicLayoutEffect } from './utils/use-isomorphic-effect.mjs';\nexport { useUnmountEffect } from './utils/use-unmount-effect.mjs';\nexport { domAnimation } from './render/dom/features-animation.mjs';\nexport { domMax } from './render/dom/features-max.mjs';\nexport { domMin } from './render/dom/features-min.mjs';\nexport { useMotionValueEvent } from './utils/use-motion-value-event.mjs';\nexport { useElementScroll } from './value/scroll/use-element-scroll.mjs';\nexport { useViewportScroll } from './value/scroll/use-viewport-scroll.mjs';\nexport { useMotionTemplate } from './value/use-motion-template.mjs';\nexport { useMotionValue } from './value/use-motion-value.mjs';\nexport { useScroll } from './value/use-scroll.mjs';\nexport { useSpring } from './value/use-spring.mjs';\nexport { useTime } from './value/use-time.mjs';\nexport { useTransform } from './value/use-transform.mjs';\nexport { useVelocity } from './value/use-velocity.mjs';\nexport { useWillChange } from './value/use-will-change/index.mjs';\nexport { WillChangeMotionValue } from './value/use-will-change/WillChangeMotionValue.mjs';\nexport { resolveMotionValue } from './value/utils/resolve-motion-value.mjs';\nexport { useReducedMotion } from './utils/reduced-motion/use-reduced-motion.mjs';\nexport { useReducedMotionConfig } from './utils/reduced-motion/use-reduced-motion-config.mjs';\nexport * from 'motion-utils';\nexport { MotionGlobalConfig } from 'motion-utils';\nexport { animationControls } from './animation/hooks/animation-controls.mjs';\nexport { useAnimate } from './animation/hooks/use-animate.mjs';\nexport { useAnimateMini } from './animation/hooks/use-animate-style.mjs';\nexport { useAnimation, useAnimationControls } from './animation/hooks/use-animation.mjs';\nexport { animateVisualElement } from './animation/interfaces/visual-element.mjs';\nexport { useIsPresent, usePresence } from './components/AnimatePresence/use-presence.mjs';\nexport { usePresenceData } from './components/AnimatePresence/use-presence-data.mjs';\nexport { useDomEvent } from './events/use-dom-event.mjs';\nexport { DragControls, useDragControls } from './gestures/drag/use-drag-controls.mjs';\nexport { createRendererMotionComponent } from './motion/index.mjs';\nexport { isMotionComponent } from './motion/utils/is-motion-component.mjs';\nexport { unwrapMotionComponent } from './motion/utils/unwrap-motion-component.mjs';\nexport { isValidMotionProp } from './motion/utils/valid-prop.mjs';\nexport { addScaleCorrector } from './projection/styles/scale-correction.mjs';\nexport { useInstantLayoutTransition } from './projection/use-instant-layout-transition.mjs';\nexport { useResetProjection } from './projection/use-reset-projection.mjs';\nexport { buildTransform } from './render/html/utils/build-transform.mjs';\nexport { visualElementStore } from './render/store.mjs';\nexport { VisualElement } from './render/VisualElement.mjs';\nexport { useAnimationFrame } from './utils/use-animation-frame.mjs';\nexport { useCycle } from './utils/use-cycle.mjs';\nexport { useInView } from './utils/use-in-view.mjs';\nexport { disableInstantTransitions, useInstantTransition } from './utils/use-instant-transition.mjs';\nexport { optimizedAppearDataAttribute } from './animation/optimized-appear/data-id.mjs';\nexport { startOptimizedAppearAnimation } from './animation/optimized-appear/start.mjs';\nexport { LayoutGroupContext } from './context/LayoutGroupContext.mjs';\nexport { MotionConfigContext } from './context/MotionConfigContext.mjs';\nexport { MotionContext } from './context/MotionContext/index.mjs';\nexport { PresenceContext } from './context/PresenceContext.mjs';\nexport { SwitchLayoutGroupContext } from './context/SwitchLayoutGroupContext.mjs';\nexport { FlatTree } from './render/utils/flat-tree.mjs';\nexport { useAnimatedState as useDeprecatedAnimatedState } from './animation/hooks/use-animated-state.mjs';\nexport { AnimateSharedLayout } from './components/AnimateSharedLayout.mjs';\nexport { DeprecatedLayoutGroupContext } from './context/DeprecatedLayoutGroupContext.mjs';\nexport { useInvertedScale as useDeprecatedInvertedScale } from './value/use-inverted-scale.mjs';\nexport { delay } from './utils/delay.mjs';\nimport * as namespace from './components/Reorder/namespace.mjs';\nexport { namespace as Reorder };\nexport { animate, createScopedAnimate } from './animation/animate/index.mjs';\nexport { animateMini } from './animation/animators/waapi/animate-style.mjs';\nexport { scroll } from './render/dom/scroll/index.mjs';\nexport { scrollInfo } from './render/dom/scroll/track.mjs';\nexport { inView } from './render/dom/viewport/index.mjs';\nexport { stagger } from './animation/utils/stagger.mjs';\nexport { distance, distance2D } from './utils/distance.mjs';\nexport * from 'motion-dom';\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,wCAAwC;AACxE,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,CAAC,QAAQ,iCAAiC;AACnD,SAASC,MAAM,QAAQ,sCAAsC;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,UAAU,QAAQ,sCAAsC;AACjE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,WAAW,QAAQ,qCAAqC;AACjE,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,qBAAqB,QAAQ,mDAAmD;AACzF,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,cAAc,cAAc;AAC5B,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,qCAAqC;AACxF,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,YAAY,EAAEC,WAAW,QAAQ,+CAA+C;AACzF,SAASC,eAAe,QAAQ,oDAAoD;AACpF,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,YAAY,EAAEC,eAAe,QAAQ,uCAAuC;AACrF,SAASC,6BAA6B,QAAQ,oBAAoB;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,0BAA0B,QAAQ,gDAAgD;AAC3F,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,yBAAyB,EAAEC,oBAAoB,QAAQ,oCAAoC;AACpG,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,6BAA6B,QAAQ,wCAAwC;AACtF,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,wBAAwB,QAAQ,wCAAwC;AACjF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,gBAAgB,IAAIC,0BAA0B,QAAQ,0CAA0C;AACzG,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,4BAA4B,QAAQ,4CAA4C;AACzF,SAASC,gBAAgB,IAAIC,0BAA0B,QAAQ,gCAAgC;AAC/F,SAASC,KAAK,QAAQ,mBAAmB;AACzC,OAAO,KAAKC,SAAS,MAAM,oCAAoC;AAC/D,SAASA,SAAS,IAAIC,OAAO;AAC7B,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,+BAA+B;AAC5E,SAASC,WAAW,QAAQ,+CAA+C;AAC3E,SAASC,MAAM,QAAQ,+BAA+B;AACtD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,MAAM,QAAQ,iCAAiC;AACxD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,QAAQ,EAAEC,UAAU,QAAQ,sBAAsB;AAC3D,cAAc,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}