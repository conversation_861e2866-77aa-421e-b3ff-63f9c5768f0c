{"ast": null, "code": "import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\nclass MotionValueState {\n  constructor() {\n    this.latest = {};\n    this.values = new Map();\n  }\n  set(name, value, render, computed) {\n    const existingValue = this.values.get(name);\n    if (existingValue) {\n      existingValue.onRemove();\n    }\n    const onChange = () => {\n      this.latest[name] = getValueAsType(value.get(), numberValueTypes[name]);\n      render && frame.render(render);\n    };\n    onChange();\n    const cancelOnChange = value.on(\"change\", onChange);\n    computed && value.addDependent(computed);\n    const remove = () => {\n      cancelOnChange();\n      render && cancelFrame(render);\n      this.values.delete(name);\n      computed && value.removeDependent(computed);\n    };\n    this.values.set(name, {\n      value,\n      onRemove: remove\n    });\n    return remove;\n  }\n  get(name) {\n    return this.values.get(name)?.value;\n  }\n  destroy() {\n    for (const value of this.values.values()) {\n      value.onRemove();\n    }\n  }\n}\nexport { MotionValueState };", "map": {"version": 3, "names": ["frame", "cancelFrame", "numberValueTypes", "getValueAsType", "MotionValueState", "constructor", "latest", "values", "Map", "set", "name", "value", "render", "computed", "existingValue", "get", "onRemove", "onChange", "cancelOnChange", "on", "addDependent", "remove", "delete", "removeDependent", "destroy"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/effects/MotionValueState.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\n\nclass MotionValueState {\n    constructor() {\n        this.latest = {};\n        this.values = new Map();\n    }\n    set(name, value, render, computed) {\n        const existingValue = this.values.get(name);\n        if (existingValue) {\n            existingValue.onRemove();\n        }\n        const onChange = () => {\n            this.latest[name] = getValueAsType(value.get(), numberValueTypes[name]);\n            render && frame.render(render);\n        };\n        onChange();\n        const cancelOnChange = value.on(\"change\", onChange);\n        computed && value.addDependent(computed);\n        const remove = () => {\n            cancelOnChange();\n            render && cancelFrame(render);\n            this.values.delete(name);\n            computed && value.removeDependent(computed);\n        };\n        this.values.set(name, { value, onRemove: remove });\n        return remove;\n    }\n    get(name) {\n        return this.values.get(name)?.value;\n    }\n    destroy() {\n        for (const value of this.values.values()) {\n            value.onRemove();\n        }\n    }\n}\n\nexport { MotionValueState };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,cAAc,QAAQ,sCAAsC;AAErE,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;EACAC,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACP,MAAM,CAACQ,GAAG,CAACL,IAAI,CAAC;IAC3C,IAAII,aAAa,EAAE;MACfA,aAAa,CAACE,QAAQ,CAAC,CAAC;IAC5B;IACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnB,IAAI,CAACX,MAAM,CAACI,IAAI,CAAC,GAAGP,cAAc,CAACQ,KAAK,CAACI,GAAG,CAAC,CAAC,EAAEb,gBAAgB,CAACQ,IAAI,CAAC,CAAC;MACvEE,MAAM,IAAIZ,KAAK,CAACY,MAAM,CAACA,MAAM,CAAC;IAClC,CAAC;IACDK,QAAQ,CAAC,CAAC;IACV,MAAMC,cAAc,GAAGP,KAAK,CAACQ,EAAE,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IACnDJ,QAAQ,IAAIF,KAAK,CAACS,YAAY,CAACP,QAAQ,CAAC;IACxC,MAAMQ,MAAM,GAAGA,CAAA,KAAM;MACjBH,cAAc,CAAC,CAAC;MAChBN,MAAM,IAAIX,WAAW,CAACW,MAAM,CAAC;MAC7B,IAAI,CAACL,MAAM,CAACe,MAAM,CAACZ,IAAI,CAAC;MACxBG,QAAQ,IAAIF,KAAK,CAACY,eAAe,CAACV,QAAQ,CAAC;IAC/C,CAAC;IACD,IAAI,CAACN,MAAM,CAACE,GAAG,CAACC,IAAI,EAAE;MAAEC,KAAK;MAAEK,QAAQ,EAAEK;IAAO,CAAC,CAAC;IAClD,OAAOA,MAAM;EACjB;EACAN,GAAGA,CAACL,IAAI,EAAE;IACN,OAAO,IAAI,CAACH,MAAM,CAACQ,GAAG,CAACL,IAAI,CAAC,EAAEC,KAAK;EACvC;EACAa,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMb,KAAK,IAAI,IAAI,CAACJ,MAAM,CAACA,MAAM,CAAC,CAAC,EAAE;MACtCI,KAAK,CAACK,QAAQ,CAAC,CAAC;IACpB;EACJ;AACJ;AAEA,SAASZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}