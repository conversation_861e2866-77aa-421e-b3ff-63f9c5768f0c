{"ast": null, "code": "import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an HTML element in a way\n * that works across iframes\n */\nfunction isHTMLElement(element) {\n  return isObject(element) && \"offsetHeight\" in element;\n}\nexport { isHTMLElement };", "map": {"version": 3, "names": ["isObject", "isHTMLElement", "element"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/utils/is-html-element.mjs"], "sourcesContent": ["import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an HTML element in a way\n * that works across iframes\n */\nfunction isHTMLElement(element) {\n    return isObject(element) && \"offsetHeight\" in element;\n}\n\nexport { isHTMLElement };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;;AAEvC;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC5B,OAAOF,QAAQ,CAACE,OAAO,CAAC,IAAI,cAAc,IAAIA,OAAO;AACzD;AAEA,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}