{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import styles from'./toolBar.module.css';// icons\nimport icon_1 from'../../asset/imgs/icons/icon1-black.svg';import icon_2 from'../../asset/imgs/icons/icon3-black.svg';import icon_3 from'../../asset/imgs/icons/icon2-black.svg';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const btnListData=[{id:1,title:'Тест-драйв',icon:icon_1,url:'/book-a-test-drive'},{id:2,title:'Дилерский центр',icon:icon_2,url:'/contact'},{id:3,title:'Связаться с нами',icon:icon_3,url:'/contact'}];const ToolBar=()=>{return/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.content,children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u0427\\u0435\\u043C \\u043C\\u044B \\u043C\\u043E\\u0436\\u0435\\u043C \\u0432\\u0430\\u043C \\u043F\\u043E\\u043C\\u043E\\u0447\\u044C?\"}),/*#__PURE__*/_jsx(\"div\",{className:styles.btnsList,children:btnListData.map(item=>/*#__PURE__*/_jsx(Link,{to:item.url,className:styles.btnItem,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.item,children:[/*#__PURE__*/_jsx(\"img\",{src:item.icon,alt:item.title,loading:\"lazy\"}),/*#__PURE__*/_jsx(\"span\",{children:item.title})]})},item.id))})]})})});};export default ToolBar;", "map": {"version": 3, "names": ["React", "Link", "styles", "icon_1", "icon_2", "icon_3", "jsx", "_jsx", "jsxs", "_jsxs", "btnListData", "id", "title", "icon", "url", "<PERSON><PERSON><PERSON><PERSON>", "className", "section", "children", "content", "btnsList", "map", "item", "to", "btnItem", "src", "alt", "loading"], "sources": ["/var/www/html/gwm.tj/src/components/ToolBar/ToolBar.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styles from './toolBar.module.css';\n// icons\nimport icon_1 from '../../asset/imgs/icons/icon1-black.svg';\nimport icon_2 from '../../asset/imgs/icons/icon3-black.svg';\nimport icon_3 from '../../asset/imgs/icons/icon2-black.svg';\n\nconst btnListData = [\n  {\n    id: 1,\n    title: 'Тест-драйв',\n    icon: icon_1,\n    url: '/book-a-test-drive',\n  },\n\n  {\n    id: 2,\n    title: 'Дилерский центр',\n    icon: icon_2,\n    url: '/contact',\n  },\n  {\n    id: 3,\n    title: 'Связаться с нами',\n    icon: icon_3,\n    url: '/contact',\n  },\n];\n\nconst ToolBar = () => {\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.content}>\n          <h3>Чем мы можем вам помочь?</h3>\n          {/* Кнопки */}\n          <div className={styles.btnsList}>\n            {btnListData.map((item) => (\n              <Link to={item.url} key={item.id} className={styles.btnItem}>\n                <div className={styles.item}>\n                  <img src={item.icon} alt={item.title} loading=\"lazy\" />\n                  <span>{item.title}</span>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ToolBar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC;AACA,MAAO,CAAAC,MAAM,KAAM,wCAAwC,CAC3D,MAAO,CAAAC,MAAM,KAAM,wCAAwC,CAC3D,MAAO,CAAAC,MAAM,KAAM,wCAAwC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,WAAW,CAAG,CAClB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBC,IAAI,CAAEV,MAAM,CACZW,GAAG,CAAE,oBACP,CAAC,CAED,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBC,IAAI,CAAET,MAAM,CACZU,GAAG,CAAE,UACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,kBAAkB,CACzBC,IAAI,CAAER,MAAM,CACZS,GAAG,CAAE,UACP,CAAC,CACF,CAED,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,mBACER,IAAA,YAASS,SAAS,CAAEd,MAAM,CAACe,OAAQ,CAAAC,QAAA,cACjCX,IAAA,QAAKS,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBT,KAAA,QAAKO,SAAS,CAAEd,MAAM,CAACiB,OAAQ,CAAAD,QAAA,eAC7BX,IAAA,OAAAW,QAAA,CAAI,yHAAwB,CAAI,CAAC,cAEjCX,IAAA,QAAKS,SAAS,CAAEd,MAAM,CAACkB,QAAS,CAAAF,QAAA,CAC7BR,WAAW,CAACW,GAAG,CAAEC,IAAI,eACpBf,IAAA,CAACN,IAAI,EAACsB,EAAE,CAAED,IAAI,CAACR,GAAI,CAAeE,SAAS,CAAEd,MAAM,CAACsB,OAAQ,CAAAN,QAAA,cAC1DT,KAAA,QAAKO,SAAS,CAAEd,MAAM,CAACoB,IAAK,CAAAJ,QAAA,eAC1BX,IAAA,QAAKkB,GAAG,CAAEH,IAAI,CAACT,IAAK,CAACa,GAAG,CAAEJ,IAAI,CAACV,KAAM,CAACe,OAAO,CAAC,MAAM,CAAE,CAAC,cACvDpB,IAAA,SAAAW,QAAA,CAAOI,IAAI,CAACV,KAAK,CAAO,CAAC,EACtB,CAAC,EAJiBU,IAAI,CAACX,EAKxB,CACP,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}