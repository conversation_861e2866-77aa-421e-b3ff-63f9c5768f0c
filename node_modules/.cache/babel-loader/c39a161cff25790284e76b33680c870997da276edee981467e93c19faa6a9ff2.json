{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { s as setCSSProperty, e as elementChildren, c as createElement } from '../shared/utils.mjs';\nfunction Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n    if (params.cache) {\n      swiper.virtual.cache[index] = slideEl;\n    }\n    return slideEl;\n  }\n  function update(force, beforeInit, forceActiveIndex) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop,\n      initialSlide\n    } = swiper.params;\n    if (beforeInit && !isLoop && initialSlide > 0) {\n      return;\n    }\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = typeof forceActiveIndex === 'undefined' ? swiper.activeIndex || 0 : forceActiveIndex;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = \"\".concat(offset - Math.abs(swiper.cssOverflowAdjustment()), \"px\");\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slides.filter(el => el.matches(\".\".concat(swiper.params.slideClass, \", swiper-slide\"))).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slides.filter(el => el.matches(\".\".concat(swiper.params.slideClass, \"[data-swiper-slide-index=\\\"\").concat(slideIndex, \"\\\"], swiper-slide[data-swiper-slide-index=\\\"\").concat(slideIndex, \"\\\"]\"))).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = \"\".concat(offset - Math.abs(swiper.cssOverflowAdjustment()), \"px\");\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n          // shift cache indexes\n          Object.keys(swiper.virtual.cache).forEach(key => {\n            if (key > slidesIndexes) {\n              swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n              swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n              delete swiper.virtual.cache[key];\n            }\n          });\n        }\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n        // shift cache indexes\n        Object.keys(swiper.virtual.cache).forEach(key => {\n          if (key > slidesIndexes) {\n            swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n            swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n            delete swiper.virtual.cache[key];\n          }\n        });\n      }\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(\".\".concat(swiper.params.slideClass, \", swiper-slide\")));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(\"\".concat(swiper.params.containerModifierClass, \"virtual\"));\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    update(false, true);\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', \"\".concat(swiper.virtualSize, \"px\"));\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}\nexport { Virtual as default };", "map": {"version": 3, "names": ["g", "getDocument", "s", "setCSSProperty", "e", "elementChildren", "c", "createElement", "Virtual", "_ref", "swiper", "extendParams", "on", "emit", "virtual", "enabled", "slides", "cache", "renderSlide", "renderExternal", "renderExternalUpdate", "addSlidesBefore", "addSlidesAfter", "cssModeTimeout", "document", "from", "undefined", "to", "offset", "slidesGrid", "tempDOM", "slide", "index", "params", "slideEl", "call", "innerHTML", "children", "isElement", "slideClass", "setAttribute", "update", "force", "beforeInit", "forceActiveIndex", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "centeredSlides", "loop", "isLoop", "initialSlide", "previousFrom", "previousTo", "previousSlidesGrid", "previousOffset", "cssMode", "updateActiveIndex", "activeIndex", "offsetProp", "rtlTranslate", "isHorizontal", "slidesAfter", "slidesBefore", "Math", "floor", "max", "min", "length", "Object", "assign", "onRendered", "updateSlides", "updateProgress", "updateSlidesClasses", "for<PERSON>ach", "style", "concat", "abs", "cssOverflowAdjustment", "getSlides", "slidesToRender", "i", "push", "prependIndexes", "appendIndexes", "getSlideIndex", "slideIndex", "filter", "el", "matches", "remove", "loopFrom", "loopTo", "slidesEl", "append", "prepend", "sort", "a", "b", "appendSlide", "prependSlide", "newActiveIndex", "numberOfNewSlides", "Array", "isArray", "unshift", "newCache", "keys", "cachedIndex", "cachedEl", "cachedElIndex", "getAttribute", "parseInt", "slideTo", "removeSlide", "slidesIndexes", "key", "splice", "removeAllSlides", "domSlidesAssigned", "passedParams", "classNames", "containerModifierClass", "watchSlidesProgress", "originalParams", "_immediateVirtual", "clearTimeout", "setTimeout", "wrapperEl", "virtualSize", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/modules/virtual.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { s as setCSSProperty, e as elementChildren, c as createElement } from '../shared/utils.mjs';\n\nfunction Virtual(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    virtual: {\n      enabled: false,\n      slides: [],\n      cache: true,\n      renderSlide: null,\n      renderExternal: null,\n      renderExternalUpdate: true,\n      addSlidesBefore: 0,\n      addSlidesAfter: 0\n    }\n  });\n  let cssModeTimeout;\n  const document = getDocument();\n  swiper.virtual = {\n    cache: {},\n    from: undefined,\n    to: undefined,\n    slides: [],\n    offset: 0,\n    slidesGrid: []\n  };\n  const tempDOM = document.createElement('div');\n  function renderSlide(slide, index) {\n    const params = swiper.params.virtual;\n    if (params.cache && swiper.virtual.cache[index]) {\n      return swiper.virtual.cache[index];\n    }\n    // eslint-disable-next-line\n    let slideEl;\n    if (params.renderSlide) {\n      slideEl = params.renderSlide.call(swiper, slide, index);\n      if (typeof slideEl === 'string') {\n        tempDOM.innerHTML = slideEl;\n        slideEl = tempDOM.children[0];\n      }\n    } else if (swiper.isElement) {\n      slideEl = createElement('swiper-slide');\n    } else {\n      slideEl = createElement('div', swiper.params.slideClass);\n    }\n    slideEl.setAttribute('data-swiper-slide-index', index);\n    if (!params.renderSlide) {\n      slideEl.innerHTML = slide;\n    }\n    if (params.cache) {\n      swiper.virtual.cache[index] = slideEl;\n    }\n    return slideEl;\n  }\n  function update(force, beforeInit, forceActiveIndex) {\n    const {\n      slidesPerView,\n      slidesPerGroup,\n      centeredSlides,\n      loop: isLoop,\n      initialSlide\n    } = swiper.params;\n    if (beforeInit && !isLoop && initialSlide > 0) {\n      return;\n    }\n    const {\n      addSlidesBefore,\n      addSlidesAfter\n    } = swiper.params.virtual;\n    const {\n      from: previousFrom,\n      to: previousTo,\n      slides,\n      slidesGrid: previousSlidesGrid,\n      offset: previousOffset\n    } = swiper.virtual;\n    if (!swiper.params.cssMode) {\n      swiper.updateActiveIndex();\n    }\n    const activeIndex = typeof forceActiveIndex === 'undefined' ? swiper.activeIndex || 0 : forceActiveIndex;\n    let offsetProp;\n    if (swiper.rtlTranslate) offsetProp = 'right';else offsetProp = swiper.isHorizontal() ? 'left' : 'top';\n    let slidesAfter;\n    let slidesBefore;\n    if (centeredSlides) {\n      slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n    } else {\n      slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesAfter;\n      slidesBefore = (isLoop ? slidesPerView : slidesPerGroup) + addSlidesBefore;\n    }\n    let from = activeIndex - slidesBefore;\n    let to = activeIndex + slidesAfter;\n    if (!isLoop) {\n      from = Math.max(from, 0);\n      to = Math.min(to, slides.length - 1);\n    }\n    let offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n    if (isLoop && activeIndex >= slidesBefore) {\n      from -= slidesBefore;\n      if (!centeredSlides) offset += swiper.slidesGrid[0];\n    } else if (isLoop && activeIndex < slidesBefore) {\n      from = -slidesBefore;\n      if (centeredSlides) offset += swiper.slidesGrid[0];\n    }\n    Object.assign(swiper.virtual, {\n      from,\n      to,\n      offset,\n      slidesGrid: swiper.slidesGrid,\n      slidesBefore,\n      slidesAfter\n    });\n    function onRendered() {\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n      emit('virtualUpdate');\n    }\n    if (previousFrom === from && previousTo === to && !force) {\n      if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n        swiper.slides.forEach(slideEl => {\n          slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n        });\n      }\n      swiper.updateProgress();\n      emit('virtualUpdate');\n      return;\n    }\n    if (swiper.params.virtual.renderExternal) {\n      swiper.params.virtual.renderExternal.call(swiper, {\n        offset,\n        from,\n        to,\n        slides: function getSlides() {\n          const slidesToRender = [];\n          for (let i = from; i <= to; i += 1) {\n            slidesToRender.push(slides[i]);\n          }\n          return slidesToRender;\n        }()\n      });\n      if (swiper.params.virtual.renderExternalUpdate) {\n        onRendered();\n      } else {\n        emit('virtualUpdate');\n      }\n      return;\n    }\n    const prependIndexes = [];\n    const appendIndexes = [];\n    const getSlideIndex = index => {\n      let slideIndex = index;\n      if (index < 0) {\n        slideIndex = slides.length + index;\n      } else if (slideIndex >= slides.length) {\n        // eslint-disable-next-line\n        slideIndex = slideIndex - slides.length;\n      }\n      return slideIndex;\n    };\n    if (force) {\n      swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`)).forEach(slideEl => {\n        slideEl.remove();\n      });\n    } else {\n      for (let i = previousFrom; i <= previousTo; i += 1) {\n        if (i < from || i > to) {\n          const slideIndex = getSlideIndex(i);\n          swiper.slides.filter(el => el.matches(`.${swiper.params.slideClass}[data-swiper-slide-index=\"${slideIndex}\"], swiper-slide[data-swiper-slide-index=\"${slideIndex}\"]`)).forEach(slideEl => {\n            slideEl.remove();\n          });\n        }\n      }\n    }\n    const loopFrom = isLoop ? -slides.length : 0;\n    const loopTo = isLoop ? slides.length * 2 : slides.length;\n    for (let i = loopFrom; i < loopTo; i += 1) {\n      if (i >= from && i <= to) {\n        const slideIndex = getSlideIndex(i);\n        if (typeof previousTo === 'undefined' || force) {\n          appendIndexes.push(slideIndex);\n        } else {\n          if (i > previousTo) appendIndexes.push(slideIndex);\n          if (i < previousFrom) prependIndexes.push(slideIndex);\n        }\n      }\n    }\n    appendIndexes.forEach(index => {\n      swiper.slidesEl.append(renderSlide(slides[index], index));\n    });\n    if (isLoop) {\n      for (let i = prependIndexes.length - 1; i >= 0; i -= 1) {\n        const index = prependIndexes[i];\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      }\n    } else {\n      prependIndexes.sort((a, b) => b - a);\n      prependIndexes.forEach(index => {\n        swiper.slidesEl.prepend(renderSlide(slides[index], index));\n      });\n    }\n    elementChildren(swiper.slidesEl, '.swiper-slide, swiper-slide').forEach(slideEl => {\n      slideEl.style[offsetProp] = `${offset - Math.abs(swiper.cssOverflowAdjustment())}px`;\n    });\n    onRendered();\n  }\n  function appendSlide(slides) {\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.push(slides[i]);\n      }\n    } else {\n      swiper.virtual.slides.push(slides);\n    }\n    update(true);\n  }\n  function prependSlide(slides) {\n    const activeIndex = swiper.activeIndex;\n    let newActiveIndex = activeIndex + 1;\n    let numberOfNewSlides = 1;\n    if (Array.isArray(slides)) {\n      for (let i = 0; i < slides.length; i += 1) {\n        if (slides[i]) swiper.virtual.slides.unshift(slides[i]);\n      }\n      newActiveIndex = activeIndex + slides.length;\n      numberOfNewSlides = slides.length;\n    } else {\n      swiper.virtual.slides.unshift(slides);\n    }\n    if (swiper.params.virtual.cache) {\n      const cache = swiper.virtual.cache;\n      const newCache = {};\n      Object.keys(cache).forEach(cachedIndex => {\n        const cachedEl = cache[cachedIndex];\n        const cachedElIndex = cachedEl.getAttribute('data-swiper-slide-index');\n        if (cachedElIndex) {\n          cachedEl.setAttribute('data-swiper-slide-index', parseInt(cachedElIndex, 10) + numberOfNewSlides);\n        }\n        newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cachedEl;\n      });\n      swiper.virtual.cache = newCache;\n    }\n    update(true);\n    swiper.slideTo(newActiveIndex, 0);\n  }\n  function removeSlide(slidesIndexes) {\n    if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) return;\n    let activeIndex = swiper.activeIndex;\n    if (Array.isArray(slidesIndexes)) {\n      for (let i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes[i]];\n          // shift cache indexes\n          Object.keys(swiper.virtual.cache).forEach(key => {\n            if (key > slidesIndexes) {\n              swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n              swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n              delete swiper.virtual.cache[key];\n            }\n          });\n        }\n        swiper.virtual.slides.splice(slidesIndexes[i], 1);\n        if (slidesIndexes[i] < activeIndex) activeIndex -= 1;\n        activeIndex = Math.max(activeIndex, 0);\n      }\n    } else {\n      if (swiper.params.virtual.cache) {\n        delete swiper.virtual.cache[slidesIndexes];\n        // shift cache indexes\n        Object.keys(swiper.virtual.cache).forEach(key => {\n          if (key > slidesIndexes) {\n            swiper.virtual.cache[key - 1] = swiper.virtual.cache[key];\n            swiper.virtual.cache[key - 1].setAttribute('data-swiper-slide-index', key - 1);\n            delete swiper.virtual.cache[key];\n          }\n        });\n      }\n      swiper.virtual.slides.splice(slidesIndexes, 1);\n      if (slidesIndexes < activeIndex) activeIndex -= 1;\n      activeIndex = Math.max(activeIndex, 0);\n    }\n    update(true);\n    swiper.slideTo(activeIndex, 0);\n  }\n  function removeAllSlides() {\n    swiper.virtual.slides = [];\n    if (swiper.params.virtual.cache) {\n      swiper.virtual.cache = {};\n    }\n    update(true);\n    swiper.slideTo(0, 0);\n  }\n  on('beforeInit', () => {\n    if (!swiper.params.virtual.enabled) return;\n    let domSlidesAssigned;\n    if (typeof swiper.passedParams.virtual.slides === 'undefined') {\n      const slides = [...swiper.slidesEl.children].filter(el => el.matches(`.${swiper.params.slideClass}, swiper-slide`));\n      if (slides && slides.length) {\n        swiper.virtual.slides = [...slides];\n        domSlidesAssigned = true;\n        slides.forEach((slideEl, slideIndex) => {\n          slideEl.setAttribute('data-swiper-slide-index', slideIndex);\n          swiper.virtual.cache[slideIndex] = slideEl;\n          slideEl.remove();\n        });\n      }\n    }\n    if (!domSlidesAssigned) {\n      swiper.virtual.slides = swiper.params.virtual.slides;\n    }\n    swiper.classNames.push(`${swiper.params.containerModifierClass}virtual`);\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n    update(false, true);\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode && !swiper._immediateVirtual) {\n      clearTimeout(cssModeTimeout);\n      cssModeTimeout = setTimeout(() => {\n        update();\n      }, 100);\n    } else {\n      update();\n    }\n  });\n  on('init update resize', () => {\n    if (!swiper.params.virtual.enabled) return;\n    if (swiper.params.cssMode) {\n      setCSSProperty(swiper.wrapperEl, '--swiper-virtual-size', `${swiper.virtualSize}px`);\n    }\n  });\n  Object.assign(swiper.virtual, {\n    appendSlide,\n    prependSlide,\n    removeSlide,\n    removeAllSlides,\n    update\n  });\n}\n\nexport { Virtual as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;AAC/D,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AAEnG,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACRE,YAAY,CAAC;IACXG,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,IAAI;MACpBC,oBAAoB,EAAE,IAAI;MAC1BC,eAAe,EAAE,CAAC;MAClBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,IAAIC,cAAc;EAClB,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9BS,MAAM,CAACI,OAAO,GAAG;IACfG,KAAK,EAAE,CAAC,CAAC;IACTQ,IAAI,EAAEC,SAAS;IACfC,EAAE,EAAED,SAAS;IACbV,MAAM,EAAE,EAAE;IACVY,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd,CAAC;EACD,MAAMC,OAAO,GAAGN,QAAQ,CAACjB,aAAa,CAAC,KAAK,CAAC;EAC7C,SAASW,WAAWA,CAACa,KAAK,EAAEC,KAAK,EAAE;IACjC,MAAMC,MAAM,GAAGvB,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACpC,IAAImB,MAAM,CAAChB,KAAK,IAAIP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,EAAE;MAC/C,OAAOtB,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC;IACpC;IACA;IACA,IAAIE,OAAO;IACX,IAAID,MAAM,CAACf,WAAW,EAAE;MACtBgB,OAAO,GAAGD,MAAM,CAACf,WAAW,CAACiB,IAAI,CAACzB,MAAM,EAAEqB,KAAK,EAAEC,KAAK,CAAC;MACvD,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;QAC/BJ,OAAO,CAACM,SAAS,GAAGF,OAAO;QAC3BA,OAAO,GAAGJ,OAAO,CAACO,QAAQ,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IAAI3B,MAAM,CAAC4B,SAAS,EAAE;MAC3BJ,OAAO,GAAG3B,aAAa,CAAC,cAAc,CAAC;IACzC,CAAC,MAAM;MACL2B,OAAO,GAAG3B,aAAa,CAAC,KAAK,EAAEG,MAAM,CAACuB,MAAM,CAACM,UAAU,CAAC;IAC1D;IACAL,OAAO,CAACM,YAAY,CAAC,yBAAyB,EAAER,KAAK,CAAC;IACtD,IAAI,CAACC,MAAM,CAACf,WAAW,EAAE;MACvBgB,OAAO,CAACE,SAAS,GAAGL,KAAK;IAC3B;IACA,IAAIE,MAAM,CAAChB,KAAK,EAAE;MAChBP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACe,KAAK,CAAC,GAAGE,OAAO;IACvC;IACA,OAAOA,OAAO;EAChB;EACA,SAASO,MAAMA,CAACC,KAAK,EAAEC,UAAU,EAAEC,gBAAgB,EAAE;IACnD,MAAM;MACJC,aAAa;MACbC,cAAc;MACdC,cAAc;MACdC,IAAI,EAAEC,MAAM;MACZC;IACF,CAAC,GAAGxC,MAAM,CAACuB,MAAM;IACjB,IAAIU,UAAU,IAAI,CAACM,MAAM,IAAIC,YAAY,GAAG,CAAC,EAAE;MAC7C;IACF;IACA,MAAM;MACJ7B,eAAe;MACfC;IACF,CAAC,GAAGZ,MAAM,CAACuB,MAAM,CAACnB,OAAO;IACzB,MAAM;MACJW,IAAI,EAAE0B,YAAY;MAClBxB,EAAE,EAAEyB,UAAU;MACdpC,MAAM;MACNa,UAAU,EAAEwB,kBAAkB;MAC9BzB,MAAM,EAAE0B;IACV,CAAC,GAAG5C,MAAM,CAACI,OAAO;IAClB,IAAI,CAACJ,MAAM,CAACuB,MAAM,CAACsB,OAAO,EAAE;MAC1B7C,MAAM,CAAC8C,iBAAiB,CAAC,CAAC;IAC5B;IACA,MAAMC,WAAW,GAAG,OAAOb,gBAAgB,KAAK,WAAW,GAAGlC,MAAM,CAAC+C,WAAW,IAAI,CAAC,GAAGb,gBAAgB;IACxG,IAAIc,UAAU;IACd,IAAIhD,MAAM,CAACiD,YAAY,EAAED,UAAU,GAAG,OAAO,CAAC,KAAKA,UAAU,GAAGhD,MAAM,CAACkD,YAAY,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK;IACtG,IAAIC,WAAW;IACf,IAAIC,YAAY;IAChB,IAAIf,cAAc,EAAE;MAClBc,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACnB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGxB,cAAc;MAC7EwC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACnB,aAAa,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAGzB,eAAe;IACjF,CAAC,MAAM;MACLwC,WAAW,GAAGhB,aAAa,IAAIC,cAAc,GAAG,CAAC,CAAC,GAAGxB,cAAc;MACnEwC,YAAY,GAAG,CAACb,MAAM,GAAGJ,aAAa,GAAGC,cAAc,IAAIzB,eAAe;IAC5E;IACA,IAAII,IAAI,GAAGgC,WAAW,GAAGK,YAAY;IACrC,IAAInC,EAAE,GAAG8B,WAAW,GAAGI,WAAW;IAClC,IAAI,CAACZ,MAAM,EAAE;MACXxB,IAAI,GAAGsC,IAAI,CAACE,GAAG,CAACxC,IAAI,EAAE,CAAC,CAAC;MACxBE,EAAE,GAAGoC,IAAI,CAACG,GAAG,CAACvC,EAAE,EAAEX,MAAM,CAACmD,MAAM,GAAG,CAAC,CAAC;IACtC;IACA,IAAIvC,MAAM,GAAG,CAAClB,MAAM,CAACmB,UAAU,CAACJ,IAAI,CAAC,IAAI,CAAC,KAAKf,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzE,IAAIoB,MAAM,IAAIQ,WAAW,IAAIK,YAAY,EAAE;MACzCrC,IAAI,IAAIqC,YAAY;MACpB,IAAI,CAACf,cAAc,EAAEnB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIoB,MAAM,IAAIQ,WAAW,GAAGK,YAAY,EAAE;MAC/CrC,IAAI,GAAG,CAACqC,YAAY;MACpB,IAAIf,cAAc,EAAEnB,MAAM,IAAIlB,MAAM,CAACmB,UAAU,CAAC,CAAC,CAAC;IACpD;IACAuC,MAAM,CAACC,MAAM,CAAC3D,MAAM,CAACI,OAAO,EAAE;MAC5BW,IAAI;MACJE,EAAE;MACFC,MAAM;MACNC,UAAU,EAAEnB,MAAM,CAACmB,UAAU;MAC7BiC,YAAY;MACZD;IACF,CAAC,CAAC;IACF,SAASS,UAAUA,CAAA,EAAG;MACpB5D,MAAM,CAAC6D,YAAY,CAAC,CAAC;MACrB7D,MAAM,CAAC8D,cAAc,CAAC,CAAC;MACvB9D,MAAM,CAAC+D,mBAAmB,CAAC,CAAC;MAC5B5D,IAAI,CAAC,eAAe,CAAC;IACvB;IACA,IAAIsC,YAAY,KAAK1B,IAAI,IAAI2B,UAAU,KAAKzB,EAAE,IAAI,CAACe,KAAK,EAAE;MACxD,IAAIhC,MAAM,CAACmB,UAAU,KAAKwB,kBAAkB,IAAIzB,MAAM,KAAK0B,cAAc,EAAE;QACzE5C,MAAM,CAACM,MAAM,CAAC0D,OAAO,CAACxC,OAAO,IAAI;UAC/BA,OAAO,CAACyC,KAAK,CAACjB,UAAU,CAAC,MAAAkB,MAAA,CAAMhD,MAAM,GAAGmC,IAAI,CAACc,GAAG,CAACnE,MAAM,CAACoE,qBAAqB,CAAC,CAAC,CAAC,OAAI;QACtF,CAAC,CAAC;MACJ;MACApE,MAAM,CAAC8D,cAAc,CAAC,CAAC;MACvB3D,IAAI,CAAC,eAAe,CAAC;MACrB;IACF;IACA,IAAIH,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,EAAE;MACxCT,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACK,cAAc,CAACgB,IAAI,CAACzB,MAAM,EAAE;QAChDkB,MAAM;QACNH,IAAI;QACJE,EAAE;QACFX,MAAM,EAAE,SAAS+D,SAASA,CAAA,EAAG;UAC3B,MAAMC,cAAc,GAAG,EAAE;UACzB,KAAK,IAAIC,CAAC,GAAGxD,IAAI,EAAEwD,CAAC,IAAItD,EAAE,EAAEsD,CAAC,IAAI,CAAC,EAAE;YAClCD,cAAc,CAACE,IAAI,CAAClE,MAAM,CAACiE,CAAC,CAAC,CAAC;UAChC;UACA,OAAOD,cAAc;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAItE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACM,oBAAoB,EAAE;QAC9CkD,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLzD,IAAI,CAAC,eAAe,CAAC;MACvB;MACA;IACF;IACA,MAAMsE,cAAc,GAAG,EAAE;IACzB,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,aAAa,GAAGrD,KAAK,IAAI;MAC7B,IAAIsD,UAAU,GAAGtD,KAAK;MACtB,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbsD,UAAU,GAAGtE,MAAM,CAACmD,MAAM,GAAGnC,KAAK;MACpC,CAAC,MAAM,IAAIsD,UAAU,IAAItE,MAAM,CAACmD,MAAM,EAAE;QACtC;QACAmB,UAAU,GAAGA,UAAU,GAAGtE,MAAM,CAACmD,MAAM;MACzC;MACA,OAAOmB,UAAU;IACnB,CAAC;IACD,IAAI5C,KAAK,EAAE;MACThC,MAAM,CAACM,MAAM,CAACuE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,KAAAb,MAAA,CAAKlE,MAAM,CAACuB,MAAM,CAACM,UAAU,mBAAgB,CAAC,CAAC,CAACmC,OAAO,CAACxC,OAAO,IAAI;QACtGA,OAAO,CAACwD,MAAM,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAK,IAAIT,CAAC,GAAG9B,YAAY,EAAE8B,CAAC,IAAI7B,UAAU,EAAE6B,CAAC,IAAI,CAAC,EAAE;QAClD,IAAIA,CAAC,GAAGxD,IAAI,IAAIwD,CAAC,GAAGtD,EAAE,EAAE;UACtB,MAAM2D,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;UACnCvE,MAAM,CAACM,MAAM,CAACuE,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,KAAAb,MAAA,CAAKlE,MAAM,CAACuB,MAAM,CAACM,UAAU,iCAAAqC,MAAA,CAA6BU,UAAU,kDAAAV,MAAA,CAA6CU,UAAU,QAAI,CAAC,CAAC,CAACZ,OAAO,CAACxC,OAAO,IAAI;YACxLA,OAAO,CAACwD,MAAM,CAAC,CAAC;UAClB,CAAC,CAAC;QACJ;MACF;IACF;IACA,MAAMC,QAAQ,GAAG1C,MAAM,GAAG,CAACjC,MAAM,CAACmD,MAAM,GAAG,CAAC;IAC5C,MAAMyB,MAAM,GAAG3C,MAAM,GAAGjC,MAAM,CAACmD,MAAM,GAAG,CAAC,GAAGnD,MAAM,CAACmD,MAAM;IACzD,KAAK,IAAIc,CAAC,GAAGU,QAAQ,EAAEV,CAAC,GAAGW,MAAM,EAAEX,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIA,CAAC,IAAIxD,IAAI,IAAIwD,CAAC,IAAItD,EAAE,EAAE;QACxB,MAAM2D,UAAU,GAAGD,aAAa,CAACJ,CAAC,CAAC;QACnC,IAAI,OAAO7B,UAAU,KAAK,WAAW,IAAIV,KAAK,EAAE;UAC9C0C,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;QAChC,CAAC,MAAM;UACL,IAAIL,CAAC,GAAG7B,UAAU,EAAEgC,aAAa,CAACF,IAAI,CAACI,UAAU,CAAC;UAClD,IAAIL,CAAC,GAAG9B,YAAY,EAAEgC,cAAc,CAACD,IAAI,CAACI,UAAU,CAAC;QACvD;MACF;IACF;IACAF,aAAa,CAACV,OAAO,CAAC1C,KAAK,IAAI;MAC7BtB,MAAM,CAACmF,QAAQ,CAACC,MAAM,CAAC5E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC;IACF,IAAIiB,MAAM,EAAE;MACV,KAAK,IAAIgC,CAAC,GAAGE,cAAc,CAAChB,MAAM,GAAG,CAAC,EAAEc,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACtD,MAAMjD,KAAK,GAAGmD,cAAc,CAACF,CAAC,CAAC;QAC/BvE,MAAM,CAACmF,QAAQ,CAACE,OAAO,CAAC7E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC,MAAM;MACLmD,cAAc,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;MACpCd,cAAc,CAACT,OAAO,CAAC1C,KAAK,IAAI;QAC9BtB,MAAM,CAACmF,QAAQ,CAACE,OAAO,CAAC7E,WAAW,CAACF,MAAM,CAACgB,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ;IACA3B,eAAe,CAACK,MAAM,CAACmF,QAAQ,EAAE,6BAA6B,CAAC,CAACnB,OAAO,CAACxC,OAAO,IAAI;MACjFA,OAAO,CAACyC,KAAK,CAACjB,UAAU,CAAC,MAAAkB,MAAA,CAAMhD,MAAM,GAAGmC,IAAI,CAACc,GAAG,CAACnE,MAAM,CAACoE,qBAAqB,CAAC,CAAC,CAAC,OAAI;IACtF,CAAC,CAAC;IACFR,UAAU,CAAC,CAAC;EACd;EACA,SAAS6B,WAAWA,CAACnF,MAAM,EAAE;IAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;MACpD,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,MAAM,CAACmD,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;QACzC,IAAIjE,MAAM,CAACiE,CAAC,CAAC,EAAEvE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACkE,IAAI,CAAClE,MAAM,CAACiE,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,MAAM;MACLvE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACkE,IAAI,CAAClE,MAAM,CAAC;IACpC;IACAyB,MAAM,CAAC,IAAI,CAAC;EACd;EACA,SAAS2D,YAAYA,CAACpF,MAAM,EAAE;IAC5B,MAAMyC,WAAW,GAAG/C,MAAM,CAAC+C,WAAW;IACtC,IAAI4C,cAAc,GAAG5C,WAAW,GAAG,CAAC;IACpC,IAAI6C,iBAAiB,GAAG,CAAC;IACzB,IAAIC,KAAK,CAACC,OAAO,CAACxF,MAAM,CAAC,EAAE;MACzB,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,MAAM,CAACmD,MAAM,EAAEc,CAAC,IAAI,CAAC,EAAE;QACzC,IAAIjE,MAAM,CAACiE,CAAC,CAAC,EAAEvE,MAAM,CAACI,OAAO,CAACE,MAAM,CAACyF,OAAO,CAACzF,MAAM,CAACiE,CAAC,CAAC,CAAC;MACzD;MACAoB,cAAc,GAAG5C,WAAW,GAAGzC,MAAM,CAACmD,MAAM;MAC5CmC,iBAAiB,GAAGtF,MAAM,CAACmD,MAAM;IACnC,CAAC,MAAM;MACLzD,MAAM,CAACI,OAAO,CAACE,MAAM,CAACyF,OAAO,CAACzF,MAAM,CAAC;IACvC;IACA,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/B,MAAMA,KAAK,GAAGP,MAAM,CAACI,OAAO,CAACG,KAAK;MAClC,MAAMyF,QAAQ,GAAG,CAAC,CAAC;MACnBtC,MAAM,CAACuC,IAAI,CAAC1F,KAAK,CAAC,CAACyD,OAAO,CAACkC,WAAW,IAAI;QACxC,MAAMC,QAAQ,GAAG5F,KAAK,CAAC2F,WAAW,CAAC;QACnC,MAAME,aAAa,GAAGD,QAAQ,CAACE,YAAY,CAAC,yBAAyB,CAAC;QACtE,IAAID,aAAa,EAAE;UACjBD,QAAQ,CAACrE,YAAY,CAAC,yBAAyB,EAAEwE,QAAQ,CAACF,aAAa,EAAE,EAAE,CAAC,GAAGR,iBAAiB,CAAC;QACnG;QACAI,QAAQ,CAACM,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC,GAAGN,iBAAiB,CAAC,GAAGO,QAAQ;MACpE,CAAC,CAAC;MACFnG,MAAM,CAACI,OAAO,CAACG,KAAK,GAAGyF,QAAQ;IACjC;IACAjE,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACuG,OAAO,CAACZ,cAAc,EAAE,CAAC,CAAC;EACnC;EACA,SAASa,WAAWA,CAACC,aAAa,EAAE;IAClC,IAAI,OAAOA,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;IACpE,IAAI1D,WAAW,GAAG/C,MAAM,CAAC+C,WAAW;IACpC,IAAI8C,KAAK,CAACC,OAAO,CAACW,aAAa,CAAC,EAAE;MAChC,KAAK,IAAIlC,CAAC,GAAGkC,aAAa,CAAChD,MAAM,GAAG,CAAC,EAAEc,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QACrD,IAAIvE,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;UAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACkG,aAAa,CAAClC,CAAC,CAAC,CAAC;UAC7C;UACAb,MAAM,CAACuC,IAAI,CAACjG,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC,CAACyD,OAAO,CAAC0C,GAAG,IAAI;YAC/C,IAAIA,GAAG,GAAGD,aAAa,EAAE;cACvBzG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,GAAG,CAAC,CAAC,GAAG1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,CAAC;cACzD1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,GAAG,CAAC,CAAC,CAAC5E,YAAY,CAAC,yBAAyB,EAAE4E,GAAG,GAAG,CAAC,CAAC;cAC9E,OAAO1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,CAAC;YAClC;UACF,CAAC,CAAC;QACJ;QACA1G,MAAM,CAACI,OAAO,CAACE,MAAM,CAACqG,MAAM,CAACF,aAAa,CAAClC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjD,IAAIkC,aAAa,CAAClC,CAAC,CAAC,GAAGxB,WAAW,EAAEA,WAAW,IAAI,CAAC;QACpDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;MACxC;IACF,CAAC,MAAM;MACL,IAAI/C,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;QAC/B,OAAOP,MAAM,CAACI,OAAO,CAACG,KAAK,CAACkG,aAAa,CAAC;QAC1C;QACA/C,MAAM,CAACuC,IAAI,CAACjG,MAAM,CAACI,OAAO,CAACG,KAAK,CAAC,CAACyD,OAAO,CAAC0C,GAAG,IAAI;UAC/C,IAAIA,GAAG,GAAGD,aAAa,EAAE;YACvBzG,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,GAAG,CAAC,CAAC,GAAG1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,CAAC;YACzD1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,GAAG,CAAC,CAAC,CAAC5E,YAAY,CAAC,yBAAyB,EAAE4E,GAAG,GAAG,CAAC,CAAC;YAC9E,OAAO1G,MAAM,CAACI,OAAO,CAACG,KAAK,CAACmG,GAAG,CAAC;UAClC;QACF,CAAC,CAAC;MACJ;MACA1G,MAAM,CAACI,OAAO,CAACE,MAAM,CAACqG,MAAM,CAACF,aAAa,EAAE,CAAC,CAAC;MAC9C,IAAIA,aAAa,GAAG1D,WAAW,EAAEA,WAAW,IAAI,CAAC;MACjDA,WAAW,GAAGM,IAAI,CAACE,GAAG,CAACR,WAAW,EAAE,CAAC,CAAC;IACxC;IACAhB,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACuG,OAAO,CAACxD,WAAW,EAAE,CAAC,CAAC;EAChC;EACA,SAAS6D,eAAeA,CAAA,EAAG;IACzB5G,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,EAAE;IAC1B,IAAIN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACG,KAAK,EAAE;MAC/BP,MAAM,CAACI,OAAO,CAACG,KAAK,GAAG,CAAC,CAAC;IAC3B;IACAwB,MAAM,CAAC,IAAI,CAAC;IACZ/B,MAAM,CAACuG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB;EACArG,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIwG,iBAAiB;IACrB,IAAI,OAAO7G,MAAM,CAAC8G,YAAY,CAAC1G,OAAO,CAACE,MAAM,KAAK,WAAW,EAAE;MAC7D,MAAMA,MAAM,GAAG,CAAC,GAAGN,MAAM,CAACmF,QAAQ,CAACxD,QAAQ,CAAC,CAACkD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,OAAO,KAAAb,MAAA,CAAKlE,MAAM,CAACuB,MAAM,CAACM,UAAU,mBAAgB,CAAC,CAAC;MACnH,IAAIvB,MAAM,IAAIA,MAAM,CAACmD,MAAM,EAAE;QAC3BzD,MAAM,CAACI,OAAO,CAACE,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC;QACnCuG,iBAAiB,GAAG,IAAI;QACxBvG,MAAM,CAAC0D,OAAO,CAAC,CAACxC,OAAO,EAAEoD,UAAU,KAAK;UACtCpD,OAAO,CAACM,YAAY,CAAC,yBAAyB,EAAE8C,UAAU,CAAC;UAC3D5E,MAAM,CAACI,OAAO,CAACG,KAAK,CAACqE,UAAU,CAAC,GAAGpD,OAAO;UAC1CA,OAAO,CAACwD,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ;IACF;IACA,IAAI,CAAC6B,iBAAiB,EAAE;MACtB7G,MAAM,CAACI,OAAO,CAACE,MAAM,GAAGN,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACE,MAAM;IACtD;IACAN,MAAM,CAAC+G,UAAU,CAACvC,IAAI,IAAAN,MAAA,CAAIlE,MAAM,CAACuB,MAAM,CAACyF,sBAAsB,YAAS,CAAC;IACxEhH,MAAM,CAACuB,MAAM,CAAC0F,mBAAmB,GAAG,IAAI;IACxCjH,MAAM,CAACkH,cAAc,CAACD,mBAAmB,GAAG,IAAI;IAChDlF,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC;EACrB,CAAC,CAAC;EACF7B,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACsB,OAAO,IAAI,CAAC7C,MAAM,CAACmH,iBAAiB,EAAE;MACtDC,YAAY,CAACvG,cAAc,CAAC;MAC5BA,cAAc,GAAGwG,UAAU,CAAC,MAAM;QAChCtF,MAAM,CAAC,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLA,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF7B,EAAE,CAAC,oBAAoB,EAAE,MAAM;IAC7B,IAAI,CAACF,MAAM,CAACuB,MAAM,CAACnB,OAAO,CAACC,OAAO,EAAE;IACpC,IAAIL,MAAM,CAACuB,MAAM,CAACsB,OAAO,EAAE;MACzBpD,cAAc,CAACO,MAAM,CAACsH,SAAS,EAAE,uBAAuB,KAAApD,MAAA,CAAKlE,MAAM,CAACuH,WAAW,OAAI,CAAC;IACtF;EACF,CAAC,CAAC;EACF7D,MAAM,CAACC,MAAM,CAAC3D,MAAM,CAACI,OAAO,EAAE;IAC5BqF,WAAW;IACXC,YAAY;IACZc,WAAW;IACXI,eAAe;IACf7E;EACF,CAAC,CAAC;AACJ;AAEA,SAASjC,OAAO,IAAI0H,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}