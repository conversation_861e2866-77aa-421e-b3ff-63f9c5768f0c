{"ast": null, "code": "import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\nfunction memoSupports(callback, supportsFlag) {\n  const memoized = memo(callback);\n  return () => {\n    var _supportsFlags$suppor;\n    return (_supportsFlags$suppor = supportsFlags[supportsFlag]) !== null && _supportsFlags$suppor !== void 0 ? _supportsFlags$suppor : memoized();\n  };\n}\nexport { memoSupports };", "map": {"version": 3, "names": ["memo", "supportsFlags", "memoSupports", "callback", "supportsFlag", "memoized", "_supportsFlags$suppor"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/utils/supports/memo.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => supportsFlags[supportsFlag] ?? memoized();\n}\n\nexport { memoSupports };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,aAAa,QAAQ,aAAa;AAE3C,SAASC,YAAYA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC1C,MAAMC,QAAQ,GAAGL,IAAI,CAACG,QAAQ,CAAC;EAC/B,OAAO;IAAA,IAAAG,qBAAA;IAAA,QAAAA,qBAAA,GAAML,aAAa,CAACG,YAAY,CAAC,cAAAE,qBAAA,cAAAA,qBAAA,GAAID,QAAQ,CAAC,CAAC;EAAA;AAC1D;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}