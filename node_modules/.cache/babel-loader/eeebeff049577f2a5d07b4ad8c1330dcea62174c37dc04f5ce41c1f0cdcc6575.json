{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'./owners.module.css';import Sidebar from'./components/sidebar/Sidebar';import img from'../../asset/imgs/owners/BANNER-HOME-FAMILIA_1800x600.webp';import OwnersForm from'./components/form/OwnersForm';// animation\nimport AOS from'aos';import'aos/dist/aos.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Owners=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';// Отключаем скролл при загрузке\nconst timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';// Возвращаем скролл после загрузки\n},300);// 1 секунда для имитации загрузки\nreturn()=>{clearTimeout(timer);document.body.style.overflow='visible';// На случай размонтирования компонента\n};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0412\\u041B\\u0410\\u0414\\u0415\\u041B\\u042C\\u0426\\u0410\\u041C GWM\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",className:styles.redLine}),/*#__PURE__*/_jsx(\"img\",{src:img,alt:\"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",className:styles.banner,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\"}),/*#__PURE__*/_jsxs(\"div\",{className:styles.textContent,children:[/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u041C\\u044B \\u0433\\u043E\\u0440\\u0434\\u0438\\u043C\\u0441\\u044F \\u0442\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043E\\u0440\\u0438\\u0435\\u043D\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u044B \\u043D\\u0430 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430, \\u0438 \\u0441\\u0447\\u0438\\u0442\\u0430\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043C\\u043E\\u043C\\u0435\\u043D\\u0442, \\u043A\\u043E\\u0433\\u0434\\u0430 \\u0432\\u044B \\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0438\\u0442\\u0435\\u0441\\u044C \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u043C \\u0441\\u0432\\u043E\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F, \\u2014 \\u044D\\u0442\\u043E \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u043D\\u0430\\u0447\\u0430\\u043B\\u043E \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u043F\\u0443\\u0442\\u0438 \\u043A \\u043E\\u043F\\u044B\\u0442\\u0443 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u043C. \\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u043C\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u043C \\u043A\\u043E\\u043C\\u043F\\u043B\\u0435\\u043A\\u0441\\u043D\\u0443\\u044E \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0443 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u043C\\u0441\\u044F \\u0441\\u0434\\u0435\\u043B\\u0430\\u0442\\u044C \\u0432\\u0441\\u0451 \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0435, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u0442\\u044C, \\u0447\\u0442\\u043E \\u0432\\u0441\\u0435 \\u043D\\u0430\\u0448\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u044B \\u043F\\u043E\\u043B\\u0443\\u0447\\u0430\\u0442 \\u043D\\u0430\\u0438\\u043B\\u0443\\u0447\\u0448\\u0443\\u044E \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C \\u0432 \\u043B\\u044E\\u0431\\u043E\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F.\"}),/*#__PURE__*/_jsxs(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[\"\\u041C\\u044B \\u0441\\u0442\\u0440\\u0430\\u0441\\u0442\\u043D\\u043E \\u0441\\u043E\\u0441\\u0440\\u0435\\u0434\\u043E\\u0442\\u043E\\u0447\\u0435\\u043D\\u044B \\u043D\\u0430 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430\\u0445 \\u0438 \\u0438\\u0445 \\u043E\\u043F\\u044B\\u0442\\u0435 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F, \\u0433\\u0434\\u0435 \",/*#__PURE__*/_jsx(\"strong\",{children:\"GWM CARE\"}),\" \\u2014 \\u044D\\u0442\\u043E \\u043D\\u0430\\u0448 \\u0431\\u0440\\u0435\\u043D\\u0434 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0439 \\u0444\\u043E\\u0440\\u043C\\u0438\\u0440\\u0443\\u0435\\u0442 \\u043D\\u0430\\u0448\\u0443 \\u0441\\u0442\\u0440\\u0443\\u043A\\u0442\\u0443\\u0440\\u0443 \\u043E\\u043F\\u044B\\u0442\\u0430 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u043C \\u0434\\u043B\\u044F \\u0443\\u043A\\u0440\\u0435\\u043F\\u043B\\u0435\\u043D\\u0438\\u044F \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430 \\u0438 \\u0434\\u0438\\u0444\\u0444\\u0435\\u0440\\u0435\\u043D\\u0446\\u0438\\u0430\\u0446\\u0438\\u0438 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u0442\\u0432 \\u043F\\u043E\\u0441\\u043B\\u0435\\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u043D\\u043E\\u0433\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u0439 \\u0438 \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u0432\\u043B\\u0430\\u0434\\u0435\\u043D\\u0438\\u044F.\"]})]}),/*#__PURE__*/_jsx(OwnersForm,{})]})})]})})})});};export default Owners;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "Sidebar", "img", "OwnersForm", "AOS", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Owners", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "redLine", "src", "alt", "banner", "textContent"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Owners.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './owners.module.css';\nimport Sidebar from './components/sidebar/Sidebar';\nimport img from '../../asset/imgs/owners/BANNER-HOME-FAMILIA_1800x600.webp';\nimport OwnersForm from './components/form/OwnersForm';\n// animation\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Owners = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                     <strong>ВЛАДЕЛЬЦАМ GWM</strong>\n                  </h1>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                    className={styles.redLine}\n                  ></i>\n                  <img\n                    src={img}\n                    alt=\"Баннер владельцев GWM\"\n                    className={styles.banner}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                  />\n                  <div className={styles.textContent}>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                      Мы гордимся тем, что ориентированы на клиента, и считаем,\n                      что момент, когда вы становитесь владельцем своего\n                      автомобиля, — это только начало вашего пути к опыту\n                      владения брендом. Для полного спокойствия мы предлагаем\n                      комплексную поддержку послепродажного обслуживания и\n                      всегда стремимся сделать всё возможное, чтобы\n                      гарантировать, что все наши клиенты получат наилучшую\n                      помощь в любое время.\n                    </p>\n                    <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                      Мы страстно сосредоточены на наших клиентах и их опыте\n                      владения, где <strong>GWM CARE</strong> — это наш бренд\n                      послепродажного обслуживания, который формирует нашу\n                      структуру опыта владения клиентом для укрепления\n                      обслуживания, бренда и дифференциации наших обязательств\n                      послепродажного обслуживания, ценностных предложений и\n                      спокойствия владения.\n                    </p>\n                  </div>\n                  <OwnersForm />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Owners;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,GAAG,KAAM,2DAA2D,CAC3E,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CACrD;AACA,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1B,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdM,GAAG,CAACU,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAAE;AAEzC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAE3B,MAAM,CAAC4B,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACL,OAAO,GAAE,CAAC,cACXK,IAAA,SAAMqB,SAAS,CAAE3B,MAAM,CAAC6B,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAE3B,MAAM,CAAC8B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACnBpB,IAAA,WAAAoB,QAAA,CAAQ,kEAAc,CAAQ,CAAC,CAC9B,CAAC,cACLpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAE3B,MAAM,CAAC+B,OAAQ,CACxB,CAAC,cACLzB,IAAA,QACE0B,GAAG,CAAE9B,GAAI,CACT+B,GAAG,CAAC,uGAAuB,CAC3BN,SAAS,CAAE3B,MAAM,CAACkC,MAAO,CACzB,WAAS,SAAS,CAClB,iBAAe,KAAK,CACrB,CAAC,cACF1B,KAAA,QAAKmB,SAAS,CAAE3B,MAAM,CAACmC,WAAY,CAAAT,QAAA,eACjCpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,s/DAS3C,CAAG,CAAC,cACJlB,KAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAkB,QAAA,EAAC,oWAE3B,cAAApB,IAAA,WAAAoB,QAAA,CAAQ,UAAQ,CAAQ,CAAC,20CAMzC,EAAG,CAAC,EACD,CAAC,cACNpB,IAAA,CAACH,UAAU,GAAE,CAAC,EACX,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAQ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}