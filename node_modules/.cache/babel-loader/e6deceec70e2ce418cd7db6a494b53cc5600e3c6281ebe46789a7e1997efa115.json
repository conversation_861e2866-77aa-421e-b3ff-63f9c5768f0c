{"ast": null, "code": "// Import all model data\nimport tank300Data from './models/tank-300.js';\nimport tank500Data from './models/tank-500.js';\nimport tank700Data from './models/tank-700.js';\nimport tank700PhevData from './models/tank-700-phev.js';\nimport havalH6Data from './models/haval-h6.js';\nimport havalH6HevData from './models/haval-h6-hev.js';\nimport havalJolionNewData from './models/haval-jolion-new.js';\nimport havalM6Data from './models/haval-m6.js';\nimport gwmCommercialPoerData from './models/gwm-commercial-poer.js';\nimport gwmWingle7Data from './models/gwm-wingle-7.js';\n\n// Model data registry\nconst modelData = {\n  'tank-300': tank300Data,\n  'tank-500': tank500Data,\n  'tank-700': tank700Data,\n  'tank-700-phev': tank700PhevData,\n  'haval-h6': havalH6Data,\n  'haval-h6-hev': havalH6HevData,\n  'haval-jolion': havalJolionNewData,\n  'haval-m6': havalM6Data,\n  'gwm-commercial-poer': gwmCommercialPoerData,\n  'gwm-wingle-7': gwmWingle7Data\n};\n\n// Function to get model data by slug\nexport const getModelData = slug => {\n  return modelData[slug] || null;\n};\n\n// Function to get all available model slugs\nexport const getAvailableModels = () => {\n  return Object.keys(modelData);\n};", "map": {"version": 3, "names": ["tank300Data", "tank500Data", "tank700Data", "tank700PhevData", "havalH6Data", "havalH6HevData", "havalJolionNewData", "havalM6Data", "gwmCommercialPoerData", "gwmWingle7Data", "modelData", "getModelData", "slug", "getAvailableModels", "Object", "keys"], "sources": ["/var/www/html/gwm.tj/src/data/modelLoader.js"], "sourcesContent": ["// Import all model data\nimport tank300Data from './models/tank-300.js';\nimport tank500Data from './models/tank-500.js';\nimport tank700Data from './models/tank-700.js';\nimport tank700PhevData from './models/tank-700-phev.js';\nimport havalH6Data from './models/haval-h6.js';\nimport havalH6HevData from './models/haval-h6-hev.js';\nimport havalJolionNewData from './models/haval-jolion-new.js';\nimport havalM6Data from './models/haval-m6.js';\nimport gwmCommercialPoerData from './models/gwm-commercial-poer.js';\nimport gwmWingle7Data from './models/gwm-wingle-7.js';\n\n// Model data registry\nconst modelData = {\n  'tank-300': tank300Data,\n  'tank-500': tank500Data,\n  'tank-700': tank700Data,\n  'tank-700-phev': tank700PhevData,\n  'haval-h6': havalH6Data,\n  'haval-h6-hev': havalH6HevData,\n  'haval-jolion': havalJolionNewData,\n  'haval-m6': havalM6Data,\n  'gwm-commercial-poer': gwmCommercialPoerData,\n  'gwm-wingle-7': gwmWingle7Data\n};\n\n// Function to get model data by slug\nexport const getModelData = (slug) => {\n  return modelData[slug] || null;\n};\n\n// Function to get all available model slugs\nexport const getAvailableModels = () => {\n  return Object.keys(modelData);\n};\n"], "mappings": "AAAA;AACA,OAAOA,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,qBAAqB,MAAM,iCAAiC;AACnE,OAAOC,cAAc,MAAM,0BAA0B;;AAErD;AACA,MAAMC,SAAS,GAAG;EAChB,UAAU,EAAEV,WAAW;EACvB,UAAU,EAAEC,WAAW;EACvB,UAAU,EAAEC,WAAW;EACvB,eAAe,EAAEC,eAAe;EAChC,UAAU,EAAEC,WAAW;EACvB,cAAc,EAAEC,cAAc;EAC9B,cAAc,EAAEC,kBAAkB;EAClC,UAAU,EAAEC,WAAW;EACvB,qBAAqB,EAAEC,qBAAqB;EAC5C,cAAc,EAAEC;AAClB,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAIC,IAAI,IAAK;EACpC,OAAOF,SAAS,CAACE,IAAI,CAAC,IAAI,IAAI;AAChC,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EACtC,OAAOC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}