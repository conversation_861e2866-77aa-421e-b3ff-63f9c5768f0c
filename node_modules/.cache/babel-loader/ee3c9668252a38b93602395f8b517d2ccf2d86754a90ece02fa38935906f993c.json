{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Privacy/Privacy.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport styles from './privacy.module.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Privacy = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [openIndex, setOpenIndex] = useState(null);\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  const toggleSection = index => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n  const sections = [{\n    title: 'Введение',\n    content: `Компания Haval Motors Tajikistan уважает право каждого пользователя на конфиденциальность и защиту персональных данных. Настоящая Политика конфиденциальности разработана с целью объяснить, какие персональные данные мы собираем, как мы их используем, храним и защищаем, а также какие у вас есть права в отношении этой информации.\nВ условиях стремительного развития цифровых технологий и расширения онлайн-сервисов защита персональных данных становится крайне важной задачей. Мы строго соблюдаем требования действующего законодательства Республики Таджикистан в области защиты персональных данных и международные стандарты, включая принципы законности, прозрачности, конфиденциальности и безопасности.\nИспользуя наш веб-сайт, услуги, продукты или предоставляя нам информацию иным способом, вы соглашаетесь с условиями настоящей Политики конфиденциальности.`\n  }, {\n    title: 'Что такое личная информация?',\n    content: `Личная информация — это любые данные, которые прямо или косвенно позволяют идентифицировать вас как личность. \n    К таким данным относятся, в частности:\n    • ФИО (имя, фамилия, отчество);  \n    • номер телефона;  \n    • адрес электронной почты;  \n    • адрес проживания или регистрации;  \n    • данные удостоверения личности (паспорт, ID-карта и т.д.);  \n    • VIN-код автомобиля или регистрационные номера;  \n    • IP-адрес или данные об устройстве при использовании онлайн-сервисов.\n    \n    Личная информация может собираться нами при обращении в сервисные центры, использовании веб-сайта, участии в опросах или акциях, а также при оформлении заявок на услуги.`\n  }, {\n    title: 'Является ли предоставление персональной информации добровольным или обязательным?',\n    content: `В зависимости от характера запрашиваемой услуги, предоставление личной информации может быть как обязательным, так и добровольным.\n    \n    В случаях, когда такие данные необходимы для выполнения наших юридических или договорных обязательств (например, обработка заявки, регистрация на сервис, выдача гарантии и т.п.), предоставление персональной информации является обязательным. Без этих данных мы не сможем предоставить соответствующую услугу.\n    \n    В других ситуациях (например, при участии в опросах, маркетинговых рассылках или добровольной регистрации на мероприятие), предоставление данных остается на ваше усмотрение и носит добровольный характер.\n    \n    Мы всегда указываем, какие поля являются обязательными, а какие — опциональными.`\n  }, {\n    title: 'Когда мы будем обрабатывать вашу личную информацию?',\n    content: `Мы будем обрабатывать Ваши персональные данные только в законных целях, связанных с нашим бизнесом, если применимо следующее:\nесли Вы дали на это согласие;\nесли лицо, законно уполномоченное Вами, законом или судом, дало на это согласие;\nесли это необходимо для заключения или исполнения договора, который мы заключили с Вами;\nесли это требуется или разрешается законом;\nесли это требуется для защиты или осуществления Ваших, наших или законных интересов третьей стороны.`\n  }, {\n    title: 'Причины, по которым нам необходимо обрабатывать вашу персональную информацию',\n    content: `Мы будем обрабатывать вашу личную информацию по следующим причинам:\n\nДля предоставления вам продуктов, товаров и услуг\nДля продвижения наших продуктов, товаров и услуг на ваш рынок.\nДля ответа на ваши запросы и жалобы.\nДля соблюдения законодательных, нормативных требований, требований риска и соответствия (включая директивы, санкции и правила), добровольных и недобровольных кодексов поведения и отраслевых соглашений или для выполнения требований по отчетности и запросов на информацию.\nДля проведения рыночных и поведенческих исследований, включая оценку и анализ, чтобы определить, соответствуете ли вы требованиям для продуктов и услуг или определить ваш кредитный или страховой риск.\nДля разработки, тестирования и улучшения продуктов и услуг для вас.\nДля исторических, статистических и исследовательских целей, таких как сегментация рынка.\nДля обработки платежных инструментов.\nДля создания, изготовления и печати платежных документов (например, расчетного листка)\nДля того, чтобы мы могли доставлять вам товары, документы или уведомления.\nДля обеспечения безопасности, проверки личности и проверки точности вашей личной информации.\nДля связи с вами и выполнения ваших инструкций и запросов.\nДля опросов удовлетворенности клиентов, рекламных предложений,\nандеррайтинга и администрирования страхования и страхования.\nДля обработки, рассмотрения или оценки страховых или гарантийных претензий.\nДля предоставления страховых и гарантийных полисов, продуктов и сопутствующих услуг.\nДля предоставления вам возможности участвовать в программах лояльности клиентов, определения вашего соответствия критериям участия, накопления бонусных баллов, определения уровня вашего вознаграждения, отслеживания вашего покупательского поведения у наших партнеров по программе лояльности для начисления корректных баллов или информирования вас о продуктах, товарах и услугах, которые могут вас заинтересовать, а также для информирования наших партнеров по программе лояльности о вашем покупательском поведении.\nДля предоставления вам возможности участвовать в программах и услугах с добавленной стоимостью и пользоваться ими.\nДля оценки наших кредитных и страховых рисков; и/или\nдля любых других связанных целей.`\n  }, {\n    title: 'Когда, как и с кем мы делимся вашей личной информацией?',\n    content: `Как правило, мы передаем ваши персональные данные только в следующих случаях:\n\nесли вы дали на это согласие;\nесли это необходимо для заключения или исполнения договора, заключённого между нами;\nесли это требуется законом; и/или\nесли это необходимо для защиты или реализации ваших, наших или третьих лиц законных интересов.`\n  }, {\n    title: 'Как мы защищаем вашу личную информацию?',\n    content: `Мы предпримем необходимые и разумные технические и организационные меры для защиты ваших персональных данных в соответствии с передовыми отраслевыми практиками. Наши меры безопасности (включая физические, технологические и процессуальные гарантии) будут надлежащими и разумными. Это включает в себя следующее:\n\nобеспечение безопасности наших систем (например, мониторинг доступа и использования);\nбезопасное хранение наших записей;\nконтроль доступа к нашим зданиям, системам и/или записям;\nбезопасное уничтожение или удаление записей.\nОбеспечить соблюдение передовых практик.`\n  }, {\n    title: 'Наша политика в отношении файлов cookie',\n    content: `Файл cookie — это небольшой фрагмент данных, отправляемый нашими веб-сайтами или приложениями на жесткий диск вашего компьютера или устройства, а также в интернет-браузер, где он сохраняется. Файл cookie содержит информацию, персонализирующую ваш опыт использования наших веб-сайтов или приложений, и может улучшить ваш опыт использования веб-сайтов или приложений. Файл cookie также идентифицирует ваше устройство, например, компьютер или смартфон.\n\nИспользуя наши веб-сайты или приложения, вы соглашаетесь с тем, что файлы cookie могут быть пересланы с соответствующего веб-сайта или приложения на ваш компьютер или устройство. Файл cookie позволит нам узнать, посещали ли вы веб-сайт или приложение ранее, и идентифицировать вас. Мы также можем использовать файл cookie для предотвращения мошенничества и аналитики.`\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u041F\\u041E\\u041B\\u0418\\u0422\\u0418\\u041A\\u0410 \\u041A\\u041E\\u041D\\u0424\\u0418\\u0414\\u0415\\u041D\\u0426\\u0418\\u0410\\u041B\\u042C\\u041D\\u041E\\u0421\\u0422\\u0418\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"redLine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.content,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"box\",\n            children: sections.map((section, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.accordion,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleSection(index),\n                className: styles.accordionHeader,\n                children: [section.title, /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: openIndex === index ? '-' : '+'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `${styles.accordionContentWrapper} ${openIndex === index ? styles.open : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.accordionContent,\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: section.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Privacy, \"XKW7q3geh1OirsAKDik/mPqTAEA=\");\n_c = Privacy;\nexport default Privacy;\nvar _c;\n$RefreshReg$(_c, \"Privacy\");", "map": {"version": 3, "names": ["useEffect", "useState", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Privacy", "_s", "loading", "setLoading", "openIndex", "setOpenIndex", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "toggleSection", "index", "sections", "title", "content", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "section", "accordion", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "accordionContentWrapper", "open", "accordion<PERSON>ontent", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Privacy/Privacy.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport styles from './privacy.module.css';\n\nconst Privacy = () => {\n  const [loading, setLoading] = useState(true);\n  const [openIndex, setOpenIndex] = useState(null);\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  const toggleSection = (index) => {\n    setOpenIndex(openIndex === index ? null : index);\n  };\n\n  const sections = [\n    {\n      title: 'Введение',\n      content: `Компания Haval Motors Tajikistan уважает право каждого пользователя на конфиденциальность и защиту персональных данных. Настоящая Политика конфиденциальности разработана с целью объяснить, какие персональные данные мы собираем, как мы их используем, храним и защищаем, а также какие у вас есть права в отношении этой информации.\nВ условиях стремительного развития цифровых технологий и расширения онлайн-сервисов защита персональных данных становится крайне важной задачей. Мы строго соблюдаем требования действующего законодательства Республики Таджикистан в области защиты персональных данных и международные стандарты, включая принципы законности, прозрачности, конфиденциальности и безопасности.\nИспользуя наш веб-сайт, услуги, продукты или предоставляя нам информацию иным способом, вы соглашаетесь с условиями настоящей Политики конфиденциальности.`,\n    },\n    {\n      title: 'Что такое личная информация?',\n      content: `Личная информация — это любые данные, которые прямо или косвенно позволяют идентифицировать вас как личность. \n    К таким данным относятся, в частности:\n    • ФИО (имя, фамилия, отчество);  \n    • номер телефона;  \n    • адрес электронной почты;  \n    • адрес проживания или регистрации;  \n    • данные удостоверения личности (паспорт, ID-карта и т.д.);  \n    • VIN-код автомобиля или регистрационные номера;  \n    • IP-адрес или данные об устройстве при использовании онлайн-сервисов.\n    \n    Личная информация может собираться нами при обращении в сервисные центры, использовании веб-сайта, участии в опросах или акциях, а также при оформлении заявок на услуги.`,\n    },\n    {\n      title:\n        'Является ли предоставление персональной информации добровольным или обязательным?',\n      content: `В зависимости от характера запрашиваемой услуги, предоставление личной информации может быть как обязательным, так и добровольным.\n    \n    В случаях, когда такие данные необходимы для выполнения наших юридических или договорных обязательств (например, обработка заявки, регистрация на сервис, выдача гарантии и т.п.), предоставление персональной информации является обязательным. Без этих данных мы не сможем предоставить соответствующую услугу.\n    \n    В других ситуациях (например, при участии в опросах, маркетинговых рассылках или добровольной регистрации на мероприятие), предоставление данных остается на ваше усмотрение и носит добровольный характер.\n    \n    Мы всегда указываем, какие поля являются обязательными, а какие — опциональными.`,\n    },\n    {\n      title: 'Когда мы будем обрабатывать вашу личную информацию?',\n      content: `Мы будем обрабатывать Ваши персональные данные только в законных целях, связанных с нашим бизнесом, если применимо следующее:\nесли Вы дали на это согласие;\nесли лицо, законно уполномоченное Вами, законом или судом, дало на это согласие;\nесли это необходимо для заключения или исполнения договора, который мы заключили с Вами;\nесли это требуется или разрешается законом;\nесли это требуется для защиты или осуществления Ваших, наших или законных интересов третьей стороны.`,\n    },\n    {\n      title:\n        'Причины, по которым нам необходимо обрабатывать вашу персональную информацию',\n      content: `Мы будем обрабатывать вашу личную информацию по следующим причинам:\n\nДля предоставления вам продуктов, товаров и услуг\nДля продвижения наших продуктов, товаров и услуг на ваш рынок.\nДля ответа на ваши запросы и жалобы.\nДля соблюдения законодательных, нормативных требований, требований риска и соответствия (включая директивы, санкции и правила), добровольных и недобровольных кодексов поведения и отраслевых соглашений или для выполнения требований по отчетности и запросов на информацию.\nДля проведения рыночных и поведенческих исследований, включая оценку и анализ, чтобы определить, соответствуете ли вы требованиям для продуктов и услуг или определить ваш кредитный или страховой риск.\nДля разработки, тестирования и улучшения продуктов и услуг для вас.\nДля исторических, статистических и исследовательских целей, таких как сегментация рынка.\nДля обработки платежных инструментов.\nДля создания, изготовления и печати платежных документов (например, расчетного листка)\nДля того, чтобы мы могли доставлять вам товары, документы или уведомления.\nДля обеспечения безопасности, проверки личности и проверки точности вашей личной информации.\nДля связи с вами и выполнения ваших инструкций и запросов.\nДля опросов удовлетворенности клиентов, рекламных предложений,\nандеррайтинга и администрирования страхования и страхования.\nДля обработки, рассмотрения или оценки страховых или гарантийных претензий.\nДля предоставления страховых и гарантийных полисов, продуктов и сопутствующих услуг.\nДля предоставления вам возможности участвовать в программах лояльности клиентов, определения вашего соответствия критериям участия, накопления бонусных баллов, определения уровня вашего вознаграждения, отслеживания вашего покупательского поведения у наших партнеров по программе лояльности для начисления корректных баллов или информирования вас о продуктах, товарах и услугах, которые могут вас заинтересовать, а также для информирования наших партнеров по программе лояльности о вашем покупательском поведении.\nДля предоставления вам возможности участвовать в программах и услугах с добавленной стоимостью и пользоваться ими.\nДля оценки наших кредитных и страховых рисков; и/или\nдля любых других связанных целей.`,\n    },\n    {\n      title: 'Когда, как и с кем мы делимся вашей личной информацией?',\n      content: `Как правило, мы передаем ваши персональные данные только в следующих случаях:\n\nесли вы дали на это согласие;\nесли это необходимо для заключения или исполнения договора, заключённого между нами;\nесли это требуется законом; и/или\nесли это необходимо для защиты или реализации ваших, наших или третьих лиц законных интересов.`,\n    },\n    {\n      title: 'Как мы защищаем вашу личную информацию?',\n      content: `Мы предпримем необходимые и разумные технические и организационные меры для защиты ваших персональных данных в соответствии с передовыми отраслевыми практиками. Наши меры безопасности (включая физические, технологические и процессуальные гарантии) будут надлежащими и разумными. Это включает в себя следующее:\n\nобеспечение безопасности наших систем (например, мониторинг доступа и использования);\nбезопасное хранение наших записей;\nконтроль доступа к нашим зданиям, системам и/или записям;\nбезопасное уничтожение или удаление записей.\nОбеспечить соблюдение передовых практик.`,\n    },\n    {\n      title: 'Наша политика в отношении файлов cookie',\n      content: `Файл cookie — это небольшой фрагмент данных, отправляемый нашими веб-сайтами или приложениями на жесткий диск вашего компьютера или устройства, а также в интернет-браузер, где он сохраняется. Файл cookie содержит информацию, персонализирующую ваш опыт использования наших веб-сайтов или приложений, и может улучшить ваш опыт использования веб-сайтов или приложений. Файл cookie также идентифицирует ваше устройство, например, компьютер или смартфон.\n\nИспользуя наши веб-сайты или приложения, вы соглашаетесь с тем, что файлы cookie могут быть пересланы с соответствующего веб-сайта или приложения на ваш компьютер или устройство. Файл cookie позволит нам узнать, посещали ли вы веб-сайт или приложение ранее, и идентифицировать вас. Мы также можем использовать файл cookie для предотвращения мошенничества и аналитики.`,\n    },\n  ];\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <section className=\"container\">\n            <h1 className=\"title\">\n              <strong>\n                ПОЛИТИКА КОНФИДЕНЦИАЛЬНОСТИ\n              </strong>\n            </h1>\n            <i className=\"redLine\"></i>\n            <div className={styles.content}>\n              <div className=\"box\">\n                {sections.map((section, index) => (\n                  <div key={index} className={styles.accordion}>\n                    <button\n                      onClick={() => toggleSection(index)}\n                      className={styles.accordionHeader}\n                    >\n                      {section.title}\n                      <span>{openIndex === index ? '-' : '+'}</span>\n                    </button>\n                    <div\n                      className={`${styles.accordionContentWrapper} ${\n                        openIndex === index ? styles.open : ''\n                      }`}\n                    >\n                      <div className={styles.accordionContent}>\n                        <p>{section.content}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </section>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Privacy;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAEhDD,SAAS,CAAC,MAAM;IACda,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BV,UAAU,CAAC,KAAK,CAAC;MACjBK,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,aAAa,GAAIC,KAAK,IAAK;IAC/BX,YAAY,CAACD,SAAS,KAAKY,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EAClD,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE;AACf;AACA;EACI,CAAC,EACD;IACED,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EACH,mFAAmF;IACrFC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EAAE,qDAAqD;IAC5DC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EACH,8EAA8E;IAChFC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EAAE,yDAAyD;IAChEC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EAAE,yCAAyC;IAChDC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;EACI,CAAC,EACD;IACED,KAAK,EAAE,yCAAyC;IAChDC,OAAO,EAAE;AACf;AACA;EACI,CAAC,CACF;EAED,oBACEtB,OAAA,CAAAE,SAAA;IAAAqB,QAAA,EACGlB,OAAO,gBACNL,OAAA;MAAKwB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BvB,OAAA;QAAKwB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAEN5B,OAAA;MAAKwB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBvB,OAAA;QAASwB,SAAS,EAAC,WAAW;QAAAD,QAAA,gBAC5BvB,OAAA;UAAIwB,SAAS,EAAC,OAAO;UAAAD,QAAA,eACnBvB,OAAA;YAAAuB,QAAA,EAAQ;UAER;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACL5B,OAAA;UAAGwB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B5B,OAAA;UAAKwB,SAAS,EAAE1B,MAAM,CAACwB,OAAQ;UAAAC,QAAA,eAC7BvB,OAAA;YAAKwB,SAAS,EAAC,KAAK;YAAAD,QAAA,EACjBH,QAAQ,CAACS,GAAG,CAAC,CAACC,OAAO,EAAEX,KAAK,kBAC3BnB,OAAA;cAAiBwB,SAAS,EAAE1B,MAAM,CAACiC,SAAU;cAAAR,QAAA,gBAC3CvB,OAAA;gBACEgC,OAAO,EAAEA,CAAA,KAAMd,aAAa,CAACC,KAAK,CAAE;gBACpCK,SAAS,EAAE1B,MAAM,CAACmC,eAAgB;gBAAAV,QAAA,GAEjCO,OAAO,CAACT,KAAK,eACdrB,OAAA;kBAAAuB,QAAA,EAAOhB,SAAS,KAAKY,KAAK,GAAG,GAAG,GAAG;gBAAG;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACT5B,OAAA;gBACEwB,SAAS,EAAE,GAAG1B,MAAM,CAACoC,uBAAuB,IAC1C3B,SAAS,KAAKY,KAAK,GAAGrB,MAAM,CAACqC,IAAI,GAAG,EAAE,EACrC;gBAAAZ,QAAA,eAEHvB,OAAA;kBAAKwB,SAAS,EAAE1B,MAAM,CAACsC,gBAAiB;kBAAAb,QAAA,eACtCvB,OAAA;oBAAAuB,QAAA,EAAIO,OAAO,CAACR;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAhBET,KAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EACN,gBACD,CAAC;AAEP,CAAC;AAACxB,EAAA,CAjKID,OAAO;AAAAkC,EAAA,GAAPlC,OAAO;AAmKb,eAAeA,OAAO;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}