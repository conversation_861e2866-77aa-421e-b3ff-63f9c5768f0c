{"ast": null, "code": "const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\nexport { floatRegex };", "map": {"version": 3, "names": ["floatRegex"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs"], "sourcesContent": ["const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,6BAA6B;AAEhD,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}