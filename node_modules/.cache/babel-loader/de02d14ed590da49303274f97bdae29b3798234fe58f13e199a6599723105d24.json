{"ast": null, "code": "import { isMotionValue } from 'motion-dom';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n  return isMotionValue(value) ? value.get() : value;\n}\nexport { resolveMotionValue };", "map": {"version": 3, "names": ["isMotionValue", "resolveMotionValue", "value", "get"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n    return isMotionValue(value) ? value.get() : value;\n}\n\nexport { resolveMotionValue };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;;AAE1C;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,OAAOF,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,CAACC,GAAG,CAAC,CAAC,GAAGD,KAAK;AACrD;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}