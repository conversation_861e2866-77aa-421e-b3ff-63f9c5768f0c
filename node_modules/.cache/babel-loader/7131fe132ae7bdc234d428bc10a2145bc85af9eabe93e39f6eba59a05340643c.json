{"ast": null, "code": "import _objectSpread from\"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import Select from'react-select';import styles from'./form.module.css';import{useMask}from'@react-input/mask';import Notification from'../../components/Notification/Notification';import{sanitizeAndValidateForm,formRateLimiter}from'../../utils/validation';import{fetchModels,submitFeedback}from'../../utils/api';/**\n * Contact form component with validation and security features\n * @component\n * @param {Object} props - Component props\n * @param {string} props.formType - Type of form ('contact', 'test-drive', 'service', etc.)\n * @param {string} props.title - Form title (optional)\n * @param {string} props.defaultModel - Default selected model (optional)\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Form=_ref=>{let{formType='contact',title,defaultModel}=_ref;const[formData,setFormData]=useState({firstName:'',lastName:'',phone:'',topic:null,message:'',consent:false});const[notification,setNotification]=useState({message:'',type:''});const[carOptions,setCarOptions]=useState([]);const[btnLoading,setBtnLoading]=useState(false);const[formErrors,setFormErrors]=useState({});const[isSubmitting,setIsSubmitting]=useState(false);/**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */const showNotification=useCallback(function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);},[]);const phoneRef=useMask({mask:'+992 ___-__-__-__',replacement:{_:/\\d/}});/**\n   * Fetch car models on component mount and set default model\n   */useEffect(()=>{const loadCarModels=async()=>{try{const models=await fetchModels();const formattedOptions=models.map(model=>({value:model.slug||model.title,label:model.title}));setCarOptions(formattedOptions);// Set default model if provided\nif(defaultModel){const defaultOption=formattedOptions.find(option=>option.value===defaultModel||option.label===defaultModel);if(defaultOption){setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{topic:defaultOption}));}}}catch(error){showNotification(error.message,'error');}};loadCarModels();},[showNotification,defaultModel]);/**\n   * Handle input changes and clear field-specific errors\n   * @param {Event} e - Input change event\n   */const handleChange=useCallback(e=>{const{name,value,type,checked}=e.target;const newValue=type==='checkbox'?checked:value;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:newValue}));// Clear field-specific error when user starts typing\nif(formErrors[name]){setFormErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));}},[formErrors]);/**\n   * Handle select dropdown changes\n   * @param {Object} selectedOption - Selected option from react-select\n   */const handleSelectChange=useCallback(selectedOption=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{topic:selectedOption}));// Clear topic error when user selects an option\nif(formErrors.topic){setFormErrors(prev=>_objectSpread(_objectSpread({},prev),{},{topic:''}));}},[formErrors.topic]);/**\n   * Handle form submission with validation and security checks\n   * @param {Event} e - Form submit event\n   */const handleSubmit=async e=>{e.preventDefault();// Prevent double submission\nif(isSubmitting)return;// Check rate limiting\nif(!formRateLimiter.isAllowed()){showNotification('Слишком много попыток отправки. Подождите минуту.','error');return;}setIsSubmitting(true);setBtnLoading(true);setFormErrors({});try{var _sanitizedData$topic;// Validate and sanitize form data\nconst{data:sanitizedData,errors,isValid}=sanitizeAndValidateForm(formData);if(!isValid){setFormErrors(errors);showNotification('Пожалуйста, исправьте ошибки в форме','error');return;}// Prepare payload for API\nconst payload={firstName:sanitizedData.firstName,lastName:sanitizedData.lastName,phone:sanitizedData.phone,topic:((_sanitizedData$topic=sanitizedData.topic)===null||_sanitizedData$topic===void 0?void 0:_sanitizedData$topic.value)||'',message:sanitizedData.message,consent:sanitizedData.consent,formType:formType// Include form type for API\n};// Submit form\nawait submitFeedback(payload);// Success\nshowNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.','success');// Reset form\nsetFormData({firstName:'',lastName:'',phone:'',topic:null,message:'',consent:false});setFormErrors({});}catch(error){showNotification(error.message,'error');}finally{setBtnLoading(false);setIsSubmitting(false);}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),title&&/*#__PURE__*/_jsx(\"h2\",{className:styles.formTitle,children:title}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:styles.form,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.row,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"firstName\",className:styles.label,children:[\"\\u0418\\u043C\\u044F\",' ',/*#__PURE__*/_jsx(\"span\",{className:styles.required,\"aria-label\":\"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{id:\"firstName\",type:\"text\",name:\"firstName\",value:formData.firstName,className:\"\".concat(styles.input,\" \").concat(formErrors.firstName?styles.inputError:''),required:true,onChange:handleChange,minLength:\"2\",maxLength:\"50\",\"aria-describedby\":formErrors.firstName?'firstName-error':undefined,\"aria-invalid\":formErrors.firstName?'true':'false',disabled:isSubmitting}),formErrors.firstName&&/*#__PURE__*/_jsx(\"div\",{id:\"firstName-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.firstName})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"lastName\",className:styles.label,children:\"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"input\",{id:\"lastName\",type:\"text\",name:\"lastName\",value:formData.lastName,className:\"\".concat(styles.input,\" \").concat(formErrors.lastName?styles.inputError:''),onChange:handleChange,minLength:\"2\",maxLength:\"50\",\"aria-describedby\":formErrors.lastName?'lastName-error':undefined,\"aria-invalid\":formErrors.lastName?'true':'false',disabled:isSubmitting}),formErrors.lastName&&/*#__PURE__*/_jsx(\"div\",{id:\"lastName-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.lastName})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.row,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"phone\",className:styles.label,children:[\"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430\",' ',/*#__PURE__*/_jsx(\"span\",{className:styles.required,\"aria-label\":\"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{id:\"phone\",type:\"tel\",name:\"phone\",value:formData.phone,className:\"\".concat(styles.input,\" \").concat(formErrors.phone?styles.inputError:''),required:true,ref:phoneRef,onChange:handleChange,placeholder:\"+992 XXX-XX-XX-XX\",\"aria-describedby\":formErrors.phone?'phone-error':'phone-help',\"aria-invalid\":formErrors.phone?'true':'false',disabled:isSubmitting}),/*#__PURE__*/_jsx(\"div\",{id:\"phone-help\",className:styles.helpText,children:\"\\u0424\\u043E\\u0440\\u043C\\u0430\\u0442: +992 XXX-XX-XX-XX\"}),formErrors.phone&&/*#__PURE__*/_jsx(\"div\",{id:\"phone-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.phone})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"message\",className:styles.label,children:\"\\u0412\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"message\",name:\"message\",value:formData.message,className:\"\".concat(styles.input,\" \").concat(styles.textarea,\" \").concat(formErrors.message?styles.inputError:''),onChange:handleChange,maxLength:\"1000\",rows:\"1\",placeholder:\"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435 (\\u043D\\u0435\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E)\",\"aria-describedby\":formErrors.message?'message-error':'message-help',\"aria-invalid\":formErrors.message?'true':'false',disabled:isSubmitting}),/*#__PURE__*/_jsx(\"div\",{id:\"message-help\",className:styles.helpText,children:\"\\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0443\\u043C 1000 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432\"}),formErrors.message&&/*#__PURE__*/_jsx(\"div\",{id:\"message-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.message})]})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.row,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.inputGroup,children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"topic\",className:styles.label,children:[\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\",' ',/*#__PURE__*/_jsx(\"span\",{className:styles.required,\"aria-label\":\"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.selectWrapper,children:[/*#__PURE__*/_jsx(Select,{inputId:\"topic\",classNamePrefix:\"react-select\",options:carOptions,placeholder:\"\\u0412\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\",onChange:handleSelectChange,value:formData.topic,isSearchable:false,isDisabled:isSubmitting,menuPortalTarget:document.body,\"aria-describedby\":formErrors.topic?'topic-error':undefined,\"aria-invalid\":formErrors.topic?'true':'false',styles:{container:provided=>_objectSpread(_objectSpread({},provided),{},{width:'100%'}),control:(provided,state)=>_objectSpread(_objectSpread({},provided),{},{width:'100%',paddingLeft:'8px',height:'58px',borderRadius:'2px',border:\"1px solid \".concat(state.isFocused?'#d7000f':'#8e8e93'),fontSize:'16px',backgroundColor:'#fff',boxShadow:'none',zIndex:1,transition:'all 0.2s ease','&:hover':{borderColor:state.isFocused?'#d7000f':'#8e8e93'}}),placeholder:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#888'}),singleValue:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#000'}),indicatorsContainer:provided=>_objectSpread(_objectSpread({},provided),{},{paddingRight:'10px'}),dropdownIndicator:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#8e8e93'}),menuPortal:base=>_objectSpread(_objectSpread({},base),{},{zIndex:100}),menu:provided=>_objectSpread(_objectSpread({},provided),{},{border:'1px solid #8e8e93',borderRadius:'2px',boxShadow:'none',marginTop:'4px',backgroundColor:'#fff',zIndex:100}),menuList:provided=>_objectSpread(_objectSpread({},provided),{},{padding:0}),option:(provided,state)=>_objectSpread(_objectSpread({},provided),{},{padding:'14px 16px',fontSize:'14px',backgroundColor:state.isFocused?'#f2f2f2':'#fff',color:'#000',cursor:'pointer','&:active':{backgroundColor:'#e6e6e6'}})}}),formErrors.topic&&/*#__PURE__*/_jsx(\"div\",{id:\"topic-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.topic})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.checkboxGroup,children:[/*#__PURE__*/_jsxs(\"label\",{className:styles.checkboxWrapper,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"consent\",required:true,checked:formData.consent,onChange:handleChange,className:styles.checkbox,\"aria-describedby\":formErrors.consent?'consent-error':'consent-help',\"aria-invalid\":formErrors.consent?'true':'false',disabled:isSubmitting}),/*#__PURE__*/_jsx(\"span\",{className:styles.checkboxLabel,children:\"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"})]}),/*#__PURE__*/_jsx(\"div\",{id:\"consent-help\",className:styles.helpText,children:\"\\u041E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E \\u0434\\u043B\\u044F \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043A\\u0438 \\u0444\\u043E\\u0440\\u043C\\u044B\"}),formErrors.consent&&/*#__PURE__*/_jsx(\"div\",{id:\"consent-error\",className:styles.errorMessage,role:\"alert\",children:formErrors.consent})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"\".concat(styles.button,\" \").concat(isSubmitting?styles.buttonLoading:''),disabled:isSubmitting,\"aria-describedby\":\"submit-help\",children:btnLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:styles.spinner,\"aria-hidden\":\"true\"}),\"\\u041E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043A\\u0430...\"]}):'Отправить'}),/*#__PURE__*/_jsx(\"div\",{id:\"submit-help\",className:styles.helpText,children:\"\\u041D\\u0430\\u0436\\u0438\\u043C\\u0430\\u044F \\u043A\\u043D\\u043E\\u043F\\u043A\\u0443, \\u0432\\u044B \\u0441\\u043E\\u0433\\u043B\\u0430\\u0448\\u0430\\u0435\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u043E\\u0439 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"})]})]});};export default Form;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Select", "styles", "useMask", "Notification", "sanitizeAndValidateForm", "formRateLimiter", "fetchModels", "submitFeedback", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Form", "_ref", "formType", "title", "defaultModel", "formData", "setFormData", "firstName", "lastName", "phone", "topic", "message", "consent", "notification", "setNotification", "type", "carOptions", "setCarOptions", "btnLoading", "setBtnLoading", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "arguments", "length", "undefined", "setTimeout", "phoneRef", "mask", "replacement", "_", "loadCarModels", "models", "formattedOptions", "map", "model", "value", "slug", "label", "defaultOption", "find", "option", "prev", "_objectSpread", "error", "handleChange", "e", "name", "checked", "target", "newValue", "handleSelectChange", "selectedOption", "handleSubmit", "preventDefault", "isAllowed", "_sanitizedData$topic", "data", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "children", "className", "formTitle", "onSubmit", "form", "row", "inputGroup", "htmlFor", "required", "id", "concat", "input", "inputError", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "disabled", "errorMessage", "role", "ref", "placeholder", "helpText", "textarea", "rows", "selectWrapper", "inputId", "classNamePrefix", "options", "isSearchable", "isDisabled", "menuPortalTarget", "document", "body", "container", "provided", "width", "control", "state", "paddingLeft", "height", "borderRadius", "border", "isFocused", "fontSize", "backgroundColor", "boxShadow", "zIndex", "transition", "borderColor", "color", "singleValue", "indicatorsContainer", "paddingRight", "dropdownIndicator", "menuPortal", "base", "menu", "marginTop", "menuList", "padding", "cursor", "checkboxGroup", "checkboxWrapper", "checkbox", "checkboxLabel", "button", "buttonLoading", "spinner"], "sources": ["/var/www/html/gwm.tj/src/components/Form/Form.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../components/Notification/Notification';\nimport {\n  sanitizeAndValidateForm,\n  formRateLimiter,\n} from '../../utils/validation';\nimport { fetchModels, submitFeedback } from '../../utils/api';\n\n/**\n * Contact form component with validation and security features\n * @component\n * @param {Object} props - Component props\n * @param {string} props.formType - Type of form ('contact', 'test-drive', 'service', etc.)\n * @param {string} props.title - Form title (optional)\n * @param {string} props.defaultModel - Default selected model (optional)\n */\nconst Form = ({ formType = 'contact', title, defaultModel }) => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false,\n  });\n\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [carOptions, setCarOptions] = useState([]);\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  /**\n   * Fetch car models on component mount and set default model\n   */\n  useEffect(() => {\n    const loadCarModels = async () => {\n      try {\n        const models = await fetchModels();\n        const formattedOptions = models.map((model) => ({\n          value: model.slug || model.title,\n          label: model.title,\n        }));\n        setCarOptions(formattedOptions);\n\n        // Set default model if provided\n        if (defaultModel) {\n          const defaultOption = formattedOptions.find(\n            (option) =>\n              option.value === defaultModel || option.label === defaultModel\n          );\n          if (defaultOption) {\n            setFormData((prev) => ({\n              ...prev,\n              topic: defaultOption,\n            }));\n          }\n        }\n      } catch (error) {\n        showNotification(error.message, 'error');\n      }\n    };\n\n    loadCarModels();\n  }, [showNotification, defaultModel]);\n\n  /**\n   * Handle input changes and clear field-specific errors\n   * @param {Event} e - Input change event\n   */\n  const handleChange = useCallback(\n    (e) => {\n      const { name, value, type, checked } = e.target;\n      const newValue = type === 'checkbox' ? checked : value;\n\n      setFormData((prev) => ({\n        ...prev,\n        [name]: newValue,\n      }));\n\n      // Clear field-specific error when user starts typing\n      if (formErrors[name]) {\n        setFormErrors((prev) => ({\n          ...prev,\n          [name]: '',\n        }));\n      }\n    },\n    [formErrors]\n  );\n\n  /**\n   * Handle select dropdown changes\n   * @param {Object} selectedOption - Selected option from react-select\n   */\n  const handleSelectChange = useCallback(\n    (selectedOption) => {\n      setFormData((prev) => ({\n        ...prev,\n        topic: selectedOption,\n      }));\n\n      // Clear topic error when user selects an option\n      if (formErrors.topic) {\n        setFormErrors((prev) => ({\n          ...prev,\n          topic: '',\n        }));\n      }\n    },\n    [formErrors.topic]\n  );\n\n  /**\n   * Handle form submission with validation and security checks\n   * @param {Event} e - Form submit event\n   */\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification(\n        'Слишком много попыток отправки. Подождите минуту.',\n        'error'\n      );\n      return;\n    }\n\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n\n    try {\n      // Validate and sanitize form data\n      const {\n        data: sanitizedData,\n        errors,\n        isValid,\n      } = sanitizeAndValidateForm(formData);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: sanitizedData.topic?.value || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: formType, // Include form type for API\n      };\n\n      // Submit form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification(\n        'Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.',\n        'success'\n      );\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false,\n      });\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {title && <h2 className={styles.formTitle}>{title}</h2>}\n      <form onSubmit={handleSubmit} className={styles.form}>\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"firstName\" className={styles.label}>\n              Имя{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <input\n              id=\"firstName\"\n              type=\"text\"\n              name=\"firstName\"\n              value={formData.firstName}\n              className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n              required\n              onChange={handleChange}\n              minLength=\"2\"\n              maxLength=\"50\"\n              aria-describedby={\n                formErrors.firstName ? 'firstName-error' : undefined\n              }\n              aria-invalid={formErrors.firstName ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            {formErrors.firstName && (\n              <div\n                id=\"firstName-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.firstName}\n              </div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"lastName\" className={styles.label}>\n              Фамилия\n            </label>\n            <input\n              id=\"lastName\"\n              type=\"text\"\n              name=\"lastName\"\n              value={formData.lastName}\n              className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n              onChange={handleChange}\n              minLength=\"2\"\n              maxLength=\"50\"\n              aria-describedby={\n                formErrors.lastName ? 'lastName-error' : undefined\n              }\n              aria-invalid={formErrors.lastName ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            {formErrors.lastName && (\n              <div\n                id=\"lastName-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.lastName}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"phone\" className={styles.label}>\n              Номер мобильного телефона{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <input\n              id=\"phone\"\n              type=\"tel\"\n              name=\"phone\"\n              value={formData.phone}\n              className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n              required\n              ref={phoneRef}\n              onChange={handleChange}\n              placeholder=\"+992 XXX-XX-XX-XX\"\n              aria-describedby={formErrors.phone ? 'phone-error' : 'phone-help'}\n              aria-invalid={formErrors.phone ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <div id=\"phone-help\" className={styles.helpText}>\n              Формат: +992 XXX-XX-XX-XX\n            </div>\n            {formErrors.phone && (\n              <div\n                id=\"phone-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.phone}\n              </div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"message\" className={styles.label}>\n              Ваше сообщение\n            </label>\n            <textarea\n              id=\"message\"\n              name=\"message\"\n              value={formData.message}\n              className={`${styles.input} ${styles.textarea} ${formErrors.message ? styles.inputError : ''}`}\n              onChange={handleChange}\n              maxLength=\"1000\"\n              rows=\"1\"\n              placeholder=\"Введите ваше сообщение (необязательно)\"\n              aria-describedby={\n                formErrors.message ? 'message-error' : 'message-help'\n              }\n              aria-invalid={formErrors.message ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <div id=\"message-help\" className={styles.helpText}>\n              Максимум 1000 символов\n            </div>\n            {formErrors.message && (\n              <div\n                id=\"message-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.message}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"topic\" className={styles.label}>\n              Модель автомобиля{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <div className={styles.selectWrapper}>\n              <Select\n                inputId=\"topic\"\n                classNamePrefix=\"react-select\"\n                options={carOptions}\n                placeholder=\"Выберите модель автомобиля\"\n                onChange={handleSelectChange}\n                value={formData.topic}\n                isSearchable={false}\n                isDisabled={isSubmitting}\n                menuPortalTarget={document.body}\n                aria-describedby={formErrors.topic ? 'topic-error' : undefined}\n                aria-invalid={formErrors.topic ? 'true' : 'false'}\n                styles={{\n                  container: (provided) => ({\n                    ...provided,\n                    width: '100%',\n                  }),\n\n                  control: (provided, state) => ({\n                    ...provided,\n                    width: '100%',\n                    paddingLeft: '8px',\n                    height: '58px',\n                    borderRadius: '2px',\n                    border: `1px solid ${\n                      state.isFocused ? '#d7000f' : '#8e8e93'\n                    }`,\n                    fontSize: '16px',\n                    backgroundColor: '#fff',\n                    boxShadow: 'none',\n                    zIndex: 1,\n                    transition: 'all 0.2s ease',\n                    '&:hover': {\n                      borderColor: state.isFocused ? '#d7000f' : '#8e8e93',\n                    },\n                  }),\n\n                  placeholder: (provided) => ({\n                    ...provided,\n                    color: '#888',\n                  }),\n\n                  singleValue: (provided) => ({\n                    ...provided,\n                    color: '#000',\n                  }),\n\n                  indicatorsContainer: (provided) => ({\n                    ...provided,\n                    paddingRight: '10px',\n                  }),\n\n                  dropdownIndicator: (provided) => ({\n                    ...provided,\n                    color: '#8e8e93',\n                  }),\n\n                  menuPortal: (base) => ({\n                    ...base,\n                    zIndex: 100,\n                  }),\n\n                  menu: (provided) => ({\n                    ...provided,\n                    border: '1px solid #8e8e93',\n                    borderRadius: '2px',\n                    boxShadow: 'none',\n                    marginTop: '4px',\n                    backgroundColor: '#fff',\n                    zIndex: 100,\n                  }),\n\n                  menuList: (provided) => ({\n                    ...provided,\n                    padding: 0,\n                  }),\n\n                  option: (provided, state) => ({\n                    ...provided,\n                    padding: '14px 16px',\n                    fontSize: '14px',\n                    backgroundColor: state.isFocused ? '#f2f2f2' : '#fff',\n                    color: '#000',\n                    cursor: 'pointer',\n                    '&:active': {\n                      backgroundColor: '#e6e6e6',\n                    },\n                  }),\n                }}\n              />\n              {formErrors.topic && (\n                <div\n                  id=\"topic-error\"\n                  className={styles.errorMessage}\n                  role=\"alert\"\n                >\n                  {formErrors.topic}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className={styles.checkboxGroup}>\n          <label className={styles.checkboxWrapper}>\n            <input\n              type=\"checkbox\"\n              name=\"consent\"\n              required\n              checked={formData.consent}\n              onChange={handleChange}\n              className={styles.checkbox}\n              aria-describedby={\n                formErrors.consent ? 'consent-error' : 'consent-help'\n              }\n              aria-invalid={formErrors.consent ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <span className={styles.checkboxLabel}>\n              Я даю согласие на обработку моих персональных данных\n            </span>\n          </label>\n          <div id=\"consent-help\" className={styles.helpText}>\n            Обязательно для отправки формы\n          </div>\n          {formErrors.consent && (\n            <div\n              id=\"consent-error\"\n              className={styles.errorMessage}\n              role=\"alert\"\n            >\n              {formErrors.consent}\n            </div>\n          )}\n        </div>\n\n        <button\n          type=\"submit\"\n          className={`${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`}\n          disabled={isSubmitting}\n          aria-describedby=\"submit-help\"\n        >\n          {btnLoading ? (\n            <>\n              <span className={styles.spinner} aria-hidden=\"true\"></span>\n              Отправка...\n            </>\n          ) : (\n            'Отправить'\n          )}\n        </button>\n        <div id=\"submit-help\" className={styles.helpText}>\n          Нажимая кнопку, вы соглашаетесь с обработкой персональных данных\n        </div>\n      </form>\n    </>\n  );\n};\n\nexport default Form;\n"], "mappings": "yGAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,mBAAmB,CAC3C,MAAO,CAAAC,YAAY,KAAM,4CAA4C,CACrE,OACEC,uBAAuB,CACvBC,eAAe,KACV,wBAAwB,CAC/B,OAASC,WAAW,CAAEC,cAAc,KAAQ,iBAAiB,CAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAPA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAQA,KAAM,CAAAC,IAAI,CAAGC,IAAA,EAAmD,IAAlD,CAAEC,QAAQ,CAAG,SAAS,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAAH,IAAA,CACzD,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAC,CACvCwB,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CAEF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAC,CAAE4B,OAAO,CAAE,EAAE,CAAEI,IAAI,CAAE,EAAG,CAAC,CAAC,CAC3E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACmC,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACqC,UAAU,CAAEC,aAAa,CAAC,CAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACF;AACA;AACA;AACA,KACE,KAAM,CAAAyC,gBAAgB,CAAGvC,WAAW,CAAC,SAAC0B,OAAO,CAAuB,IAArB,CAAAI,IAAI,CAAAU,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CAC7DX,eAAe,CAAC,CAAEH,OAAO,CAAEI,IAAK,CAAC,CAAC,CAClCa,UAAU,CAAC,IAAMd,eAAe,CAAC,CAAEH,OAAO,CAAE,EAAE,CAAEI,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAc,QAAQ,CAAGzC,OAAO,CAAC,CACvB0C,IAAI,CAAE,mBAAmB,CACzBC,WAAW,CAAE,CAAEC,CAAC,CAAE,IAAK,CACzB,CAAC,CAAC,CAEF;AACF;AACA,KACEhD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA1C,WAAW,CAAC,CAAC,CAClC,KAAM,CAAA2C,gBAAgB,CAAGD,MAAM,CAACE,GAAG,CAAEC,KAAK,GAAM,CAC9CC,KAAK,CAAED,KAAK,CAACE,IAAI,EAAIF,KAAK,CAAClC,KAAK,CAChCqC,KAAK,CAAEH,KAAK,CAAClC,KACf,CAAC,CAAC,CAAC,CACHc,aAAa,CAACkB,gBAAgB,CAAC,CAE/B;AACA,GAAI/B,YAAY,CAAE,CAChB,KAAM,CAAAqC,aAAa,CAAGN,gBAAgB,CAACO,IAAI,CACxCC,MAAM,EACLA,MAAM,CAACL,KAAK,GAAKlC,YAAY,EAAIuC,MAAM,CAACH,KAAK,GAAKpC,YACtD,CAAC,CACD,GAAIqC,aAAa,CAAE,CACjBnC,WAAW,CAAEsC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPlC,KAAK,CAAE+B,aAAa,EACpB,CAAC,CACL,CACF,CACF,CAAE,MAAOK,KAAK,CAAE,CACdtB,gBAAgB,CAACsB,KAAK,CAACnC,OAAO,CAAE,OAAO,CAAC,CAC1C,CACF,CAAC,CAEDsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAACT,gBAAgB,CAAEpB,YAAY,CAAC,CAAC,CAEpC;AACF;AACA;AACA,KACE,KAAM,CAAA2C,YAAY,CAAG9D,WAAW,CAC7B+D,CAAC,EAAK,CACL,KAAM,CAAEC,IAAI,CAAEX,KAAK,CAAEvB,IAAI,CAAEmC,OAAQ,CAAC,CAAGF,CAAC,CAACG,MAAM,CAC/C,KAAM,CAAAC,QAAQ,CAAGrC,IAAI,GAAK,UAAU,CAAGmC,OAAO,CAAGZ,KAAK,CAEtDhC,WAAW,CAAEsC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACP,CAACK,IAAI,EAAGG,QAAQ,EAChB,CAAC,CAEH;AACA,GAAIhC,UAAU,CAAC6B,IAAI,CAAC,CAAE,CACpB5B,aAAa,CAAEuB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACdD,IAAI,MACP,CAACK,IAAI,EAAG,EAAE,EACV,CAAC,CACL,CACF,CAAC,CACD,CAAC7B,UAAU,CACb,CAAC,CAED;AACF;AACA;AACA,KACE,KAAM,CAAAiC,kBAAkB,CAAGpE,WAAW,CACnCqE,cAAc,EAAK,CAClBhD,WAAW,CAAEsC,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPlC,KAAK,CAAE4C,cAAc,EACrB,CAAC,CAEH;AACA,GAAIlC,UAAU,CAACV,KAAK,CAAE,CACpBW,aAAa,CAAEuB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACdD,IAAI,MACPlC,KAAK,CAAE,EAAE,EACT,CAAC,CACL,CACF,CAAC,CACD,CAACU,UAAU,CAACV,KAAK,CACnB,CAAC,CAED;AACF;AACA;AACA,KACE,KAAM,CAAA6C,YAAY,CAAG,KAAO,CAAAP,CAAC,EAAK,CAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAElB;AACA,GAAIlC,YAAY,CAAE,OAElB;AACA,GAAI,CAAC/B,eAAe,CAACkE,SAAS,CAAC,CAAC,CAAE,CAChCjC,gBAAgB,CACd,mDAAmD,CACnD,OACF,CAAC,CACD,OACF,CAEAD,eAAe,CAAC,IAAI,CAAC,CACrBJ,aAAa,CAAC,IAAI,CAAC,CACnBE,aAAa,CAAC,CAAC,CAAC,CAAC,CAEjB,GAAI,KAAAqC,oBAAA,CACF;AACA,KAAM,CACJC,IAAI,CAAEC,aAAa,CACnBC,MAAM,CACNC,OACF,CAAC,CAAGxE,uBAAuB,CAACe,QAAQ,CAAC,CAErC,GAAI,CAACyD,OAAO,CAAE,CACZzC,aAAa,CAACwC,MAAM,CAAC,CACrBrC,gBAAgB,CAAC,sCAAsC,CAAE,OAAO,CAAC,CACjE,OACF,CAEA;AACA,KAAM,CAAAuC,OAAO,CAAG,CACdxD,SAAS,CAAEqD,aAAa,CAACrD,SAAS,CAClCC,QAAQ,CAAEoD,aAAa,CAACpD,QAAQ,CAChCC,KAAK,CAAEmD,aAAa,CAACnD,KAAK,CAC1BC,KAAK,CAAE,EAAAgD,oBAAA,CAAAE,aAAa,CAAClD,KAAK,UAAAgD,oBAAA,iBAAnBA,oBAAA,CAAqBpB,KAAK,GAAI,EAAE,CACvC3B,OAAO,CAAEiD,aAAa,CAACjD,OAAO,CAC9BC,OAAO,CAAEgD,aAAa,CAAChD,OAAO,CAC9BV,QAAQ,CAAEA,QAAU;AACtB,CAAC,CAED;AACA,KAAM,CAAAT,cAAc,CAACsE,OAAO,CAAC,CAE7B;AACAvC,gBAAgB,CACd,iEAAiE,CACjE,SACF,CAAC,CAED;AACAlB,WAAW,CAAC,CACVC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,EAAE,CACXC,OAAO,CAAE,KACX,CAAC,CAAC,CACFS,aAAa,CAAC,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOyB,KAAK,CAAE,CACdtB,gBAAgB,CAACsB,KAAK,CAACnC,OAAO,CAAE,OAAO,CAAC,CAC1C,CAAC,OAAS,CACRQ,aAAa,CAAC,KAAK,CAAC,CACpBI,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,mBACE1B,KAAA,CAAAE,SAAA,EAAAiE,QAAA,eACErE,IAAA,CAACN,YAAY,EAACsB,OAAO,CAAEE,YAAY,CAACF,OAAQ,CAACI,IAAI,CAAEF,YAAY,CAACE,IAAK,CAAE,CAAC,CACvEZ,KAAK,eAAIR,IAAA,OAAIsE,SAAS,CAAE9E,MAAM,CAAC+E,SAAU,CAAAF,QAAA,CAAE7D,KAAK,CAAK,CAAC,cACvDN,KAAA,SAAMsE,QAAQ,CAAEZ,YAAa,CAACU,SAAS,CAAE9E,MAAM,CAACiF,IAAK,CAAAJ,QAAA,eACnDnE,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACkF,GAAI,CAAAL,QAAA,eACzBnE,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACmF,UAAW,CAAAN,QAAA,eAChCnE,KAAA,UAAO0E,OAAO,CAAC,WAAW,CAACN,SAAS,CAAE9E,MAAM,CAACqD,KAAM,CAAAwB,QAAA,EAAC,oBAC/C,CAAC,GAAG,cACPrE,IAAA,SAAMsE,SAAS,CAAE9E,MAAM,CAACqF,QAAS,CAAC,aAAW,mGAAmB,CAAAR,QAAA,CAAC,GAEjE,CAAM,CAAC,EACF,CAAC,cACRrE,IAAA,UACE8E,EAAE,CAAC,WAAW,CACd1D,IAAI,CAAC,MAAM,CACXkC,IAAI,CAAC,WAAW,CAChBX,KAAK,CAAEjC,QAAQ,CAACE,SAAU,CAC1B0D,SAAS,IAAAS,MAAA,CAAKvF,MAAM,CAACwF,KAAK,MAAAD,MAAA,CAAItD,UAAU,CAACb,SAAS,CAAGpB,MAAM,CAACyF,UAAU,CAAG,EAAE,CAAG,CAC9EJ,QAAQ,MACRK,QAAQ,CAAE9B,YAAa,CACvB+B,SAAS,CAAC,GAAG,CACbC,SAAS,CAAC,IAAI,CACd,mBACE3D,UAAU,CAACb,SAAS,CAAG,iBAAiB,CAAGoB,SAC5C,CACD,eAAcP,UAAU,CAACb,SAAS,CAAG,MAAM,CAAG,OAAQ,CACtDyE,QAAQ,CAAE1D,YAAa,CACxB,CAAC,CACDF,UAAU,CAACb,SAAS,eACnBZ,IAAA,QACE8E,EAAE,CAAC,iBAAiB,CACpBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACb,SAAS,CAClB,CACN,EACE,CAAC,cAENV,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACmF,UAAW,CAAAN,QAAA,eAChCrE,IAAA,UAAO4E,OAAO,CAAC,UAAU,CAACN,SAAS,CAAE9E,MAAM,CAACqD,KAAM,CAAAwB,QAAA,CAAC,4CAEnD,CAAO,CAAC,cACRrE,IAAA,UACE8E,EAAE,CAAC,UAAU,CACb1D,IAAI,CAAC,MAAM,CACXkC,IAAI,CAAC,UAAU,CACfX,KAAK,CAAEjC,QAAQ,CAACG,QAAS,CACzByD,SAAS,IAAAS,MAAA,CAAKvF,MAAM,CAACwF,KAAK,MAAAD,MAAA,CAAItD,UAAU,CAACZ,QAAQ,CAAGrB,MAAM,CAACyF,UAAU,CAAG,EAAE,CAAG,CAC7EC,QAAQ,CAAE9B,YAAa,CACvB+B,SAAS,CAAC,GAAG,CACbC,SAAS,CAAC,IAAI,CACd,mBACE3D,UAAU,CAACZ,QAAQ,CAAG,gBAAgB,CAAGmB,SAC1C,CACD,eAAcP,UAAU,CAACZ,QAAQ,CAAG,MAAM,CAAG,OAAQ,CACrDwE,QAAQ,CAAE1D,YAAa,CACxB,CAAC,CACDF,UAAU,CAACZ,QAAQ,eAClBb,IAAA,QACE8E,EAAE,CAAC,gBAAgB,CACnBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACZ,QAAQ,CACjB,CACN,EACE,CAAC,EACH,CAAC,cAENX,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACkF,GAAI,CAAAL,QAAA,eACzBnE,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACmF,UAAW,CAAAN,QAAA,eAChCnE,KAAA,UAAO0E,OAAO,CAAC,OAAO,CAACN,SAAS,CAAE9E,MAAM,CAACqD,KAAM,CAAAwB,QAAA,EAAC,8IACrB,CAAC,GAAG,cAC7BrE,IAAA,SAAMsE,SAAS,CAAE9E,MAAM,CAACqF,QAAS,CAAC,aAAW,mGAAmB,CAAAR,QAAA,CAAC,GAEjE,CAAM,CAAC,EACF,CAAC,cACRrE,IAAA,UACE8E,EAAE,CAAC,OAAO,CACV1D,IAAI,CAAC,KAAK,CACVkC,IAAI,CAAC,OAAO,CACZX,KAAK,CAAEjC,QAAQ,CAACI,KAAM,CACtBwD,SAAS,IAAAS,MAAA,CAAKvF,MAAM,CAACwF,KAAK,MAAAD,MAAA,CAAItD,UAAU,CAACX,KAAK,CAAGtB,MAAM,CAACyF,UAAU,CAAG,EAAE,CAAG,CAC1EJ,QAAQ,MACRW,GAAG,CAAEtD,QAAS,CACdgD,QAAQ,CAAE9B,YAAa,CACvBqC,WAAW,CAAC,mBAAmB,CAC/B,mBAAkBhE,UAAU,CAACX,KAAK,CAAG,aAAa,CAAG,YAAa,CAClE,eAAcW,UAAU,CAACX,KAAK,CAAG,MAAM,CAAG,OAAQ,CAClDuE,QAAQ,CAAE1D,YAAa,CACxB,CAAC,cACF3B,IAAA,QAAK8E,EAAE,CAAC,YAAY,CAACR,SAAS,CAAE9E,MAAM,CAACkG,QAAS,CAAArB,QAAA,CAAC,yDAEjD,CAAK,CAAC,CACL5C,UAAU,CAACX,KAAK,eACfd,IAAA,QACE8E,EAAE,CAAC,aAAa,CAChBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACX,KAAK,CACd,CACN,EACE,CAAC,cAENZ,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACmF,UAAW,CAAAN,QAAA,eAChCrE,IAAA,UAAO4E,OAAO,CAAC,SAAS,CAACN,SAAS,CAAE9E,MAAM,CAACqD,KAAM,CAAAwB,QAAA,CAAC,iFAElD,CAAO,CAAC,cACRrE,IAAA,aACE8E,EAAE,CAAC,SAAS,CACZxB,IAAI,CAAC,SAAS,CACdX,KAAK,CAAEjC,QAAQ,CAACM,OAAQ,CACxBsD,SAAS,IAAAS,MAAA,CAAKvF,MAAM,CAACwF,KAAK,MAAAD,MAAA,CAAIvF,MAAM,CAACmG,QAAQ,MAAAZ,MAAA,CAAItD,UAAU,CAACT,OAAO,CAAGxB,MAAM,CAACyF,UAAU,CAAG,EAAE,CAAG,CAC/FC,QAAQ,CAAE9B,YAAa,CACvBgC,SAAS,CAAC,MAAM,CAChBQ,IAAI,CAAC,GAAG,CACRH,WAAW,CAAC,6MAAwC,CACpD,mBACEhE,UAAU,CAACT,OAAO,CAAG,eAAe,CAAG,cACxC,CACD,eAAcS,UAAU,CAACT,OAAO,CAAG,MAAM,CAAG,OAAQ,CACpDqE,QAAQ,CAAE1D,YAAa,CACxB,CAAC,cACF3B,IAAA,QAAK8E,EAAE,CAAC,cAAc,CAACR,SAAS,CAAE9E,MAAM,CAACkG,QAAS,CAAArB,QAAA,CAAC,wGAEnD,CAAK,CAAC,CACL5C,UAAU,CAACT,OAAO,eACjBhB,IAAA,QACE8E,EAAE,CAAC,eAAe,CAClBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACT,OAAO,CAChB,CACN,EACE,CAAC,EACH,CAAC,cAENhB,IAAA,QAAKsE,SAAS,CAAE9E,MAAM,CAACkF,GAAI,CAAAL,QAAA,cACzBnE,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACmF,UAAW,CAAAN,QAAA,eAChCnE,KAAA,UAAO0E,OAAO,CAAC,OAAO,CAACN,SAAS,CAAE9E,MAAM,CAACqD,KAAM,CAAAwB,QAAA,EAAC,mGAC7B,CAAC,GAAG,cACrBrE,IAAA,SAAMsE,SAAS,CAAE9E,MAAM,CAACqF,QAAS,CAAC,aAAW,mGAAmB,CAAAR,QAAA,CAAC,GAEjE,CAAM,CAAC,EACF,CAAC,cACRnE,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAACqG,aAAc,CAAAxB,QAAA,eACnCrE,IAAA,CAACT,MAAM,EACLuG,OAAO,CAAC,OAAO,CACfC,eAAe,CAAC,cAAc,CAC9BC,OAAO,CAAE3E,UAAW,CACpBoE,WAAW,CAAC,oJAA4B,CACxCP,QAAQ,CAAExB,kBAAmB,CAC7Bf,KAAK,CAAEjC,QAAQ,CAACK,KAAM,CACtBkF,YAAY,CAAE,KAAM,CACpBC,UAAU,CAAEvE,YAAa,CACzBwE,gBAAgB,CAAEC,QAAQ,CAACC,IAAK,CAChC,mBAAkB5E,UAAU,CAACV,KAAK,CAAG,aAAa,CAAGiB,SAAU,CAC/D,eAAcP,UAAU,CAACV,KAAK,CAAG,MAAM,CAAG,OAAQ,CAClDvB,MAAM,CAAE,CACN8G,SAAS,CAAGC,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACfqD,QAAQ,MACXC,KAAK,CAAE,MAAM,EACb,CAEFC,OAAO,CAAEA,CAACF,QAAQ,CAAEG,KAAK,GAAAxD,aAAA,CAAAA,aAAA,IACpBqD,QAAQ,MACXC,KAAK,CAAE,MAAM,CACbG,WAAW,CAAE,KAAK,CAClBC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,cAAA/B,MAAA,CACJ2B,KAAK,CAACK,SAAS,CAAG,SAAS,CAAG,SAAS,CACvC,CACFC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,MAAM,CACvBC,SAAS,CAAE,MAAM,CACjBC,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTC,WAAW,CAAEX,KAAK,CAACK,SAAS,CAAG,SAAS,CAAG,SAC7C,CAAC,EACD,CAEFtB,WAAW,CAAGc,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACjBqD,QAAQ,MACXe,KAAK,CAAE,MAAM,EACb,CAEFC,WAAW,CAAGhB,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACjBqD,QAAQ,MACXe,KAAK,CAAE,MAAM,EACb,CAEFE,mBAAmB,CAAGjB,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACzBqD,QAAQ,MACXkB,YAAY,CAAE,MAAM,EACpB,CAEFC,iBAAiB,CAAGnB,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACvBqD,QAAQ,MACXe,KAAK,CAAE,SAAS,EAChB,CAEFK,UAAU,CAAGC,IAAI,EAAA1E,aAAA,CAAAA,aAAA,IACZ0E,IAAI,MACPT,MAAM,CAAE,GAAG,EACX,CAEFU,IAAI,CAAGtB,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACVqD,QAAQ,MACXO,MAAM,CAAE,mBAAmB,CAC3BD,YAAY,CAAE,KAAK,CACnBK,SAAS,CAAE,MAAM,CACjBY,SAAS,CAAE,KAAK,CAChBb,eAAe,CAAE,MAAM,CACvBE,MAAM,CAAE,GAAG,EACX,CAEFY,QAAQ,CAAGxB,QAAQ,EAAArD,aAAA,CAAAA,aAAA,IACdqD,QAAQ,MACXyB,OAAO,CAAE,CAAC,EACV,CAEFhF,MAAM,CAAEA,CAACuD,QAAQ,CAAEG,KAAK,GAAAxD,aAAA,CAAAA,aAAA,IACnBqD,QAAQ,MACXyB,OAAO,CAAE,WAAW,CACpBhB,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAEP,KAAK,CAACK,SAAS,CAAG,SAAS,CAAG,MAAM,CACrDO,KAAK,CAAE,MAAM,CACbW,MAAM,CAAE,SAAS,CACjB,UAAU,CAAE,CACVhB,eAAe,CAAE,SACnB,CAAC,EAEL,CAAE,CACH,CAAC,CACDxF,UAAU,CAACV,KAAK,eACff,IAAA,QACE8E,EAAE,CAAC,aAAa,CAChBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACV,KAAK,CACd,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAENb,KAAA,QAAKoE,SAAS,CAAE9E,MAAM,CAAC0I,aAAc,CAAA7D,QAAA,eACnCnE,KAAA,UAAOoE,SAAS,CAAE9E,MAAM,CAAC2I,eAAgB,CAAA9D,QAAA,eACvCrE,IAAA,UACEoB,IAAI,CAAC,UAAU,CACfkC,IAAI,CAAC,SAAS,CACduB,QAAQ,MACRtB,OAAO,CAAE7C,QAAQ,CAACO,OAAQ,CAC1BiE,QAAQ,CAAE9B,YAAa,CACvBkB,SAAS,CAAE9E,MAAM,CAAC4I,QAAS,CAC3B,mBACE3G,UAAU,CAACR,OAAO,CAAG,eAAe,CAAG,cACxC,CACD,eAAcQ,UAAU,CAACR,OAAO,CAAG,MAAM,CAAG,OAAQ,CACpDoE,QAAQ,CAAE1D,YAAa,CACxB,CAAC,cACF3B,IAAA,SAAMsE,SAAS,CAAE9E,MAAM,CAAC6I,aAAc,CAAAhE,QAAA,CAAC,uRAEvC,CAAM,CAAC,EACF,CAAC,cACRrE,IAAA,QAAK8E,EAAE,CAAC,cAAc,CAACR,SAAS,CAAE9E,MAAM,CAACkG,QAAS,CAAArB,QAAA,CAAC,uKAEnD,CAAK,CAAC,CACL5C,UAAU,CAACR,OAAO,eACjBjB,IAAA,QACE8E,EAAE,CAAC,eAAe,CAClBR,SAAS,CAAE9E,MAAM,CAAC8F,YAAa,CAC/BC,IAAI,CAAC,OAAO,CAAAlB,QAAA,CAEX5C,UAAU,CAACR,OAAO,CAChB,CACN,EACE,CAAC,cAENjB,IAAA,WACEoB,IAAI,CAAC,QAAQ,CACbkD,SAAS,IAAAS,MAAA,CAAKvF,MAAM,CAAC8I,MAAM,MAAAvD,MAAA,CAAIpD,YAAY,CAAGnC,MAAM,CAAC+I,aAAa,CAAG,EAAE,CAAG,CAC1ElD,QAAQ,CAAE1D,YAAa,CACvB,mBAAiB,aAAa,CAAA0C,QAAA,CAE7B9C,UAAU,cACTrB,KAAA,CAAAE,SAAA,EAAAiE,QAAA,eACErE,IAAA,SAAMsE,SAAS,CAAE9E,MAAM,CAACgJ,OAAQ,CAAC,cAAY,MAAM,CAAO,CAAC,sDAE7D,EAAE,CAAC,CAEH,WACD,CACK,CAAC,cACTxI,IAAA,QAAK8E,EAAE,CAAC,aAAa,CAACR,SAAS,CAAE9E,MAAM,CAACkG,QAAS,CAAArB,QAAA,CAAC,0VAElD,CAAK,CAAC,EACF,CAAC,EACP,CAAC,CAEP,CAAC,CAED,cAAe,CAAAhE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}