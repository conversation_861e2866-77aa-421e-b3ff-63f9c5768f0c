{"ast": null, "code": "import { clamp } from '../clamp.mjs';\nfunction steps(numSteps) {\n  let direction = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"end\";\n  return progress => {\n    progress = direction === \"end\" ? Math.min(progress, 0.999) : Math.max(progress, 0.001);\n    const expanded = progress * numSteps;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return clamp(0, 1, rounded / numSteps);\n  };\n}\nexport { steps };", "map": {"version": 3, "names": ["clamp", "steps", "numSteps", "direction", "arguments", "length", "undefined", "progress", "Math", "min", "max", "expanded", "rounded", "floor", "ceil"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-utils/dist/es/easing/steps.mjs"], "sourcesContent": ["import { clamp } from '../clamp.mjs';\n\nfunction steps(numSteps, direction = \"end\") {\n    return (progress) => {\n        progress =\n            direction === \"end\"\n                ? Math.min(progress, 0.999)\n                : Math.max(progress, 0.001);\n        const expanded = progress * numSteps;\n        const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n        return clamp(0, 1, rounded / numSteps);\n    };\n}\n\nexport { steps };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,cAAc;AAEpC,SAASC,KAAKA,CAACC,QAAQ,EAAqB;EAAA,IAAnBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACtC,OAAQG,QAAQ,IAAK;IACjBA,QAAQ,GACJJ,SAAS,KAAK,KAAK,GACbK,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAE,KAAK,CAAC,GACzBC,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAE,KAAK,CAAC;IACnC,MAAMI,QAAQ,GAAGJ,QAAQ,GAAGL,QAAQ;IACpC,MAAMU,OAAO,GAAGT,SAAS,KAAK,KAAK,GAAGK,IAAI,CAACK,KAAK,CAACF,QAAQ,CAAC,GAAGH,IAAI,CAACM,IAAI,CAACH,QAAQ,CAAC;IAChF,OAAOX,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEY,OAAO,GAAGV,QAAQ,CAAC;EAC1C,CAAC;AACL;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}