{"ast": null, "code": "import { microtask } from 'motion-dom';\nimport { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n  var _window$MotionHandoff, _window, _window$MotionHasOpti, _window2;\n  const {\n    visualElement: parent\n  } = useContext(MotionContext);\n  const lazyContext = useContext(LazyContext);\n  const presenceContext = useContext(PresenceContext);\n  const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n  const visualElementRef = useRef(null);\n  /**\n   * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n   */\n  createVisualElement = createVisualElement || lazyContext.renderer;\n  if (!visualElementRef.current && createVisualElement) {\n    visualElementRef.current = createVisualElement(Component, {\n      visualState,\n      parent,\n      props,\n      presenceContext,\n      blockInitialAnimation: presenceContext ? presenceContext.initial === false : false,\n      reducedMotionConfig\n    });\n  }\n  const visualElement = visualElementRef.current;\n  /**\n   * Load Motion gesture and animation features. These are rendered as renderless\n   * components so each feature can optionally make use of React lifecycle methods.\n   */\n  const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n  if (visualElement && !visualElement.projection && ProjectionNodeConstructor && (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n    createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n  }\n  const isMounted = useRef(false);\n  useInsertionEffect(() => {\n    /**\n     * Check the component has already mounted before calling\n     * `update` unnecessarily. This ensures we skip the initial update.\n     */\n    if (visualElement && isMounted.current) {\n      visualElement.update(props, presenceContext);\n    }\n  });\n  /**\n   * Cache this value as we want to know whether HandoffAppearAnimations\n   * was present on initial render - it will be deleted after this.\n   */\n  const optimisedAppearId = props[optimizedAppearDataAttribute];\n  const wantsHandoff = useRef(Boolean(optimisedAppearId) && !((_window$MotionHandoff = (_window = window).MotionHandoffIsComplete) !== null && _window$MotionHandoff !== void 0 && _window$MotionHandoff.call(_window, optimisedAppearId)) && ((_window$MotionHasOpti = (_window2 = window).MotionHasOptimisedAnimation) === null || _window$MotionHasOpti === void 0 ? void 0 : _window$MotionHasOpti.call(_window2, optimisedAppearId)));\n  useIsomorphicLayoutEffect(() => {\n    if (!visualElement) return;\n    isMounted.current = true;\n    window.MotionIsMounted = true;\n    visualElement.updateFeatures();\n    microtask.render(visualElement.render);\n    /**\n     * Ideally this function would always run in a useEffect.\n     *\n     * However, if we have optimised appear animations to handoff from,\n     * it needs to happen synchronously to ensure there's no flash of\n     * incorrect styles in the event of a hydration error.\n     *\n     * So if we detect a situtation where optimised appear animations\n     * are running, we use useLayoutEffect to trigger animations.\n     */\n    if (wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n  });\n  useEffect(() => {\n    if (!visualElement) return;\n    if (!wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n    if (wantsHandoff.current) {\n      // This ensures all future calls to animateChanges() in this component will run in useEffect\n      queueMicrotask(() => {\n        var _window$MotionHandoff2, _window3;\n        (_window$MotionHandoff2 = (_window3 = window).MotionHandoffMarkAsComplete) === null || _window$MotionHandoff2 === void 0 || _window$MotionHandoff2.call(_window3, optimisedAppearId);\n      });\n      wantsHandoff.current = false;\n    }\n  });\n  return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n  const {\n    layoutId,\n    layout,\n    drag,\n    dragConstraints,\n    layoutScroll,\n    layoutRoot,\n    layoutCrossfade\n  } = props;\n  visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"] ? undefined : getClosestProjectingNode(visualElement.parent));\n  visualElement.projection.setOptions({\n    layoutId,\n    layout,\n    alwaysMeasureLayout: Boolean(drag) || dragConstraints && isRefObject(dragConstraints),\n    visualElement,\n    /**\n     * TODO: Update options in an effect. This could be tricky as it'll be too late\n     * to update by the time layout animations run.\n     * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n     * ensuring it gets called if there's no potential layout animations.\n     *\n     */\n    animationType: typeof layout === \"string\" ? layout : \"both\",\n    initialPromotionConfig,\n    crossfade: layoutCrossfade,\n    layoutScroll,\n    layoutRoot\n  });\n}\nfunction getClosestProjectingNode(visualElement) {\n  if (!visualElement) return undefined;\n  return visualElement.options.allowProjection !== false ? visualElement.projection : getClosestProjectingNode(visualElement.parent);\n}\nexport { useVisualElement };", "map": {"version": 3, "names": ["microtask", "useContext", "useRef", "useInsertionEffect", "useEffect", "optimizedAppearDataAttribute", "LazyContext", "MotionConfigContext", "MotionContext", "PresenceContext", "SwitchLayoutGroupContext", "isRefObject", "useIsomorphicLayoutEffect", "useVisualElement", "Component", "visualState", "props", "createVisualElement", "ProjectionNodeConstructor", "_window$MotionHandoff", "_window", "_window$MotionHasOpti", "_window2", "visualElement", "parent", "lazyContext", "presenceContext", "reducedMotionConfig", "reducedMotion", "visualElementRef", "renderer", "current", "blockInitialAnimation", "initial", "initialLayoutGroupConfig", "projection", "type", "createProjectionNode", "isMounted", "update", "optimisedAppearId", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "MotionHandoffIsComplete", "call", "MotionHasOptimisedAnimation", "MotionIsMounted", "updateFeatures", "render", "animationState", "animateChanges", "queueMicrotask", "_window$MotionHandoff2", "_window3", "MotionHandoffMarkAsComplete", "initialPromotionConfig", "layoutId", "layout", "drag", "dragConstraints", "layoutScroll", "layoutRoot", "layoutCrossfade", "latestValues", "undefined", "getClosestProjectingNode", "setOptions", "alwaysMeasureLayout", "animationType", "crossfade", "options", "allowProjection"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs"], "sourcesContent": ["import { microtask } from 'motion-dom';\nimport { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = useContext(MotionContext);\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = useRef(false);\n    useInsertionEffect(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[optimizedAppearDataAttribute];\n    const wantsHandoff = useRef(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    useIsomorphicLayoutEffect(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        microtask.render(visualElement.render);\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useEffect(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && isRefObject(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\nexport { useVisualElement };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,OAAO;AACzE,SAASC,4BAA4B,QAAQ,8CAA8C;AAC3F,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,yBAAyB,QAAQ,uCAAuC;AAEjF,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAE;EAAA,IAAAC,qBAAA,EAAAC,OAAA,EAAAC,qBAAA,EAAAC,QAAA;EACrG,MAAM;IAAEC,aAAa,EAAEC;EAAO,CAAC,GAAGvB,UAAU,CAACO,aAAa,CAAC;EAC3D,MAAMiB,WAAW,GAAGxB,UAAU,CAACK,WAAW,CAAC;EAC3C,MAAMoB,eAAe,GAAGzB,UAAU,CAACQ,eAAe,CAAC;EACnD,MAAMkB,mBAAmB,GAAG1B,UAAU,CAACM,mBAAmB,CAAC,CAACqB,aAAa;EACzE,MAAMC,gBAAgB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACrC;AACJ;AACA;EACIe,mBAAmB,GAAGA,mBAAmB,IAAIQ,WAAW,CAACK,QAAQ;EACjE,IAAI,CAACD,gBAAgB,CAACE,OAAO,IAAId,mBAAmB,EAAE;IAClDY,gBAAgB,CAACE,OAAO,GAAGd,mBAAmB,CAACH,SAAS,EAAE;MACtDC,WAAW;MACXS,MAAM;MACNR,KAAK;MACLU,eAAe;MACfM,qBAAqB,EAAEN,eAAe,GAChCA,eAAe,CAACO,OAAO,KAAK,KAAK,GACjC,KAAK;MACXN;IACJ,CAAC,CAAC;EACN;EACA,MAAMJ,aAAa,GAAGM,gBAAgB,CAACE,OAAO;EAC9C;AACJ;AACA;AACA;EACI,MAAMG,wBAAwB,GAAGjC,UAAU,CAACS,wBAAwB,CAAC;EACrE,IAAIa,aAAa,IACb,CAACA,aAAa,CAACY,UAAU,IACzBjB,yBAAyB,KACxBK,aAAa,CAACa,IAAI,KAAK,MAAM,IAAIb,aAAa,CAACa,IAAI,KAAK,KAAK,CAAC,EAAE;IACjEC,oBAAoB,CAACR,gBAAgB,CAACE,OAAO,EAAEf,KAAK,EAAEE,yBAAyB,EAAEgB,wBAAwB,CAAC;EAC9G;EACA,MAAMI,SAAS,GAAGpC,MAAM,CAAC,KAAK,CAAC;EAC/BC,kBAAkB,CAAC,MAAM;IACrB;AACR;AACA;AACA;IACQ,IAAIoB,aAAa,IAAIe,SAAS,CAACP,OAAO,EAAE;MACpCR,aAAa,CAACgB,MAAM,CAACvB,KAAK,EAAEU,eAAe,CAAC;IAChD;EACJ,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI,MAAMc,iBAAiB,GAAGxB,KAAK,CAACX,4BAA4B,CAAC;EAC7D,MAAMoC,YAAY,GAAGvC,MAAM,CAACwC,OAAO,CAACF,iBAAiB,CAAC,IAClD,GAAArB,qBAAA,GAAC,CAAAC,OAAA,GAAAuB,MAAM,EAACC,uBAAuB,cAAAzB,qBAAA,eAA9BA,qBAAA,CAAA0B,IAAA,CAAAzB,OAAA,EAAiCoB,iBAAiB,CAAC,OAAAnB,qBAAA,GACpD,CAAAC,QAAA,GAAAqB,MAAM,EAACG,2BAA2B,cAAAzB,qBAAA,uBAAlCA,qBAAA,CAAAwB,IAAA,CAAAvB,QAAA,EAAqCkB,iBAAiB,CAAC,EAAC;EAC5D5B,yBAAyB,CAAC,MAAM;IAC5B,IAAI,CAACW,aAAa,EACd;IACJe,SAAS,CAACP,OAAO,GAAG,IAAI;IACxBY,MAAM,CAACI,eAAe,GAAG,IAAI;IAC7BxB,aAAa,CAACyB,cAAc,CAAC,CAAC;IAC9BhD,SAAS,CAACiD,MAAM,CAAC1B,aAAa,CAAC0B,MAAM,CAAC;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIR,YAAY,CAACV,OAAO,IAAIR,aAAa,CAAC2B,cAAc,EAAE;MACtD3B,aAAa,CAAC2B,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;EACJ,CAAC,CAAC;EACF/C,SAAS,CAAC,MAAM;IACZ,IAAI,CAACmB,aAAa,EACd;IACJ,IAAI,CAACkB,YAAY,CAACV,OAAO,IAAIR,aAAa,CAAC2B,cAAc,EAAE;MACvD3B,aAAa,CAAC2B,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;IACA,IAAIV,YAAY,CAACV,OAAO,EAAE;MACtB;MACAqB,cAAc,CAAC,MAAM;QAAA,IAAAC,sBAAA,EAAAC,QAAA;QACjB,CAAAD,sBAAA,IAAAC,QAAA,GAAAX,MAAM,EAACY,2BAA2B,cAAAF,sBAAA,eAAlCA,sBAAA,CAAAR,IAAA,CAAAS,QAAA,EAAqCd,iBAAiB,CAAC;MAC3D,CAAC,CAAC;MACFC,YAAY,CAACV,OAAO,GAAG,KAAK;IAChC;EACJ,CAAC,CAAC;EACF,OAAOR,aAAa;AACxB;AACA,SAASc,oBAAoBA,CAACd,aAAa,EAAEP,KAAK,EAAEE,yBAAyB,EAAEsC,sBAAsB,EAAE;EACnG,MAAM;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAiB,CAAC,GAAG/C,KAAK;EACrGO,aAAa,CAACY,UAAU,GAAG,IAAIjB,yBAAyB,CAACK,aAAa,CAACyC,YAAY,EAAEhD,KAAK,CAAC,uBAAuB,CAAC,GAC7GiD,SAAS,GACTC,wBAAwB,CAAC3C,aAAa,CAACC,MAAM,CAAC,CAAC;EACrDD,aAAa,CAACY,UAAU,CAACgC,UAAU,CAAC;IAChCV,QAAQ;IACRC,MAAM;IACNU,mBAAmB,EAAE1B,OAAO,CAACiB,IAAI,CAAC,IAAKC,eAAe,IAAIjD,WAAW,CAACiD,eAAe,CAAE;IACvFrC,aAAa;IACb;AACR;AACA;AACA;AACA;AACA;AACA;IACQ8C,aAAa,EAAE,OAAOX,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,MAAM;IAC3DF,sBAAsB;IACtBc,SAAS,EAAEP,eAAe;IAC1BF,YAAY;IACZC;EACJ,CAAC,CAAC;AACN;AACA,SAASI,wBAAwBA,CAAC3C,aAAa,EAAE;EAC7C,IAAI,CAACA,aAAa,EACd,OAAO0C,SAAS;EACpB,OAAO1C,aAAa,CAACgD,OAAO,CAACC,eAAe,KAAK,KAAK,GAChDjD,aAAa,CAACY,UAAU,GACxB+B,wBAAwB,CAAC3C,aAAa,CAACC,MAAM,CAAC;AACxD;AAEA,SAASX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}