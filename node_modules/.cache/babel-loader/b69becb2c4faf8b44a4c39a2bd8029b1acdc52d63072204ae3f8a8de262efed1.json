{"ast": null, "code": "import styles from'./simpleCard.module.css';import img_1 from'../../asset/imgs/home/<USER>';import img_2 from'../../asset/imgs/home/<USER>';import{Link}from'react-router-dom';import arrowIcon from'../../asset/imgs/icons/arrow.svg';import{useEffect}from'react';import AOS from'aos';import'aos/dist/aos.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const data=[{id:1,title:'Специальные предложения',text:'Воспользуйтесь эксклюзивными предложениями на автомобили GWM.',img:img_1,url:'/offers'},{id:2,title:'Фл<PERSON><PERSON> GWM',text:'Расширьте возможности своего бизнеса с помощью гибких и надежных решений GWM для автопарков, отвечающих вашим эксплуатационным потребностям.',img:img_2,url:'/models'}];const SimpleCard=()=>{useEffect(()=>{AOS.init({duration:1000,once:false});},[]);return/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"div\",{className:styles.cards,children:data.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:styles.card,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.left,children:[/*#__PURE__*/_jsx(\"h3\",{children:item.title}),/*#__PURE__*/_jsx(\"p\",{children:item.text}),/*#__PURE__*/_jsxs(Link,{to:item.url,className:\"link\",children:[\"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u0435\\u0435\",/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.right,\"data-aos\":\"fade-left\",children:/*#__PURE__*/_jsx(\"img\",{src:item.img,alt:\"\"})})]},item.id))})})});};export default SimpleCard;", "map": {"version": 3, "names": ["styles", "img_1", "img_2", "Link", "arrowIcon", "useEffect", "AOS", "jsx", "_jsx", "jsxs", "_jsxs", "data", "id", "title", "text", "img", "url", "SimpleCard", "init", "duration", "once", "className", "section", "children", "cards", "map", "item", "card", "left", "to", "src", "alt", "right"], "sources": ["/var/www/html/gwm.tj/src/components/SimpleCard/SimpleCard.jsx"], "sourcesContent": ["import styles from './simpleCard.module.css';\nimport img_1 from '../../asset/imgs/home/<USER>';\nimport img_2 from '../../asset/imgs/home/<USER>';\nimport { Link } from 'react-router-dom';\nimport arrowIcon from '../../asset/imgs/icons/arrow.svg';\nimport { useEffect } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst data = [\n  {\n    id: 1,\n    title: 'Специальные предложения',\n    text: 'Воспользуйтесь эксклюзивными предложениями на автомобили GWM.',\n    img: img_1,\n    url: '/offers',\n  },\n  {\n    id: 2,\n    title: 'Флот GWM',\n    text: 'Расширьте возможности своего бизнеса с помощью гибких и надежных решений GWM для автопарков, отвечающих вашим эксплуатационным потребностям.',\n    img: img_2,\n    url: '/models',\n  },\n];\n\nconst SimpleCard = () => {\n  useEffect(() => {\n    AOS.init({\n      duration: 1000,\n      once: false,\n    });\n  }, []);\n\n  return (\n    <section className={styles.section}>\n      <div className=\"container\">\n        <div className={styles.cards}>\n          {data.map((item) => (\n            <div className={styles.card} key={item.id}>\n              <div className={styles.left}>\n                <h3>{item.title}</h3>\n                <p>{item.text}</p>\n                <Link to={item.url} className=\"link\">\n                  Подробнее\n                  <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                </Link>\n              </div>\n              <div className={styles.right} data-aos=\"fade-left\">\n                <img src={item.img} alt=\"\" />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SimpleCard;\n"], "mappings": "AAAA,MAAO,CAAAA,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,0DAA0D,CAC5E,MAAO,CAAAC,KAAK,KAAM,8CAA8C,CAChE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,SAAS,KAAM,kCAAkC,CACxD,OAASC,SAAS,KAAQ,OAAO,CACjC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,IAAI,CAAG,CACX,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,+DAA+D,CACrEC,GAAG,CAAEd,KAAK,CACVe,GAAG,CAAE,SACP,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,UAAU,CACjBC,IAAI,CAAE,8IAA8I,CACpJC,GAAG,CAAEb,KAAK,CACVc,GAAG,CAAE,SACP,CAAC,CACF,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvBZ,SAAS,CAAC,IAAM,CACdC,GAAG,CAACY,IAAI,CAAC,CACPC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,KACR,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEZ,IAAA,YAASa,SAAS,CAAErB,MAAM,CAACsB,OAAQ,CAAAC,QAAA,cACjCf,IAAA,QAAKa,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBf,IAAA,QAAKa,SAAS,CAAErB,MAAM,CAACwB,KAAM,CAAAD,QAAA,CAC1BZ,IAAI,CAACc,GAAG,CAAEC,IAAI,eACbhB,KAAA,QAAKW,SAAS,CAAErB,MAAM,CAAC2B,IAAK,CAAAJ,QAAA,eAC1Bb,KAAA,QAAKW,SAAS,CAAErB,MAAM,CAAC4B,IAAK,CAAAL,QAAA,eAC1Bf,IAAA,OAAAe,QAAA,CAAKG,IAAI,CAACb,KAAK,CAAK,CAAC,cACrBL,IAAA,MAAAe,QAAA,CAAIG,IAAI,CAACZ,IAAI,CAAI,CAAC,cAClBJ,KAAA,CAACP,IAAI,EAAC0B,EAAE,CAAEH,IAAI,CAACV,GAAI,CAACK,SAAS,CAAC,MAAM,CAAAE,QAAA,EAAC,wDAEnC,cAAAf,IAAA,QAAKsB,GAAG,CAAE1B,SAAU,CAAC2B,GAAG,CAAC,EAAE,CAACV,SAAS,CAAC,UAAU,CAAE,CAAC,EAC/C,CAAC,EACJ,CAAC,cACNb,IAAA,QAAKa,SAAS,CAAErB,MAAM,CAACgC,KAAM,CAAC,WAAS,WAAW,CAAAT,QAAA,cAChDf,IAAA,QAAKsB,GAAG,CAAEJ,IAAI,CAACX,GAAI,CAACgB,GAAG,CAAC,EAAE,CAAE,CAAC,CAC1B,CAAC,GAX0BL,IAAI,CAACd,EAYlC,CACN,CAAC,CACC,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}