{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'./contact.module.css';import AOS from'aos';import'aos/dist/aos.css';import{FaMapMarkerAlt,FaClock,FaPhoneAlt}from'react-icons/fa';import CallbackForm from'./ContactForm';import Notification from'../../../components/Notification/Notification';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const iconMap={address:/*#__PURE__*/_jsx(FaMapMarkerAlt,{}),time:/*#__PURE__*/_jsx(FaClock,{}),phone:/*#__PURE__*/_jsx(FaPhoneAlt,{})};const Contact=()=>{const[loading,setLoading]=useState(true);const[dataLoading,setDataLoading]=useState(true);// 👈 состояние для данных\nconst[contactData,setContactData]=useState([]);const[mapSrc,setMapSrc]=useState('');const[notification,setNotification]=useState({message:'',type:''});const showNotification=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);};useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);useEffect(()=>{fetch('https://api.gwm.tj/api/v1/contacts').then(res=>res.json()).then(data=>{const c=data.contacts||{};const result=[];if(c.address)result.push({id:'address',title:'Адрес',value:c.address});if(c.time)result.push({id:'time',title:'Режим работы',value:c.time});if(c.phone)result.push({id:'phone',title:'Телефон',value:c.phone});setContactData(result);if(c.google_maps)setMapSrc(c.google_maps);}).catch(err=>{showNotification('Не удалось загрузить контакты. Возможно идет тех.работа','error');}).finally(()=>setDataLoading(false));// 👈 когда данные пришли\n},[]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041A\\u043E\\u043D\\u0442\\u0430\\u043A\\u0442\\u044B\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",className:\"redLine\"})]}),/*#__PURE__*/_jsx(\"section\",{className:styles.contactSection,children:/*#__PURE__*/_jsx(\"div\",{className:styles.leftColumn,children:/*#__PURE__*/_jsx(\"div\",{className:styles.cardWrapper,children:dataLoading?[1,2,3].map(i=>/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(styles.cardItem,\" \").concat(styles.skeletonCard),\"data-aos\":\"fade-up\",children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.cardItemHeader,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.iconSkeleton}),/*#__PURE__*/_jsx(\"div\",{className:styles.textSkeleton,style:{width:'60%'}})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.textSkeleton,style:{width:'80%',height:'16px'}})]},i)):contactData.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:styles.cardItem,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.cardItemHeader,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.icon,children:iconMap[item.id]}),/*#__PURE__*/_jsx(\"h3\",{children:item.title})]}),item.id==='phone'?/*#__PURE__*/_jsx(\"a\",{href:\"tel:\".concat(item.value),className:styles.link,children:item.value}):/*#__PURE__*/_jsx(\"p\",{children:item.value})]},item.id))})})}),/*#__PURE__*/_jsx(CallbackForm,{formType:\"contact\",title:\"\\u0417\\u0430\\u043A\\u0430\\u0437\\u0430\\u0442\\u044C \\u0417\\u0432\\u043E\\u043D\\u043E\\u043A\"})]})})]});};export default Contact;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "AOS", "FaMapMarkerAlt", "FaClock", "FaPhoneAlt", "CallbackForm", "Notification", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "iconMap", "address", "time", "phone", "Contact", "loading", "setLoading", "dataLoading", "setDataLoading", "contactData", "setContactData", "mapSrc", "setMapSrc", "notification", "setNotification", "message", "type", "showNotification", "arguments", "length", "undefined", "setTimeout", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "clearTimeout", "fetch", "then", "res", "json", "data", "c", "contacts", "result", "push", "id", "title", "value", "google_maps", "catch", "err", "finally", "children", "className", "contactSection", "leftColumn", "cardWrapper", "map", "i", "concat", "cardItem", "skeletonCard", "cardItemHeader", "iconSkeleton", "textSkeleton", "width", "height", "item", "icon", "href", "link", "formType"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/Contact/Contact.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './contact.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { FaMapMarkerAlt, FaClock, FaPhoneAlt } from 'react-icons/fa';\nimport CallbackForm from './ContactForm';\nimport Notification from '../../../components/Notification/Notification';\n\nconst iconMap = {\n  address: <FaMapMarkerAlt />,\n  time: <FaClock />,\n  phone: <FaPhoneAlt />,\n};\n\nconst Contact = () => {\n  const [loading, setLoading] = useState(true);\n  const [dataLoading, setDataLoading] = useState(true); // 👈 состояние для данных\n  const [contactData, setContactData] = useState([]);\n  const [mapSrc, setMapSrc] = useState('');\n  const [notification, setNotification] = useState({ message: '', type: '' });\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  };\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  useEffect(() => {\n    fetch('https://api.gwm.tj/api/v1/contacts')\n      .then((res) => res.json())\n      .then((data) => {\n        const c = data.contacts || {};\n        const result = [];\n\n        if (c.address)\n          result.push({ id: 'address', title: 'Адрес', value: c.address });\n        if (c.time)\n          result.push({ id: 'time', title: 'Режим работы', value: c.time });\n        if (c.phone)\n          result.push({ id: 'phone', title: 'Телефон', value: c.phone });\n\n        setContactData(result);\n        if (c.google_maps) setMapSrc(c.google_maps);\n      })\n      .catch((err) => {\n        showNotification(\n          'Не удалось загрузить контакты. Возможно идет тех.работа',\n          'error'\n        );\n      })\n      .finally(() => setDataLoading(false)); // 👈 когда данные пришли\n  }, []);\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\">\n                <strong>Контакты</strong>\n              </h1>\n              <i\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n                className=\"redLine\"\n              ></i>\n            </div>\n            <section className={styles.contactSection}>\n              <div className={styles.leftColumn}>\n                <div className={styles.cardWrapper}>\n                  {dataLoading\n                    ? [1, 2, 3].map((i) => (\n                      <div\n                        key={i}\n                        className={`${styles.cardItem} ${styles.skeletonCard}`}\n                        data-aos=\"fade-up\"\n                      >\n                        <div className={styles.cardItemHeader}>\n                          <div className={styles.iconSkeleton}></div>\n                          <div\n                            className={styles.textSkeleton}\n                            style={{ width: '60%' }}\n                          ></div>\n                        </div>\n                        <div\n                          className={styles.textSkeleton}\n                          style={{ width: '80%', height: '16px' }}\n                        ></div>\n                      </div>\n                    ))\n                    : contactData.map((item) => (\n                      <div key={item.id} className={styles.cardItem}>\n                        <div className={styles.cardItemHeader}>\n                          <div className={styles.icon}>\n                            {iconMap[item.id]}\n                          </div>\n                          <h3>{item.title}</h3>\n                        </div>\n                        {item.id === 'phone' ? (\n                          <a\n                            href={`tel:${item.value}`}\n                            className={styles.link}\n                          >\n                            {item.value}\n                          </a>\n                        ) : (\n                          <p>{item.value}</p>\n                        )}\n                      </div>\n                    ))}\n                </div>\n              </div>\n              {/* <div className={styles.rightColumn} data-aos=\"fade-up\">\n                <div className={styles.mapWrapper}>\n                  {dataLoading ? (\n                    <div className={styles.mapSkeleton}></div>\n                  ) : (\n                    <iframe\n                      className={styles.map}\n                      title=\"Google maps location\"\n                      src={mapSrc}\n                      loading=\"lazy\"\n                      allowFullScreen\n                    ></iframe>\n                  )}\n                </div>\n              </div> */}\n            </section>\n            <CallbackForm formType=\"contact\" title=\"Заказать Звонок\" />\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Contact;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,OAASC,cAAc,CAAEC,OAAO,CAAEC,UAAU,KAAQ,gBAAgB,CACpE,MAAO,CAAAC,YAAY,KAAM,eAAe,CACxC,MAAO,CAAAC,YAAY,KAAM,+CAA+C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzE,KAAM,CAAAC,OAAO,CAAG,CACdC,OAAO,cAAEN,IAAA,CAACN,cAAc,GAAE,CAAC,CAC3Ba,IAAI,cAAEP,IAAA,CAACL,OAAO,GAAE,CAAC,CACjBa,KAAK,cAAER,IAAA,CAACJ,UAAU,GAAE,CACtB,CAAC,CAED,KAAM,CAAAa,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqB,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAAE;AACtD,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACyB,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,CAAE6B,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAE3E,KAAM,CAAAC,gBAAgB,CAAG,QAAAA,CAACF,OAAO,CAAuB,IAArB,CAAAC,IAAI,CAAAE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACjDJ,eAAe,CAAC,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAC,CAClCK,UAAU,CAAC,IAAMP,eAAe,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAED/B,SAAS,CAAC,IAAM,CACdG,GAAG,CAACkC,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGV,UAAU,CAAC,IAAM,CAC7Bf,UAAU,CAAC,KAAK,CAAC,CACjBqB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXE,YAAY,CAACD,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN7C,SAAS,CAAC,IAAM,CACdgD,KAAK,CAAC,oCAAoC,CAAC,CACxCC,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,EAAK,CACd,KAAM,CAAAC,CAAC,CAAGD,IAAI,CAACE,QAAQ,EAAI,CAAC,CAAC,CAC7B,KAAM,CAAAC,MAAM,CAAG,EAAE,CAEjB,GAAIF,CAAC,CAACrC,OAAO,CACXuC,MAAM,CAACC,IAAI,CAAC,CAAEC,EAAE,CAAE,SAAS,CAAEC,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAEN,CAAC,CAACrC,OAAQ,CAAC,CAAC,CAClE,GAAIqC,CAAC,CAACpC,IAAI,CACRsC,MAAM,CAACC,IAAI,CAAC,CAAEC,EAAE,CAAE,MAAM,CAAEC,KAAK,CAAE,cAAc,CAAEC,KAAK,CAAEN,CAAC,CAACpC,IAAK,CAAC,CAAC,CACnE,GAAIoC,CAAC,CAACnC,KAAK,CACTqC,MAAM,CAACC,IAAI,CAAC,CAAEC,EAAE,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAEN,CAAC,CAACnC,KAAM,CAAC,CAAC,CAEhEO,cAAc,CAAC8B,MAAM,CAAC,CACtB,GAAIF,CAAC,CAACO,WAAW,CAAEjC,SAAS,CAAC0B,CAAC,CAACO,WAAW,CAAC,CAC7C,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,EAAK,CACd9B,gBAAgB,CACd,yDAAyD,CACzD,OACF,CAAC,CACH,CAAC,CAAC,CACD+B,OAAO,CAAC,IAAMxC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAE;AAC3C,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEX,KAAA,CAAAE,SAAA,EAAAkD,QAAA,eACEtD,IAAA,CAACF,YAAY,EAACsB,OAAO,CAAEF,YAAY,CAACE,OAAQ,CAACC,IAAI,CAAEH,YAAY,CAACG,IAAK,CAAE,CAAC,CACvEX,OAAO,cACNV,IAAA,QAAKuD,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BtD,IAAA,QAAKuD,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENvD,IAAA,QAAKuD,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBpD,KAAA,QAAKqD,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBtD,IAAA,OAAIuD,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAAD,QAAA,cACtCtD,IAAA,WAAAsD,QAAA,CAAQ,kDAAQ,CAAQ,CAAC,CACvB,CAAC,cACLtD,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBuD,SAAS,CAAC,SAAS,CACjB,CAAC,EACF,CAAC,cACNvD,IAAA,YAASuD,SAAS,CAAE/D,MAAM,CAACgE,cAAe,CAAAF,QAAA,cACxCtD,IAAA,QAAKuD,SAAS,CAAE/D,MAAM,CAACiE,UAAW,CAAAH,QAAA,cAChCtD,IAAA,QAAKuD,SAAS,CAAE/D,MAAM,CAACkE,WAAY,CAAAJ,QAAA,CAChC1C,WAAW,CACR,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC+C,GAAG,CAAEC,CAAC,eAChB1D,KAAA,QAEEqD,SAAS,IAAAM,MAAA,CAAKrE,MAAM,CAACsE,QAAQ,MAAAD,MAAA,CAAIrE,MAAM,CAACuE,YAAY,CAAG,CACvD,WAAS,SAAS,CAAAT,QAAA,eAElBpD,KAAA,QAAKqD,SAAS,CAAE/D,MAAM,CAACwE,cAAe,CAAAV,QAAA,eACpCtD,IAAA,QAAKuD,SAAS,CAAE/D,MAAM,CAACyE,YAAa,CAAM,CAAC,cAC3CjE,IAAA,QACEuD,SAAS,CAAE/D,MAAM,CAAC0E,YAAa,CAC/BhC,KAAK,CAAE,CAAEiC,KAAK,CAAE,KAAM,CAAE,CACpB,CAAC,EACJ,CAAC,cACNnE,IAAA,QACEuD,SAAS,CAAE/D,MAAM,CAAC0E,YAAa,CAC/BhC,KAAK,CAAE,CAAEiC,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,MAAO,CAAE,CACpC,CAAC,GAdFR,CAeF,CACN,CAAC,CACA9C,WAAW,CAAC6C,GAAG,CAAEU,IAAI,eACrBnE,KAAA,QAAmBqD,SAAS,CAAE/D,MAAM,CAACsE,QAAS,CAAAR,QAAA,eAC5CpD,KAAA,QAAKqD,SAAS,CAAE/D,MAAM,CAACwE,cAAe,CAAAV,QAAA,eACpCtD,IAAA,QAAKuD,SAAS,CAAE/D,MAAM,CAAC8E,IAAK,CAAAhB,QAAA,CACzBjD,OAAO,CAACgE,IAAI,CAACtB,EAAE,CAAC,CACd,CAAC,cACN/C,IAAA,OAAAsD,QAAA,CAAKe,IAAI,CAACrB,KAAK,CAAK,CAAC,EAClB,CAAC,CACLqB,IAAI,CAACtB,EAAE,GAAK,OAAO,cAClB/C,IAAA,MACEuE,IAAI,QAAAV,MAAA,CAASQ,IAAI,CAACpB,KAAK,CAAG,CAC1BM,SAAS,CAAE/D,MAAM,CAACgF,IAAK,CAAAlB,QAAA,CAEtBe,IAAI,CAACpB,KAAK,CACV,CAAC,cAEJjD,IAAA,MAAAsD,QAAA,CAAIe,IAAI,CAACpB,KAAK,CAAI,CACnB,GAhBOoB,IAAI,CAACtB,EAiBV,CACN,CAAC,CACD,CAAC,CACH,CAAC,CAgBC,CAAC,cACV/C,IAAA,CAACH,YAAY,EAAC4E,QAAQ,CAAC,SAAS,CAACzB,KAAK,CAAC,uFAAiB,CAAE,CAAC,EACxD,CAAC,CACH,CACN,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAvC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}