{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/Pages/Parts/Parts.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/parts/parts-1.webp';\nimport img_2 from '../../../../asset/imgs/owners/parts/parts-2.webp';\nimport img_3 from '../../../../asset/imgs/owners/parts/parts-3.webp';\nimport styles from '../../owners.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Parts = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.layout,\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: styles.main,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.mainContainer,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                \"data-aos\": \"fade-up\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u041E\\u0420\\u0418\\u0413\\u0418\\u041D\\u0410\\u041B\\u042C\\u041D\\u042B\\u0415 \\u0414\\u0415\\u0422\\u0410\\u041B\\u0418\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.underText,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"100\",\n                children: \"\\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0437\\u0430\\u043F\\u0447\\u0430\\u0441\\u0442\\u0438 GWM \\u0441\\u043E\\u0445\\u0440\\u0430\\u043D\\u044F\\u0442 \\u0432\\u0430\\u0448 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C \\u0432 \\u043F\\u0435\\u0440\\u0432\\u043E\\u043D\\u0430\\u0447\\u0430\\u043B\\u044C\\u043D\\u043E\\u043C \\u0438 \\u0430\\u0443\\u0442\\u0435\\u043D\\u0442\\u0438\\u0447\\u043D\\u043E\\u043C \\u0432\\u0438\\u0434\\u0435, \\u0447\\u0442\\u043E \\u0441\\u043E\\u0445\\u0440\\u0430\\u043D\\u0438\\u0442 \\u0438 \\u0443\\u0432\\u0435\\u043B\\u0438\\u0447\\u0438\\u0442 \\u0435\\u0433\\u043E \\u043E\\u0431\\u0449\\u0443\\u044E \\u0441\\u0442\\u043E\\u0438\\u043C\\u043E\\u0441\\u0442\\u044C \\u0432 \\u0434\\u043E\\u043B\\u0433\\u043E\\u0441\\u0440\\u043E\\u0447\\u043D\\u043E\\u0439 \\u043F\\u0435\\u0440\\u0441\\u043F\\u0435\\u043A\\u0442\\u0438\\u0432\\u0435 \\u043D\\u0430 \\u043F\\u0440\\u043E\\u0442\\u044F\\u0436\\u0435\\u043D\\u0438\\u0438 \\u0432\\u0441\\u0435\\u0433\\u043E \\u0441\\u0440\\u043E\\u043A\\u0430 \\u0441\\u043B\\u0443\\u0436\\u0431\\u044B.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"150\",\n                className: styles.redLine\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.flexImg,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"100\",\n                  src: img,\n                  alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                  className: styles.banner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"150\",\n                  src: img_2,\n                  alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                  className: styles.banner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"200\",\n                  src: img_3,\n                  alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                  className: styles.banner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.textContent,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"400\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u041E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0438 GWM \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u044C\\u043D\\u043E \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u044B \\u0434\\u043B\\u044F \\u043E\\u043F\\u0442\\u0438\\u043C\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0443\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u043A\\u0438, \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u043E\\u043D\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0440\\u0430\\u0431\\u043E\\u0442\\u044B \\u0431\\u0435\\u0437 \\u043D\\u0435\\u0431\\u043B\\u0430\\u0433\\u043E\\u043F\\u0440\\u0438\\u044F\\u0442\\u043D\\u043E\\u0433\\u043E \\u0432\\u043E\\u0437\\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u043D\\u0430 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0435 \\u043A\\u043E\\u043C\\u043F\\u043E\\u043D\\u0435\\u043D\\u0442\\u044B \\u0438\\u043B\\u0438 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C. \\u041F\\u043E\\u0434\\u043A\\u0440\\u0435\\u043F\\u043B\\u0435\\u043D\\u044B \\u043F\\u043E\\u043B\\u043D\\u043E\\u0439 \\u0433\\u0430\\u0440\\u0430\\u043D\\u0442\\u0438\\u0435\\u0439 \\u043D\\u0430 1 \\u0433\\u043E\\u0434/20 000 \\u043A\\u043C \\u043F\\u0440\\u043E\\u0431\\u0435\\u0433\\u0430 \\u043F\\u043E \\u0432\\u0441\\u0435\\u0439 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0435. \\u041F\\u0440\\u0438 \\u0443\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u043A\\u0435 \\u0430\\u0432\\u0442\\u043E\\u0440\\u0438\\u0437\\u043E\\u0432\\u0430\\u043D\\u043D\\u044B\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u043E\\u043C GWM \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0438 GWM \\u043E\\u0442\\u0441\\u043B\\u0435\\u0436\\u0438\\u0432\\u0430\\u044E\\u0442\\u0441\\u044F \\u043D\\u0430 \\u044D\\u0442\\u0430\\u043F\\u0435 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0430, \\u043E\\u0431\\u0435\\u0441\\u043F\\u0435\\u0447\\u0438\\u0432\\u0430\\u044E\\u0442 \\u043E\\u0442\\u043B\\u0438\\u0447\\u043D\\u043E\\u0435 \\u0441\\u043E\\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0446\\u0435\\u043D\\u044B \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0441 \\u0442\\u043E\\u0447\\u043A\\u0438 \\u0437\\u0440\\u0435\\u043D\\u0438\\u044F \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430, \\u044D\\u043A\\u0441\\u043F\\u043B\\u0443\\u0430\\u0442\\u0430\\u0446\\u0438\\u043E\\u043D\\u043D\\u044B\\u0445 \\u0440\\u0430\\u0441\\u0445\\u043E\\u0434\\u043E\\u0432 \\u0438 \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u041D\\u0435 \\u0438\\u0434\\u0438\\u0442\\u0435 \\u043D\\u0430 \\u043A\\u043E\\u043C\\u043F\\u0440\\u043E\\u043C\\u0438\\u0441\\u0441 \\u043E\\u0442\\u043D\\u043E\\u0441\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u043D\\u0430\\u0441\\u0442\\u0430\\u0438\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u0430 \\u0443\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u043A\\u0435 \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0435\\u0439 GWM \\u043D\\u0430 \\u043F\\u0440\\u043E\\u0442\\u044F\\u0436\\u0435\\u043D\\u0438\\u0438 \\u0432\\u0441\\u0435\\u0433\\u043E \\u0441\\u0440\\u043E\\u043A\\u0430 \\u0441\\u043B\\u0443\\u0436\\u0431\\u044B \\u0432\\u0430\\u0448\\u0435\\u0433\\u043E \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0414\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u0434\\u043E\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u0438\\u043B\\u0438 \\u0437\\u0430\\u043A\\u0430\\u0437\\u0430 \\u043E\\u0440\\u0438\\u0433\\u0438\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0435\\u0439 GWM \\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0432\\u0430\\u0448\\u0438\\u043C \\u043C\\u0435\\u0441\\u0442\\u043D\\u044B\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u043E\\u043C GWM \\u0441\\u0435\\u0433\\u043E\\u0434\\u043D\\u044F!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Parts, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Parts;\nexport default Parts;\nvar _c;\n$RefreshReg$(_c, \"Parts\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "img_2", "img_3", "styles", "AOS", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Parts", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "main", "mainContainer", "underText", "redLine", "flexImg", "src", "alt", "banner", "textContent", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Parts/Parts.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/parts/parts-1.webp';\nimport img_2 from '../../../../asset/imgs/owners/parts/parts-2.webp';\nimport img_3 from '../../../../asset/imgs/owners/parts/parts-3.webp';\nimport styles from '../../owners.module.css';\n\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nconst Parts = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ОРИГИНАЛЬНЫЕ ДЕТАЛИ</strong>\n                  </h1>\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Оригинальные запчасти GWM сохранят ваш автомобиль в\n                    первоначальном и аутентичном виде, что сохранит и увеличит\n                    его общую стоимость в долгосрочной перспективе на протяжении\n                    всего срока службы.\n                  </span>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n\n                  <div className={styles.flexImg}>\n                    <img\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"100\"\n                      src={img}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <img\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"150\"\n                      src={img_2}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <img\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"200\"\n                      src={img_3}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                  </div>\n\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"400\"\n                  >\n                    <p>\n                      Оригинальные детали GWM специально разработаны для\n                      оптимальной установки, функционирования и работы без\n                      неблагоприятного воздействия на другие компоненты или\n                      производительность. Подкреплены полной гарантией на 1\n                      год/20 000 км пробега по всей стране. При установке\n                      авторизованным дилером GWM оригинальные детали GWM\n                      отслеживаются на этапе производства, обеспечивают отличное\n                      соотношение цены и качества с точки зрения качества,\n                      эксплуатационных расходов и полного спокойствия.\n                    </p>\n                    <p>\n                      Не идите на компромисс относительно качества и всегда\n                      настаивайте на установке только оригинальных деталей GWM\n                      на протяжении всего срока службы вашего автомобиля.\n                    </p>\n                    <p>\n                      Для получения дополнительной информации или заказа\n                      оригинальных деталей GWM свяжитесь с вашим местным дилером\n                      GWM сегодня!\n                    </p>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Parts;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,GAAG,MAAM,kDAAkD;AAClE,OAAOC,KAAK,MAAM,kDAAkD;AACpE,OAAOC,KAAK,MAAM,kDAAkD;AACpE,OAAOC,MAAM,MAAM,yBAAyB;AAE5C,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdO,GAAG,CAACS,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBnB,OAAA;UAAKoB,SAAS,EAAEvB,MAAM,CAAC4B,MAAO;UAAAN,QAAA,gBAC5BnB,OAAA,CAACP,OAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXxB,OAAA;YAAMoB,SAAS,EAAEvB,MAAM,CAAC6B,IAAK;YAAAP,QAAA,eAC3BnB,OAAA;cAAKoB,SAAS,EAAEvB,MAAM,CAAC8B,aAAc;cAAAR,QAAA,gBACnCnB,OAAA;gBAAI,YAAS,SAAS;gBAAAmB,QAAA,eACpBnB,OAAA;kBAAAmB,QAAA,EAAQ;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACLxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAAC+B,SAAU;gBAC5B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAT,QAAA,EACrB;cAKD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPxB,OAAA;gBACE,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBACpBoB,SAAS,EAAEvB,MAAM,CAACgC;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAELxB,OAAA;gBAAKoB,SAAS,EAAEvB,MAAM,CAACiC,OAAQ;gBAAAX,QAAA,gBAC7BnB,OAAA;kBACE,YAAS,SAAS;kBAClB,kBAAe,KAAK;kBACpB+B,GAAG,EAAErC,GAAI;kBACTsC,GAAG,EAAC,uGAAuB;kBAC3BZ,SAAS,EAAEvB,MAAM,CAACoC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFxB,OAAA;kBACE,YAAS,SAAS;kBAClB,kBAAe,KAAK;kBACpB+B,GAAG,EAAEpC,KAAM;kBACXqC,GAAG,EAAC,uGAAuB;kBAC3BZ,SAAS,EAAEvB,MAAM,CAACoC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFxB,OAAA;kBACE,YAAS,SAAS;kBAClB,kBAAe,KAAK;kBACpB+B,GAAG,EAAEnC,KAAM;kBACXoC,GAAG,EAAC,uGAAuB;kBAC3BZ,SAAS,EAAEvB,MAAM,CAACoC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAACqC,WAAY;gBAC9B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAf,QAAA,gBAEpBnB,OAAA;kBAAAmB,QAAA,EAAG;gBAUH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAAmB,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJxB,OAAA;kBAAAmB,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CA/GID,KAAK;AAAAgC,EAAA,GAALhC,KAAK;AAiHX,eAAeA,KAAK;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}