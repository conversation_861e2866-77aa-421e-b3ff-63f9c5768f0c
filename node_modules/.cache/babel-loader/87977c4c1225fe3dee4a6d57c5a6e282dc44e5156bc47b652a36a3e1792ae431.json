{"ast": null, "code": "import { spring } from 'motion-dom';\nimport { createAnimationsFromSequence } from '../sequence/create.mjs';\nimport { animateSubject } from './subject.mjs';\nfunction animateSequence(sequence, options, scope) {\n  const animations = [];\n  const animationDefinitions = createAnimationsFromSequence(sequence, options, scope, {\n    spring\n  });\n  animationDefinitions.forEach(({\n    keyframes,\n    transition\n  }, subject) => {\n    animations.push(...animateSubject(subject, keyframes, transition));\n  });\n  return animations;\n}\nexport { animateSequence };", "map": {"version": 3, "names": ["spring", "createAnimationsFromSequence", "animateSubject", "animateSequence", "sequence", "options", "scope", "animations", "animationDefinitions", "for<PERSON>ach", "keyframes", "transition", "subject", "push"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/animate/sequence.mjs"], "sourcesContent": ["import { spring } from 'motion-dom';\nimport { createAnimationsFromSequence } from '../sequence/create.mjs';\nimport { animateSubject } from './subject.mjs';\n\nfunction animateSequence(sequence, options, scope) {\n    const animations = [];\n    const animationDefinitions = createAnimationsFromSequence(sequence, options, scope, { spring });\n    animationDefinitions.forEach(({ keyframes, transition }, subject) => {\n        animations.push(...animateSubject(subject, keyframes, transition));\n    });\n    return animations;\n}\n\nexport { animateSequence };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,YAAY;AACnC,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,cAAc,QAAQ,eAAe;AAE9C,SAASC,eAAeA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAE;EAC/C,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,oBAAoB,GAAGP,4BAA4B,CAACG,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAAEN;EAAO,CAAC,CAAC;EAC/FQ,oBAAoB,CAACC,OAAO,CAAC,CAAC;IAAEC,SAAS;IAAEC;EAAW,CAAC,EAAEC,OAAO,KAAK;IACjEL,UAAU,CAACM,IAAI,CAAC,GAAGX,cAAc,CAACU,OAAO,EAAEF,SAAS,EAAEC,UAAU,CAAC,CAAC;EACtE,CAAC,CAAC;EACF,OAAOJ,UAAU;AACrB;AAEA,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}