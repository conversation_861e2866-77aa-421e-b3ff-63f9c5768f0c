{"ast": null, "code": "const cubicBezierAsString = _ref => {\n  let [a, b, c, d] = _ref;\n  return \"cubic-bezier(\".concat(a, \", \").concat(b, \", \").concat(c, \", \").concat(d, \")\");\n};\nexport { cubicBezierAsString };", "map": {"version": 3, "names": ["cubicBezierAsString", "_ref", "a", "b", "c", "d", "concat"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs"], "sourcesContent": ["const cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n\nexport { cubicBezierAsString };\n"], "mappings": "AAAA,MAAMA,mBAAmB,GAAGC,IAAA;EAAA,IAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAAJ,IAAA;EAAA,uBAAAK,MAAA,CAAqBJ,CAAC,QAAAI,MAAA,CAAKH,CAAC,QAAAG,MAAA,CAAKF,CAAC,QAAAE,MAAA,CAAKD,CAAC;AAAA,CAAG;AAEpF,SAASL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}