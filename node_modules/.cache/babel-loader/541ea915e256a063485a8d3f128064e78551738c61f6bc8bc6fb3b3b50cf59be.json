{"ast": null, "code": "// btn imsg\nimport news from '../imgs/icons/11.png';\nimport loc from '../imgs/icons/21.png';\nimport drive from '../imgs/icons/31.png';\nexport const btnListData = [{\n  id: 1,\n  title: 'Новости',\n  icon: news,\n  url: '/news-list'\n}, {\n  id: 2,\n  title: 'Дилерский центр',\n  icon: loc,\n  url: '/contact'\n}, {\n  id: 3,\n  title: 'Акции',\n  icon: drive,\n  url: '/offers'\n}, {\n  id: 4,\n  title: 'Тест-драйв',\n  icon: drive,\n  url: '/book-a-test-drive'\n}];\nexport const footerMenuData = [{\n  id: 1,\n  title: 'GWM',\n  items: [{\n    id: 1,\n    title: 'Все модели',\n    url: '/models'\n  }, {\n    id: 2,\n    title: 'Внедорожник',\n    url: '/models'\n  }, {\n    id: 3,\n    title: '<PERSON><PERSON>тчбек',\n    url: '/models'\n  }, {\n    id: 4,\n    title: 'Кроссовер',\n    url: '/models'\n  }, {\n    id: 5,\n    title: 'Купе',\n    url: '/models'\n  }]\n}, {\n  id: 2,\n  title: 'Выбор и покупка',\n  items: [{\n    id: 1,\n    title: 'Тест-драйв',\n    url: '/book-a-test-drive'\n  }, {\n    id: 2,\n    title: 'Акции',\n    url: '/offers'\n  }, {\n    id: 3,\n    title: 'Дилерский центр',\n    url: '/contact'\n  }]\n}, {\n  id: 3,\n  title: 'Откройте для себя GWM',\n  items: [{\n    id: 1,\n    title: 'Новости',\n    url: '/news-list'\n  }, {\n    id: 2,\n    title: 'О GWM',\n    url: '/about-gwm'\n  }, {\n    id: 3,\n    title: 'История',\n    url: '/about-gwm/history'\n  }, {\n    id: 4,\n    title: 'Контакты',\n    url: '/contact'\n  }, {\n    id: 5,\n    title: 'Карьера',\n    url: 'https://job.vector.tj/' // url\n  }]\n}, {\n  id: 4,\n  title: 'Владельцам',\n  items: [{\n    id: 1,\n    title: 'Обслуживание клиентов',\n    url: '/owners/care'\n  }, {\n    id: 2,\n    title: 'Гарантия и обслуживание',\n    url: '/owners/warranty'\n  }, {\n    id: 3,\n    title: 'Оригинальные детали',\n    url: '/owners/accessories'\n  }]\n}];", "map": {"version": 3, "names": ["news", "loc", "drive", "btnListData", "id", "title", "icon", "url", "footerMenuData", "items"], "sources": ["/var/www/html/gwm.tj/src/asset/data/footerData.js"], "sourcesContent": ["// btn imsg\nimport news from '../imgs/icons/11.png';\nimport loc from '../imgs/icons/21.png';\nimport drive from '../imgs/icons/31.png';\n\nexport const btnListData = [\n  {\n    id: 1,\n    title: 'Новости',\n    icon: news,\n    url: '/news-list',\n  },\n  {\n    id: 2,\n    title: 'Дилерский центр',\n    icon: loc,\n    url: '/contact',\n  },\n  {\n    id: 3,\n    title: 'Акции',\n    icon: drive,\n    url: '/offers',\n  },\n  {\n    id: 4,\n    title: 'Тест-драйв',\n    icon: drive,\n    url: '/book-a-test-drive',\n  },\n];\n\nexport const footerMenuData = [\n  {\n    id: 1,\n    title: 'GWM',\n    items: [\n      {\n        id: 1,\n        title: 'Все модели',\n        url: '/models',\n      },\n      {\n        id: 2,\n        title: 'Внедорожник',\n        url: '/models',\n      },\n      {\n        id: 3,\n        title: 'Хэтчбек',\n        url: '/models',\n      },\n      {\n        id: 4,\n        title: 'Кроссовер',\n        url: '/models',\n      },\n      {\n        id: 5,\n        title: 'Купе',\n        url: '/models',\n      },\n    ],\n  },\n  {\n    id: 2,\n    title: 'Выбор и покупка',\n    items: [\n      {\n        id: 1,\n        title: 'Тест-драйв',\n        url: '/book-a-test-drive',\n      },\n      {\n        id: 2,\n        title: 'Акции',\n        url: '/offers',\n      },\n      {\n        id: 3,\n        title: 'Дилерский центр',\n        url: '/contact',\n      },\n    ],\n  },\n  {\n    id: 3,\n    title: 'Откройте для себя GWM',\n    items: [\n      {\n        id: 1,\n        title: 'Новости',\n        url: '/news-list',\n      },\n      {\n        id: 2,\n        title: 'О GWM',\n        url: '/about-gwm',\n      },\n      {\n        id: 3,\n        title: 'История',\n        url: '/about-gwm/history',\n      },\n      {\n        id: 4,\n        title: 'Контакты',\n        url: '/contact',\n      },\n      {\n        id: 5,\n        title: 'Карьера',\n        url: 'https://job.vector.tj/', // url\n      },\n    ],\n  },\n  {\n    id: 4,\n    title: 'Владельцам',\n    items: [\n      {\n        id: 1,\n        title: 'Обслуживание клиентов',\n        url: '/owners/care',\n      },\n      {\n        id: 2,\n        title: 'Гарантия и обслуживание',\n        url: '/owners/warranty',\n      },\n      {\n        id: 3,\n        title: 'Оригинальные детали',\n        url: '/owners/accessories',\n      },\n    ],\n  },\n];\n"], "mappings": "AAAA;AACA,OAAOA,IAAI,MAAM,sBAAsB;AACvC,OAAOC,GAAG,MAAM,sBAAsB;AACtC,OAAOC,KAAK,MAAM,sBAAsB;AAExC,OAAO,MAAMC,WAAW,GAAG,CACzB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAEN,IAAI;EACVO,GAAG,EAAE;AACP,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAEL,GAAG;EACTM,GAAG,EAAE;AACP,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAEJ,KAAK;EACXK,GAAG,EAAE;AACP,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAEJ,KAAK;EACXK,GAAG,EAAE;AACP,CAAC,CACF;AAED,OAAO,MAAMC,cAAc,GAAG,CAC5B;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,KAAK;EACZI,KAAK,EAAE,CACL;IACEL,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,YAAY;IACnBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,aAAa;IACpBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,WAAW;IAClBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE;EACP,CAAC;AAEL,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,iBAAiB;EACxBI,KAAK,EAAE,CACL;IACEL,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,YAAY;IACnBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,OAAO;IACdE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBE,GAAG,EAAE;EACP,CAAC;AAEL,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BI,KAAK,EAAE,CACL;IACEL,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,OAAO;IACdE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,UAAU;IACjBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE,wBAAwB,CAAE;EACjC,CAAC;AAEL,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBI,KAAK,EAAE,CACL;IACEL,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BE,GAAG,EAAE;EACP,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}