{"ast": null, "code": "import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { setStyle } from '../render/dom/style-set.mjs';\nimport { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation extends WithPromise {\n  constructor(options) {\n    super();\n    this.finishedTime = null;\n    this.isStopped = false;\n    if (!options) return;\n    const {\n      element,\n      name,\n      keyframes,\n      pseudoElement,\n      allowFlatten = false,\n      finalKeyframe,\n      onComplete\n    } = options;\n    this.isPseudoElement = Boolean(pseudoElement);\n    this.allowFlatten = allowFlatten;\n    this.options = options;\n    invariant(typeof options.type !== \"string\", \"animateMini doesn't support \\\"type\\\" as a string. Did you mean to import { spring } from \\\"motion\\\"?\");\n    const transition = applyGeneratorOptions(options);\n    this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n    if (transition.autoplay === false) {\n      this.animation.pause();\n    }\n    this.animation.onfinish = () => {\n      this.finishedTime = this.time;\n      if (!pseudoElement) {\n        const keyframe = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        if (this.updateMotionValue) {\n          this.updateMotionValue(keyframe);\n        } else {\n          /**\n           * If we can, we want to commit the final style as set by the user,\n           * rather than the computed keyframe value supplied by the animation.\n           */\n          setStyle(element, name, keyframe);\n        }\n        this.animation.cancel();\n      }\n      onComplete === null || onComplete === void 0 || onComplete();\n      this.notifyFinished();\n    };\n  }\n  play() {\n    if (this.isStopped) return;\n    this.animation.play();\n    if (this.state === \"finished\") {\n      this.updateFinished();\n    }\n  }\n  pause() {\n    this.animation.pause();\n  }\n  complete() {\n    var _this$animation$finis, _this$animation;\n    (_this$animation$finis = (_this$animation = this.animation).finish) === null || _this$animation$finis === void 0 || _this$animation$finis.call(_this$animation);\n  }\n  cancel() {\n    try {\n      this.animation.cancel();\n    } catch (e) {}\n  }\n  stop() {\n    if (this.isStopped) return;\n    this.isStopped = true;\n    const {\n      state\n    } = this;\n    if (state === \"idle\" || state === \"finished\") {\n      return;\n    }\n    if (this.updateMotionValue) {\n      this.updateMotionValue();\n    } else {\n      this.commitStyles();\n    }\n    if (!this.isPseudoElement) this.cancel();\n  }\n  /**\n   * WAAPI doesn't natively have any interruption capabilities.\n   *\n   * In this method, we commit styles back to the DOM before cancelling\n   * the animation.\n   *\n   * This is designed to be overridden by NativeAnimationExtended, which\n   * will create a renderless JS animation and sample it twice to calculate\n   * its current value, \"previous\" value, and therefore allow\n   * Motion to also correctly calculate velocity for any subsequent animation\n   * while deferring the commit until the next animation frame.\n   */\n  commitStyles() {\n    if (!this.isPseudoElement) {\n      var _this$animation$commi, _this$animation2;\n      (_this$animation$commi = (_this$animation2 = this.animation).commitStyles) === null || _this$animation$commi === void 0 || _this$animation$commi.call(_this$animation2);\n    }\n  }\n  get duration() {\n    var _this$animation$effec, _this$animation$effec2;\n    const duration = ((_this$animation$effec = this.animation.effect) === null || _this$animation$effec === void 0 || (_this$animation$effec2 = _this$animation$effec.getComputedTiming) === null || _this$animation$effec2 === void 0 ? void 0 : _this$animation$effec2.call(_this$animation$effec).duration) || 0;\n    return millisecondsToSeconds(Number(duration));\n  }\n  get time() {\n    return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n  }\n  set time(newTime) {\n    this.finishedTime = null;\n    this.animation.currentTime = secondsToMilliseconds(newTime);\n  }\n  /**\n   * The playback speed of the animation.\n   * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n   */\n  get speed() {\n    return this.animation.playbackRate;\n  }\n  set speed(newSpeed) {\n    // Allow backwards playback after finishing\n    if (newSpeed < 0) this.finishedTime = null;\n    this.animation.playbackRate = newSpeed;\n  }\n  get state() {\n    return this.finishedTime !== null ? \"finished\" : this.animation.playState;\n  }\n  get startTime() {\n    return Number(this.animation.startTime);\n  }\n  set startTime(newStartTime) {\n    this.animation.startTime = newStartTime;\n  }\n  /**\n   * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n   */\n  attachTimeline(_ref) {\n    let {\n      timeline,\n      observe\n    } = _ref;\n    if (this.allowFlatten) {\n      var _this$animation$effec3;\n      (_this$animation$effec3 = this.animation.effect) === null || _this$animation$effec3 === void 0 || _this$animation$effec3.updateTiming({\n        easing: \"linear\"\n      });\n    }\n    this.animation.onfinish = null;\n    if (timeline && supportsScrollTimeline()) {\n      this.animation.timeline = timeline;\n      return noop;\n    } else {\n      return observe(this);\n    }\n  }\n}\nexport { NativeAnimation };", "map": {"version": 3, "names": ["invariant", "millisecondsToSeconds", "secondsToMilliseconds", "noop", "setStyle", "supportsScrollTimeline", "getFinalKeyframe", "WithPromise", "startWaapiAnimation", "applyGeneratorOptions", "NativeAnimation", "constructor", "options", "finishedTime", "isStopped", "element", "name", "keyframes", "pseudoElement", "allowFlatten", "finalKeyframe", "onComplete", "isPseudoElement", "Boolean", "type", "transition", "animation", "autoplay", "pause", "onfinish", "time", "keyframe", "speed", "updateMotionValue", "cancel", "notifyFinished", "play", "state", "updateFinished", "complete", "_this$animation$finis", "_this$animation", "finish", "call", "e", "stop", "commitStyles", "_this$animation$commi", "_this$animation2", "duration", "_this$animation$effec", "_this$animation$effec2", "effect", "getComputedTiming", "Number", "currentTime", "newTime", "playbackRate", "newSpeed", "playState", "startTime", "newStartTime", "attachTimeline", "_ref", "timeline", "observe", "_this$animation$effec3", "updateTiming", "easing"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs"], "sourcesContent": ["import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { setStyle } from '../render/dom/style-set.mjs';\nimport { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.finishedTime = null;\n        this.isStopped = false;\n        if (!options)\n            return;\n        const { element, name, keyframes, pseudoElement, allowFlatten = false, finalKeyframe, onComplete, } = options;\n        this.isPseudoElement = Boolean(pseudoElement);\n        this.allowFlatten = allowFlatten;\n        this.options = options;\n        invariant(typeof options.type !== \"string\", `animateMini doesn't support \"type\" as a string. Did you mean to import { spring } from \"motion\"?`);\n        const transition = applyGeneratorOptions(options);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.animation.onfinish = () => {\n            this.finishedTime = this.time;\n            if (!pseudoElement) {\n                const keyframe = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n                if (this.updateMotionValue) {\n                    this.updateMotionValue(keyframe);\n                }\n                else {\n                    /**\n                     * If we can, we want to commit the final style as set by the user,\n                     * rather than the computed keyframe value supplied by the animation.\n                     */\n                    setStyle(element, name, keyframe);\n                }\n                this.animation.cancel();\n            }\n            onComplete?.();\n            this.notifyFinished();\n        };\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        this.animation.play();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n        }\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish?.();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n    }\n    stop() {\n        if (this.isStopped)\n            return;\n        this.isStopped = true;\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        if (this.updateMotionValue) {\n            this.updateMotionValue();\n        }\n        else {\n            this.commitStyles();\n        }\n        if (!this.isPseudoElement)\n            this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        if (!this.isPseudoElement) {\n            this.animation.commitStyles?.();\n        }\n    }\n    get duration() {\n        const duration = this.animation.effect?.getComputedTiming?.().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.finishedTime = null;\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        // Allow backwards playback after finishing\n        if (newSpeed < 0)\n            this.finishedTime = null;\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.finishedTime !== null\n            ? \"finished\"\n            : this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    set startTime(newStartTime) {\n        this.animation.startTime = newStartTime;\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline({ timeline, observe }) {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n        this.animation.onfinish = null;\n        if (timeline && supportsScrollTimeline()) {\n            this.animation.timeline = timeline;\n            return noop;\n        }\n        else {\n            return observe(this);\n        }\n    }\n}\n\nexport { NativeAnimation };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,IAAI,QAAQ,cAAc;AAC5F,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,sBAAsB,QAAQ,uCAAuC;AAC9E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,qBAAqB,QAAQ,mCAAmC;;AAEzE;AACA;AACA;AACA,MAAMC,eAAe,SAASH,WAAW,CAAC;EACtCI,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACF,OAAO,EACR;IACJ,MAAM;MAAEG,OAAO;MAAEC,IAAI;MAAEC,SAAS;MAAEC,aAAa;MAAEC,YAAY,GAAG,KAAK;MAAEC,aAAa;MAAEC;IAAY,CAAC,GAAGT,OAAO;IAC7G,IAAI,CAACU,eAAe,GAAGC,OAAO,CAACL,aAAa,CAAC;IAC7C,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACP,OAAO,GAAGA,OAAO;IACtBZ,SAAS,CAAC,OAAOY,OAAO,CAACY,IAAI,KAAK,QAAQ,wGAAoG,CAAC;IAC/I,MAAMC,UAAU,GAAGhB,qBAAqB,CAACG,OAAO,CAAC;IACjD,IAAI,CAACc,SAAS,GAAGlB,mBAAmB,CAACO,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEQ,UAAU,EAAEP,aAAa,CAAC;IACzF,IAAIO,UAAU,CAACE,QAAQ,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC;IAC1B;IACA,IAAI,CAACF,SAAS,CAACG,QAAQ,GAAG,MAAM;MAC5B,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACiB,IAAI;MAC7B,IAAI,CAACZ,aAAa,EAAE;QAChB,MAAMa,QAAQ,GAAGzB,gBAAgB,CAACW,SAAS,EAAE,IAAI,CAACL,OAAO,EAAEQ,aAAa,EAAE,IAAI,CAACY,KAAK,CAAC;QACrF,IAAI,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACA,iBAAiB,CAACF,QAAQ,CAAC;QACpC,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoB3B,QAAQ,CAACW,OAAO,EAAEC,IAAI,EAAEe,QAAQ,CAAC;QACrC;QACA,IAAI,CAACL,SAAS,CAACQ,MAAM,CAAC,CAAC;MAC3B;MACAb,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAG,CAAC;MACd,IAAI,CAACc,cAAc,CAAC,CAAC;IACzB,CAAC;EACL;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACtB,SAAS,EACd;IACJ,IAAI,CAACY,SAAS,CAACU,IAAI,CAAC,CAAC;IACrB,IAAI,IAAI,CAACC,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;EACJ;EACAV,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,SAAS,CAACE,KAAK,CAAC,CAAC;EAC1B;EACAW,QAAQA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,eAAA;IACP,CAAAD,qBAAA,IAAAC,eAAA,OAAI,CAACf,SAAS,EAACgB,MAAM,cAAAF,qBAAA,eAArBA,qBAAA,CAAAG,IAAA,CAAAF,eAAwB,CAAC;EAC7B;EACAP,MAAMA,CAAA,EAAG;IACL,IAAI;MACA,IAAI,CAACR,SAAS,CAACQ,MAAM,CAAC,CAAC;IAC3B,CAAC,CACD,OAAOU,CAAC,EAAE,CAAE;EAChB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC/B,SAAS,EACd;IACJ,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,MAAM;MAAEuB;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;MAC1C;IACJ;IACA,IAAI,IAAI,CAACJ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,IAAI,CAACa,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,CAAC,IAAI,CAACxB,eAAe,EACrB,IAAI,CAACY,MAAM,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIY,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACxB,eAAe,EAAE;MAAA,IAAAyB,qBAAA,EAAAC,gBAAA;MACvB,CAAAD,qBAAA,IAAAC,gBAAA,OAAI,CAACtB,SAAS,EAACoB,YAAY,cAAAC,qBAAA,eAA3BA,qBAAA,CAAAJ,IAAA,CAAAK,gBAA8B,CAAC;IACnC;EACJ;EACA,IAAIC,QAAQA,CAAA,EAAG;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACX,MAAMF,QAAQ,GAAG,EAAAC,qBAAA,OAAI,CAACxB,SAAS,CAAC0B,MAAM,cAAAF,qBAAA,gBAAAC,sBAAA,GAArBD,qBAAA,CAAuBG,iBAAiB,cAAAF,sBAAA,uBAAxCA,sBAAA,CAAAR,IAAA,CAAAO,qBAA2C,CAAC,CAACD,QAAQ,KAAI,CAAC;IAC3E,OAAOhD,qBAAqB,CAACqD,MAAM,CAACL,QAAQ,CAAC,CAAC;EAClD;EACA,IAAInB,IAAIA,CAAA,EAAG;IACP,OAAO7B,qBAAqB,CAACqD,MAAM,CAAC,IAAI,CAAC5B,SAAS,CAAC6B,WAAW,CAAC,IAAI,CAAC,CAAC;EACzE;EACA,IAAIzB,IAAIA,CAAC0B,OAAO,EAAE;IACd,IAAI,CAAC3C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACa,SAAS,CAAC6B,WAAW,GAAGrD,qBAAqB,CAACsD,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;AACA;EACI,IAAIxB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACN,SAAS,CAAC+B,YAAY;EACtC;EACA,IAAIzB,KAAKA,CAAC0B,QAAQ,EAAE;IAChB;IACA,IAAIA,QAAQ,GAAG,CAAC,EACZ,IAAI,CAAC7C,YAAY,GAAG,IAAI;IAC5B,IAAI,CAACa,SAAS,CAAC+B,YAAY,GAAGC,QAAQ;EAC1C;EACA,IAAIrB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxB,YAAY,KAAK,IAAI,GAC3B,UAAU,GACV,IAAI,CAACa,SAAS,CAACiC,SAAS;EAClC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAON,MAAM,CAAC,IAAI,CAAC5B,SAAS,CAACkC,SAAS,CAAC;EAC3C;EACA,IAAIA,SAASA,CAACC,YAAY,EAAE;IACxB,IAAI,CAACnC,SAAS,CAACkC,SAAS,GAAGC,YAAY;EAC3C;EACA;AACJ;AACA;EACIC,cAAcA,CAAAC,IAAA,EAAwB;IAAA,IAAvB;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAAF,IAAA;IAChC,IAAI,IAAI,CAAC5C,YAAY,EAAE;MAAA,IAAA+C,sBAAA;MACnB,CAAAA,sBAAA,OAAI,CAACxC,SAAS,CAAC0B,MAAM,cAAAc,sBAAA,eAArBA,sBAAA,CAAuBC,YAAY,CAAC;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;IAC7D;IACA,IAAI,CAAC1C,SAAS,CAACG,QAAQ,GAAG,IAAI;IAC9B,IAAImC,QAAQ,IAAI3D,sBAAsB,CAAC,CAAC,EAAE;MACtC,IAAI,CAACqB,SAAS,CAACsC,QAAQ,GAAGA,QAAQ;MAClC,OAAO7D,IAAI;IACf,CAAC,MACI;MACD,OAAO8D,OAAO,CAAC,IAAI,CAAC;IACxB;EACJ;AACJ;AAEA,SAASvD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}