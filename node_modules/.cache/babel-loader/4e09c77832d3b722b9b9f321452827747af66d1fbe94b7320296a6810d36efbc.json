{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"axis\", \"container\"];\nimport { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\nfunction scroll(onScroll) {\n  let _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    {\n      axis = \"y\",\n      container = document.scrollingElement\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  if (!container) return noop;\n  const optionsWithDefaults = _objectSpread({\n    axis,\n    container\n  }, options);\n  return typeof onScroll === \"function\" ? attachToFunction(onScroll, optionsWithDefaults) : attachToAnimation(onScroll, optionsWithDefaults);\n}\nexport { scroll };", "map": {"version": 3, "names": ["noop", "attachToAnimation", "attachToFunction", "scroll", "onScroll", "_ref", "arguments", "length", "undefined", "axis", "container", "document", "scrollingElement", "options", "_objectWithoutProperties", "_excluded", "optionsWithDefaults", "_objectSpread"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { attachToAnimation } from './attach-animation.mjs';\nimport { attachToFunction } from './attach-function.mjs';\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? attachToFunction(onScroll, optionsWithDefaults)\n        : attachToAnimation(onScroll, optionsWithDefaults);\n}\n\nexport { scroll };\n"], "mappings": ";;;AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,gBAAgB,QAAQ,uBAAuB;AAExD,SAASC,MAAMA,CAACC,QAAQ,EAA0E;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;IAAtE;MAAEG,IAAI,GAAG,GAAG;MAAEC,SAAS,GAAGC,QAAQ,CAACC;IAA6B,CAAC,GAAAP,IAAA;IAATQ,OAAO,GAAAC,wBAAA,CAAAT,IAAA,EAAAU,SAAA;EACrF,IAAI,CAACL,SAAS,EACV,OAAOV,IAAI;EACf,MAAMgB,mBAAmB,GAAAC,aAAA;IAAKR,IAAI;IAAEC;EAAS,GAAKG,OAAO,CAAE;EAC3D,OAAO,OAAOT,QAAQ,KAAK,UAAU,GAC/BF,gBAAgB,CAACE,QAAQ,EAAEY,mBAAmB,CAAC,GAC/Cf,iBAAiB,CAACG,QAAQ,EAAEY,mBAAmB,CAAC;AAC1D;AAEA,SAASb,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}