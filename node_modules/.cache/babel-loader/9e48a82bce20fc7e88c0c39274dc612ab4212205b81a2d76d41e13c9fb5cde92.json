{"ast": null, "code": "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\nfunction animateVisualElement(visualElement, definition) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  visualElement.notify(\"AnimationStart\", definition);\n  let animation;\n  if (Array.isArray(definition)) {\n    const animations = definition.map(variant => animateVariant(visualElement, variant, options));\n    animation = Promise.all(animations);\n  } else if (typeof definition === \"string\") {\n    animation = animateVariant(visualElement, definition, options);\n  } else {\n    const resolvedDefinition = typeof definition === \"function\" ? resolveVariant(visualElement, definition, options.custom) : definition;\n    animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n  }\n  return animation.then(() => {\n    visualElement.notify(\"AnimationComplete\", definition);\n  });\n}\nexport { animateVisualElement };", "map": {"version": 3, "names": ["resolveV<PERSON>t", "animate<PERSON>arget", "animate<PERSON><PERSON><PERSON>", "animateVisualElement", "visualElement", "definition", "options", "arguments", "length", "undefined", "notify", "animation", "Array", "isArray", "animations", "map", "variant", "Promise", "all", "resolvedDefinition", "custom", "then"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => animateVariant(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = animateVariant(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? resolveVariant(visualElement, definition, options.custom)\n            : definition;\n        animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n    }\n    return animation.then(() => {\n        visualElement.notify(\"AnimationComplete\", definition);\n    });\n}\n\nexport { animateVisualElement };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iDAAiD;AAChF,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,UAAU,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjEH,aAAa,CAACM,MAAM,CAAC,gBAAgB,EAAEL,UAAU,CAAC;EAClD,IAAIM,SAAS;EACb,IAAIC,KAAK,CAACC,OAAO,CAACR,UAAU,CAAC,EAAE;IAC3B,MAAMS,UAAU,GAAGT,UAAU,CAACU,GAAG,CAAEC,OAAO,IAAKd,cAAc,CAACE,aAAa,EAAEY,OAAO,EAAEV,OAAO,CAAC,CAAC;IAC/FK,SAAS,GAAGM,OAAO,CAACC,GAAG,CAACJ,UAAU,CAAC;EACvC,CAAC,MACI,IAAI,OAAOT,UAAU,KAAK,QAAQ,EAAE;IACrCM,SAAS,GAAGT,cAAc,CAACE,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAAC;EAClE,CAAC,MACI;IACD,MAAMa,kBAAkB,GAAG,OAAOd,UAAU,KAAK,UAAU,GACrDL,cAAc,CAACI,aAAa,EAAEC,UAAU,EAAEC,OAAO,CAACc,MAAM,CAAC,GACzDf,UAAU;IAChBM,SAAS,GAAGM,OAAO,CAACC,GAAG,CAACjB,aAAa,CAACG,aAAa,EAAEe,kBAAkB,EAAEb,OAAO,CAAC,CAAC;EACtF;EACA,OAAOK,SAAS,CAACU,IAAI,CAAC,MAAM;IACxBjB,aAAa,CAACM,MAAM,CAAC,mBAAmB,EAAEL,UAAU,CAAC;EACzD,CAAC,CAAC;AACN;AAEA,SAASF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}