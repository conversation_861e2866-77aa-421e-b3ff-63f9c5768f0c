{"ast": null, "code": "export const menuData=[{id:1,title:'Гарантия',url:'/owners/warranty'},{id:2,title:'Помощь на дороге',url:'/owners/road-assistance'},{id:3,title:'Сервис и обслуживание',url:'/owners/service'},{id:4,title:'План обслуживания',url:'/owners/service-plans'},{id:5,title:'Оригинальные запчасти',url:'/owners/parts'},{id:6,title:'Аксессуары',url:'/owners/accessories'},{id:7,title:'Обслуживание клиентов',url:'/owners/care'},{id:8,title:'Справочная таблица транспортных средств',url:'/owners/vehicle-reference-table'}];", "map": {"version": 3, "names": ["menuData", "id", "title", "url"], "sources": ["/var/www/html/gwm.tj/src/asset/data/ownersMenu.js"], "sourcesContent": ["export const menuData = [\n  {\n    id: 1,\n    title: 'Гарантия',\n    url: '/owners/warranty',\n  },\n  {\n    id: 2,\n    title: 'Помощь на дороге',\n    url: '/owners/road-assistance',\n  },\n  {\n    id: 3,\n    title: 'Сервис и обслуживание',\n    url: '/owners/service',\n  },\n  {\n    id: 4,\n    title: 'План обслуживания',\n    url: '/owners/service-plans',\n  },\n  {\n    id: 5,\n    title: 'Оригинальные запчасти',\n    url: '/owners/parts',\n  },\n  {\n    id: 6,\n    title: 'Аксессуары',\n    url: '/owners/accessories',\n  },\n  {\n    id: 7,\n    title: 'Обслуживание клиентов',\n    url: '/owners/care',\n  },\n  {\n    id: 8,\n    title: 'Справочная таблица транспортных средств',\n    url: '/owners/vehicle-reference-table',\n  },\n];\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,QAAQ,CAAG,CACtB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,UAAU,CACjBC,GAAG,CAAE,kBACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,kBAAkB,CACzBC,GAAG,CAAE,yBACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,uBAAuB,CAC9BC,GAAG,CAAE,iBACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,mBAAmB,CAC1BC,GAAG,CAAE,uBACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,uBAAuB,CAC9BC,GAAG,CAAE,eACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBC,GAAG,CAAE,qBACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,uBAAuB,CAC9BC,GAAG,CAAE,cACP,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,yCAAyC,CAChDC,GAAG,CAAE,iCACP,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}