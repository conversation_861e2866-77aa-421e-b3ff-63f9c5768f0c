{"ast": null, "code": "import { useContext } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nfunction usePresenceData() {\n  const context = useContext(PresenceContext);\n  return context ? context.custom : undefined;\n}\nexport { usePresenceData };", "map": {"version": 3, "names": ["useContext", "PresenceContext", "usePresenceData", "context", "custom", "undefined"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence-data.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\nfunction usePresenceData() {\n    const context = useContext(PresenceContext);\n    return context ? context.custom : undefined;\n}\n\nexport { usePresenceData };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,mCAAmC;AAEnE,SAASC,eAAeA,CAAA,EAAG;EACvB,MAAMC,OAAO,GAAGH,UAAU,CAACC,eAAe,CAAC;EAC3C,OAAOE,OAAO,GAAGA,OAAO,CAACC,MAAM,GAAGC,SAAS;AAC/C;AAEA,SAASH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}