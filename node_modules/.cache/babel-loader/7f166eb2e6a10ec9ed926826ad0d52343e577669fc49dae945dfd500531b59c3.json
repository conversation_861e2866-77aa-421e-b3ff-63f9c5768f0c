{"ast": null, "code": "import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n  let runNextFrame = false;\n  let useDefaultElapsed = true;\n  const state = {\n    delta: 0.0,\n    timestamp: 0.0,\n    isProcessing: false\n  };\n  const flagRunNextFrame = () => runNextFrame = true;\n  const steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n    return acc;\n  }, {});\n  const {\n    setup,\n    read,\n    resolveKeyframes,\n    preUpdate,\n    update,\n    preRender,\n    render,\n    postRender\n  } = steps;\n  const processBatch = () => {\n    const timestamp = MotionGlobalConfig.useManualTiming ? state.timestamp : performance.now();\n    runNextFrame = false;\n    if (!MotionGlobalConfig.useManualTiming) {\n      state.delta = useDefaultElapsed ? 1000 / 60 : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n    }\n    state.timestamp = timestamp;\n    state.isProcessing = true;\n    // Unrolled render loop for better per-frame performance\n    setup.process(state);\n    read.process(state);\n    resolveKeyframes.process(state);\n    preUpdate.process(state);\n    update.process(state);\n    preRender.process(state);\n    render.process(state);\n    postRender.process(state);\n    state.isProcessing = false;\n    if (runNextFrame && allowKeepAlive) {\n      useDefaultElapsed = false;\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const wake = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!state.isProcessing) {\n      scheduleNextBatch(processBatch);\n    }\n  };\n  const schedule = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n      if (!runNextFrame) wake();\n      return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n  }, {});\n  const cancel = process => {\n    for (let i = 0; i < stepsOrder.length; i++) {\n      steps[stepsOrder[i]].cancel(process);\n    }\n  };\n  return {\n    schedule,\n    cancel,\n    state,\n    steps\n  };\n}\nexport { createRenderBatcher };", "map": {"version": 3, "names": ["MotionGlobalConfig", "stepsOrder", "createRenderStep", "maxElapsed", "createRenderBatcher", "scheduleNextBatch", "allowKeepAlive", "runNextFrame", "useDefaultElapsed", "state", "delta", "timestamp", "isProcessing", "flagRunNextFrame", "steps", "reduce", "acc", "key", "undefined", "setup", "read", "resolveKeyframes", "preUpdate", "update", "preRender", "render", "postRender", "processBatch", "useManualTiming", "performance", "now", "Math", "max", "min", "process", "wake", "schedule", "step", "keepAlive", "immediate", "cancel", "i", "length"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,gBAAgB,QAAQ,mBAAmB;AAEpD,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,mBAAmBA,CAACC,iBAAiB,EAAEC,cAAc,EAAE;EAC5D,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,MAAMC,KAAK,GAAG;IACVC,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,GAAG;IACdC,YAAY,EAAE;EAClB,CAAC;EACD,MAAMC,gBAAgB,GAAGA,CAAA,KAAON,YAAY,GAAG,IAAK;EACpD,MAAMO,KAAK,GAAGb,UAAU,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC1CD,GAAG,CAACC,GAAG,CAAC,GAAGf,gBAAgB,CAACW,gBAAgB,EAAEP,cAAc,GAAGW,GAAG,GAAGC,SAAS,CAAC;IAC/E,OAAOF,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAM;IAAEG,KAAK;IAAEC,IAAI;IAAEC,gBAAgB;IAAEC,SAAS;IAAEC,MAAM;IAAEC,SAAS;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGZ,KAAK;EAClG,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMhB,SAAS,GAAGX,kBAAkB,CAAC4B,eAAe,GAC9CnB,KAAK,CAACE,SAAS,GACfkB,WAAW,CAACC,GAAG,CAAC,CAAC;IACvBvB,YAAY,GAAG,KAAK;IACpB,IAAI,CAACP,kBAAkB,CAAC4B,eAAe,EAAE;MACrCnB,KAAK,CAACC,KAAK,GAAGF,iBAAiB,GACzB,IAAI,GAAG,EAAE,GACTuB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACtB,SAAS,GAAGF,KAAK,CAACE,SAAS,EAAER,UAAU,CAAC,EAAE,CAAC,CAAC;IACxE;IACAM,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAACG,YAAY,GAAG,IAAI;IACzB;IACAO,KAAK,CAACe,OAAO,CAACzB,KAAK,CAAC;IACpBW,IAAI,CAACc,OAAO,CAACzB,KAAK,CAAC;IACnBY,gBAAgB,CAACa,OAAO,CAACzB,KAAK,CAAC;IAC/Ba,SAAS,CAACY,OAAO,CAACzB,KAAK,CAAC;IACxBc,MAAM,CAACW,OAAO,CAACzB,KAAK,CAAC;IACrBe,SAAS,CAACU,OAAO,CAACzB,KAAK,CAAC;IACxBgB,MAAM,CAACS,OAAO,CAACzB,KAAK,CAAC;IACrBiB,UAAU,CAACQ,OAAO,CAACzB,KAAK,CAAC;IACzBA,KAAK,CAACG,YAAY,GAAG,KAAK;IAC1B,IAAIL,YAAY,IAAID,cAAc,EAAE;MAChCE,iBAAiB,GAAG,KAAK;MACzBH,iBAAiB,CAACsB,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMQ,IAAI,GAAGA,CAAA,KAAM;IACf5B,YAAY,GAAG,IAAI;IACnBC,iBAAiB,GAAG,IAAI;IACxB,IAAI,CAACC,KAAK,CAACG,YAAY,EAAE;MACrBP,iBAAiB,CAACsB,YAAY,CAAC;IACnC;EACJ,CAAC;EACD,MAAMS,QAAQ,GAAGnC,UAAU,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC7C,MAAMoB,IAAI,GAAGvB,KAAK,CAACG,GAAG,CAAC;IACvBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAACiB,OAAO,EAAEI,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;MAC1D,IAAI,CAAChC,YAAY,EACb4B,IAAI,CAAC,CAAC;MACV,OAAOE,IAAI,CAACD,QAAQ,CAACF,OAAO,EAAEI,SAAS,EAAEC,SAAS,CAAC;IACvD,CAAC;IACD,OAAOvB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAMwB,MAAM,GAAIN,OAAO,IAAK;IACxB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxC,UAAU,CAACyC,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC3B,KAAK,CAACb,UAAU,CAACwC,CAAC,CAAC,CAAC,CAACD,MAAM,CAACN,OAAO,CAAC;IACxC;EACJ,CAAC;EACD,OAAO;IAAEE,QAAQ;IAAEI,MAAM;IAAE/B,KAAK;IAAEK;EAAM,CAAC;AAC7C;AAEA,SAASV,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}