{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Owners/Pages/Care/Care.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/approve.webp';\nimport { Link } from 'react-router-dom';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport styles from '../../owners.module.css';\nimport OwnersForm from '../../components/form/OwnersForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Care = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.layout,\n          children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            className: styles.main,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.mainContainer,\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                \"data-aos\": \"fade-up\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u041E\\u0411\\u0421\\u041B\\u0423\\u0416\\u0418\\u0412\\u0410\\u041D\\u0418\\u0415 \\u041A\\u041B\\u0418\\u0415\\u041D\\u0422\\u041E\\u0412\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 48,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.underText,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"100\",\n                children: \"\\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u043C\\u0441\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u044F\\u0442\\u044C \\u0432\\u0430\\u043C \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0441\\u0430\\u043C\\u043E\\u0433\\u043E \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u0433\\u043E \\u0443\\u0440\\u043E\\u0432\\u043D\\u044F.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"150\",\n                className: styles.redLine\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.flexContent,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"200\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: img,\n                  alt: \"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",\n                  className: styles.banner\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.textContent,\n                  \"data-aos\": \"fade-up\",\n                  \"data-aos-delay\": \"300\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"\\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0433\\u043E\\u0442\\u043E\\u0432\\u044B \\u043F\\u043E\\u043C\\u043E\\u0447\\u044C! \\u041B\\u044E\\u0431\\u0430\\u044F \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F, \\u043E\\u043F\\u0435\\u0440\\u0430\\u0442\\u0438\\u0432\\u043D\\u0430\\u044F \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C \\u0438\\u043B\\u0438 \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0430 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 \\u2014 \\u0432\\u0441\\u0435 \\u044D\\u0442\\u043E \\u043C\\u043E\\u0436\\u043D\\u043E \\u043F\\u043E\\u043B\\u0443\\u0447\\u0438\\u0442\\u044C, \\u043F\\u0440\\u043E\\u0441\\u0442\\u043E \\u043F\\u043E\\u0437\\u0432\\u043E\\u043D\\u0438\\u0432 \\u043F\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0443\", ' ', /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"tel:6677\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"6677\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 83,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 82,\n                      columnNumber: 25\n                    }, this), ' ', \"\\u0438\\u043B\\u0438 \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u0438\\u0432 \\u0437\\u0430\\u044F\\u0432\\u043A\\u0443\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.textContent,\n                \"data-aos\": \"fade-up\",\n                \"data-aos-delay\": \"400\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0412 \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u0438\\u0445 \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430\\u0445 GWM \\u0441 \\u0432\\u0430\\u043C\\u0438 \\u0441\\u0432\\u044F\\u0436\\u0443\\u0442\\u0441\\u044F, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0443\\u0431\\u0435\\u0434\\u0438\\u0442\\u044C\\u0441\\u044F \\u0432 \\u0432\\u0430\\u0448\\u0435\\u0439 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043F\\u043E\\u0441\\u043B\\u0435 \\u043F\\u043E\\u0441\\u0435\\u0449\\u0435\\u043D\\u0438\\u044F \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430. \\u041D\\u0430\\u0448\\u0438 \\u0434\\u0438\\u043B\\u0435\\u0440\\u044B \\u0433\\u043E\\u0440\\u0434\\u044F\\u0442\\u0441\\u044F \\u0441\\u0432\\u043E\\u0438\\u043C\\u0438 \\u0441\\u0442\\u0430\\u043D\\u0434\\u0430\\u0440\\u0442\\u0430\\u043C\\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0445\\u043E\\u0442\\u0435\\u043B\\u0438 \\u0431\\u044B \\u0438\\u043C\\u0435\\u0442\\u044C \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u044C \\u0440\\u0435\\u0448\\u0438\\u0442\\u044C \\u043B\\u044E\\u0431\\u044B\\u0435 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u044B, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0443 \\u0432\\u0430\\u0441 \\u0432\\u043E\\u0437\\u043D\\u0438\\u043A\\u043D\\u0443\\u0442\\u044C. \\u0415\\u0441\\u043B\\u0438 \\u0443 \\u0432\\u0430\\u0441 \\u0435\\u0441\\u0442\\u044C \\u043A\\u0430\\u043A\\u0438\\u0435-\\u043B\\u0438\\u0431\\u043E \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441\\u044B \\u043F\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E \\u0438\\u043B\\u0438 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441\\u044B, \\u0432\\u044B\\u0437\\u044B\\u0432\\u0430\\u044E\\u0449\\u0438\\u0435 \\u0431\\u0435\\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u043E, \\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043C\\u0435\\u043D\\u0435\\u0434\\u0436\\u0435\\u0440\\u043E\\u043C \\u043F\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438 \\u043E\\u0431\\u044A\\u044F\\u0441\\u043D\\u0438\\u0442\\u0435 \\u0441\\u0432\\u043E\\u044E \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u0443. \\u0415\\u0441\\u043B\\u0438 \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0440\\u0435\\u0448\\u0435\\u043D \\u043D\\u0430 \\u044D\\u0442\\u043E\\u043C \\u044D\\u0442\\u0430\\u043F\\u0435, \\u0432\\u0430\\u043C \\u0441\\u043B\\u0435\\u0434\\u0443\\u0435\\u0442 \\u043F\\u043E\\u043F\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u043F\\u043E\\u0433\\u043E\\u0432\\u043E\\u0440\\u0438\\u0442\\u044C \\u0441 \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u0435\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438\\u043B\\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u0440\\u0435\\u043A\\u0442\\u043E\\u0440\\u043E\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430. \\u0415\\u0441\\u043B\\u0438 \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0440\\u0435\\u0448\\u0435\\u043D \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u0435\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438\\u043B\\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u0440\\u0435\\u043A\\u0442\\u043E\\u0440\\u043E\\u043C, \\u0432\\u0430\\u043C \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u0442\\u0441\\u044F \\u043F\\u0435\\u0440\\u0435\\u0434\\u0430\\u0442\\u044C \\u0435\\u0433\\u043E \\u0432 \\u0441\\u043B\\u0443\\u0436\\u0431\\u0443 \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 GWM. \\u0417\\u0430\\u0442\\u0435\\u043C \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0437\\u0430\\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D \\u0438 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D \\u0441\\u043E\\u0432\\u043C\\u0435\\u0441\\u0442\\u043D\\u043E \\u0441 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u0438\\u043C \\u0446\\u0435\\u043D\\u0442\\u0440\\u043E\\u043C, \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0438\\u0432 \\u0432\\u0441\\u0435 \\u0443\\u0441\\u0438\\u043B\\u0438\\u044F \\u0434\\u043B\\u044F \\u0441\\u043A\\u043E\\u0440\\u0435\\u0439\\u0448\\u0435\\u0433\\u043E \\u0435\\u0433\\u043E \\u0440\\u0435\\u0448\\u0435\\u043D\\u0438\\u044F \\u043A \\u0432\\u0430\\u0448\\u0435\\u043C\\u0443 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u0438\\u044E.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(OwnersForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(Care, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c = Care;\nexport default Care;\nvar _c;\n$RefreshReg$(_c, \"Care\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "Link", "AOS", "styles", "OwnersForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Care", "_s", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "main", "mainContainer", "underText", "redLine", "flexContent", "src", "alt", "banner", "textContent", "href", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Care/Care.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/approve.webp';\nimport { Link } from 'react-router-dom';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nimport styles from '../../owners.module.css';\nimport OwnersForm from '../../components/form/OwnersForm';\n\nconst Care = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false,\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ОБСЛУЖИВАНИЕ КЛИЕНТОВ</strong>\n                  </h1>\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Мы всегда стремимся предоставлять вам обслуживание клиентов\n                    и качество самого высокого уровня.\n                  </span>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n                  <div\n                    className={styles.flexContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  >\n                    <img\n                      src={img}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <div\n                      className={styles.textContent}\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"300\"\n                    >\n                      <p>\n                        Мы всегда готовы помочь! Любая информация, оперативная\n                        помощь или поддержка клиентов — все это можно получить,\n                        просто позвонив по телефону{' '}\n                        <a href=\"tel:6677\">\n                          <strong>6677</strong>\n                        </a>{' '}\n                        или отправив заявку\n                      </p>\n                    </div>\n                  </div>\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"400\"\n                  >\n                    <p>\n                      В дилерских центрах GWM с вами свяжутся, чтобы убедиться в\n                      вашей удовлетворенности после посещения дилерского центра.\n                      Наши дилеры гордятся своими стандартами обслуживания и\n                      хотели бы иметь возможность решить любые проблемы, которые\n                      могут у вас возникнуть. Если у вас есть какие-либо вопросы\n                      по обслуживанию или вопросы, вызывающие беспокойство,\n                      свяжитесь с менеджером по обслуживанию дилерского центра и\n                      объясните свою проблему. Если ваш вопрос не будет решен на\n                      этом этапе, вам следует попросить поговорить с\n                      руководителем дилерского центра или управляющим директором\n                      дилерского центра. Если ваш вопрос не будет решен\n                      руководителем дилерского центра или управляющим\n                      директором, вам предлагается передать его в службу\n                      поддержки клиентов GWM. Затем ваш вопрос будет\n                      зарегистрирован и обработан совместно с соответствующим\n                      дилерским центром, приложив все усилия для скорейшего его\n                      решения к вашему удовлетворению.\n                    </p>\n                  </div>\n                  <OwnersForm />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Care;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,OAAO,MAAM,kCAAkC;AACtD,OAAOC,GAAG,MAAM,4CAA4C;AAC5D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;AAEzB,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,UAAU,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdK,GAAG,CAACW,IAAI,CAAC;MACPC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE;IACR,CAAC,CAAC;IAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,UAAU,CAAC,KAAK,CAAC;MACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEf,OAAA,CAAAE,SAAA;IAAAiB,QAAA,EACGd,OAAO,gBACNL,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BnB,OAAA;QAAKoB,SAAS,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAENxB,OAAA;MAAKoB,SAAS,EAAC,SAAS;MAAAD,QAAA,eACtBnB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBnB,OAAA;UAAKoB,SAAS,EAAEvB,MAAM,CAAC4B,MAAO;UAAAN,QAAA,gBAC5BnB,OAAA,CAACP,OAAO;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXxB,OAAA;YAAMoB,SAAS,EAAEvB,MAAM,CAAC6B,IAAK;YAAAP,QAAA,eAC3BnB,OAAA;cAAKoB,SAAS,EAAEvB,MAAM,CAAC8B,aAAc;cAAAR,QAAA,gBACnCnB,OAAA;gBAAI,YAAS,SAAS;gBAAAmB,QAAA,eACpBnB,OAAA;kBAAAmB,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAAC+B,SAAU;gBAC5B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAT,QAAA,EACrB;cAGD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPxB,OAAA;gBACE,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBACpBoB,SAAS,EAAEvB,MAAM,CAACgC;cAAQ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACLxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAACiC,WAAY;gBAC9B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAX,QAAA,gBAEpBnB,OAAA;kBACE+B,GAAG,EAAErC,GAAI;kBACTsC,GAAG,EAAC,uGAAuB;kBAC3BZ,SAAS,EAAEvB,MAAM,CAACoC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFxB,OAAA;kBACEoB,SAAS,EAAEvB,MAAM,CAACqC,WAAY;kBAC9B,YAAS,SAAS;kBAClB,kBAAe,KAAK;kBAAAf,QAAA,eAEpBnB,OAAA;oBAAAmB,QAAA,GAAG,gtBAG0B,EAAC,GAAG,eAC/BnB,OAAA;sBAAGmC,IAAI,EAAC,UAAU;sBAAAhB,QAAA,eAChBnB,OAAA;wBAAAmB,QAAA,EAAQ;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,EAAC,GAAG,EAAC,0GAEX;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxB,OAAA;gBACEoB,SAAS,EAAEvB,MAAM,CAACqC,WAAY;gBAC9B,YAAS,SAAS;gBAClB,kBAAe,KAAK;gBAAAf,QAAA,eAEpBnB,OAAA;kBAAAmB,QAAA,EAAG;gBAkBH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxB,OAAA,CAACF,UAAU;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACpB,EAAA,CAhHID,IAAI;AAAAiC,EAAA,GAAJjC,IAAI;AAkHV,eAAeA,IAAI;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}