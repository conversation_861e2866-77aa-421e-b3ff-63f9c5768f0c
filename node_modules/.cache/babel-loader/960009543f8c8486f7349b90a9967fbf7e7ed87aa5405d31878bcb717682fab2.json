{"ast": null, "code": "import { attachSpring, isMotionValue } from 'motion-dom';\nimport { useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\nfunction useSpring(source) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  const getFromSource = () => isMotionValue(source) ? source.get() : source;\n  // isStatic will never change, allowing early hooks return\n  if (isStatic) {\n    return useTransform(getFromSource);\n  }\n  const value = useMotionValue(getFromSource());\n  useInsertionEffect(() => {\n    return attachSpring(value, source, options);\n  }, [value, JSON.stringify(options)]);\n  return value;\n}\nexport { useSpring };", "map": {"version": 3, "names": ["attachSpring", "isMotionValue", "useContext", "useInsertionEffect", "MotionConfigContext", "useMotionValue", "useTransform", "useSpring", "source", "options", "arguments", "length", "undefined", "isStatic", "getFromSource", "get", "value", "JSON", "stringify"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-spring.mjs"], "sourcesContent": ["import { attachSpring, isMotionValue } from 'motion-dom';\nimport { useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { useTransform } from './use-transform.mjs';\n\nfunction useSpring(source, options = {}) {\n    const { isStatic } = useContext(MotionConfigContext);\n    const getFromSource = () => (isMotionValue(source) ? source.get() : source);\n    // isStatic will never change, allowing early hooks return\n    if (isStatic) {\n        return useTransform(getFromSource);\n    }\n    const value = useMotionValue(getFromSource());\n    useInsertionEffect(() => {\n        return attachSpring(value, source, options);\n    }, [value, JSON.stringify(options)]);\n    return value;\n}\n\nexport { useSpring };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,YAAY;AACxD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,OAAO;AACtD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,SAASA,CAACC,MAAM,EAAgB;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACnC,MAAM;IAAEG;EAAS,CAAC,GAAGX,UAAU,CAACE,mBAAmB,CAAC;EACpD,MAAMU,aAAa,GAAGA,CAAA,KAAOb,aAAa,CAACO,MAAM,CAAC,GAAGA,MAAM,CAACO,GAAG,CAAC,CAAC,GAAGP,MAAO;EAC3E;EACA,IAAIK,QAAQ,EAAE;IACV,OAAOP,YAAY,CAACQ,aAAa,CAAC;EACtC;EACA,MAAME,KAAK,GAAGX,cAAc,CAACS,aAAa,CAAC,CAAC,CAAC;EAC7CX,kBAAkB,CAAC,MAAM;IACrB,OAAOH,YAAY,CAACgB,KAAK,EAAER,MAAM,EAAEC,OAAO,CAAC;EAC/C,CAAC,EAAE,CAACO,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACT,OAAO,CAAC,CAAC,CAAC;EACpC,OAAOO,KAAK;AAChB;AAEA,SAAST,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}