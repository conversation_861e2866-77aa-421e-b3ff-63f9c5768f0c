{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Models/Pages/components/FilterSlide/FilterSwiper.jsx\";\nimport React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterSwiper = ({\n  activeModel,\n  setActiveModel,\n  cars\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.carsModelsList,\n    children: /*#__PURE__*/_jsxDEV(Swiper, {\n      breakpoints: {\n        320: {\n          slidesPerView: 2,\n          spaceBetween: 0\n        },\n        580: {\n          slidesPerView: 2.2,\n          spaceBetween: 0\n        },\n        750: {\n          slidesPerView: 3.2,\n          spaceBetween: 10\n        },\n        860: {\n          slidesPerView: 3.5,\n          spaceBetween: 0\n        },\n        1160: {\n          slidesPerView: 4.1,\n          spaceBetween: 0\n        },\n        1460: {\n          slidesPerView: 6.5,\n          spaceBetween: 0\n        }\n      },\n      spaceBetween: 10,\n      grabCursor: true,\n      className: \"mySwiper\",\n      children: [cars.map(item => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.btn} ${activeModel === item.id ? styles.active : ''}`,\n          onClick: () => setActiveModel(item.id),\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.activeBar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = FilterSwiper;\nexport default FilterSwiper;\nvar _c;\n$RefreshReg$(_c, \"FilterSwiper\");", "map": {"version": 3, "names": ["React", "Swiper", "SwiperSlide", "styles", "jsxDEV", "_jsxDEV", "FilterSwiper", "activeModel", "setActiveModel", "cars", "className", "carsModelsList", "children", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "grabCursor", "map", "item", "btn", "id", "active", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "activeBar", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/components/FilterSlide/FilterSwiper.jsx"], "sourcesContent": ["import React from 'react';\n// swiper\nimport { Swiper, SwiperSlide } from 'swiper/react';\n// Import Swiper styles\nimport 'swiper/css';\nimport styles from './filter.module.css';\n\nconst FilterSwiper = ({ activeModel, setActiveModel, cars }) => {\n  return (\n    <div className={styles.carsModelsList}>\n      <Swiper\n        breakpoints={{\n          320: {\n            slidesPerView: 2,\n            spaceBetween: 0,\n          },\n          580: {\n            slidesPerView: 2.2,\n            spaceBetween: 0,\n          },\n          750: {\n            slidesPerView: 3.2,\n            spaceBetween: 10,\n          },\n          860: {\n            slidesPerView: 3.5,\n            spaceBetween: 0,\n          },\n          1160: {\n            slidesPerView: 4.1,\n            spaceBetween: 0,\n          },\n\n          1460: {\n            slidesPerView: 6.5,\n            spaceBetween: 0,\n          },\n        }}\n        spaceBetween={10}\n        grabCursor={true}\n        className=\"mySwiper\"\n      >\n        {cars.map((item) => (\n          <SwiperSlide key={item.id}>\n            <div\n              className={`${styles.btn} ${\n                activeModel === item.id ? styles.active : ''\n              }`}\n              onClick={() => setActiveModel(item.id)}\n            >\n              {item.title}\n            </div>\n          </SwiperSlide>\n        ))}\n        <div className={styles.activeBar}></div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default FilterSwiper;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AACA,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD;AACA,OAAO,YAAY;AACnB,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW;EAAEC,cAAc;EAAEC;AAAK,CAAC,KAAK;EAC9D,oBACEJ,OAAA;IAAKK,SAAS,EAAEP,MAAM,CAACQ,cAAe;IAAAC,QAAA,eACpCP,OAAA,CAACJ,MAAM;MACLY,WAAW,EAAE;QACX,GAAG,EAAE;UACHC,aAAa,EAAE,CAAC;UAChBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,GAAG,EAAE;UACHD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QACD,IAAI,EAAE;UACJD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB,CAAC;QAED,IAAI,EAAE;UACJD,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE;QAChB;MACF,CAAE;MACFA,YAAY,EAAE,EAAG;MACjBC,UAAU,EAAE,IAAK;MACjBN,SAAS,EAAC,UAAU;MAAAE,QAAA,GAEnBH,IAAI,CAACQ,GAAG,CAAEC,IAAI,iBACbb,OAAA,CAACH,WAAW;QAAAU,QAAA,eACVP,OAAA;UACEK,SAAS,EAAE,GAAGP,MAAM,CAACgB,GAAG,IACtBZ,WAAW,KAAKW,IAAI,CAACE,EAAE,GAAGjB,MAAM,CAACkB,MAAM,GAAG,EAAE,EAC3C;UACHC,OAAO,EAAEA,CAAA,KAAMd,cAAc,CAACU,IAAI,CAACE,EAAE,CAAE;UAAAR,QAAA,EAEtCM,IAAI,CAACK;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC,GARUT,IAAI,CAACE,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASZ,CACd,CAAC,eACFtB,OAAA;QAAKK,SAAS,EAAEP,MAAM,CAACyB;MAAU;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GAnDIvB,YAAY;AAqDlB,eAAeA,YAAY;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}