{"ast": null, "code": "import { cancelFrame, frame } from 'motion-dom';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nfunction useCombineMotionValues(values, combineValues) {\n  /**\n   * Initialise the returned motion value. This remains the same between renders.\n   */\n  const value = useMotionValue(combineValues());\n  /**\n   * Create a function that will update the template motion value with the latest values.\n   * This is pre-bound so whenever a motion value updates it can schedule its\n   * execution in Framesync. If it's already been scheduled it won't be fired twice\n   * in a single frame.\n   */\n  const updateValue = () => value.set(combineValues());\n  /**\n   * Synchronously update the motion value with the latest values during the render.\n   * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n   */\n  updateValue();\n  /**\n   * Subscribe to all motion values found within the template. Whenever any of them change,\n   * schedule an update.\n   */\n  useIsomorphicLayoutEffect(() => {\n    const scheduleUpdate = () => frame.preRender(updateValue, false, true);\n    const subscriptions = values.map(v => v.on(\"change\", scheduleUpdate));\n    return () => {\n      subscriptions.forEach(unsubscribe => unsubscribe());\n      cancelFrame(updateValue);\n    };\n  });\n  return value;\n}\nexport { useCombineMotionValues };", "map": {"version": 3, "names": ["cancelFrame", "frame", "useIsomorphicLayoutEffect", "useMotionValue", "useCombineMotionValues", "values", "combineValues", "value", "updateValue", "set", "scheduleUpdate", "preRender", "subscriptions", "map", "v", "on", "for<PERSON>ach", "unsubscribe"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-combine-values.mjs"], "sourcesContent": ["import { cancelFrame, frame } from 'motion-dom';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = useMotionValue(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    useIsomorphicLayoutEffect(() => {\n        const scheduleUpdate = () => frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            cancelFrame(updateValue);\n        };\n    });\n    return value;\n}\n\nexport { useCombineMotionValues };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,KAAK,QAAQ,YAAY;AAC/C,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,cAAc,QAAQ,wBAAwB;AAEvD,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACnD;AACJ;AACA;EACI,MAAMC,KAAK,GAAGJ,cAAc,CAACG,aAAa,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,WAAW,GAAGA,CAAA,KAAMD,KAAK,CAACE,GAAG,CAACH,aAAa,CAAC,CAAC,CAAC;EACpD;AACJ;AACA;AACA;EACIE,WAAW,CAAC,CAAC;EACb;AACJ;AACA;AACA;EACIN,yBAAyB,CAAC,MAAM;IAC5B,MAAMQ,cAAc,GAAGA,CAAA,KAAMT,KAAK,CAACU,SAAS,CAACH,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;IACtE,MAAMI,aAAa,GAAGP,MAAM,CAACQ,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC,QAAQ,EAAEL,cAAc,CAAC,CAAC;IACvE,OAAO,MAAM;MACTE,aAAa,CAACI,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;MACrDjB,WAAW,CAACQ,WAAW,CAAC;IAC5B,CAAC;EACL,CAAC,CAAC;EACF,OAAOD,KAAK;AAChB;AAEA,SAASH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}