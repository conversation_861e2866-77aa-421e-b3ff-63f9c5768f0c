{"ast": null, "code": "import { useContext, useId, useEffect, useCallback } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence() {\n  let subscribe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  const context = useContext(PresenceContext);\n  if (context === null) return [true, null];\n  const {\n    isPresent,\n    onExitComplete,\n    register\n  } = context;\n  // It's safe to call the following hooks conditionally (after an early return) because the context will always\n  // either be null or non-null for the lifespan of the component.\n  const id = useId();\n  useEffect(() => {\n    if (subscribe) {\n      return register(id);\n    }\n  }, [subscribe]);\n  const safeToRemove = useCallback(() => subscribe && onExitComplete && onExitComplete(id), [id, onExitComplete, subscribe]);\n  return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n  return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n  return context === null ? true : context.isPresent;\n}\nexport { isPresent, useIsPresent, usePresence };", "map": {"version": 3, "names": ["useContext", "useId", "useEffect", "useCallback", "PresenceContext", "usePresence", "subscribe", "arguments", "length", "undefined", "context", "isPresent", "onExitComplete", "register", "id", "safeToRemove", "useIsPresent"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs"], "sourcesContent": ["import { useContext, useId, useEffect, useCallback } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence(subscribe = true) {\n    const context = useContext(PresenceContext);\n    if (context === null)\n        return [true, null];\n    const { isPresent, onExitComplete, register } = context;\n    // It's safe to call the following hooks conditionally (after an early return) because the context will always\n    // either be null or non-null for the lifespan of the component.\n    const id = useId();\n    useEffect(() => {\n        if (subscribe) {\n            return register(id);\n        }\n    }, [subscribe]);\n    const safeToRemove = useCallback(() => subscribe && onExitComplete && onExitComplete(id), [id, onExitComplete, subscribe]);\n    return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n    return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n    return context === null ? true : context.isPresent;\n}\n\nexport { isPresent, useIsPresent, usePresence };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACjE,SAASC,eAAe,QAAQ,mCAAmC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAmB;EAAA,IAAlBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACjC,MAAMG,OAAO,GAAGV,UAAU,CAACI,eAAe,CAAC;EAC3C,IAAIM,OAAO,KAAK,IAAI,EAChB,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;EACvB,MAAM;IAAEC,SAAS;IAAEC,cAAc;IAAEC;EAAS,CAAC,GAAGH,OAAO;EACvD;EACA;EACA,MAAMI,EAAE,GAAGb,KAAK,CAAC,CAAC;EAClBC,SAAS,CAAC,MAAM;IACZ,IAAII,SAAS,EAAE;MACX,OAAOO,QAAQ,CAACC,EAAE,CAAC;IACvB;EACJ,CAAC,EAAE,CAACR,SAAS,CAAC,CAAC;EACf,MAAMS,YAAY,GAAGZ,WAAW,CAAC,MAAMG,SAAS,IAAIM,cAAc,IAAIA,cAAc,CAACE,EAAE,CAAC,EAAE,CAACA,EAAE,EAAEF,cAAc,EAAEN,SAAS,CAAC,CAAC;EAC1H,OAAO,CAACK,SAAS,IAAIC,cAAc,GAAG,CAAC,KAAK,EAAEG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAOL,SAAS,CAACX,UAAU,CAACI,eAAe,CAAC,CAAC;AACjD;AACA,SAASO,SAASA,CAACD,OAAO,EAAE;EACxB,OAAOA,OAAO,KAAK,IAAI,GAAG,IAAI,GAAGA,OAAO,CAACC,SAAS;AACtD;AAEA,SAASA,SAAS,EAAEK,YAAY,EAAEX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}