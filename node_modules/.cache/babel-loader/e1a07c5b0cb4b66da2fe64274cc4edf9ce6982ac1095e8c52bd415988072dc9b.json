{"ast": null, "code": "// components/SkeletonCard.jsx\nimport React from'react';import styles from'./SkeletonCard.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SkeletonCard=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:styles.skeletonCard,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.badge}),/*#__PURE__*/_jsx(\"div\",{className:styles.image}),/*#__PURE__*/_jsx(\"div\",{className:styles.title})]});};export default SkeletonCard;", "map": {"version": 3, "names": ["React", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "SkeletonCard", "className", "skeletonCard", "children", "badge", "image", "title"], "sources": ["/var/www/html/gwm.tj/src/components/SkeletonCard/SkeletonCard.jsx"], "sourcesContent": ["// components/SkeletonCard.jsx\nimport React from 'react';\nimport styles from './SkeletonCard.module.css';\n\nconst SkeletonCard = () => {\n  return (\n    <div className={styles.skeletonCard}>\n      <div className={styles.badge} />\n      <div className={styles.image} />\n      <div className={styles.title} />\n    </div>\n  );\n};\n\nexport default SkeletonCard;\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,mBACED,KAAA,QAAKE,SAAS,CAAEN,MAAM,CAACO,YAAa,CAAAC,QAAA,eAClCN,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACS,KAAM,CAAE,CAAC,cAChCP,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACU,KAAM,CAAE,CAAC,cAChCR,IAAA,QAAKI,SAAS,CAAEN,MAAM,CAACW,KAAM,CAAE,CAAC,EAC7B,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}