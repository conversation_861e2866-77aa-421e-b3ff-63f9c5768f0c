{"ast": null, "code": "import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n  constructor({\n    autoplay = true,\n    delay = 0,\n    type = \"keyframes\",\n    repeat = 0,\n    repeatDelay = 0,\n    repeatType = \"loop\",\n    keyframes,\n    name,\n    motionValue,\n    element,\n    ...options\n  }) {\n    super();\n    /**\n     * Bound to support return animation.stop pattern\n     */\n    this.stop = () => {\n      if (this._animation) {\n        this._animation.stop();\n        this.stopTimeline?.();\n      }\n      this.keyframeResolver?.cancel();\n    };\n    this.createdAt = time.now();\n    const optionsWithDefaults = {\n      autoplay,\n      delay,\n      type,\n      repeat,\n      repeatDelay,\n      repeatType,\n      name,\n      motionValue,\n      element,\n      ...options\n    };\n    const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n    this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n    this.keyframeResolver?.scheduleResolve();\n  }\n  onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n    this.keyframeResolver = undefined;\n    const {\n      name,\n      type,\n      velocity,\n      delay,\n      isHandoff,\n      onUpdate\n    } = options;\n    this.resolvedAt = time.now();\n    /**\n     * If we can't animate this value with the resolved keyframes\n     * then we should complete it immediately.\n     */\n    if (!canAnimate(keyframes, name, type, velocity)) {\n      if (MotionGlobalConfig.instantAnimations || !delay) {\n        onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n      }\n      keyframes[0] = keyframes[keyframes.length - 1];\n      options.duration = 0;\n      options.repeat = 0;\n    }\n    /**\n     * Resolve startTime for the animation.\n     *\n     * This method uses the createdAt and resolvedAt to calculate the\n     * animation startTime. *Ideally*, we would use the createdAt time as t=0\n     * as the following frame would then be the first frame of the animation in\n     * progress, which would feel snappier.\n     *\n     * However, if there's a delay (main thread work) between the creation of\n     * the animation and the first commited frame, we prefer to use resolvedAt\n     * to avoid a sudden jump into the animation.\n     */\n    const startTime = sync ? !this.resolvedAt ? this.createdAt : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY ? this.resolvedAt : this.createdAt : undefined;\n    const resolvedOptions = {\n      startTime,\n      finalKeyframe,\n      ...options,\n      keyframes\n    };\n    /**\n     * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions) ? new NativeAnimationExtended({\n      ...resolvedOptions,\n      element: resolvedOptions.motionValue.owner.current\n    }) : new JSAnimation(resolvedOptions);\n    animation.finished.then(() => this.notifyFinished()).catch(noop);\n    if (this.pendingTimeline) {\n      this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n      this.pendingTimeline = undefined;\n    }\n    this._animation = animation;\n  }\n  get finished() {\n    if (!this._animation) {\n      return this._finished;\n    } else {\n      return this.animation.finished;\n    }\n  }\n  then(onResolve, _onReject) {\n    return this.finished.finally(onResolve).then(() => {});\n  }\n  get animation() {\n    if (!this._animation) {\n      this.keyframeResolver?.resume();\n      flushKeyframeResolvers();\n    }\n    return this._animation;\n  }\n  get duration() {\n    return this.animation.duration;\n  }\n  get time() {\n    return this.animation.time;\n  }\n  set time(newTime) {\n    this.animation.time = newTime;\n  }\n  get speed() {\n    return this.animation.speed;\n  }\n  get state() {\n    return this.animation.state;\n  }\n  set speed(newSpeed) {\n    this.animation.speed = newSpeed;\n  }\n  get startTime() {\n    return this.animation.startTime;\n  }\n  attachTimeline(timeline) {\n    if (this._animation) {\n      this.stopTimeline = this.animation.attachTimeline(timeline);\n    } else {\n      this.pendingTimeline = timeline;\n    }\n    return () => this.stop();\n  }\n  play() {\n    this.animation.play();\n  }\n  pause() {\n    this.animation.pause();\n  }\n  complete() {\n    this.animation.complete();\n  }\n  cancel() {\n    if (this._animation) {\n      this.animation.cancel();\n    }\n    this.keyframeResolver?.cancel();\n  }\n}\nexport { AsyncMotionValueAnimation };", "map": {"version": 3, "names": ["MotionGlobalConfig", "noop", "time", "JSAnimation", "getFinalKeyframe", "KeyframeResolver", "flushKeyframeResolvers", "NativeAnimationExtended", "canAnimate", "WithPromise", "supportsBrowserAnimation", "MAX_RESOLVE_DELAY", "AsyncMotionValueAnimation", "constructor", "autoplay", "delay", "type", "repeat", "repeatDelay", "repeatType", "keyframes", "name", "motionValue", "element", "options", "stop", "_animation", "stopTimeline", "keyframeResolver", "cancel", "createdAt", "now", "optionsWithDefaults", "KeyframeResolver$1", "resolvedKeyframes", "finalKeyframe", "forced", "onKeyframesResolved", "scheduleResolve", "sync", "undefined", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "onUpdate", "resolvedAt", "instantAnimations", "length", "duration", "startTime", "resolvedOptions", "animation", "owner", "current", "finished", "then", "notifyFinished", "catch", "pendingTimeline", "attachTimeline", "_finished", "onResolve", "_onReject", "finally", "resume", "newTime", "speed", "state", "newSpeed", "timeline", "play", "pause", "complete"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs"], "sourcesContent": ["import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n    constructor({ autoplay = true, delay = 0, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", keyframes, name, motionValue, element, ...options }) {\n        super();\n        /**\n         * Bound to support return animation.stop pattern\n         */\n        this.stop = () => {\n            if (this._animation) {\n                this._animation.stop();\n                this.stopTimeline?.();\n            }\n            this.keyframeResolver?.cancel();\n        };\n        this.createdAt = time.now();\n        const optionsWithDefaults = {\n            autoplay,\n            delay,\n            type,\n            repeat,\n            repeatDelay,\n            repeatType,\n            name,\n            motionValue,\n            element,\n            ...options,\n        };\n        const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n        this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n        this.keyframeResolver?.scheduleResolve();\n    }\n    onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n        this.keyframeResolver = undefined;\n        const { name, type, velocity, delay, isHandoff, onUpdate } = options;\n        this.resolvedAt = time.now();\n        /**\n         * If we can't animate this value with the resolved keyframes\n         * then we should complete it immediately.\n         */\n        if (!canAnimate(keyframes, name, type, velocity)) {\n            if (MotionGlobalConfig.instantAnimations || !delay) {\n                onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n            }\n            keyframes[0] = keyframes[keyframes.length - 1];\n            options.duration = 0;\n            options.repeat = 0;\n        }\n        /**\n         * Resolve startTime for the animation.\n         *\n         * This method uses the createdAt and resolvedAt to calculate the\n         * animation startTime. *Ideally*, we would use the createdAt time as t=0\n         * as the following frame would then be the first frame of the animation in\n         * progress, which would feel snappier.\n         *\n         * However, if there's a delay (main thread work) between the creation of\n         * the animation and the first commited frame, we prefer to use resolvedAt\n         * to avoid a sudden jump into the animation.\n         */\n        const startTime = sync\n            ? !this.resolvedAt\n                ? this.createdAt\n                : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY\n                    ? this.resolvedAt\n                    : this.createdAt\n            : undefined;\n        const resolvedOptions = {\n            startTime,\n            finalKeyframe,\n            ...options,\n            keyframes,\n        };\n        /**\n         * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions)\n            ? new NativeAnimationExtended({\n                ...resolvedOptions,\n                element: resolvedOptions.motionValue.owner.current,\n            })\n            : new JSAnimation(resolvedOptions);\n        animation.finished.then(() => this.notifyFinished()).catch(noop);\n        if (this.pendingTimeline) {\n            this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n            this.pendingTimeline = undefined;\n        }\n        this._animation = animation;\n    }\n    get finished() {\n        if (!this._animation) {\n            return this._finished;\n        }\n        else {\n            return this.animation.finished;\n        }\n    }\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n    get animation() {\n        if (!this._animation) {\n            this.keyframeResolver?.resume();\n            flushKeyframeResolvers();\n        }\n        return this._animation;\n    }\n    get duration() {\n        return this.animation.duration;\n    }\n    get time() {\n        return this.animation.time;\n    }\n    set time(newTime) {\n        this.animation.time = newTime;\n    }\n    get speed() {\n        return this.animation.speed;\n    }\n    get state() {\n        return this.animation.state;\n    }\n    set speed(newSpeed) {\n        this.animation.speed = newSpeed;\n    }\n    get startTime() {\n        return this.animation.startTime;\n    }\n    attachTimeline(timeline) {\n        if (this._animation) {\n            this.stopTimeline = this.animation.attachTimeline(timeline);\n        }\n        else {\n            this.pendingTimeline = timeline;\n        }\n        return () => this.stop();\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.complete();\n    }\n    cancel() {\n        if (this._animation) {\n            this.animation.cancel();\n        }\n        this.keyframeResolver?.cancel();\n    }\n}\n\nexport { AsyncMotionValueAnimation };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,IAAI,QAAQ,cAAc;AACvD,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,mCAAmC;AAC5F,SAASC,uBAAuB,QAAQ,+BAA+B;AACvE,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,wBAAwB,QAAQ,4BAA4B;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,yBAAyB,SAASH,WAAW,CAAC;EAChDI,WAAWA,CAAC;IAAEC,QAAQ,GAAG,IAAI;IAAEC,KAAK,GAAG,CAAC;IAAEC,IAAI,GAAG,WAAW;IAAEC,MAAM,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAEC,UAAU,GAAG,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC,WAAW;IAAEC,OAAO;IAAE,GAAGC;EAAQ,CAAC,EAAE;IACjK,KAAK,CAAC,CAAC;IACP;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MACd,IAAI,IAAI,CAACC,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACD,IAAI,CAAC,CAAC;QACtB,IAAI,CAACE,YAAY,GAAG,CAAC;MACzB;MACA,IAAI,CAACC,gBAAgB,EAAEC,MAAM,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,CAACC,SAAS,GAAG5B,IAAI,CAAC6B,GAAG,CAAC,CAAC;IAC3B,MAAMC,mBAAmB,GAAG;MACxBlB,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,MAAM;MACNC,WAAW;MACXC,UAAU;MACVE,IAAI;MACJC,WAAW;MACXC,OAAO;MACP,GAAGC;IACP,CAAC;IACD,MAAMS,kBAAkB,GAAGV,OAAO,EAAElB,gBAAgB,IAAIA,gBAAgB;IACxE,IAAI,CAACuB,gBAAgB,GAAG,IAAIK,kBAAkB,CAACb,SAAS,EAAE,CAACc,iBAAiB,EAAEC,aAAa,EAAEC,MAAM,KAAK,IAAI,CAACC,mBAAmB,CAACH,iBAAiB,EAAEC,aAAa,EAAEH,mBAAmB,EAAE,CAACI,MAAM,CAAC,EAAEf,IAAI,EAAEC,WAAW,EAAEC,OAAO,CAAC;IAC7N,IAAI,CAACK,gBAAgB,EAAEU,eAAe,CAAC,CAAC;EAC5C;EACAD,mBAAmBA,CAACjB,SAAS,EAAEe,aAAa,EAAEX,OAAO,EAAEe,IAAI,EAAE;IACzD,IAAI,CAACX,gBAAgB,GAAGY,SAAS;IACjC,MAAM;MAAEnB,IAAI;MAAEL,IAAI;MAAEyB,QAAQ;MAAE1B,KAAK;MAAE2B,SAAS;MAAEC;IAAS,CAAC,GAAGnB,OAAO;IACpE,IAAI,CAACoB,UAAU,GAAG1C,IAAI,CAAC6B,GAAG,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACvB,UAAU,CAACY,SAAS,EAAEC,IAAI,EAAEL,IAAI,EAAEyB,QAAQ,CAAC,EAAE;MAC9C,IAAIzC,kBAAkB,CAAC6C,iBAAiB,IAAI,CAAC9B,KAAK,EAAE;QAChD4B,QAAQ,GAAGvC,gBAAgB,CAACgB,SAAS,EAAEI,OAAO,EAAEW,aAAa,CAAC,CAAC;MACnE;MACAf,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACA,SAAS,CAAC0B,MAAM,GAAG,CAAC,CAAC;MAC9CtB,OAAO,CAACuB,QAAQ,GAAG,CAAC;MACpBvB,OAAO,CAACP,MAAM,GAAG,CAAC;IACtB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+B,SAAS,GAAGT,IAAI,GAChB,CAAC,IAAI,CAACK,UAAU,GACZ,IAAI,CAACd,SAAS,GACd,IAAI,CAACc,UAAU,GAAG,IAAI,CAACd,SAAS,GAAGnB,iBAAiB,GAChD,IAAI,CAACiC,UAAU,GACf,IAAI,CAACd,SAAS,GACtBU,SAAS;IACf,MAAMS,eAAe,GAAG;MACpBD,SAAS;MACTb,aAAa;MACb,GAAGX,OAAO;MACVJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,MAAM8B,SAAS,GAAG,CAACR,SAAS,IAAIhC,wBAAwB,CAACuC,eAAe,CAAC,GACnE,IAAI1C,uBAAuB,CAAC;MAC1B,GAAG0C,eAAe;MAClB1B,OAAO,EAAE0B,eAAe,CAAC3B,WAAW,CAAC6B,KAAK,CAACC;IAC/C,CAAC,CAAC,GACA,IAAIjD,WAAW,CAAC8C,eAAe,CAAC;IACtCC,SAAS,CAACG,QAAQ,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,KAAK,CAACvD,IAAI,CAAC;IAChE,IAAI,IAAI,CAACwD,eAAe,EAAE;MACtB,IAAI,CAAC9B,YAAY,GAAGuB,SAAS,CAACQ,cAAc,CAAC,IAAI,CAACD,eAAe,CAAC;MAClE,IAAI,CAACA,eAAe,GAAGjB,SAAS;IACpC;IACA,IAAI,CAACd,UAAU,GAAGwB,SAAS;EAC/B;EACA,IAAIG,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE;MAClB,OAAO,IAAI,CAACiC,SAAS;IACzB,CAAC,MACI;MACD,OAAO,IAAI,CAACT,SAAS,CAACG,QAAQ;IAClC;EACJ;EACAC,IAAIA,CAACM,SAAS,EAAEC,SAAS,EAAE;IACvB,OAAO,IAAI,CAACR,QAAQ,CAACS,OAAO,CAACF,SAAS,CAAC,CAACN,IAAI,CAAC,MAAM,CAAE,CAAC,CAAC;EAC3D;EACA,IAAIJ,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACxB,UAAU,EAAE;MAClB,IAAI,CAACE,gBAAgB,EAAEmC,MAAM,CAAC,CAAC;MAC/BzD,sBAAsB,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACoB,UAAU;EAC1B;EACA,IAAIqB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,SAAS,CAACH,QAAQ;EAClC;EACA,IAAI7C,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACgD,SAAS,CAAChD,IAAI;EAC9B;EACA,IAAIA,IAAIA,CAAC8D,OAAO,EAAE;IACd,IAAI,CAACd,SAAS,CAAChD,IAAI,GAAG8D,OAAO;EACjC;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACf,SAAS,CAACe,KAAK;EAC/B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChB,SAAS,CAACgB,KAAK;EAC/B;EACA,IAAID,KAAKA,CAACE,QAAQ,EAAE;IAChB,IAAI,CAACjB,SAAS,CAACe,KAAK,GAAGE,QAAQ;EACnC;EACA,IAAInB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACE,SAAS,CAACF,SAAS;EACnC;EACAU,cAAcA,CAACU,QAAQ,EAAE;IACrB,IAAI,IAAI,CAAC1C,UAAU,EAAE;MACjB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACuB,SAAS,CAACQ,cAAc,CAACU,QAAQ,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACX,eAAe,GAAGW,QAAQ;IACnC;IACA,OAAO,MAAM,IAAI,CAAC3C,IAAI,CAAC,CAAC;EAC5B;EACA4C,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnB,SAAS,CAACmB,IAAI,CAAC,CAAC;EACzB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpB,SAAS,CAACoB,KAAK,CAAC,CAAC;EAC1B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrB,SAAS,CAACqB,QAAQ,CAAC,CAAC;EAC7B;EACA1C,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACH,UAAU,EAAE;MACjB,IAAI,CAACwB,SAAS,CAACrB,MAAM,CAAC,CAAC;IAC3B;IACA,IAAI,CAACD,gBAAgB,EAAEC,MAAM,CAAC,CAAC;EACnC;AACJ;AAEA,SAASjB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}