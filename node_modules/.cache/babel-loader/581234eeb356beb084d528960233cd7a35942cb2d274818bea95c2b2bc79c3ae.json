{"ast": null, "code": "export const menuData = [{\n  id: 1,\n  title: 'Гарантия',\n  url: '/owners/warranty'\n}, {\n  id: 2,\n  title: 'Помощь на дороге',\n  url: '/owners/road-assistance'\n}, {\n  id: 3,\n  title: 'Сервис и обслуживание',\n  url: '/owners/service'\n}, {\n  id: 4,\n  title: 'План обслуживания',\n  url: '/owners/service-plans'\n}, {\n  id: 5,\n  title: 'Оригинальные запчасти',\n  url: '/owners/parts'\n}, {\n  id: 6,\n  title: 'Аксессуары',\n  url: '/owners/accessories'\n}, {\n  id: 7,\n  title: 'Обслуживание клиентов',\n  url: '/owners/care'\n}, {\n  id: 8,\n  title: 'Справочная таблица транспортных средств',\n  url: '/owners/vehicle-reference-table'\n}];", "map": {"version": 3, "names": ["menuData", "id", "title", "url"], "sources": ["/var/www/html/gwm.tj/src/asset/data/ownersMenu.js"], "sourcesContent": ["export const menuData = [\n  {\n    id: 1,\n    title: 'Гарантия',\n    url: '/owners/warranty',\n  },\n  {\n    id: 2,\n    title: 'Помощь на дороге',\n    url: '/owners/road-assistance',\n  },\n  {\n    id: 3,\n    title: 'Сервис и обслуживание',\n    url: '/owners/service',\n  },\n  {\n    id: 4,\n    title: 'План обслуживания',\n    url: '/owners/service-plans',\n  },\n  {\n    id: 5,\n    title: 'Оригинальные запчасти',\n    url: '/owners/parts',\n  },\n  {\n    id: 6,\n    title: 'Аксессуары',\n    url: '/owners/accessories',\n  },\n  {\n    id: 7,\n    title: 'Обслуживание клиентов',\n    url: '/owners/care',\n  },\n  {\n    id: 8,\n    title: 'Справочная таблица транспортных средств',\n    url: '/owners/vehicle-reference-table',\n  },\n];\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,UAAU;EACjBC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,kBAAkB;EACzBC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,mBAAmB;EAC1BC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,uBAAuB;EAC9BC,GAAG,EAAE;AACP,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,yCAAyC;EAChDC,GAAG,EAAE;AACP,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}