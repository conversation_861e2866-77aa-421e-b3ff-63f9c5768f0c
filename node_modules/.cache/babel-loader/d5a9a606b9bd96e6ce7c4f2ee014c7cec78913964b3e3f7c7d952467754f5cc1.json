{"ast": null, "code": "import React,{useEffect,useRef,useState}from'react';import styles from'./videoCard.module.css';import video from'./Homepage-Multimedia-components-01-GWM-care.mp4';import AOS from'aos';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const VideoCard=()=>{const wrapperRef=useRef(null);const[isAnimated,setIsAnimated]=useState(false);useEffect(()=>{AOS.init({duration:500,once:false});const observer=new IntersectionObserver(_ref=>{let[entry]=_ref;// Если секция в зоне видимости — запускаем анимацию\nsetIsAnimated(entry.isIntersecting);},{threshold:0.9// 50% видимости\n});if(wrapperRef.current){observer.observe(wrapperRef.current);}return()=>{observer.disconnect();};},[]);return/*#__PURE__*/_jsx(\"section\",{ref:wrapperRef,className:\"\".concat(styles.wrapper,\" \").concat(isAnimated?styles.wrapperActive:''),children:/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.container,\" \").concat(isAnimated?styles.containerActive:''),children:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(styles.videoWrapper,\" \").concat(isAnimated?styles.videoWrapperActive:''),children:[/*#__PURE__*/_jsx(\"video\",{src:video,autoPlay:true,muted:true,loop:true,playsInline:true,className:\"\".concat(styles.video,\" \").concat(isAnimated?styles.videoActive:''),\"aria-label\":\"\\u041F\\u0440\\u043E\\u043C\\u043E-\\u0432\\u0438\\u0434\\u0435\\u043E GWM\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"\".concat(styles.text,\" \"),\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:\"\\u041E\\u0442\\u043A\\u0440\\u043E\\u0439 \\u0434\\u043B\\u044F \\u0441\\u0435\\u0431\\u044F GWM\"})]})})});};export default VideoCard;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "styles", "video", "AOS", "jsx", "_jsx", "jsxs", "_jsxs", "VideoCard", "wrapperRef", "isAnimated", "setIsAnimated", "init", "duration", "once", "observer", "IntersectionObserver", "_ref", "entry", "isIntersecting", "threshold", "current", "observe", "disconnect", "ref", "className", "concat", "wrapper", "wrapperActive", "children", "container", "containerActive", "videoWrapper", "videoWrapperActive", "src", "autoPlay", "muted", "loop", "playsInline", "videoActive", "text"], "sources": ["/var/www/html/gwm.tj/src/pages/Home/components/video-card/VideoCard.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport styles from './videoCard.module.css';\nimport video from './Homepage-Multimedia-components-01-GWM-care.mp4';\nimport AOS from 'aos';\n\nconst VideoCard = () => {\n  const wrapperRef = useRef(null);\n  const [isAnimated, setIsAnimated] = useState(false);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        // Если секция в зоне видимости — запускаем анимацию\n        setIsAnimated(entry.isIntersecting);\n      },\n      {\n        threshold: 0.9, // 50% видимости\n      }\n    );\n\n    if (wrapperRef.current) {\n      observer.observe(wrapperRef.current);\n    }\n\n    return () => {\n      observer.disconnect();\n    };\n  }, []);\n\n  return (\n    <section ref={wrapperRef}\n      className={`${styles.wrapper} ${isAnimated ? styles.wrapperActive : ''\n        }`}\n    >\n      <div\n        className={`${styles.container} ${isAnimated ? styles.containerActive : ''\n          }`}\n      >\n        <div\n          className={`${styles.videoWrapper} ${isAnimated ? styles.videoWrapperActive : ''\n            }`}\n        >\n          <video\n            src={video}\n            autoPlay\n            muted\n            loop\n            playsInline\n            className={`${styles.video} ${isAnimated ? styles.videoActive : ''\n              }`}\n            aria-label=\"Промо-видео GWM\"\n          />\n          <h1\n            className={`${styles.text} `}\n            data-aos=\"fade-up\"\n            data-aos-delay=\"200\"\n          >\n            Открой для себя GWM\n          </h1>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default VideoCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,OAAO,CAC1D,MAAO,CAAAC,MAAM,KAAM,wBAAwB,CAC3C,MAAO,CAAAC,KAAK,KAAM,kDAAkD,CACpE,MAAO,CAAAC,GAAG,KAAM,KAAK,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtB,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,UAAU,CAAGV,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAACW,UAAU,CAAEC,aAAa,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CAEnDF,SAAS,CAAC,IAAM,CACdK,GAAG,CAACS,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxC,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,oBAAoB,CACvCC,IAAA,EAAa,IAAZ,CAACC,KAAK,CAAC,CAAAD,IAAA,CACN;AACAN,aAAa,CAACO,KAAK,CAACC,cAAc,CAAC,CACrC,CAAC,CACD,CACEC,SAAS,CAAE,GAAK;AAClB,CACF,CAAC,CAED,GAAIX,UAAU,CAACY,OAAO,CAAE,CACtBN,QAAQ,CAACO,OAAO,CAACb,UAAU,CAACY,OAAO,CAAC,CACtC,CAEA,MAAO,IAAM,CACXN,QAAQ,CAACQ,UAAU,CAAC,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACElB,IAAA,YAASmB,GAAG,CAAEf,UAAW,CACvBgB,SAAS,IAAAC,MAAA,CAAKzB,MAAM,CAAC0B,OAAO,MAAAD,MAAA,CAAIhB,UAAU,CAAGT,MAAM,CAAC2B,aAAa,CAAG,EAAE,CACjE,CAAAC,QAAA,cAELxB,IAAA,QACEoB,SAAS,IAAAC,MAAA,CAAKzB,MAAM,CAAC6B,SAAS,MAAAJ,MAAA,CAAIhB,UAAU,CAAGT,MAAM,CAAC8B,eAAe,CAAG,EAAE,CACrE,CAAAF,QAAA,cAELtB,KAAA,QACEkB,SAAS,IAAAC,MAAA,CAAKzB,MAAM,CAAC+B,YAAY,MAAAN,MAAA,CAAIhB,UAAU,CAAGT,MAAM,CAACgC,kBAAkB,CAAG,EAAE,CAC3E,CAAAJ,QAAA,eAELxB,IAAA,UACE6B,GAAG,CAAEhC,KAAM,CACXiC,QAAQ,MACRC,KAAK,MACLC,IAAI,MACJC,WAAW,MACXb,SAAS,IAAAC,MAAA,CAAKzB,MAAM,CAACC,KAAK,MAAAwB,MAAA,CAAIhB,UAAU,CAAGT,MAAM,CAACsC,WAAW,CAAG,EAAE,CAC7D,CACL,aAAW,mEAAiB,CAC7B,CAAC,cACFlC,IAAA,OACEoB,SAAS,IAAAC,MAAA,CAAKzB,MAAM,CAACuC,IAAI,KAAI,CAC7B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAX,QAAA,CACrB,sFAED,CAAI,CAAC,EACF,CAAC,CACH,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAArB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}