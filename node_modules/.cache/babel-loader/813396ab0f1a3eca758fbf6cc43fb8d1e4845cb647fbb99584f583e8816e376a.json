{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = /* @__PURE__ */createContext(null);\nexport { PresenceContext };", "map": {"version": 3, "names": ["createContext", "PresenceContext"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/context/PresenceContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = \n/* @__PURE__ */ createContext(null);\n\nexport { PresenceContext };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA,MAAMC,eAAe,GACrB,eAAgBD,aAAa,CAAC,IAAI,CAAC;AAEnC,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}