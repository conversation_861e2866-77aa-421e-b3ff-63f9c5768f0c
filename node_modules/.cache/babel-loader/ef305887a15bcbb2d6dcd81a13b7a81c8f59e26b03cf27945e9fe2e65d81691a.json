{"ast": null, "code": "import { NativeAnimation } from './NativeAnimation.mjs';\nclass NativeAnimationWrapper extends NativeAnimation {\n  constructor(animation) {\n    super();\n    this.animation = animation;\n    animation.onfinish = () => {\n      this.finishedTime = this.time;\n      this.notifyFinished();\n    };\n  }\n}\nexport { NativeAnimationWrapper };", "map": {"version": 3, "names": ["NativeAnimation", "NativeAnimationWrapper", "constructor", "animation", "onfinish", "finishedTime", "time", "notifyFinished"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/NativeAnimationWrapper.mjs"], "sourcesContent": ["import { NativeAnimation } from './NativeAnimation.mjs';\n\nclass NativeAnimationWrapper extends NativeAnimation {\n    constructor(animation) {\n        super();\n        this.animation = animation;\n        animation.onfinish = () => {\n            this.finishedTime = this.time;\n            this.notifyFinished();\n        };\n    }\n}\n\nexport { NativeAnimationWrapper };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AAEvD,MAAMC,sBAAsB,SAASD,eAAe,CAAC;EACjDE,WAAWA,CAACC,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1BA,SAAS,CAACC,QAAQ,GAAG,MAAM;MACvB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACC,IAAI;MAC7B,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB,CAAC;EACL;AACJ;AAEA,SAASN,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}