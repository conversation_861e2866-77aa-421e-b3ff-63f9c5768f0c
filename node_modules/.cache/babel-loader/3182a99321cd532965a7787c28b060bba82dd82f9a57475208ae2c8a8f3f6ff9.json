{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/pages/Models/Pages/DynamicModelPage/DynamicModelPage.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { getModelData } from '../../../../data/modelLoader';\nimport styles from '../modelPage.module.css';\nimport icon from '../../../../asset/imgs/icons/download.png';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\n// Components\nimport Modal from '../components/Modal';\nimport Specifications from '../components/Specifications';\n\n// SEO\nimport SEO from '../../../../hooks/useSEO';\nimport Form from '../../../../components/Form/Form';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DynamicModelPage = () => {\n  _s();\n  var _modelData$hero, _modelData$hero2, _modelData$sections$3;\n  const {\n    slug\n  } = useParams();\n  const [loading, setLoading] = useState(true);\n  const [modelData, setModelData] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(null);\n  const [modalContent, setModalContent] = useState(null);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    setLoading(true); // сбрасываем при смене slug\n    AOS.init({\n      duration: 500,\n      once: false\n    });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, [slug]);\n  useEffect(() => {\n    const loadModelData = () => {\n      setLoading(true);\n      try {\n        const data = getModelData(slug);\n        if (!data) {\n          setError('Модель не найдена');\n          setLoading(false);\n          return;\n        }\n        setModelData(data);\n        if (data.colors && data.colors.length > 0) {\n          setSelectedColor(data.colors[0]);\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load model data:', err);\n        setError('Модель не найдена');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (slug) {\n      loadModelData();\n    }\n  }, [slug]);\n  const handleColorChange = color => {\n    setSelectedColor(color);\n  };\n  const openModal = (title, desc) => {\n    setModalContent({\n      title,\n      desc\n    });\n  };\n  if (error || !modelData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: \"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u041A \\u0441\\u043E\\u0436\\u0430\\u043B\\u0435\\u043D\\u0438\\u044E, \\u0437\\u0430\\u043F\\u0440\\u0430\\u0448\\u0438\\u0432\\u0430\\u0435\\u043C\\u0430\\u044F \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Prepare data for specifications component\n  const tabData = {\n    tabsBtn: modelData.specifications.trims,\n    tableBtn: modelData.specifications.categories\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SEO, {\n      ...modelData.seo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loaderWrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loaderPage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"topmenu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.header,\n        style: {\n          backgroundImage: modelData !== null && modelData !== void 0 && (_modelData$hero = modelData.hero) !== null && _modelData$hero !== void 0 && _modelData$hero.image ? `url(${modelData.hero.image})` : 'none',\n          backgroundColor: modelData !== null && modelData !== void 0 && (_modelData$hero2 = modelData.hero) !== null && _modelData$hero2 !== void 0 && _modelData$hero2.image ? 'transparent' : '#ccc',\n          // светло-серый цвет\n          backgroundSize: 'cover',\n          backgroundPosition: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.slideContent,\n            children: [modelData.hero.engine && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.innerNew,\n              \"data-aos\": \"fade-up\",\n              children: modelData.hero.engine\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 19\n            }, this), modelData.hero.title && /*#__PURE__*/_jsxDEV(\"h1\", {\n              \"data-aos\": \"fade-up\",\n              children: modelData.hero.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this), modelData.hero.price && /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: styles.price,\n              \"data-aos\": \"fade-up\",\n              children: modelData.hero.price\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 19\n            }, this), modelData.hero.features && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.about,\n              \"data-aos\": \"fade-up\",\n              children: modelData.hero.features[0]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/book-a-test-drive\",\n              \"data-aos\": \"fade-up\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button-black\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0422\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"\\u041F\\u0440\\u0438\\u043C\\u0435\\u043D\\u044F\\u044E\\u0442\\u0441\\u044F \\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F. \\u0418\\u0437\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u044F \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u0434\\u043B\\u044F \\u0438\\u043B\\u043B\\u044E\\u0441\\u0442\\u0440\\u0430\\u0442\\u0438\\u0432\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043B\\u0435\\u0439. E&OE. \\u041E\\u0442\\u043A\\u0430\\u0437 \\u043E\\u0442 \\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438: \\u0432\\u0441\\u0435 \\u0446\\u0435\\u043D\\u044B \\u0438 \\u0445\\u0430\\u0440\\u0430\\u043A\\u0442\\u0435\\u0440\\u0438\\u0441\\u0442\\u0438\\u043A\\u0438 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0431\\u044B\\u0442\\u044C \\u0438\\u0437\\u043C\\u0435\\u043D\\u0435\\u043D\\u044B \\u0431\\u0435\\u0437 \\u043F\\u0440\\u0435\\u0434\\u0432\\u0430\\u0440\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0443\\u0432\\u0435\\u0434\\u043E\\u043C\\u043B\\u0435\\u043D\\u0438\\u044F. \\u0421\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0431\\u043B\\u0438\\u0436\\u0430\\u0439\\u0448\\u0438\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u043E\\u043C \\u0434\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u0446\\u0435\\u043D.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: styles.colorTitle,\n            style: {\n              color: selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.hex_code,\n              fontWeight: '900'\n            },\n            \"data-aos\": \"fade-up\",\n            children: selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.image,\n              alt: selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.name,\n              className: styles.colorImage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.colorPickerWrapper,\n            children: modelData.colors.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleColorChange(color),\n              className: `${styles.colorBtn} ${(selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.id) === color.id ? styles.active : ''}`,\n              style: {\n                backgroundColor: color.hex_code\n              },\n              children: (selectedColor === null || selectedColor === void 0 ? void 0 : selectedColor.id) === color.id ? color.name : ''\n            }, color.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title\",\n            children: [modelData.sections[0].title && /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: modelData.sections[0].title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), modelData.sections[0].description && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: modelData.sections[0].description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.cardContnet,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.rightCard,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.top,\n                children: [0, 1, 4].map(index => {\n                  var _modelData$sections$;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.box,\n                    children: ((_modelData$sections$ = modelData.sections[0].cards[index]) === null || _modelData$sections$ === void 0 ? void 0 : _modelData$sections$.img) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: modelData.sections[0].cards[index].img,\n                        alt: \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 213,\n                        columnNumber: 29\n                      }, this), (modelData.sections[0].cards[index].modalTitle || modelData.sections[0].cards[index].modalText) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: styles.plus,\n                        onClick: () => openModal(modelData.sections[0].cards[index].modalTitle, modelData.sections[0].cards[index].modalText),\n                        children: \"+\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 31\n                      }, this), modelData.sections[0].cards[index].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: styles.overlay,\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: modelData.sections[0].cards[index].modalTitle\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 233,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.bottom,\n                children: [2, 3].map(index => {\n                  var _modelData$sections$2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.box,\n                    children: ((_modelData$sections$2 = modelData.sections[0].cards[index]) === null || _modelData$sections$2 === void 0 ? void 0 : _modelData$sections$2.img) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: modelData.sections[0].cards[index].img,\n                        alt: \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 29\n                      }, this), (modelData.sections[0].cards[index].modalTitle || modelData.sections[0].cards[index].modalText) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: styles.plus,\n                        onClick: () => openModal(modelData.sections[0].cards[index].modalTitle, modelData.sections[0].cards[index].modalText),\n                        children: \"+\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 31\n                      }, this), modelData.sections[0].cards[index].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: styles.overlay,\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: modelData.sections[0].cards[index].modalTitle\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 273,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.leftCard,\n              children: ((_modelData$sections$3 = modelData.sections[0].cards[5]) === null || _modelData$sections$3 === void 0 ? void 0 : _modelData$sections$3.img) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: modelData.sections[0].cards[5].img,\n                  alt: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), (modelData.sections[0].cards[5].modalTitle || modelData.sections[0].cards[5].modalText) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.plus,\n                  onClick: () => openModal(modelData.sections[0].cards[5].modalTitle, modelData.sections[0].cards[5].modalText),\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 25\n                }, this), modelData.sections[0].cards[5].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.overlay,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: modelData.sections[0].cards[5].modalTitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.flex,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.left,\n              children: modelData.sections[1].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: modelData.sections[1].img,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.right,\n              children: [modelData.sections[1].title && /*#__PURE__*/_jsxDEV(\"h2\", {\n                children: modelData.sections[1].title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this), modelData.sections[1].description && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: modelData.sections[1].description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [modelData.sections[2].title && /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: modelData.sections[2].title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this), modelData.sections[2].description && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: modelData.sections[2].description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.cardContnet,\n            children: [modelData.sections[2].cards[0] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.right,\n              children: [modelData.sections[2].cards[0].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: modelData.sections[2].cards[0].img,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 23\n              }, this), modelData.sections[2].cards[0].modalText && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.plus,\n                onClick: () => openModal(modelData.sections[2].cards[0].modalTitle, modelData.sections[2].cards[0].modalText),\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 23\n              }, this), modelData.sections[2].cards[0].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.overlay,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: modelData.sections[2].cards[0].modalTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.middle,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.top,\n                children: [modelData.sections[2].cards[1].img && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.topRight,\n                  children: [modelData.sections[2].cards[1].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: modelData.sections[2].cards[1].img,\n                    alt: \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 27\n                  }, this), modelData.sections[2].cards[1].modalText && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.plus,\n                    onClick: () => openModal(modelData.sections[2].cards[1].modalTitle, modelData.sections[2].cards[1].modalText),\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 27\n                  }, this), modelData.sections[2].cards[1].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.overlay,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: modelData.sections[2].cards[1].modalTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.topLeft,\n                  children: [modelData.sections[2].cards[2].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: modelData.sections[2].cards[2].img,\n                    alt: \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this), modelData.sections[2].cards[2].modalText && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.plus,\n                    onClick: () => openModal(modelData.sections[2].cards[2].modalTitle, modelData.sections[2].cards[2].modalText),\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 25\n                  }, this), modelData.sections[2].cards[2].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.overlay,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: modelData.sections[2].cards[2].modalTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.buttom,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.box,\n                  children: [modelData.sections[2].cards[3].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: modelData.sections[2].cards[3].img,\n                    alt: \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 25\n                  }, this), modelData.sections[2].cards[3].modalText && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.plus,\n                    onClick: () => openModal(modelData.sections[2].cards[3].modalTitle, modelData.sections[2].cards[3].modalText),\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 25\n                  }, this), modelData.sections[2].cards[3].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: styles.overlay,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: modelData.sections[2].cards[3].modalTitle\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), modelData.sections[2].cards[4] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.left,\n              children: [modelData.sections[2].cards[4].img && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: modelData.sections[2].cards[4].img,\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 23\n              }, this), modelData.sections[2].cards[4].modalText && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.plus,\n                onClick: () => openModal(modelData.sections[2].cards[4].modalTitle, modelData.sections[2].cards[4].modalText),\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 23\n              }, this), modelData.sections[2].cards[4].modalTitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.overlay,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: modelData.sections[2].cards[4].modalTitle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: styles.section,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.contant,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.text,\n              children: [modelData.sections[3].title && /*#__PURE__*/_jsxDEV(\"h2\", {\n                children: modelData.sections[3].title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this), modelData.sections[3].description && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: modelData.sections[3].description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 21\n              }, this), modelData.sections[3].brochure && /*#__PURE__*/_jsxDEV(\"a\", {\n                href: modelData.sections[3].brochure,\n                className: styles.dowBtn,\n                target: \"_blank\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u0421\\u041A\\u0410\\u0427\\u0410\\u0422\\u042C \\u0411\\u0420\\u041E\\u0428\\u042E\\u0420\\u0423\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: icon,\n                  alt: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this), modelData.sections[3].img && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.bgSection,\n          style: {\n            backgroundImage: `url(${modelData.sections[3].img})`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            \"data-aos\": \"fade-up\",\n            className: styles.title,\n            children: modelData.sections[3].imgTitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Specifications, {\n        title: modelData.specifications.title,\n        desc: modelData.specifications.description,\n        tabData: tabData,\n        tableData: modelData.specifications.data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          formType: \"purchase\",\n          title: \"\\u041E\\u0441\\u0442\\u0430\\u0432\\u0438\\u0442\\u044C \\u0437\\u0430\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0430 \\u043F\\u043E\\u043A\\u0443\\u043F\\u043A\\u0443\",\n          defaultModel: slug\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 11\n      }, this), modalContent && /*#__PURE__*/_jsxDEV(Modal, {\n        title: modalContent.title,\n        desc: modalContent.desc,\n        onClose: () => setModalContent(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(DynamicModelPage, \"lgV19ZLAjfbiAUVKFJDhpaHWJpk=\", false, function () {\n  return [useParams];\n});\n_c = DynamicModelPage;\nexport default DynamicModelPage;\nvar _c;\n$RefreshReg$(_c, \"DynamicModelPage\");", "map": {"version": 3, "names": ["useEffect", "useState", "useParams", "Link", "getModelData", "styles", "icon", "AOS", "Modal", "Specifications", "SEO", "Form", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DynamicModelPage", "_s", "_modelData$hero", "_modelData$hero2", "_modelData$sections$3", "slug", "loading", "setLoading", "modelData", "setModelData", "selectedColor", "setSelectedColor", "modalContent", "setModalContent", "error", "setError", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "loadModelData", "data", "colors", "length", "err", "console", "handleColorChange", "color", "openModal", "title", "desc", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tabData", "tabsBtn", "specifications", "trims", "tableBtn", "categories", "seo", "header", "backgroundImage", "hero", "image", "backgroundColor", "backgroundSize", "backgroundPosition", "slideContent", "engine", "innerNew", "price", "features", "about", "to", "section", "colorTitle", "hex_code", "fontWeight", "name", "src", "alt", "colorImage", "colorPickerWrapper", "map", "onClick", "colorBtn", "id", "active", "sections", "description", "cardContnet", "rightCard", "top", "index", "_modelData$sections$", "box", "cards", "img", "modalTitle", "modalText", "plus", "overlay", "bottom", "_modelData$sections$2", "leftCard", "flex", "left", "right", "middle", "topRight", "topLeft", "buttom", "contant", "text", "brochure", "href", "dowBtn", "target", "bgSection", "imgTitle", "tableData", "formType", "defaultModel", "onClose", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/DynamicModelPage/DynamicModelPage.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';\nimport { getModelData } from '../../../../data/modelLoader';\nimport styles from '../modelPage.module.css';\nimport icon from '../../../../asset/imgs/icons/download.png';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\n// Components\nimport Modal from '../components/Modal';\nimport Specifications from '../components/Specifications';\n\n// SEO\nimport SEO from '../../../../hooks/useSEO';\nimport Form from '../../../../components/Form/Form';\n\nconst DynamicModelPage = () => {\n  const { slug } = useParams();\n  const [loading, setLoading] = useState(true);\n  const [modelData, setModelData] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(null);\n  const [modalContent, setModalContent] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    setLoading(true); // сбрасываем при смене slug\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, [slug]);\n\n  useEffect(() => {\n    const loadModelData = () => {\n      setLoading(true);\n      try {\n        const data = getModelData(slug);\n        if (!data) {\n          setError('Модель не найдена');\n          setLoading(false);\n          return;\n        }\n        setModelData(data);\n        if (data.colors && data.colors.length > 0) {\n          setSelectedColor(data.colors[0]);\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load model data:', err);\n        setError('Модель не найдена');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (slug) {\n      loadModelData();\n    }\n  }, [slug]);\n\n  const handleColorChange = (color) => {\n    setSelectedColor(color);\n  };\n\n  const openModal = (title, desc) => {\n    setModalContent({ title, desc });\n  };\n\n  if (error || !modelData) {\n    return (\n      <div className=\"topmenu\">\n        <div className=\"container\">\n          <div className=\"content\">\n            <h1 className=\"title\">Модель не найдена</h1>\n            <p>К сожалению, запрашиваемая модель не найдена.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Prepare data for specifications component\n  const tabData = {\n    tabsBtn: modelData.specifications.trims,\n    tableBtn: modelData.specifications.categories,\n  };\n\n  return (\n    <>\n      <SEO {...modelData.seo} />\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          {/* Hero Section */}\n          <div\n            className={styles.header}\n            style={{\n              backgroundImage: modelData?.hero?.image\n                ? `url(${modelData.hero.image})`\n                : 'none',\n              backgroundColor: modelData?.hero?.image ? 'transparent' : '#ccc', // светло-серый цвет\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n            }}\n          >\n            <div className=\"container\">\n              <div className={styles.slideContent}>\n                {modelData.hero.engine && (\n                  <div className={styles.innerNew} data-aos=\"fade-up\">\n                    {modelData.hero.engine}\n                  </div>\n                )}\n                {modelData.hero.title && (\n                  <h1 data-aos=\"fade-up\">{modelData.hero.title}</h1>\n                )}\n                {modelData.hero.price && (\n                  <h3 className={styles.price} data-aos=\"fade-up\">\n                    {modelData.hero.price}\n                  </h3>\n                )}\n                {modelData.hero.features && (\n                  <span className={styles.about} data-aos=\"fade-up\">\n                    {modelData.hero.features[0]}\n                  </span>\n                )}\n                <Link to=\"/book-a-test-drive\" data-aos=\"fade-up\">\n                  <button className=\"button-black\">\n                    <span>Тест-драйв</span>\n                  </button>\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* Disclaimer */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <small>\n                Применяются положения и условия. Изображения только для\n                иллюстративных целей. E&OE. Отказ от ответственности: все цены и\n                характеристики могут быть изменены без предварительного\n                уведомления. Свяжитесь с ближайшим дилером для получения\n                последних цен.\n              </small>\n            </div>\n          </section>\n          {/* Color Section */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <h2\n                className={styles.colorTitle}\n                style={{ color: selectedColor?.hex_code, fontWeight: '900' }}\n                data-aos=\"fade-up\"\n              >\n                {selectedColor?.name}\n              </h2>\n\n              <div>\n                <img\n                  src={selectedColor?.image}\n                  alt={selectedColor?.name}\n                  className={styles.colorImage}\n                />\n              </div>\n\n              <div className={styles.colorPickerWrapper}>\n                {modelData.colors.map((color) => (\n                  <button\n                    key={color.id}\n                    onClick={() => handleColorChange(color)}\n                    className={`${styles.colorBtn} ${\n                      selectedColor?.id === color.id ? styles.active : ''\n                    }`}\n                    style={{ backgroundColor: color.hex_code }}\n                  >\n                    {selectedColor?.id === color.id ? color.name : ''}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </section>\n\n          {/* Section 1 — динамически на основе modelData.sections[0] */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className=\"title\">\n                {modelData.sections[0].title && (\n                  <h2>{modelData.sections[0].title}</h2>\n                )}\n                {modelData.sections[0].description && (\n                  <p>{modelData.sections[0].description}</p>\n                )}\n              </div>\n              <div className={styles.cardContnet}>\n                <div className={styles.rightCard}>\n                  <div className={styles.top}>\n                    {[0, 1, 4].map((index) => (\n                      <div key={index} className={styles.box}>\n                        {modelData.sections[0].cards[index]?.img && (\n                          <>\n                            <img\n                              src={modelData.sections[0].cards[index].img}\n                              alt=\"\"\n                            />\n                            {(modelData.sections[0].cards[index].modalTitle ||\n                              modelData.sections[0].cards[index].modalText) && (\n                              <div\n                                className={styles.plus}\n                                onClick={() =>\n                                  openModal(\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle,\n                                    modelData.sections[0].cards[index].modalText\n                                  )\n                                }\n                              >\n                                +\n                              </div>\n                            )}\n                            {modelData.sections[0].cards[index].modalTitle && (\n                              <div className={styles.overlay}>\n                                <span>\n                                  {\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle\n                                  }\n                                </span>\n                              </div>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                  <div className={styles.bottom}>\n                    {[2, 3].map((index) => (\n                      <div key={index} className={styles.box}>\n                        {modelData.sections[0].cards[index]?.img && (\n                          <>\n                            <img\n                              src={modelData.sections[0].cards[index].img}\n                              alt=\"\"\n                            />\n                            {(modelData.sections[0].cards[index].modalTitle ||\n                              modelData.sections[0].cards[index].modalText) && (\n                              <div\n                                className={styles.plus}\n                                onClick={() =>\n                                  openModal(\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle,\n                                    modelData.sections[0].cards[index].modalText\n                                  )\n                                }\n                              >\n                                +\n                              </div>\n                            )}\n                            {modelData.sections[0].cards[index].modalTitle && (\n                              <div className={styles.overlay}>\n                                <span>\n                                  {\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle\n                                  }\n                                </span>\n                              </div>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                <div className={styles.leftCard}>\n                  {modelData.sections[0].cards[5]?.img && (\n                    <>\n                      <img src={modelData.sections[0].cards[5].img} alt=\"\" />\n                      {(modelData.sections[0].cards[5].modalTitle ||\n                        modelData.sections[0].cards[5].modalText) && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[0].cards[5].modalTitle,\n                              modelData.sections[0].cards[5].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[0].cards[5].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[0].cards[5].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </>\n                  )}\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* section 2 динамически на основе modelData.sections[1]/*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className={styles.flex}>\n                <div className={styles.left}>\n                  {modelData.sections[1].img && (\n                    <img src={modelData.sections[1].img} alt=\"\" />\n                  )}\n                </div>\n                <div className={styles.right}>\n                  {modelData.sections[1].title && (\n                    <h2>{modelData.sections[1].title}</h2>\n                  )}\n                  {modelData.sections[1].description && (\n                    <p>{modelData.sections[1].description}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* section 3 динамически на основе modelData.sections[2]*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div>\n                {modelData.sections[2].title && (\n                  <h2>{modelData.sections[2].title}</h2>\n                )}\n                {modelData.sections[2].description && (\n                  <p>{modelData.sections[2].description}</p>\n                )}\n              </div>\n              <div className={styles.cardContnet}>\n                {/* /right/ */}\n                {modelData.sections[2].cards[0] && (\n                  <div className={styles.right}>\n                    {modelData.sections[2].cards[0].img && (\n                      <img src={modelData.sections[2].cards[0].img} alt=\"\" />\n                    )}\n                    {modelData.sections[2].cards[0].modalText && (\n                      <div\n                        className={styles.plus}\n                        onClick={() =>\n                          openModal(\n                            modelData.sections[2].cards[0].modalTitle,\n                            modelData.sections[2].cards[0].modalText\n                          )\n                        }\n                      >\n                        +\n                      </div>\n                    )}\n                    {modelData.sections[2].cards[0].modalTitle && (\n                      <div className={styles.overlay}>\n                        <span>{modelData.sections[2].cards[0].modalTitle}</span>\n                      </div>\n                    )}\n                  </div>\n                )}\n                {/* /middle/ */}\n                <div className={styles.middle}>\n                  <div className={styles.top}>\n                    {modelData.sections[2].cards[1].img && (\n                      <div className={styles.topRight}>\n                        {modelData.sections[2].cards[1].img && (\n                          <img\n                            src={modelData.sections[2].cards[1].img}\n                            alt=\"\"\n                          />\n                        )}\n                        {modelData.sections[2].cards[1].modalText && (\n                          <div\n                            className={styles.plus}\n                            onClick={() =>\n                              openModal(\n                                modelData.sections[2].cards[1].modalTitle,\n                                modelData.sections[2].cards[1].modalText\n                              )\n                            }\n                          >\n                            +\n                          </div>\n                        )}\n                        {modelData.sections[2].cards[1].modalTitle && (\n                          <div className={styles.overlay}>\n                            <span>\n                              {modelData.sections[2].cards[1].modalTitle}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                    <div className={styles.topLeft}>\n                      {modelData.sections[2].cards[2].img && (\n                        <img src={modelData.sections[2].cards[2].img} alt=\"\" />\n                      )}\n                      {modelData.sections[2].cards[2].modalText && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[2].cards[2].modalTitle,\n                              modelData.sections[2].cards[2].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[2].cards[2].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[2].cards[2].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  <div className={styles.buttom}>\n                    <div className={styles.box}>\n                      {modelData.sections[2].cards[3].img && (\n                        <img src={modelData.sections[2].cards[3].img} alt=\"\" />\n                      )}\n                      {modelData.sections[2].cards[3].modalText && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[2].cards[3].modalTitle,\n                              modelData.sections[2].cards[3].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[2].cards[3].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[2].cards[3].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                {/* left  */}\n                {modelData.sections[2].cards[4] && (\n                  <div className={styles.left}>\n                    {modelData.sections[2].cards[4].img && (\n                      <img src={modelData.sections[2].cards[4].img} alt=\"\" />\n                    )}\n                    {modelData.sections[2].cards[4].modalText && (\n                      <div\n                        className={styles.plus}\n                        onClick={() =>\n                          openModal(\n                            modelData.sections[2].cards[4].modalTitle,\n                            modelData.sections[2].cards[4].modalText\n                          )\n                        }\n                      >\n                        +\n                      </div>\n                    )}\n                    {modelData.sections[2].cards[4].modalTitle && (\n                      <div className={styles.overlay}>\n                        <span>{modelData.sections[2].cards[4].modalTitle}</span>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          </section>\n\n          {/* section 4 динамически на основе modelData.sections[3]/*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className={styles.contant}>\n                <div className={styles.text}>\n                  {modelData.sections[3].title && (\n                    <h2>{modelData.sections[3].title}</h2>\n                  )}\n                  {modelData.sections[3].description && (\n                    <p>{modelData.sections[3].description}</p>\n                  )}\n                  {modelData.sections[3].brochure && (\n                    <a\n                      href={modelData.sections[3].brochure}\n                      className={styles.dowBtn}\n                      target=\"_blank\"\n                    >\n                      <span>СКАЧАТЬ БРОШЮРУ</span>\n                      <img src={icon} alt=\"\" />\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n            {modelData.sections[3].img && (\n              <div\n                className={styles.bgSection}\n                style={{\n                  backgroundImage: `url(${modelData.sections[3].img})`,\n                }}\n              >\n                <h2 data-aos=\"fade-up\" className={styles.title}>\n                  {modelData.sections[3].imgTitle}\n                </h2>\n              </div>\n            )}\n          </section>\n\n          {/* Specifications */}\n          <Specifications\n            title={modelData.specifications.title}\n            desc={modelData.specifications.description}\n            tabData={tabData}\n            tableData={modelData.specifications.data}\n          />\n\n          {/* Purchase request form */}\n          <section className=\"container\">\n            <Form\n              formType=\"purchase\"\n              title=\"Оставить запрос на покупку\"\n              defaultModel={slug}\n            />\n          </section>\n\n          {/* Modal */}\n          {modalContent && (\n            <Modal\n              title={modalContent.title}\n              desc={modalContent.desc}\n              onClose={() => setModalContent(null)}\n            />\n          )}\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default DynamicModelPage;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,IAAI,MAAM,2CAA2C;AAC5D,OAAOC,GAAG,MAAM,KAAK;AACrB,OAAO,kBAAkB;;AAEzB;AACA,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,cAAc,MAAM,8BAA8B;;AAEzD;AACA,OAAOC,GAAG,MAAM,0BAA0B;AAC1C,OAAOC,IAAI,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAC7B,MAAM;IAAEC;EAAK,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAC5B,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACduB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAClBhB,GAAG,CAACyB,IAAI,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BnB,UAAU,CAAC,KAAK,CAAC;MACjBc,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAM;MACXG,YAAY,CAACF,KAAK,CAAC;MACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,SAAS;IAC1C,CAAC;EACH,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EAEVrB,SAAS,CAAC,MAAM;IACd,MAAM4C,aAAa,GAAGA,CAAA,KAAM;MAC1BrB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMsB,IAAI,GAAGzC,YAAY,CAACiB,IAAI,CAAC;QAC/B,IAAI,CAACwB,IAAI,EAAE;UACTd,QAAQ,CAAC,mBAAmB,CAAC;UAC7BR,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QACAE,YAAY,CAACoB,IAAI,CAAC;QAClB,IAAIA,IAAI,CAACC,MAAM,IAAID,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;UACzCpB,gBAAgB,CAACkB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC;QACAf,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOiB,GAAG,EAAE;QACZC,OAAO,CAACnB,KAAK,CAAC,4BAA4B,EAAEkB,GAAG,CAAC;QAChDjB,QAAQ,CAAC,mBAAmB,CAAC;MAC/B,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,IAAI,EAAE;MACRuB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACvB,IAAI,CAAC,CAAC;EAEV,MAAM6B,iBAAiB,GAAIC,KAAK,IAAK;IACnCxB,gBAAgB,CAACwB,KAAK,CAAC;EACzB,CAAC;EAED,MAAMC,SAAS,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;IACjCzB,eAAe,CAAC;MAAEwB,KAAK;MAAEC;IAAK,CAAC,CAAC;EAClC,CAAC;EAED,IAAIxB,KAAK,IAAI,CAACN,SAAS,EAAE;IACvB,oBACEX,OAAA;MAAK0C,SAAS,EAAC,SAAS;MAAAC,QAAA,eACtB3C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB3C,OAAA;UAAK0C,SAAS,EAAC,SAAS;UAAAC,QAAA,gBACtB3C,OAAA;YAAI0C,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C/C,OAAA;YAAA2C,QAAA,EAAG;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAEtC,SAAS,CAACuC,cAAc,CAACC,KAAK;IACvCC,QAAQ,EAAEzC,SAAS,CAACuC,cAAc,CAACG;EACrC,CAAC;EAED,oBACErD,OAAA,CAAAE,SAAA;IAAAyC,QAAA,gBACE3C,OAAA,CAACH,GAAG;MAAA,GAAKc,SAAS,CAAC2C;IAAG;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EACzBtC,OAAO,gBACNT,OAAA;MAAK0C,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5B3C,OAAA;QAAK0C,SAAS,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,gBAEN/C,OAAA;MAAK0C,SAAS,EAAC,SAAS;MAAAC,QAAA,gBAEtB3C,OAAA;QACE0C,SAAS,EAAElD,MAAM,CAAC+D,MAAO;QACzB7B,KAAK,EAAE;UACL8B,eAAe,EAAE7C,SAAS,aAATA,SAAS,gBAAAN,eAAA,GAATM,SAAS,CAAE8C,IAAI,cAAApD,eAAA,eAAfA,eAAA,CAAiBqD,KAAK,GACnC,OAAO/C,SAAS,CAAC8C,IAAI,CAACC,KAAK,GAAG,GAC9B,MAAM;UACVC,eAAe,EAAEhD,SAAS,aAATA,SAAS,gBAAAL,gBAAA,GAATK,SAAS,CAAE8C,IAAI,cAAAnD,gBAAA,eAAfA,gBAAA,CAAiBoD,KAAK,GAAG,aAAa,GAAG,MAAM;UAAE;UAClEE,cAAc,EAAE,OAAO;UACvBC,kBAAkB,EAAE;QACtB,CAAE;QAAAlB,QAAA,eAEF3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAACsE,YAAa;YAAAnB,QAAA,GACjChC,SAAS,CAAC8C,IAAI,CAACM,MAAM,iBACpB/D,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAACwE,QAAS;cAAC,YAAS,SAAS;cAAArB,QAAA,EAChDhC,SAAS,CAAC8C,IAAI,CAACM;YAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACN,EACApC,SAAS,CAAC8C,IAAI,CAACjB,KAAK,iBACnBxC,OAAA;cAAI,YAAS,SAAS;cAAA2C,QAAA,EAAEhC,SAAS,CAAC8C,IAAI,CAACjB;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAClD,EACApC,SAAS,CAAC8C,IAAI,CAACQ,KAAK,iBACnBjE,OAAA;cAAI0C,SAAS,EAAElD,MAAM,CAACyE,KAAM;cAAC,YAAS,SAAS;cAAAtB,QAAA,EAC5ChC,SAAS,CAAC8C,IAAI,CAACQ;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACL,EACApC,SAAS,CAAC8C,IAAI,CAACS,QAAQ,iBACtBlE,OAAA;cAAM0C,SAAS,EAAElD,MAAM,CAAC2E,KAAM;cAAC,YAAS,SAAS;cAAAxB,QAAA,EAC9ChC,SAAS,CAAC8C,IAAI,CAACS,QAAQ,CAAC,CAAC;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACP,eACD/C,OAAA,CAACV,IAAI;cAAC8E,EAAE,EAAC,oBAAoB;cAAC,YAAS,SAAS;cAAAzB,QAAA,eAC9C3C,OAAA;gBAAQ0C,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC9B3C,OAAA;kBAAA2C,QAAA,EAAM;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,eACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAA2C,QAAA,EAAO;UAMP;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEV/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,eACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3C,OAAA;YACE0C,SAAS,EAAElD,MAAM,CAAC8E,UAAW;YAC7B5C,KAAK,EAAE;cAAEY,KAAK,EAAEzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0D,QAAQ;cAAEC,UAAU,EAAE;YAAM,CAAE;YAC7D,YAAS,SAAS;YAAA7B,QAAA,EAEjB9B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4D;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEL/C,OAAA;YAAA2C,QAAA,eACE3C,OAAA;cACE0E,GAAG,EAAE7D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6C,KAAM;cAC1BiB,GAAG,EAAE9D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4D,IAAK;cACzB/B,SAAS,EAAElD,MAAM,CAACoF;YAAW;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAACqF,kBAAmB;YAAAlC,QAAA,EACvChC,SAAS,CAACsB,MAAM,CAAC6C,GAAG,CAAExC,KAAK,iBAC1BtC,OAAA;cAEE+E,OAAO,EAAEA,CAAA,KAAM1C,iBAAiB,CAACC,KAAK,CAAE;cACxCI,SAAS,EAAE,GAAGlD,MAAM,CAACwF,QAAQ,IAC3B,CAAAnE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoE,EAAE,MAAK3C,KAAK,CAAC2C,EAAE,GAAGzF,MAAM,CAAC0F,MAAM,GAAG,EAAE,EAClD;cACHxD,KAAK,EAAE;gBAAEiC,eAAe,EAAErB,KAAK,CAACiC;cAAS,CAAE;cAAA5B,QAAA,EAE1C,CAAA9B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoE,EAAE,MAAK3C,KAAK,CAAC2C,EAAE,GAAG3C,KAAK,CAACmC,IAAI,GAAG;YAAE,GAP5CnC,KAAK,CAAC2C,EAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQP,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGV/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,eACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3C,OAAA;YAAK0C,SAAS,EAAC,OAAO;YAAAC,QAAA,GACnBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C,KAAK,iBAC1BxC,OAAA;cAAA2C,QAAA,EAAKhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACtC,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,iBAChCpF,OAAA;cAAA2C,QAAA,EAAIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC;YAAW;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAAC6F,WAAY;YAAA1C,QAAA,gBACjC3C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC8F,SAAU;cAAA3C,QAAA,gBAC/B3C,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAAC+F,GAAI;gBAAA5C,QAAA,EACxB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACmC,GAAG,CAAEU,KAAK;kBAAA,IAAAC,oBAAA;kBAAA,oBACnBzF,OAAA;oBAAiB0C,SAAS,EAAElD,MAAM,CAACkG,GAAI;oBAAA/C,QAAA,EACpC,EAAA8C,oBAAA,GAAA9E,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,cAAAC,oBAAA,uBAAlCA,oBAAA,CAAoCG,GAAG,kBACtC5F,OAAA,CAAAE,SAAA;sBAAAyC,QAAA,gBACE3C,OAAA;wBACE0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACI,GAAI;wBAC5CjB,GAAG,EAAC;sBAAE;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,EACD,CAACpC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,IAC7ClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SAAS,kBAC5C9F,OAAA;wBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;wBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,EACblF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SACrC,CACD;wBAAAnD,QAAA,EACF;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,iBAC5C7F,OAAA;wBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;wBAAArD,QAAA,eAC7B3C,OAAA;0BAAA2C,QAAA,EAEIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK;wBAAU;0BAAAjD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN;oBAAA,eACD;kBACH,GAjCOyC,KAAK;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkCV,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAACyG,MAAO;gBAAAtD,QAAA,EAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAACmC,GAAG,CAAEU,KAAK;kBAAA,IAAAU,qBAAA;kBAAA,oBAChBlG,OAAA;oBAAiB0C,SAAS,EAAElD,MAAM,CAACkG,GAAI;oBAAA/C,QAAA,EACpC,EAAAuD,qBAAA,GAAAvF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,cAAAU,qBAAA,uBAAlCA,qBAAA,CAAoCN,GAAG,kBACtC5F,OAAA,CAAAE,SAAA;sBAAAyC,QAAA,gBACE3C,OAAA;wBACE0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACI,GAAI;wBAC5CjB,GAAG,EAAC;sBAAE;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,EACD,CAACpC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,IAC7ClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SAAS,kBAC5C9F,OAAA;wBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;wBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,EACblF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SACrC,CACD;wBAAAnD,QAAA,EACF;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,iBAC5C7F,OAAA;wBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;wBAAArD,QAAA,eAC7B3C,OAAA;0BAAA2C,QAAA,EAEIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK;wBAAU;0BAAAjD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEX;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN;oBAAA,eACD;kBACH,GAjCOyC,KAAK;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkCV,CAAC;gBAAA,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC2G,QAAS;cAAAxD,QAAA,EAC7B,EAAApC,qBAAA,GAAAI,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,cAAApF,qBAAA,uBAA9BA,qBAAA,CAAgCqF,GAAG,kBAClC5F,OAAA,CAAAE,SAAA;gBAAAyC,QAAA,gBACE3C,OAAA;kBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;kBAACjB,GAAG,EAAC;gBAAE;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACtD,CAACpC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,IACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,kBACxC9F,OAAA;kBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;kBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;kBAAAnD,QAAA,EACF;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;kBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;kBAAArD,QAAA,eAC7B3C,OAAA;oBAAA2C,QAAA,EACGhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;kBAAU;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN;cAAA,eACD;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGV/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,eACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAAC4G,IAAK;YAAAzD,QAAA,gBAC1B3C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC6G,IAAK;cAAA1D,QAAA,EACzBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG,iBACxB5F,OAAA;gBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAI;gBAACjB,GAAG,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC9C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN/C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC8G,KAAM;cAAA3D,QAAA,GAC1BhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C,KAAK,iBAC1BxC,OAAA;gBAAA2C,QAAA,EAAKhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACtC,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,iBAChCpF,OAAA;gBAAA2C,QAAA,EAAIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC;cAAW;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGV/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,eACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3C,OAAA;YAAA2C,QAAA,GACGhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C,KAAK,iBAC1BxC,OAAA;cAAA2C,QAAA,EAAKhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACtC,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,iBAChCpF,OAAA;cAAA2C,QAAA,EAAIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC;YAAW;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAAC6F,WAAY;YAAA1C,QAAA,GAEhChC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,iBAC7B3F,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC8G,KAAM;cAAA3D,QAAA,GAC1BhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;gBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;gBAACjB,GAAG,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACvD,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,iBACvC9F,OAAA;gBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;gBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;gBAAAnD,QAAA,EACF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;gBAAArD,QAAA,eAC7B3C,OAAA;kBAAA2C,QAAA,EAAOhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAED/C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC+G,MAAO;cAAA5D,QAAA,gBAC5B3C,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAAC+F,GAAI;gBAAA5C,QAAA,GACxBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;kBAAK0C,SAAS,EAAElD,MAAM,CAACgH,QAAS;kBAAA7D,QAAA,GAC7BhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;oBACE0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;oBACxCjB,GAAG,EAAC;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACF,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,iBACvC9F,OAAA;oBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;oBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;oBAAAnD,QAAA,EACF;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;oBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;oBAAArD,QAAA,eAC7B3C,OAAA;sBAAA2C,QAAA,EACGhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;oBAAU;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,eACD/C,OAAA;kBAAK0C,SAAS,EAAElD,MAAM,CAACiH,OAAQ;kBAAA9D,QAAA,GAC5BhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;oBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;oBAACjB,GAAG,EAAC;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvD,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,iBACvC9F,OAAA;oBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;oBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;oBAAAnD,QAAA,EACF;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;oBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;oBAAArD,QAAA,eAC7B3C,OAAA;sBAAA2C,QAAA,EACGhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;oBAAU;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/C,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAACkH,MAAO;gBAAA/D,QAAA,eAC5B3C,OAAA;kBAAK0C,SAAS,EAAElD,MAAM,CAACkG,GAAI;kBAAA/C,QAAA,GACxBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;oBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;oBAACjB,GAAG,EAAC;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvD,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,iBACvC9F,OAAA;oBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;oBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;oBAAAnD,QAAA,EACF;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;oBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;oBAAArD,QAAA,eAC7B3C,OAAA;sBAAA2C,QAAA,EACGhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;oBAAU;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELpC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,iBAC7B3F,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAAC6G,IAAK;cAAA1D,QAAA,GACzBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,iBACjC5F,OAAA;gBAAK0E,GAAG,EAAE/D,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI;gBAACjB,GAAG,EAAC;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACvD,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,iBACvC9F,OAAA;gBACE0C,SAAS,EAAElD,MAAM,CAACuG,IAAK;gBACvBhB,OAAO,EAAEA,CAAA,KACPxC,SAAS,CACP5B,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzClF,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD;gBAAAnD,QAAA,EACF;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,iBACxC7F,OAAA;gBAAK0C,SAAS,EAAElD,MAAM,CAACwG,OAAQ;gBAAArD,QAAA,eAC7B3C,OAAA;kBAAA2C,QAAA,EAAOhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE;gBAAU;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGV/C,OAAA;QAAS0C,SAAS,EAAElD,MAAM,CAAC6E,OAAQ;QAAA1B,QAAA,gBACjC3C,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB3C,OAAA;YAAK0C,SAAS,EAAElD,MAAM,CAACmH,OAAQ;YAAAhE,QAAA,eAC7B3C,OAAA;cAAK0C,SAAS,EAAElD,MAAM,CAACoH,IAAK;cAAAjE,QAAA,GACzBhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C,KAAK,iBAC1BxC,OAAA;gBAAA2C,QAAA,EAAKhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC3C;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACtC,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,iBAChCpF,OAAA;gBAAA2C,QAAA,EAAIhC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACC;cAAW;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC1C,EACApC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC0B,QAAQ,iBAC7B7G,OAAA;gBACE8G,IAAI,EAAEnG,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC0B,QAAS;gBACrCnE,SAAS,EAAElD,MAAM,CAACuH,MAAO;gBACzBC,MAAM,EAAC,QAAQ;gBAAArE,QAAA,gBAEf3C,OAAA;kBAAA2C,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5B/C,OAAA;kBAAK0E,GAAG,EAAEjF,IAAK;kBAACkF,GAAG,EAAC;gBAAE;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLpC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG,iBACxB5F,OAAA;UACE0C,SAAS,EAAElD,MAAM,CAACyH,SAAU;UAC5BvF,KAAK,EAAE;YACL8B,eAAe,EAAE,OAAO7C,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG;UACnD,CAAE;UAAAjD,QAAA,eAEF3C,OAAA;YAAI,YAAS,SAAS;YAAC0C,SAAS,EAAElD,MAAM,CAACgD,KAAM;YAAAG,QAAA,EAC5ChC,SAAS,CAACwE,QAAQ,CAAC,CAAC,CAAC,CAAC+B;UAAQ;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGV/C,OAAA,CAACJ,cAAc;QACb4C,KAAK,EAAE7B,SAAS,CAACuC,cAAc,CAACV,KAAM;QACtCC,IAAI,EAAE9B,SAAS,CAACuC,cAAc,CAACkC,WAAY;QAC3CpC,OAAO,EAAEA,OAAQ;QACjBmE,SAAS,EAAExG,SAAS,CAACuC,cAAc,CAAClB;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAGF/C,OAAA;QAAS0C,SAAS,EAAC,WAAW;QAAAC,QAAA,eAC5B3C,OAAA,CAACF,IAAI;UACHsH,QAAQ,EAAC,UAAU;UACnB5E,KAAK,EAAC,+IAA4B;UAClC6E,YAAY,EAAE7G;QAAK;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGThC,YAAY,iBACXf,OAAA,CAACL,KAAK;QACJ6C,KAAK,EAAEzB,YAAY,CAACyB,KAAM;QAC1BC,IAAI,EAAE1B,YAAY,CAAC0B,IAAK;QACxB6E,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,IAAI;MAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAC3C,EAAA,CAjiBID,gBAAgB;EAAA,QACHd,SAAS;AAAA;AAAAkI,EAAA,GADtBpH,gBAAgB;AAmiBtB,eAAeA,gBAAgB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}