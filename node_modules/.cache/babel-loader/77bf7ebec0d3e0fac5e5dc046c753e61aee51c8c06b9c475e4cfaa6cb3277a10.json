{"ast": null, "code": "// Import all model data\nimport tank300Data from'./models/tank-300.js';import tank500Data from'./models/tank-500.js';import tank700Data from'./models/tank-700.js';import tank700PhevData from'./models/tank-700-phev.js';import havalH6Data from'./models/haval-h6.js';import havalH6HevData from'./models/haval-h6-hev.js';import havalJolionNewData from'./models/haval-jolion-new.js';import havalM6Data from'./models/haval-m6.js';import gwmCommercialPoerData from'./models/gwm-commercial-poer.js';import gwmWingle7Data from'./models/gwm-wingle-7.js';// Model data registry\nconst modelData={'tank-300':tank300Data,'tank-500':tank500Data,'tank-700':tank700Data,'tank-700-phev':tank700PhevData,'haval-h6':havalH6Data,'haval-h6-hev':havalH6HevData,'haval-jolion':havalJolionNewData,'haval-m6':havalM6Data,'gwm-commercial-poer':gwmCommercialPoerData,'gwm-wingle-7':gwmWingle7Data};// Function to get model data by slug\nexport const getModelData=slug=>{return modelData[slug]||null;};// Function to get all available model slugs\nexport const getAvailableModels=()=>{return Object.keys(modelData);};", "map": {"version": 3, "names": ["tank300Data", "tank500Data", "tank700Data", "tank700PhevData", "havalH6Data", "havalH6HevData", "havalJolionNewData", "havalM6Data", "gwmCommercialPoerData", "gwmWingle7Data", "modelData", "getModelData", "slug", "getAvailableModels", "Object", "keys"], "sources": ["/var/www/html/gwm.tj/src/data/modelLoader.js"], "sourcesContent": ["// Import all model data\nimport tank300Data from './models/tank-300.js';\nimport tank500Data from './models/tank-500.js';\nimport tank700Data from './models/tank-700.js';\nimport tank700PhevData from './models/tank-700-phev.js';\nimport havalH6Data from './models/haval-h6.js';\nimport havalH6HevData from './models/haval-h6-hev.js';\nimport havalJolionNewData from './models/haval-jolion-new.js';\nimport havalM6Data from './models/haval-m6.js';\nimport gwmCommercialPoerData from './models/gwm-commercial-poer.js';\nimport gwmWingle7Data from './models/gwm-wingle-7.js';\n\n// Model data registry\nconst modelData = {\n  'tank-300': tank300Data,\n  'tank-500': tank500Data,\n  'tank-700': tank700Data,\n  'tank-700-phev': tank700PhevData,\n  'haval-h6': havalH6Data,\n  'haval-h6-hev': havalH6HevData,\n  'haval-jolion': havalJolionNewData,\n  'haval-m6': havalM6Data,\n  'gwm-commercial-poer': gwmCommercialPoerData,\n  'gwm-wingle-7': gwmWingle7Data\n};\n\n// Function to get model data by slug\nexport const getModelData = (slug) => {\n  return modelData[slug] || null;\n};\n\n// Function to get all available model slugs\nexport const getAvailableModels = () => {\n  return Object.keys(modelData);\n};\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CACrD,MAAO,CAAAC,kBAAkB,KAAM,8BAA8B,CAC7D,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,qBAAqB,KAAM,iCAAiC,CACnE,MAAO,CAAAC,cAAc,KAAM,0BAA0B,CAErD;AACA,KAAM,CAAAC,SAAS,CAAG,CAChB,UAAU,CAAEV,WAAW,CACvB,UAAU,CAAEC,WAAW,CACvB,UAAU,CAAEC,WAAW,CACvB,eAAe,CAAEC,eAAe,CAChC,UAAU,CAAEC,WAAW,CACvB,cAAc,CAAEC,cAAc,CAC9B,cAAc,CAAEC,kBAAkB,CAClC,UAAU,CAAEC,WAAW,CACvB,qBAAqB,CAAEC,qBAAqB,CAC5C,cAAc,CAAEC,cAClB,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,YAAY,CAAIC,IAAI,EAAK,CACpC,MAAO,CAAAF,SAAS,CAACE,IAAI,CAAC,EAAI,IAAI,CAChC,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CACtC,MAAO,CAAAC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}