{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"defaultTransition\"],\n  _excluded2 = [\"delay\", \"times\", \"type\", \"repeat\", \"repeatType\", \"repeatDelay\"];\nimport { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence) {\n  let _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    {\n      defaultTransition = {}\n    } = _ref,\n    sequenceTransition = _objectWithoutProperties(_ref, _excluded);\n  let scope = arguments.length > 2 ? arguments[2] : undefined;\n  let generators = arguments.length > 3 ? arguments[3] : undefined;\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = function (valueKeyframes, valueTransition, valueSequence) {\n      let elementIndex = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      let numSubjects = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n          delay = 0,\n          times = defaultOffset(valueKeyframesAsList),\n          type = \"keyframes\",\n          repeat,\n          repeatType,\n          repeatDelay = 0\n        } = valueTransition,\n        remainingTransition = _objectWithoutProperties(valueTransition, _excluded2);\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numSubjects) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      const createGenerator = isGenerator(type) ? type : generators === null || generators === void 0 ? void 0 : generators[type];\n      if (numKeyframes <= 2 && createGenerator) {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = _objectSpread({}, remainingTransition);\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration !== null && duration !== void 0 ? duration : duration = defaultDuration;\n      const startTime = currentTime + calculatedDelay;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Handle repeat options\n       */\n      if (repeat) {\n        invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n        duration = calculateRepeatDuration(duration, repeat);\n        const originalKeyframes = [...valueKeyframesAsList];\n        const originalTimes = [...times];\n        ease = Array.isArray(ease) ? [...ease] : [ease];\n        const originalEase = [...ease];\n        for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n          valueKeyframesAsList.push(...originalKeyframes);\n          for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n            times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n            ease.push(keyframeIndex === 0 ? \"linear\" : getEasingForSegment(originalEase, keyframeIndex - 1));\n          }\n        }\n        normalizeTimes(times, repeat);\n      }\n      const targetTime = startTime + duration;\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n      const numSubjects = subjects.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const thisSubject = subjects[subjectIndex];\n        const subjectSequence = getSubjectSequence(thisSubject, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = _objectSpread(_objectSpread({}, defaultTransition), {}, {\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset\n      }, sequenceTransition);\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition && transition[key] ? _objectSpread(_objectSpread({}, transition), transition[key]) : _objectSpread({}, transition);\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["isMotionValue", "defaultOffset", "isGenerator", "createGeneratorEasing", "fillOffset", "progress", "secondsToMilliseconds", "invariant", "getEasingForSegment", "resolveSubjects", "calculateRepeatDuration", "calcNextTime", "addKeyframes", "normalizeTimes", "compareByTime", "defaultSegmentEasing", "MAX_REPEAT", "createAnimationsFromSequence", "sequence", "_ref", "arguments", "length", "undefined", "defaultTransition", "sequenceTransition", "_objectWithoutProperties", "_excluded", "scope", "generators", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numSubjects", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "repeat", "repeatType", "repeatDelay", "remainingTransition", "_excluded2", "ease", "calculatedDelay", "numKeyframes", "createGenerator", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "_objectSpread", "springEasing", "startTime", "remainder", "unshift", "originalKeyframes", "originalTimes", "originalEase", "repeatIndex", "push", "keyframeIndex", "targetTime", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "subjects", "subjectIndex", "thisSubject", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "element", "sort", "valueOffset", "valueEasing", "value", "easing", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { isMotionValue, defaultOffset, isGenerator, createGeneratorEasing, fillOffset } from 'motion-dom';\nimport { progress, secondsToMilliseconds, invariant, getEasingForSegment } from 'motion-utils';\nimport { resolveSubjects } from '../animate/resolve-subjects.mjs';\nimport { calculateRepeatDuration } from './utils/calc-repeat-duration.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { normalizeTimes } from './utils/normalize-times.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nconst MAX_REPEAT = 20;\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope, generators) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numSubjects = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", repeat, repeatType, repeatDelay = 0, ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numSubjects)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            const createGenerator = isGenerator(type)\n                ? type\n                : generators?.[type];\n            if (numKeyframes <= 2 && createGenerator) {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta, createGenerator);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration ?? (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Handle repeat options\n             */\n            if (repeat) {\n                invariant(repeat < MAX_REPEAT, \"Repeat count too high, must be less than 20\");\n                duration = calculateRepeatDuration(duration, repeat);\n                const originalKeyframes = [...valueKeyframesAsList];\n                const originalTimes = [...times];\n                ease = Array.isArray(ease) ? [...ease] : [ease];\n                const originalEase = [...ease];\n                for (let repeatIndex = 0; repeatIndex < repeat; repeatIndex++) {\n                    valueKeyframesAsList.push(...originalKeyframes);\n                    for (let keyframeIndex = 0; keyframeIndex < originalKeyframes.length; keyframeIndex++) {\n                        times.push(originalTimes[keyframeIndex] + (repeatIndex + 1));\n                        ease.push(keyframeIndex === 0\n                            ? \"linear\"\n                            : getEasingForSegment(originalEase, keyframeIndex - 1));\n                    }\n                }\n                normalizeTimes(times, repeat);\n            }\n            const targetTime = startTime + duration;\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            const subjects = resolveSubjects(subject, keyframes, scope, elementCache);\n            const numSubjects = subjects.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let subjectIndex = 0; subjectIndex < numSubjects; subjectIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const thisSubject = subjects[subjectIndex];\n                const subjectSequence = getSubjectSequence(thisSubject, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), subjectIndex, numSubjects);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition && transition[key]\n        ? {\n            ...transition,\n            ...transition[key],\n        }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": ";;;;AAAA,SAASA,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,UAAU,QAAQ,YAAY;AACzG,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,mBAAmB,QAAQ,cAAc;AAC9F,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,MAAMC,UAAU,GAAG,EAAE;AACrB,SAASC,4BAA4BA,CAACC,QAAQ,EAA6E;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAvB,CAAC,CAAC;IAAtD;MAAEG,iBAAiB,GAAG,CAAC;IAAyB,CAAC,GAAAJ,IAAA;IAApBK,kBAAkB,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAAA,IAASC,KAAK,GAAAP,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAAA,IAAEM,UAAU,GAAAR,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACrH,MAAMO,eAAe,GAAGN,iBAAiB,CAACO,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,CAAC,CAAC;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,CAAC,CAAC;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,QAAQ,CAACG,MAAM,EAAEkB,CAAC,EAAE,EAAE;IACtC,MAAMC,OAAO,GAAGtB,QAAQ,CAACqB,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC7BL,UAAU,CAACM,GAAG,CAACD,OAAO,EAAEH,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BL,UAAU,CAACM,GAAG,CAACD,OAAO,CAACI,IAAI,EAAEjC,YAAY,CAAC0B,WAAW,EAAEG,OAAO,CAACK,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACW,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKvB,SAAS,EAAE;MAC7Be,WAAW,GAAG1B,YAAY,CAAC0B,WAAW,EAAEW,UAAU,CAACH,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIc,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAG,SAAAA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAwC;MAAA,IAAtCC,YAAY,GAAAlC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAEmC,WAAW,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAC3G,MAAMoC,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;UAAEO,KAAK,GAAG,CAAC;UAAEC,KAAK,GAAG1D,aAAa,CAACuD,oBAAoB,CAAC;UAAEI,IAAI,GAAG,WAAW;UAAEC,MAAM;UAAEC,UAAU;UAAEC,WAAW,GAAG;QAA0B,CAAC,GAAGX,eAAe;QAAvCY,mBAAmB,GAAAvC,wBAAA,CAAK2B,eAAe,EAAAa,UAAA;MACnK,IAAI;QAAEC,IAAI,GAAG3C,iBAAiB,CAAC2C,IAAI,IAAI,SAAS;QAAEpC;MAAS,CAAC,GAAGsB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMe,eAAe,GAAG,OAAOT,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMU,YAAY,GAAGZ,oBAAoB,CAACnC,MAAM;MAChD,MAAMgD,eAAe,GAAGnE,WAAW,CAAC0D,IAAI,CAAC,GACnCA,IAAI,GACJhC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAGgC,IAAI,CAAC;MACxB,IAAIQ,YAAY,IAAI,CAAC,IAAIC,eAAe,EAAE;QACtC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIC,aAAa,GAAG,GAAG;QACvB,IAAIF,YAAY,KAAK,CAAC,IAClBG,sBAAsB,CAACf,oBAAoB,CAAC,EAAE;UAC9C,MAAMgB,KAAK,GAAGhB,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/Dc,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAAC,aAAA,KAAQZ,mBAAmB,CAAE;QACnD,IAAIlC,QAAQ,KAAKR,SAAS,EAAE;UACxBqD,gBAAgB,CAAC7C,QAAQ,GAAGxB,qBAAqB,CAACwB,QAAQ,CAAC;QAC/D;QACA,MAAM+C,YAAY,GAAG1E,qBAAqB,CAACwE,gBAAgB,EAAEL,aAAa,EAAED,eAAe,CAAC;QAC5FH,IAAI,GAAGW,YAAY,CAACX,IAAI;QACxBpC,QAAQ,GAAG+C,YAAY,CAAC/C,QAAQ;MACpC;MACAA,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAKA,QAAQ,GAAGD,eAAe;MACvC,MAAMiD,SAAS,GAAGzC,WAAW,GAAG8B,eAAe;MAC/C;AACZ;AACA;MACY,IAAIR,KAAK,CAACtC,MAAM,KAAK,CAAC,IAAIsC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMoB,SAAS,GAAGpB,KAAK,CAACtC,MAAM,GAAGmC,oBAAoB,CAACnC,MAAM;MAC5D0D,SAAS,GAAG,CAAC,IAAI3E,UAAU,CAACuD,KAAK,EAAEoB,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYvB,oBAAoB,CAACnC,MAAM,KAAK,CAAC,IAC7BmC,oBAAoB,CAACwB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACY,IAAInB,MAAM,EAAE;QACRtD,SAAS,CAACsD,MAAM,GAAG7C,UAAU,EAAE,6CAA6C,CAAC;QAC7Ec,QAAQ,GAAGpB,uBAAuB,CAACoB,QAAQ,EAAE+B,MAAM,CAAC;QACpD,MAAMoB,iBAAiB,GAAG,CAAC,GAAGzB,oBAAoB,CAAC;QACnD,MAAM0B,aAAa,GAAG,CAAC,GAAGvB,KAAK,CAAC;QAChCO,IAAI,GAAGxB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,GAAG,CAAC,GAAGA,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC;QAC/C,MAAMiB,YAAY,GAAG,CAAC,GAAGjB,IAAI,CAAC;QAC9B,KAAK,IAAIkB,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAGvB,MAAM,EAAEuB,WAAW,EAAE,EAAE;UAC3D5B,oBAAoB,CAAC6B,IAAI,CAAC,GAAGJ,iBAAiB,CAAC;UAC/C,KAAK,IAAIK,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAGL,iBAAiB,CAAC5D,MAAM,EAAEiE,aAAa,EAAE,EAAE;YACnF3B,KAAK,CAAC0B,IAAI,CAACH,aAAa,CAACI,aAAa,CAAC,IAAIF,WAAW,GAAG,CAAC,CAAC,CAAC;YAC5DlB,IAAI,CAACmB,IAAI,CAACC,aAAa,KAAK,CAAC,GACvB,QAAQ,GACR9E,mBAAmB,CAAC2E,YAAY,EAAEG,aAAa,GAAG,CAAC,CAAC,CAAC;UAC/D;QACJ;QACAzE,cAAc,CAAC8C,KAAK,EAAEE,MAAM,CAAC;MACjC;MACA,MAAM0B,UAAU,GAAGT,SAAS,GAAGhD,QAAQ;MACvC;AACZ;AACA;MACYlB,YAAY,CAACyC,aAAa,EAAEG,oBAAoB,EAAEU,IAAI,EAAEP,KAAK,EAAEmB,SAAS,EAAES,UAAU,CAAC;MACrFtC,WAAW,GAAGwB,IAAI,CAACe,GAAG,CAACrB,eAAe,GAAGrC,QAAQ,EAAEmB,WAAW,CAAC;MAC/DX,aAAa,GAAGmC,IAAI,CAACe,GAAG,CAACD,UAAU,EAAEjD,aAAa,CAAC;IACvD,CAAC;IACD,IAAItC,aAAa,CAAC8C,OAAO,CAAC,EAAE;MACxB,MAAM2C,eAAe,GAAGC,kBAAkB,CAAC5C,OAAO,EAAEb,SAAS,CAAC;MAC9DiB,oBAAoB,CAACH,SAAS,EAAEC,UAAU,EAAE2C,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD,MAAMG,QAAQ,GAAGnF,eAAe,CAACqC,OAAO,EAAEC,SAAS,EAAEpB,KAAK,EAAEO,YAAY,CAAC;MACzE,MAAMqB,WAAW,GAAGqC,QAAQ,CAACvE,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAIwE,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGtC,WAAW,EAAEsC,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgB9C,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAM8C,WAAW,GAAGF,QAAQ,CAACC,YAAY,CAAC;QAC1C,MAAMJ,eAAe,GAAGC,kBAAkB,CAACI,WAAW,EAAE7D,SAAS,CAAC;QAClE,KAAK,MAAM8D,GAAG,IAAIhD,SAAS,EAAE;UACzBG,oBAAoB,CAACH,SAAS,CAACgD,GAAG,CAAC,EAAEC,kBAAkB,CAAChD,UAAU,EAAE+C,GAAG,CAAC,EAAEJ,gBAAgB,CAACI,GAAG,EAAEN,eAAe,CAAC,EAAEI,YAAY,EAAEtC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACAnB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIY,WAAW;EAC9B;EACA;AACJ;AACA;EACIhB,SAAS,CAACgE,OAAO,CAAC,CAACC,cAAc,EAAEC,OAAO,KAAK;IAC3C,KAAK,MAAMJ,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAM7C,aAAa,GAAG6C,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACY1C,aAAa,CAAC+C,IAAI,CAACtF,aAAa,CAAC;MACjC,MAAMiC,SAAS,GAAG,EAAE;MACpB,MAAMsD,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,aAAa,CAAChC,MAAM,EAAEkB,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEM,EAAE;UAAE0D,KAAK;UAAEC;QAAO,CAAC,GAAGnD,aAAa,CAACd,CAAC,CAAC;QAC9CQ,SAAS,CAACsC,IAAI,CAACkB,KAAK,CAAC;QACrBF,WAAW,CAAChB,IAAI,CAAChF,QAAQ,CAAC,CAAC,EAAEiC,aAAa,EAAEO,EAAE,CAAC,CAAC;QAChDyD,WAAW,CAACjB,IAAI,CAACmB,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACrB,OAAO,CAAC,CAAC,CAAC;QACtBjC,SAAS,CAACiC,OAAO,CAACjC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/BuD,WAAW,CAACtB,OAAO,CAACjE,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIsF,WAAW,CAACA,WAAW,CAAChF,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3CgF,WAAW,CAAChB,IAAI,CAAC,CAAC,CAAC;QACnBtC,SAAS,CAACsC,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAACtD,oBAAoB,CAAC0E,GAAG,CAACN,OAAO,CAAC,EAAE;QACpCpE,oBAAoB,CAACU,GAAG,CAAC0D,OAAO,EAAE;UAC9BpD,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAM0D,UAAU,GAAG3E,oBAAoB,CAAC4E,GAAG,CAACR,OAAO,CAAC;MACpDO,UAAU,CAAC3D,SAAS,CAACgD,GAAG,CAAC,GAAGhD,SAAS;MACrC2D,UAAU,CAAC1D,UAAU,CAAC+C,GAAG,CAAC,GAAAnB,aAAA,CAAAA,aAAA,KACnBrD,iBAAiB;QACpBO,QAAQ,EAAEQ,aAAa;QACvB4B,IAAI,EAAEoC,WAAW;QACjB3C,KAAK,EAAE0C;MAAW,GACf7E,kBAAkB,CACxB;IACL;EACJ,CAAC,CAAC;EACF,OAAOO,oBAAoB;AAC/B;AACA,SAAS2D,kBAAkBA,CAAC5C,OAAO,EAAEb,SAAS,EAAE;EAC5C,CAACA,SAAS,CAACwE,GAAG,CAAC3D,OAAO,CAAC,IAAIb,SAAS,CAACQ,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOb,SAAS,CAAC0E,GAAG,CAAC7D,OAAO,CAAC;AACjC;AACA,SAAS6C,gBAAgBA,CAAC/C,IAAI,EAAEX,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACW,IAAI,CAAC,EAChBX,SAAS,CAACW,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOX,SAAS,CAACW,IAAI,CAAC;AAC1B;AACA,SAASa,eAAeA,CAACV,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAASiD,kBAAkBA,CAAChD,UAAU,EAAE+C,GAAG,EAAE;EACzC,OAAO/C,UAAU,IAAIA,UAAU,CAAC+C,GAAG,CAAC,GAAAnB,aAAA,CAAAA,aAAA,KAEzB5B,UAAU,GACVA,UAAU,CAAC+C,GAAG,CAAC,IAAAnB,aAAA,KAEf5B,UAAU,CAAE;AAC3B;AACA,MAAM4D,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAMtC,sBAAsB,GAAIxB,SAAS,IAAKA,SAAS,CAAC+D,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAAS3F,4BAA4B,EAAE+E,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}