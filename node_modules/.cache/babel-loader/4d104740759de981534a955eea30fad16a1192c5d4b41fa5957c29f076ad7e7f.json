{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useRef, useMemo } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { DeprecatedLayoutGroupContext } from '../../context/DeprecatedLayoutGroupContext.mjs';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { nodeGroup } from '../../projection/node/group.mjs';\nconst shouldInheritGroup = inherit => inherit === true;\nconst shouldInheritId = inherit => shouldInheritGroup(inherit === true) || inherit === \"id\";\nconst LayoutGroup = _ref => {\n  let {\n    children,\n    id,\n    inherit = true\n  } = _ref;\n  const layoutGroupContext = useContext(LayoutGroupContext);\n  const deprecatedLayoutGroupContext = useContext(DeprecatedLayoutGroupContext);\n  const [forceRender, key] = useForceUpdate();\n  const context = useRef(null);\n  const upstreamId = layoutGroupContext.id || deprecatedLayoutGroupContext;\n  if (context.current === null) {\n    if (shouldInheritId(inherit) && upstreamId) {\n      id = id ? upstreamId + \"-\" + id : upstreamId;\n    }\n    context.current = {\n      id,\n      group: shouldInheritGroup(inherit) ? layoutGroupContext.group || nodeGroup() : nodeGroup()\n    };\n  }\n  const memoizedContext = useMemo(() => _objectSpread(_objectSpread({}, context.current), {}, {\n    forceRender\n  }), [key]);\n  return jsx(LayoutGroupContext.Provider, {\n    value: memoizedContext,\n    children: children\n  });\n};\nexport { LayoutGroup };", "map": {"version": 3, "names": ["_objectSpread", "jsx", "useContext", "useRef", "useMemo", "LayoutGroupContext", "DeprecatedLayoutGroupContext", "useForceUpdate", "nodeGroup", "shouldInheritGroup", "inherit", "shouldInheritId", "LayoutGroup", "_ref", "children", "id", "layoutGroupContext", "deprecatedLayoutGroupContext", "forceRender", "key", "context", "upstreamId", "current", "group", "memoizedContext", "Provider", "value"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/LayoutGroup/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { useContext, useRef, useMemo } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { DeprecatedLayoutGroupContext } from '../../context/DeprecatedLayoutGroupContext.mjs';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { nodeGroup } from '../../projection/node/group.mjs';\n\nconst shouldInheritGroup = (inherit) => inherit === true;\nconst shouldInheritId = (inherit) => shouldInheritGroup(inherit === true) || inherit === \"id\";\nconst LayoutGroup = ({ children, id, inherit = true }) => {\n    const layoutGroupContext = useContext(LayoutGroupContext);\n    const deprecatedLayoutGroupContext = useContext(DeprecatedLayoutGroupContext);\n    const [forceRender, key] = useForceUpdate();\n    const context = useRef(null);\n    const upstreamId = layoutGroupContext.id || deprecatedLayoutGroupContext;\n    if (context.current === null) {\n        if (shouldInheritId(inherit) && upstreamId) {\n            id = id ? upstreamId + \"-\" + id : upstreamId;\n        }\n        context.current = {\n            id,\n            group: shouldInheritGroup(inherit)\n                ? layoutGroupContext.group || nodeGroup()\n                : nodeGroup(),\n        };\n    }\n    const memoizedContext = useMemo(() => ({ ...context.current, forceRender }), [key]);\n    return (jsx(LayoutGroupContext.Provider, { value: memoizedContext, children: children }));\n};\n\nexport { LayoutGroup };\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AACb,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,QAAQ,OAAO;AACnD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,4BAA4B,QAAQ,gDAAgD;AAC7F,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,MAAMC,kBAAkB,GAAIC,OAAO,IAAKA,OAAO,KAAK,IAAI;AACxD,MAAMC,eAAe,GAAID,OAAO,IAAKD,kBAAkB,CAACC,OAAO,KAAK,IAAI,CAAC,IAAIA,OAAO,KAAK,IAAI;AAC7F,MAAME,WAAW,GAAGC,IAAA,IAAsC;EAAA,IAArC;IAAEC,QAAQ;IAAEC,EAAE;IAAEL,OAAO,GAAG;EAAK,CAAC,GAAAG,IAAA;EACjD,MAAMG,kBAAkB,GAAGd,UAAU,CAACG,kBAAkB,CAAC;EACzD,MAAMY,4BAA4B,GAAGf,UAAU,CAACI,4BAA4B,CAAC;EAC7E,MAAM,CAACY,WAAW,EAAEC,GAAG,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC3C,MAAMa,OAAO,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMkB,UAAU,GAAGL,kBAAkB,CAACD,EAAE,IAAIE,4BAA4B;EACxE,IAAIG,OAAO,CAACE,OAAO,KAAK,IAAI,EAAE;IAC1B,IAAIX,eAAe,CAACD,OAAO,CAAC,IAAIW,UAAU,EAAE;MACxCN,EAAE,GAAGA,EAAE,GAAGM,UAAU,GAAG,GAAG,GAAGN,EAAE,GAAGM,UAAU;IAChD;IACAD,OAAO,CAACE,OAAO,GAAG;MACdP,EAAE;MACFQ,KAAK,EAAEd,kBAAkB,CAACC,OAAO,CAAC,GAC5BM,kBAAkB,CAACO,KAAK,IAAIf,SAAS,CAAC,CAAC,GACvCA,SAAS,CAAC;IACpB,CAAC;EACL;EACA,MAAMgB,eAAe,GAAGpB,OAAO,CAAC,MAAAJ,aAAA,CAAAA,aAAA,KAAYoB,OAAO,CAACE,OAAO;IAAEJ;EAAW,EAAG,EAAE,CAACC,GAAG,CAAC,CAAC;EACnF,OAAQlB,GAAG,CAACI,kBAAkB,CAACoB,QAAQ,EAAE;IAAEC,KAAK,EAAEF,eAAe;IAAEV,QAAQ,EAAEA;EAAS,CAAC,CAAC;AAC5F,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}