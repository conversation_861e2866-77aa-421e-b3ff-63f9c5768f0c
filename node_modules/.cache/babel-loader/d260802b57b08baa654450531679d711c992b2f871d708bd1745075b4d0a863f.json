{"ast": null, "code": "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\nconst hsla = {\n  test: /*@__PURE__*/isColorString(\"hsl\", \"hue\"),\n  parse: /*@__PURE__*/splitColor(\"hue\", \"saturation\", \"lightness\"),\n  transform: _ref => {\n    let {\n      hue,\n      saturation,\n      lightness,\n      alpha: alpha$1 = 1\n    } = _ref;\n    return \"hsla(\" + Math.round(hue) + \", \" + percent.transform(sanitize(saturation)) + \", \" + percent.transform(sanitize(lightness)) + \", \" + sanitize(alpha.transform(alpha$1)) + \")\";\n  }\n};\nexport { hsla };", "map": {"version": 3, "names": ["alpha", "percent", "sanitize", "isColorString", "splitColor", "hsla", "test", "parse", "transform", "_ref", "hue", "saturation", "lightness", "alpha$1", "Math", "round"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/value/types/color/hsla.mjs"], "sourcesContent": ["import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: /*@__PURE__*/ isColorString(\"hsl\", \"hue\"),\n    parse: /*@__PURE__*/ splitColor(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            percent.transform(sanitize(saturation)) +\n            \", \" +\n            percent.transform(sanitize(lightness)) +\n            \", \" +\n            sanitize(alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\nexport { hsla };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,aAAa,EAAEC,UAAU,QAAQ,aAAa;AAEvD,MAAMC,IAAI,GAAG;EACTC,IAAI,EAAE,aAAcH,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC;EAC/CI,KAAK,EAAE,aAAcH,UAAU,CAAC,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC;EACjEI,SAAS,EAAEC,IAAA,IAAwD;IAAA,IAAvD;MAAEC,GAAG;MAAEC,UAAU;MAAEC,SAAS;MAAEZ,KAAK,EAAEa,OAAO,GAAG;IAAE,CAAC,GAAAJ,IAAA;IAC1D,OAAQ,OAAO,GACXK,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC,GACf,IAAI,GACJT,OAAO,CAACO,SAAS,CAACN,QAAQ,CAACS,UAAU,CAAC,CAAC,GACvC,IAAI,GACJV,OAAO,CAACO,SAAS,CAACN,QAAQ,CAACU,SAAS,CAAC,CAAC,GACtC,IAAI,GACJV,QAAQ,CAACF,KAAK,CAACQ,SAAS,CAACK,OAAO,CAAC,CAAC,GAClC,GAAG;EACX;AACJ,CAAC;AAED,SAASR,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}