{"ast": null, "code": "// Notification.js\nimport{FaCheckCircle,FaExclamationCircle}from'react-icons/fa';import styles from'./Notification.module.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Notification=_ref=>{let{message,type}=_ref;if(!message)return null;const classNames=\"\".concat(styles.notification,\" \").concat(styles[type]||'');const getIcon=()=>{switch(type){case'success':return/*#__PURE__*/_jsx(FaCheckCircle,{className:styles.icon});case'error':return/*#__PURE__*/_jsx(FaExclamationCircle,{className:styles.icon});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{className:classNames,children:[getIcon(),/*#__PURE__*/_jsx(\"span\",{children:message})]});};export default Notification;", "map": {"version": 3, "names": ["FaCheckCircle", "FaExclamationCircle", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Notification", "_ref", "message", "type", "classNames", "concat", "notification", "getIcon", "className", "icon", "children"], "sources": ["/var/www/html/gwm.tj/src/components/Notification/Notification.jsx"], "sourcesContent": ["// Notification.js\nimport { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';\nimport styles from './Notification.module.css';\n\nconst Notification = ({ message, type }) => {\n  if (!message) return null;\n\n  const classNames = `${styles.notification} ${styles[type] || ''}`;\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <FaCheckCircle className={styles.icon} />;\n      case 'error':\n        return <FaExclamationCircle className={styles.icon} />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className={classNames}>\n      {getIcon()}\n      <span>{message}</span>\n    </div>\n  );\n};\n\nexport default Notification;\n"], "mappings": "AAAA;AACA,OAASA,aAAa,CAAEC,mBAAmB,KAAQ,gBAAgB,CACnE,MAAO,CAAAC,MAAM,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAuB,IAAtB,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAF,IAAA,CACrC,GAAI,CAACC,OAAO,CAAE,MAAO,KAAI,CAEzB,KAAM,CAAAE,UAAU,IAAAC,MAAA,CAAMV,MAAM,CAACW,YAAY,MAAAD,MAAA,CAAIV,MAAM,CAACQ,IAAI,CAAC,EAAI,EAAE,CAAE,CAEjE,KAAM,CAAAI,OAAO,CAAGA,CAAA,GAAM,CACpB,OAAQJ,IAAI,EACV,IAAK,SAAS,CACZ,mBAAON,IAAA,CAACJ,aAAa,EAACe,SAAS,CAAEb,MAAM,CAACc,IAAK,CAAE,CAAC,CAClD,IAAK,OAAO,CACV,mBAAOZ,IAAA,CAACH,mBAAmB,EAACc,SAAS,CAAEb,MAAM,CAACc,IAAK,CAAE,CAAC,CACxD,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACEV,KAAA,QAAKS,SAAS,CAAEJ,UAAW,CAAAM,QAAA,EACxBH,OAAO,CAAC,CAAC,cACVV,IAAA,SAAAa,QAAA,CAAOR,OAAO,CAAO,CAAC,EACnB,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}