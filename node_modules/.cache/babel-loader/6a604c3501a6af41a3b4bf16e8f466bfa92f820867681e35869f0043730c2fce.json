{"ast": null, "code": "function getValueState(visualElement) {\n  const state = [{}, {}];\n  visualElement === null || visualElement === void 0 || visualElement.values.forEach((value, key) => {\n    state[0][key] = value.get();\n    state[1][key] = value.getVelocity();\n  });\n  return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n  /**\n   * If the variant definition is a function, resolve.\n   */\n  if (typeof definition === \"function\") {\n    const [current, velocity] = getValueState(visualElement);\n    definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n  }\n  /**\n   * If the variant definition is a variant label, or\n   * the function returned a variant label, resolve.\n   */\n  if (typeof definition === \"string\") {\n    definition = props.variants && props.variants[definition];\n  }\n  /**\n   * At this point we've resolved both functions and variant labels,\n   * but the resolved variant label might itself have been a function.\n   * If so, resolve. This can only have returned a valid target object.\n   */\n  if (typeof definition === \"function\") {\n    const [current, velocity] = getValueState(visualElement);\n    definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n  }\n  return definition;\n}\nexport { resolveVariantFromProps };", "map": {"version": 3, "names": ["getValueState", "visualElement", "state", "values", "for<PERSON>ach", "value", "key", "get", "getVelocity", "resolveVariantFromProps", "props", "definition", "custom", "current", "velocity", "undefined", "variants"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs"], "sourcesContent": ["function getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,aAAa,EAAE;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtBD,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEE,MAAM,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IAC1CJ,KAAK,CAAC,CAAC,CAAC,CAACI,GAAG,CAAC,GAAGD,KAAK,CAACE,GAAG,CAAC,CAAC;IAC3BL,KAAK,CAAC,CAAC,CAAC,CAACI,GAAG,CAAC,GAAGD,KAAK,CAACG,WAAW,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,OAAON,KAAK;AAChB;AACA,SAASO,uBAAuBA,CAACC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAAEX,aAAa,EAAE;EACvE;AACJ;AACA;EACI,IAAI,OAAOU,UAAU,KAAK,UAAU,EAAE;IAClC,MAAM,CAACE,OAAO,EAAEC,QAAQ,CAAC,GAAGd,aAAa,CAACC,aAAa,CAAC;IACxDU,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC5F;EACA;AACJ;AACA;AACA;EACI,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;IAChCA,UAAU,GAAGD,KAAK,CAACM,QAAQ,IAAIN,KAAK,CAACM,QAAQ,CAACL,UAAU,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IAClC,MAAM,CAACE,OAAO,EAAEC,QAAQ,CAAC,GAAGd,aAAa,CAACC,aAAa,CAAC;IACxDU,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKG,SAAS,GAAGH,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC5F;EACA,OAAOH,UAAU;AACrB;AAEA,SAASF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}