{"ast": null, "code": "import { positionalKeys } from '../../render/utils/keys-position.mjs';\nimport { findDimensionValueType } from '../../value/types/dimensions.mjs';\nimport { getVariableValue } from '../utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from '../utils/is-css-variable.mjs';\nimport { KeyframeResolver } from './KeyframesResolver.mjs';\nimport { isNone } from './utils/is-none.mjs';\nimport { makeNoneKeyframesAnimatable } from './utils/make-none-animatable.mjs';\nimport { isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\nclass DOMKeyframesResolver extends KeyframeResolver {\n  constructor(unresolvedKeyframes, onComplete, name, motionValue, element) {\n    super(unresolvedKeyframes, onComplete, name, motionValue, element, true);\n  }\n  readKeyframes() {\n    const {\n      unresolvedKeyframes,\n      element,\n      name\n    } = this;\n    if (!element || !element.current) return;\n    super.readKeyframes();\n    /**\n     * If any keyframe is a CSS variable, we need to find its value by sampling the element\n     */\n    for (let i = 0; i < unresolvedKeyframes.length; i++) {\n      let keyframe = unresolvedKeyframes[i];\n      if (typeof keyframe === \"string\") {\n        keyframe = keyframe.trim();\n        if (isCSSVariableToken(keyframe)) {\n          const resolved = getVariableValue(keyframe, element.current);\n          if (resolved !== undefined) {\n            unresolvedKeyframes[i] = resolved;\n          }\n          if (i === unresolvedKeyframes.length - 1) {\n            this.finalKeyframe = keyframe;\n          }\n        }\n      }\n    }\n    /**\n     * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n     * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n     * have a far bigger performance impact.\n     */\n    this.resolveNoneKeyframes();\n    /**\n     * Check to see if unit type has changed. If so schedule jobs that will\n     * temporarily set styles to the destination keyframes.\n     * Skip if we have more than two keyframes or this isn't a positional value.\n     * TODO: We can throw if there are multiple keyframes and the value type changes.\n     */\n    if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n      return;\n    }\n    const [origin, target] = unresolvedKeyframes;\n    const originType = findDimensionValueType(origin);\n    const targetType = findDimensionValueType(target);\n    /**\n     * Either we don't recognise these value types or we can animate between them.\n     */\n    if (originType === targetType) return;\n    /**\n     * If both values are numbers or pixels, we can animate between them by\n     * converting them to numbers.\n     */\n    if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n      for (let i = 0; i < unresolvedKeyframes.length; i++) {\n        const value = unresolvedKeyframes[i];\n        if (typeof value === \"string\") {\n          unresolvedKeyframes[i] = parseFloat(value);\n        }\n      }\n    } else if (positionalValues[name]) {\n      /**\n       * Else, the only way to resolve this is by measuring the element.\n       */\n      this.needsMeasurement = true;\n    }\n  }\n  resolveNoneKeyframes() {\n    const {\n      unresolvedKeyframes,\n      name\n    } = this;\n    const noneKeyframeIndexes = [];\n    for (let i = 0; i < unresolvedKeyframes.length; i++) {\n      if (unresolvedKeyframes[i] === null || isNone(unresolvedKeyframes[i])) {\n        noneKeyframeIndexes.push(i);\n      }\n    }\n    if (noneKeyframeIndexes.length) {\n      makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n    }\n  }\n  measureInitialState() {\n    const {\n      element,\n      unresolvedKeyframes,\n      name\n    } = this;\n    if (!element || !element.current) return;\n    if (name === \"height\") {\n      this.suspendedScrollY = window.pageYOffset;\n    }\n    this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n    unresolvedKeyframes[0] = this.measuredOrigin;\n    // Set final key frame to measure after next render\n    const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n    if (measureKeyframe !== undefined) {\n      element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n    }\n  }\n  measureEndState() {\n    var _this$removedTransfor;\n    const {\n      element,\n      name,\n      unresolvedKeyframes\n    } = this;\n    if (!element || !element.current) return;\n    const value = element.getValue(name);\n    value && value.jump(this.measuredOrigin, false);\n    const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n    const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n    unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n    if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n      this.finalKeyframe = finalKeyframe;\n    }\n    // If we removed transform values, reapply them before the next render\n    if ((_this$removedTransfor = this.removedTransforms) !== null && _this$removedTransfor !== void 0 && _this$removedTransfor.length) {\n      this.removedTransforms.forEach(_ref => {\n        let [unsetTransformName, unsetTransformValue] = _ref;\n        element.getValue(unsetTransformName).set(unsetTransformValue);\n      });\n    }\n    this.resolveNoneKeyframes();\n  }\n}\nexport { DOMKeyframesResolver };", "map": {"version": 3, "names": ["positional<PERSON>eys", "findDimensionValueType", "getVariableValue", "isCSSVariableToken", "KeyframeResolver", "isNone", "makeNoneKeyframesAnimatable", "isNumOrPxType", "positionalV<PERSON>ues", "DOMKeyframesResolver", "constructor", "unresolvedKeyframes", "onComplete", "name", "motionValue", "element", "readKeyframes", "current", "i", "length", "keyframe", "trim", "resolved", "undefined", "finalKeyframe", "resolveNoneKeyframes", "has", "origin", "target", "originType", "targetType", "value", "parseFloat", "needsMeasurement", "noneKeyframeIndexes", "push", "measureInitialState", "suspendedScrollY", "window", "pageYOffset", "<PERSON><PERSON><PERSON><PERSON>", "measureViewportBox", "getComputedStyle", "measureKeyframe", "getValue", "jump", "measureEndState", "_this$removedTransfor", "finalKeyframeIndex", "removedTransforms", "for<PERSON>ach", "_ref", "unsetTransformName", "unsetTransformValue", "set"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs"], "sourcesContent": ["import { positionalKeys } from '../../render/utils/keys-position.mjs';\nimport { findDimensionValueType } from '../../value/types/dimensions.mjs';\nimport { getVariableValue } from '../utils/css-variables-conversion.mjs';\nimport { isCSSVariableToken } from '../utils/is-css-variable.mjs';\nimport { KeyframeResolver } from './KeyframesResolver.mjs';\nimport { isNone } from './utils/is-none.mjs';\nimport { makeNoneKeyframesAnimatable } from './utils/make-none-animatable.mjs';\nimport { isNumOrPxType, positionalValues } from './utils/unit-conversion.mjs';\n\nclass DOMKeyframesResolver extends KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element) {\n        super(unresolvedKeyframes, onComplete, name, motionValue, element, true);\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, element, name } = this;\n        if (!element || !element.current)\n            return;\n        super.readKeyframes();\n        /**\n         * If any keyframe is a CSS variable, we need to find its value by sampling the element\n         */\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            let keyframe = unresolvedKeyframes[i];\n            if (typeof keyframe === \"string\") {\n                keyframe = keyframe.trim();\n                if (isCSSVariableToken(keyframe)) {\n                    const resolved = getVariableValue(keyframe, element.current);\n                    if (resolved !== undefined) {\n                        unresolvedKeyframes[i] = resolved;\n                    }\n                    if (i === unresolvedKeyframes.length - 1) {\n                        this.finalKeyframe = keyframe;\n                    }\n                }\n            }\n        }\n        /**\n         * Resolve \"none\" values. We do this potentially twice - once before and once after measuring keyframes.\n         * This could be seen as inefficient but it's a trade-off to avoid measurements in more situations, which\n         * have a far bigger performance impact.\n         */\n        this.resolveNoneKeyframes();\n        /**\n         * Check to see if unit type has changed. If so schedule jobs that will\n         * temporarily set styles to the destination keyframes.\n         * Skip if we have more than two keyframes or this isn't a positional value.\n         * TODO: We can throw if there are multiple keyframes and the value type changes.\n         */\n        if (!positionalKeys.has(name) || unresolvedKeyframes.length !== 2) {\n            return;\n        }\n        const [origin, target] = unresolvedKeyframes;\n        const originType = findDimensionValueType(origin);\n        const targetType = findDimensionValueType(target);\n        /**\n         * Either we don't recognise these value types or we can animate between them.\n         */\n        if (originType === targetType)\n            return;\n        /**\n         * If both values are numbers or pixels, we can animate between them by\n         * converting them to numbers.\n         */\n        if (isNumOrPxType(originType) && isNumOrPxType(targetType)) {\n            for (let i = 0; i < unresolvedKeyframes.length; i++) {\n                const value = unresolvedKeyframes[i];\n                if (typeof value === \"string\") {\n                    unresolvedKeyframes[i] = parseFloat(value);\n                }\n            }\n        }\n        else if (positionalValues[name]) {\n            /**\n             * Else, the only way to resolve this is by measuring the element.\n             */\n            this.needsMeasurement = true;\n        }\n    }\n    resolveNoneKeyframes() {\n        const { unresolvedKeyframes, name } = this;\n        const noneKeyframeIndexes = [];\n        for (let i = 0; i < unresolvedKeyframes.length; i++) {\n            if (unresolvedKeyframes[i] === null ||\n                isNone(unresolvedKeyframes[i])) {\n                noneKeyframeIndexes.push(i);\n            }\n        }\n        if (noneKeyframeIndexes.length) {\n            makeNoneKeyframesAnimatable(unresolvedKeyframes, noneKeyframeIndexes, name);\n        }\n    }\n    measureInitialState() {\n        const { element, unresolvedKeyframes, name } = this;\n        if (!element || !element.current)\n            return;\n        if (name === \"height\") {\n            this.suspendedScrollY = window.pageYOffset;\n        }\n        this.measuredOrigin = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        unresolvedKeyframes[0] = this.measuredOrigin;\n        // Set final key frame to measure after next render\n        const measureKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n        if (measureKeyframe !== undefined) {\n            element.getValue(name, measureKeyframe).jump(measureKeyframe, false);\n        }\n    }\n    measureEndState() {\n        const { element, name, unresolvedKeyframes } = this;\n        if (!element || !element.current)\n            return;\n        const value = element.getValue(name);\n        value && value.jump(this.measuredOrigin, false);\n        const finalKeyframeIndex = unresolvedKeyframes.length - 1;\n        const finalKeyframe = unresolvedKeyframes[finalKeyframeIndex];\n        unresolvedKeyframes[finalKeyframeIndex] = positionalValues[name](element.measureViewportBox(), window.getComputedStyle(element.current));\n        if (finalKeyframe !== null && this.finalKeyframe === undefined) {\n            this.finalKeyframe = finalKeyframe;\n        }\n        // If we removed transform values, reapply them before the next render\n        if (this.removedTransforms?.length) {\n            this.removedTransforms.forEach(([unsetTransformName, unsetTransformValue]) => {\n                element\n                    .getValue(unsetTransformName)\n                    .set(unsetTransformValue);\n            });\n        }\n        this.resolveNoneKeyframes();\n    }\n}\n\nexport { DOMKeyframesResolver };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sCAAsC;AACrE,SAASC,sBAAsB,QAAQ,kCAAkC;AACzE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,6BAA6B;AAE7E,MAAMC,oBAAoB,SAASL,gBAAgB,CAAC;EAChDM,WAAWA,CAACC,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACrE,KAAK,CAACJ,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,OAAO,EAAE,IAAI,CAAC;EAC5E;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEL,mBAAmB;MAAEI,OAAO;MAAEF;IAAK,CAAC,GAAG,IAAI;IACnD,IAAI,CAACE,OAAO,IAAI,CAACA,OAAO,CAACE,OAAO,EAC5B;IACJ,KAAK,CAACD,aAAa,CAAC,CAAC;IACrB;AACR;AACA;IACQ,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,mBAAmB,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAIE,QAAQ,GAAGT,mBAAmB,CAACO,CAAC,CAAC;MACrC,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;QAC9BA,QAAQ,GAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC;QAC1B,IAAIlB,kBAAkB,CAACiB,QAAQ,CAAC,EAAE;UAC9B,MAAME,QAAQ,GAAGpB,gBAAgB,CAACkB,QAAQ,EAAEL,OAAO,CAACE,OAAO,CAAC;UAC5D,IAAIK,QAAQ,KAAKC,SAAS,EAAE;YACxBZ,mBAAmB,CAACO,CAAC,CAAC,GAAGI,QAAQ;UACrC;UACA,IAAIJ,CAAC,KAAKP,mBAAmB,CAACQ,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAACK,aAAa,GAAGJ,QAAQ;UACjC;QACJ;MACJ;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACK,oBAAoB,CAAC,CAAC;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACzB,cAAc,CAAC0B,GAAG,CAACb,IAAI,CAAC,IAAIF,mBAAmB,CAACQ,MAAM,KAAK,CAAC,EAAE;MAC/D;IACJ;IACA,MAAM,CAACQ,MAAM,EAAEC,MAAM,CAAC,GAAGjB,mBAAmB;IAC5C,MAAMkB,UAAU,GAAG5B,sBAAsB,CAAC0B,MAAM,CAAC;IACjD,MAAMG,UAAU,GAAG7B,sBAAsB,CAAC2B,MAAM,CAAC;IACjD;AACR;AACA;IACQ,IAAIC,UAAU,KAAKC,UAAU,EACzB;IACJ;AACR;AACA;AACA;IACQ,IAAIvB,aAAa,CAACsB,UAAU,CAAC,IAAItB,aAAa,CAACuB,UAAU,CAAC,EAAE;MACxD,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,mBAAmB,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;QACjD,MAAMa,KAAK,GAAGpB,mBAAmB,CAACO,CAAC,CAAC;QACpC,IAAI,OAAOa,KAAK,KAAK,QAAQ,EAAE;UAC3BpB,mBAAmB,CAACO,CAAC,CAAC,GAAGc,UAAU,CAACD,KAAK,CAAC;QAC9C;MACJ;IACJ,CAAC,MACI,IAAIvB,gBAAgB,CAACK,IAAI,CAAC,EAAE;MAC7B;AACZ;AACA;MACY,IAAI,CAACoB,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACAR,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAEd,mBAAmB;MAAEE;IAAK,CAAC,GAAG,IAAI;IAC1C,MAAMqB,mBAAmB,GAAG,EAAE;IAC9B,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,mBAAmB,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,IAAIP,mBAAmB,CAACO,CAAC,CAAC,KAAK,IAAI,IAC/Bb,MAAM,CAACM,mBAAmB,CAACO,CAAC,CAAC,CAAC,EAAE;QAChCgB,mBAAmB,CAACC,IAAI,CAACjB,CAAC,CAAC;MAC/B;IACJ;IACA,IAAIgB,mBAAmB,CAACf,MAAM,EAAE;MAC5Bb,2BAA2B,CAACK,mBAAmB,EAAEuB,mBAAmB,EAAErB,IAAI,CAAC;IAC/E;EACJ;EACAuB,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAErB,OAAO;MAAEJ,mBAAmB;MAAEE;IAAK,CAAC,GAAG,IAAI;IACnD,IAAI,CAACE,OAAO,IAAI,CAACA,OAAO,CAACE,OAAO,EAC5B;IACJ,IAAIJ,IAAI,KAAK,QAAQ,EAAE;MACnB,IAAI,CAACwB,gBAAgB,GAAGC,MAAM,CAACC,WAAW;IAC9C;IACA,IAAI,CAACC,cAAc,GAAGhC,gBAAgB,CAACK,IAAI,CAAC,CAACE,OAAO,CAAC0B,kBAAkB,CAAC,CAAC,EAAEH,MAAM,CAACI,gBAAgB,CAAC3B,OAAO,CAACE,OAAO,CAAC,CAAC;IACpHN,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC6B,cAAc;IAC5C;IACA,MAAMG,eAAe,GAAGhC,mBAAmB,CAACA,mBAAmB,CAACQ,MAAM,GAAG,CAAC,CAAC;IAC3E,IAAIwB,eAAe,KAAKpB,SAAS,EAAE;MAC/BR,OAAO,CAAC6B,QAAQ,CAAC/B,IAAI,EAAE8B,eAAe,CAAC,CAACE,IAAI,CAACF,eAAe,EAAE,KAAK,CAAC;IACxE;EACJ;EACAG,eAAeA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IACd,MAAM;MAAEhC,OAAO;MAAEF,IAAI;MAAEF;IAAoB,CAAC,GAAG,IAAI;IACnD,IAAI,CAACI,OAAO,IAAI,CAACA,OAAO,CAACE,OAAO,EAC5B;IACJ,MAAMc,KAAK,GAAGhB,OAAO,CAAC6B,QAAQ,CAAC/B,IAAI,CAAC;IACpCkB,KAAK,IAAIA,KAAK,CAACc,IAAI,CAAC,IAAI,CAACL,cAAc,EAAE,KAAK,CAAC;IAC/C,MAAMQ,kBAAkB,GAAGrC,mBAAmB,CAACQ,MAAM,GAAG,CAAC;IACzD,MAAMK,aAAa,GAAGb,mBAAmB,CAACqC,kBAAkB,CAAC;IAC7DrC,mBAAmB,CAACqC,kBAAkB,CAAC,GAAGxC,gBAAgB,CAACK,IAAI,CAAC,CAACE,OAAO,CAAC0B,kBAAkB,CAAC,CAAC,EAAEH,MAAM,CAACI,gBAAgB,CAAC3B,OAAO,CAACE,OAAO,CAAC,CAAC;IACxI,IAAIO,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAKD,SAAS,EAAE;MAC5D,IAAI,CAACC,aAAa,GAAGA,aAAa;IACtC;IACA;IACA,KAAAuB,qBAAA,GAAI,IAAI,CAACE,iBAAiB,cAAAF,qBAAA,eAAtBA,qBAAA,CAAwB5B,MAAM,EAAE;MAChC,IAAI,CAAC8B,iBAAiB,CAACC,OAAO,CAACC,IAAA,IAA+C;QAAA,IAA9C,CAACC,kBAAkB,EAAEC,mBAAmB,CAAC,GAAAF,IAAA;QACrEpC,OAAO,CACF6B,QAAQ,CAACQ,kBAAkB,CAAC,CAC5BE,GAAG,CAACD,mBAAmB,CAAC;MACjC,CAAC,CAAC;IACN;IACA,IAAI,CAAC5B,oBAAoB,CAAC,CAAC;EAC/B;AACJ;AAEA,SAAShB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}