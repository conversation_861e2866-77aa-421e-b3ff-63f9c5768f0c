{"ast": null, "code": "/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\"\n// TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n// or until we implement support for linear() easing.\n// \"background-color\"\n]);\nexport { acceleratedValues };", "map": {"version": 3, "names": ["acceleratedValues", "Set"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/waapi/utils/accelerated-values.mjs"], "sourcesContent": ["/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n    // or until we implement support for linear() easing.\n    // \"background-color\"\n]);\n\nexport { acceleratedValues };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,SAAS,EACT,UAAU,EACV,QAAQ,EACR;AACA;AACA;AACA;AAAA,CACH,CAAC;AAEF,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}