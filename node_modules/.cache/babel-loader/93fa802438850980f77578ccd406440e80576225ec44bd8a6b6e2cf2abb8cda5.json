{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/components/Notification/Notification.jsx\";\n// Notification.js\nimport { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';\nimport styles from './Notification.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Notification = ({\n  message,\n  type\n}) => {\n  if (!message) return null;\n  const classNames = `${styles.notification} ${styles[type] || ''}`;\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: styles.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(FaExclamationCircle, {\n          className: styles.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: classNames,\n    children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = Notification;\nexport default Notification;\nvar _c;\n$RefreshReg$(_c, \"Notification\");", "map": {"version": 3, "names": ["FaCheckCircle", "FaExclamationCircle", "styles", "jsxDEV", "_jsxDEV", "Notification", "message", "type", "classNames", "notification", "getIcon", "className", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/components/Notification/Notification.jsx"], "sourcesContent": ["// Notification.js\nimport { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';\nimport styles from './Notification.module.css';\n\nconst Notification = ({ message, type }) => {\n  if (!message) return null;\n\n  const classNames = `${styles.notification} ${styles[type] || ''}`;\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <FaCheckCircle className={styles.icon} />;\n      case 'error':\n        return <FaExclamationCircle className={styles.icon} />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className={classNames}>\n      {getIcon()}\n      <span>{message}</span>\n    </div>\n  );\n};\n\nexport default Notification;\n"], "mappings": ";AAAA;AACA,SAASA,aAAa,EAAEC,mBAAmB,QAAQ,gBAAgB;AACnE,OAAOC,MAAM,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAK,CAAC,KAAK;EAC1C,IAAI,CAACD,OAAO,EAAE,OAAO,IAAI;EAEzB,MAAME,UAAU,GAAG,GAAGN,MAAM,CAACO,YAAY,IAAIP,MAAM,CAACK,IAAI,CAAC,IAAI,EAAE,EAAE;EAEjE,MAAMG,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQH,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOH,OAAA,CAACJ,aAAa;UAACW,SAAS,EAAET,MAAM,CAACU;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,OAAO;QACV,oBAAOZ,OAAA,CAACH,mBAAmB;UAACU,SAAS,EAAET,MAAM,CAACU;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKO,SAAS,EAAEH,UAAW;IAAAS,QAAA,GACxBP,OAAO,CAAC,CAAC,eACVN,OAAA;MAAAa,QAAA,EAAOX;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV,CAAC;AAACE,EAAA,GAtBIb,YAAY;AAwBlB,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}