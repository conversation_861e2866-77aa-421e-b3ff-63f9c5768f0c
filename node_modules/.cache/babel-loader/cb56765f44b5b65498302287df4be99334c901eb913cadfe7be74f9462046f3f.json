{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"autoplay\", \"delay\", \"type\", \"repeat\", \"repeatDelay\", \"repeatType\", \"keyframes\", \"name\", \"motionValue\", \"element\"];\nimport { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n  constructor(_ref) {\n    var _this$keyframeResolve2;\n    let {\n        autoplay = true,\n        delay = 0,\n        type = \"keyframes\",\n        repeat = 0,\n        repeatDelay = 0,\n        repeatType = \"loop\",\n        keyframes,\n        name,\n        motionValue,\n        element\n      } = _ref,\n      options = _objectWithoutProperties(_ref, _excluded);\n    super();\n    /**\n     * Bound to support return animation.stop pattern\n     */\n    this.stop = () => {\n      var _this$keyframeResolve;\n      if (this._animation) {\n        var _this$stopTimeline;\n        this._animation.stop();\n        (_this$stopTimeline = this.stopTimeline) === null || _this$stopTimeline === void 0 || _this$stopTimeline.call(this);\n      }\n      (_this$keyframeResolve = this.keyframeResolver) === null || _this$keyframeResolve === void 0 || _this$keyframeResolve.cancel();\n    };\n    this.createdAt = time.now();\n    const optionsWithDefaults = _objectSpread({\n      autoplay,\n      delay,\n      type,\n      repeat,\n      repeatDelay,\n      repeatType,\n      name,\n      motionValue,\n      element\n    }, options);\n    const KeyframeResolver$1 = (element === null || element === void 0 ? void 0 : element.KeyframeResolver) || KeyframeResolver;\n    this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n    (_this$keyframeResolve2 = this.keyframeResolver) === null || _this$keyframeResolve2 === void 0 || _this$keyframeResolve2.scheduleResolve();\n  }\n  onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n    this.keyframeResolver = undefined;\n    const {\n      name,\n      type,\n      velocity,\n      delay,\n      isHandoff,\n      onUpdate\n    } = options;\n    this.resolvedAt = time.now();\n    /**\n     * If we can't animate this value with the resolved keyframes\n     * then we should complete it immediately.\n     */\n    if (!canAnimate(keyframes, name, type, velocity)) {\n      if (MotionGlobalConfig.instantAnimations || !delay) {\n        onUpdate === null || onUpdate === void 0 || onUpdate(getFinalKeyframe(keyframes, options, finalKeyframe));\n      }\n      keyframes[0] = keyframes[keyframes.length - 1];\n      options.duration = 0;\n      options.repeat = 0;\n    }\n    /**\n     * Resolve startTime for the animation.\n     *\n     * This method uses the createdAt and resolvedAt to calculate the\n     * animation startTime. *Ideally*, we would use the createdAt time as t=0\n     * as the following frame would then be the first frame of the animation in\n     * progress, which would feel snappier.\n     *\n     * However, if there's a delay (main thread work) between the creation of\n     * the animation and the first commited frame, we prefer to use resolvedAt\n     * to avoid a sudden jump into the animation.\n     */\n    const startTime = sync ? !this.resolvedAt ? this.createdAt : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY ? this.resolvedAt : this.createdAt : undefined;\n    const resolvedOptions = _objectSpread(_objectSpread({\n      startTime,\n      finalKeyframe\n    }, options), {}, {\n      keyframes\n    });\n    /**\n     * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions) ? new NativeAnimationExtended(_objectSpread(_objectSpread({}, resolvedOptions), {}, {\n      element: resolvedOptions.motionValue.owner.current\n    })) : new JSAnimation(resolvedOptions);\n    animation.finished.then(() => this.notifyFinished()).catch(noop);\n    if (this.pendingTimeline) {\n      this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n      this.pendingTimeline = undefined;\n    }\n    this._animation = animation;\n  }\n  get finished() {\n    if (!this._animation) {\n      return this._finished;\n    } else {\n      return this.animation.finished;\n    }\n  }\n  then(onResolve, _onReject) {\n    return this.finished.finally(onResolve).then(() => {});\n  }\n  get animation() {\n    if (!this._animation) {\n      var _this$keyframeResolve3;\n      (_this$keyframeResolve3 = this.keyframeResolver) === null || _this$keyframeResolve3 === void 0 || _this$keyframeResolve3.resume();\n      flushKeyframeResolvers();\n    }\n    return this._animation;\n  }\n  get duration() {\n    return this.animation.duration;\n  }\n  get time() {\n    return this.animation.time;\n  }\n  set time(newTime) {\n    this.animation.time = newTime;\n  }\n  get speed() {\n    return this.animation.speed;\n  }\n  get state() {\n    return this.animation.state;\n  }\n  set speed(newSpeed) {\n    this.animation.speed = newSpeed;\n  }\n  get startTime() {\n    return this.animation.startTime;\n  }\n  attachTimeline(timeline) {\n    if (this._animation) {\n      this.stopTimeline = this.animation.attachTimeline(timeline);\n    } else {\n      this.pendingTimeline = timeline;\n    }\n    return () => this.stop();\n  }\n  play() {\n    this.animation.play();\n  }\n  pause() {\n    this.animation.pause();\n  }\n  complete() {\n    this.animation.complete();\n  }\n  cancel() {\n    var _this$keyframeResolve4;\n    if (this._animation) {\n      this.animation.cancel();\n    }\n    (_this$keyframeResolve4 = this.keyframeResolver) === null || _this$keyframeResolve4 === void 0 || _this$keyframeResolve4.cancel();\n  }\n}\nexport { AsyncMotionValueAnimation };", "map": {"version": 3, "names": ["MotionGlobalConfig", "noop", "time", "JSAnimation", "getFinalKeyframe", "KeyframeResolver", "flushKeyframeResolvers", "NativeAnimationExtended", "canAnimate", "WithPromise", "supportsBrowserAnimation", "MAX_RESOLVE_DELAY", "AsyncMotionValueAnimation", "constructor", "_ref", "_this$keyframeResolve2", "autoplay", "delay", "type", "repeat", "repeatDelay", "repeatType", "keyframes", "name", "motionValue", "element", "options", "_objectWithoutProperties", "_excluded", "stop", "_this$keyframeResolve", "_animation", "_this$stopTimeline", "stopTimeline", "call", "keyframeResolver", "cancel", "createdAt", "now", "optionsWithDefaults", "_objectSpread", "KeyframeResolver$1", "resolvedKeyframes", "finalKeyframe", "forced", "onKeyframesResolved", "scheduleResolve", "sync", "undefined", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "onUpdate", "resolvedAt", "instantAnimations", "length", "duration", "startTime", "resolvedOptions", "animation", "owner", "current", "finished", "then", "notifyFinished", "catch", "pendingTimeline", "attachTimeline", "_finished", "onResolve", "_onReject", "finally", "_this$keyframeResolve3", "resume", "newTime", "speed", "state", "newSpeed", "timeline", "play", "pause", "complete", "_this$keyframeResolve4"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs"], "sourcesContent": ["import { MotionGlobalConfig, noop } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { JSAnimation } from './JSAnimation.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { KeyframeResolver, flushKeyframeResolvers } from './keyframes/KeyframesResolver.mjs';\nimport { NativeAnimationExtended } from './NativeAnimationExtended.mjs';\nimport { canAnimate } from './utils/can-animate.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nimport { supportsBrowserAnimation } from './waapi/supports/waapi.mjs';\n\n/**\n * Maximum time allowed between an animation being created and it being\n * resolved for us to use the latter as the start time.\n *\n * This is to ensure that while we prefer to \"start\" an animation as soon\n * as it's triggered, we also want to avoid a visual jump if there's a big delay\n * between these two moments.\n */\nconst MAX_RESOLVE_DELAY = 40;\nclass AsyncMotionValueAnimation extends WithPromise {\n    constructor({ autoplay = true, delay = 0, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", keyframes, name, motionValue, element, ...options }) {\n        super();\n        /**\n         * Bound to support return animation.stop pattern\n         */\n        this.stop = () => {\n            if (this._animation) {\n                this._animation.stop();\n                this.stopTimeline?.();\n            }\n            this.keyframeResolver?.cancel();\n        };\n        this.createdAt = time.now();\n        const optionsWithDefaults = {\n            autoplay,\n            delay,\n            type,\n            repeat,\n            repeatDelay,\n            repeatType,\n            name,\n            motionValue,\n            element,\n            ...options,\n        };\n        const KeyframeResolver$1 = element?.KeyframeResolver || KeyframeResolver;\n        this.keyframeResolver = new KeyframeResolver$1(keyframes, (resolvedKeyframes, finalKeyframe, forced) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe, optionsWithDefaults, !forced), name, motionValue, element);\n        this.keyframeResolver?.scheduleResolve();\n    }\n    onKeyframesResolved(keyframes, finalKeyframe, options, sync) {\n        this.keyframeResolver = undefined;\n        const { name, type, velocity, delay, isHandoff, onUpdate } = options;\n        this.resolvedAt = time.now();\n        /**\n         * If we can't animate this value with the resolved keyframes\n         * then we should complete it immediately.\n         */\n        if (!canAnimate(keyframes, name, type, velocity)) {\n            if (MotionGlobalConfig.instantAnimations || !delay) {\n                onUpdate?.(getFinalKeyframe(keyframes, options, finalKeyframe));\n            }\n            keyframes[0] = keyframes[keyframes.length - 1];\n            options.duration = 0;\n            options.repeat = 0;\n        }\n        /**\n         * Resolve startTime for the animation.\n         *\n         * This method uses the createdAt and resolvedAt to calculate the\n         * animation startTime. *Ideally*, we would use the createdAt time as t=0\n         * as the following frame would then be the first frame of the animation in\n         * progress, which would feel snappier.\n         *\n         * However, if there's a delay (main thread work) between the creation of\n         * the animation and the first commited frame, we prefer to use resolvedAt\n         * to avoid a sudden jump into the animation.\n         */\n        const startTime = sync\n            ? !this.resolvedAt\n                ? this.createdAt\n                : this.resolvedAt - this.createdAt > MAX_RESOLVE_DELAY\n                    ? this.resolvedAt\n                    : this.createdAt\n            : undefined;\n        const resolvedOptions = {\n            startTime,\n            finalKeyframe,\n            ...options,\n            keyframes,\n        };\n        /**\n         * Animate via WAAPI if possible. If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        const animation = !isHandoff && supportsBrowserAnimation(resolvedOptions)\n            ? new NativeAnimationExtended({\n                ...resolvedOptions,\n                element: resolvedOptions.motionValue.owner.current,\n            })\n            : new JSAnimation(resolvedOptions);\n        animation.finished.then(() => this.notifyFinished()).catch(noop);\n        if (this.pendingTimeline) {\n            this.stopTimeline = animation.attachTimeline(this.pendingTimeline);\n            this.pendingTimeline = undefined;\n        }\n        this._animation = animation;\n    }\n    get finished() {\n        if (!this._animation) {\n            return this._finished;\n        }\n        else {\n            return this.animation.finished;\n        }\n    }\n    then(onResolve, _onReject) {\n        return this.finished.finally(onResolve).then(() => { });\n    }\n    get animation() {\n        if (!this._animation) {\n            this.keyframeResolver?.resume();\n            flushKeyframeResolvers();\n        }\n        return this._animation;\n    }\n    get duration() {\n        return this.animation.duration;\n    }\n    get time() {\n        return this.animation.time;\n    }\n    set time(newTime) {\n        this.animation.time = newTime;\n    }\n    get speed() {\n        return this.animation.speed;\n    }\n    get state() {\n        return this.animation.state;\n    }\n    set speed(newSpeed) {\n        this.animation.speed = newSpeed;\n    }\n    get startTime() {\n        return this.animation.startTime;\n    }\n    attachTimeline(timeline) {\n        if (this._animation) {\n            this.stopTimeline = this.animation.attachTimeline(timeline);\n        }\n        else {\n            this.pendingTimeline = timeline;\n        }\n        return () => this.stop();\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.complete();\n    }\n    cancel() {\n        if (this._animation) {\n            this.animation.cancel();\n        }\n        this.keyframeResolver?.cancel();\n    }\n}\n\nexport { AsyncMotionValueAnimation };\n"], "mappings": ";;;AAAA,SAASA,kBAAkB,EAAEC,IAAI,QAAQ,cAAc;AACvD,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,mCAAmC;AAC5F,SAASC,uBAAuB,QAAQ,+BAA+B;AACvE,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,wBAAwB,QAAQ,4BAA4B;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,yBAAyB,SAASH,WAAW,CAAC;EAChDI,WAAWA,CAAAC,IAAA,EAA0J;IAAA,IAAAC,sBAAA;IAAA,IAAzJ;QAAEC,QAAQ,GAAG,IAAI;QAAEC,KAAK,GAAG,CAAC;QAAEC,IAAI,GAAG,WAAW;QAAEC,MAAM,GAAG,CAAC;QAAEC,WAAW,GAAG,CAAC;QAAEC,UAAU,GAAG,MAAM;QAAEC,SAAS;QAAEC,IAAI;QAAEC,WAAW;QAAEC;MAAoB,CAAC,GAAAX,IAAA;MAATY,OAAO,GAAAC,wBAAA,CAAAb,IAAA,EAAAc,SAAA;IAC7J,KAAK,CAAC,CAAC;IACP;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MAAA,IAAAC,qBAAA;MACd,IAAI,IAAI,CAACC,UAAU,EAAE;QAAA,IAAAC,kBAAA;QACjB,IAAI,CAACD,UAAU,CAACF,IAAI,CAAC,CAAC;QACtB,CAAAG,kBAAA,OAAI,CAACC,YAAY,cAAAD,kBAAA,eAAjBA,kBAAA,CAAAE,IAAA,KAAoB,CAAC;MACzB;MACA,CAAAJ,qBAAA,OAAI,CAACK,gBAAgB,cAAAL,qBAAA,eAArBA,qBAAA,CAAuBM,MAAM,CAAC,CAAC;IACnC,CAAC;IACD,IAAI,CAACC,SAAS,GAAGnC,IAAI,CAACoC,GAAG,CAAC,CAAC;IAC3B,MAAMC,mBAAmB,GAAAC,aAAA;MACrBxB,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,MAAM;MACNC,WAAW;MACXC,UAAU;MACVE,IAAI;MACJC,WAAW;MACXC;IAAO,GACJC,OAAO,CACb;IACD,MAAMe,kBAAkB,GAAG,CAAAhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEpB,gBAAgB,KAAIA,gBAAgB;IACxE,IAAI,CAAC8B,gBAAgB,GAAG,IAAIM,kBAAkB,CAACnB,SAAS,EAAE,CAACoB,iBAAiB,EAAEC,aAAa,EAAEC,MAAM,KAAK,IAAI,CAACC,mBAAmB,CAACH,iBAAiB,EAAEC,aAAa,EAAEJ,mBAAmB,EAAE,CAACK,MAAM,CAAC,EAAErB,IAAI,EAAEC,WAAW,EAAEC,OAAO,CAAC;IAC7N,CAAAV,sBAAA,OAAI,CAACoB,gBAAgB,cAAApB,sBAAA,eAArBA,sBAAA,CAAuB+B,eAAe,CAAC,CAAC;EAC5C;EACAD,mBAAmBA,CAACvB,SAAS,EAAEqB,aAAa,EAAEjB,OAAO,EAAEqB,IAAI,EAAE;IACzD,IAAI,CAACZ,gBAAgB,GAAGa,SAAS;IACjC,MAAM;MAAEzB,IAAI;MAAEL,IAAI;MAAE+B,QAAQ;MAAEhC,KAAK;MAAEiC,SAAS;MAAEC;IAAS,CAAC,GAAGzB,OAAO;IACpE,IAAI,CAAC0B,UAAU,GAAGlD,IAAI,CAACoC,GAAG,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAAC9B,UAAU,CAACc,SAAS,EAAEC,IAAI,EAAEL,IAAI,EAAE+B,QAAQ,CAAC,EAAE;MAC9C,IAAIjD,kBAAkB,CAACqD,iBAAiB,IAAI,CAACpC,KAAK,EAAE;QAChDkC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAG/C,gBAAgB,CAACkB,SAAS,EAAEI,OAAO,EAAEiB,aAAa,CAAC,CAAC;MACnE;MACArB,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACA,SAAS,CAACgC,MAAM,GAAG,CAAC,CAAC;MAC9C5B,OAAO,CAAC6B,QAAQ,GAAG,CAAC;MACpB7B,OAAO,CAACP,MAAM,GAAG,CAAC;IACtB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMqC,SAAS,GAAGT,IAAI,GAChB,CAAC,IAAI,CAACK,UAAU,GACZ,IAAI,CAACf,SAAS,GACd,IAAI,CAACe,UAAU,GAAG,IAAI,CAACf,SAAS,GAAG1B,iBAAiB,GAChD,IAAI,CAACyC,UAAU,GACf,IAAI,CAACf,SAAS,GACtBW,SAAS;IACf,MAAMS,eAAe,GAAAjB,aAAA,CAAAA,aAAA;MACjBgB,SAAS;MACTb;IAAa,GACVjB,OAAO;MACVJ;IAAS,EACZ;IACD;AACR;AACA;AACA;AACA;IACQ,MAAMoC,SAAS,GAAG,CAACR,SAAS,IAAIxC,wBAAwB,CAAC+C,eAAe,CAAC,GACnE,IAAIlD,uBAAuB,CAAAiC,aAAA,CAAAA,aAAA,KACtBiB,eAAe;MAClBhC,OAAO,EAAEgC,eAAe,CAACjC,WAAW,CAACmC,KAAK,CAACC;IAAO,EACrD,CAAC,GACA,IAAIzD,WAAW,CAACsD,eAAe,CAAC;IACtCC,SAAS,CAACG,QAAQ,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC/D,IAAI,CAAC;IAChE,IAAI,IAAI,CAACgE,eAAe,EAAE;MACtB,IAAI,CAAChC,YAAY,GAAGyB,SAAS,CAACQ,cAAc,CAAC,IAAI,CAACD,eAAe,CAAC;MAClE,IAAI,CAACA,eAAe,GAAGjB,SAAS;IACpC;IACA,IAAI,CAACjB,UAAU,GAAG2B,SAAS;EAC/B;EACA,IAAIG,QAAQA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC9B,UAAU,EAAE;MAClB,OAAO,IAAI,CAACoC,SAAS;IACzB,CAAC,MACI;MACD,OAAO,IAAI,CAACT,SAAS,CAACG,QAAQ;IAClC;EACJ;EACAC,IAAIA,CAACM,SAAS,EAAEC,SAAS,EAAE;IACvB,OAAO,IAAI,CAACR,QAAQ,CAACS,OAAO,CAACF,SAAS,CAAC,CAACN,IAAI,CAAC,MAAM,CAAE,CAAC,CAAC;EAC3D;EACA,IAAIJ,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE;MAAA,IAAAwC,sBAAA;MAClB,CAAAA,sBAAA,OAAI,CAACpC,gBAAgB,cAAAoC,sBAAA,eAArBA,sBAAA,CAAuBC,MAAM,CAAC,CAAC;MAC/BlE,sBAAsB,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACyB,UAAU;EAC1B;EACA,IAAIwB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,SAAS,CAACH,QAAQ;EAClC;EACA,IAAIrD,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACwD,SAAS,CAACxD,IAAI;EAC9B;EACA,IAAIA,IAAIA,CAACuE,OAAO,EAAE;IACd,IAAI,CAACf,SAAS,CAACxD,IAAI,GAAGuE,OAAO;EACjC;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChB,SAAS,CAACgB,KAAK;EAC/B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjB,SAAS,CAACiB,KAAK;EAC/B;EACA,IAAID,KAAKA,CAACE,QAAQ,EAAE;IAChB,IAAI,CAAClB,SAAS,CAACgB,KAAK,GAAGE,QAAQ;EACnC;EACA,IAAIpB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACE,SAAS,CAACF,SAAS;EACnC;EACAU,cAAcA,CAACW,QAAQ,EAAE;IACrB,IAAI,IAAI,CAAC9C,UAAU,EAAE;MACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACyB,SAAS,CAACQ,cAAc,CAACW,QAAQ,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACZ,eAAe,GAAGY,QAAQ;IACnC;IACA,OAAO,MAAM,IAAI,CAAChD,IAAI,CAAC,CAAC;EAC5B;EACAiD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACpB,SAAS,CAACoB,IAAI,CAAC,CAAC;EACzB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACrB,SAAS,CAACqB,KAAK,CAAC,CAAC;EAC1B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACtB,SAAS,CAACsB,QAAQ,CAAC,CAAC;EAC7B;EACA5C,MAAMA,CAAA,EAAG;IAAA,IAAA6C,sBAAA;IACL,IAAI,IAAI,CAAClD,UAAU,EAAE;MACjB,IAAI,CAAC2B,SAAS,CAACtB,MAAM,CAAC,CAAC;IAC3B;IACA,CAAA6C,sBAAA,OAAI,CAAC9C,gBAAgB,cAAA8C,sBAAA,eAArBA,sBAAA,CAAuB7C,MAAM,CAAC,CAAC;EACnC;AACJ;AAEA,SAASxB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}