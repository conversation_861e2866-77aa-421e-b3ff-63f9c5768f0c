{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'./navDiscover.module.css';import arrowIcon from'../../../../asset/imgs/icons/arrow.svg';// data\nimport{discoverMenuData}from'../../../../asset/data/navbarData';import{NavLink}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NavDiscover=_ref=>{let{onClose,setActiveMobMenu}=_ref;const[visible,setVisible]=useState(false);useEffect(()=>{const timer=setTimeout(()=>setVisible(true),10);return()=>clearTimeout(timer);},[]);return/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.overlay,\" \").concat(visible?styles.show:''),onClick:()=>onClose(),children:/*#__PURE__*/_jsx(\"div\",{className:styles.menuWrapper,onClick:e=>e.stopPropagation(),children:/*#__PURE__*/_jsx(\"div\",{className:styles.content,children:discoverMenuData.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:styles.column,children:[/*#__PURE__*/_jsx(\"h4\",{children:item.title}),/*#__PURE__*/_jsx(\"ul\",{children:item.items.map(link=>/*#__PURE__*/_jsx(\"li\",{onClick:()=>onClose(),children:link.target_url?/*#__PURE__*/_jsxs(\"a\",{href:link.url,className:\"link\",onClick:()=>setActiveMobMenu(false),target:\"_blanck\",children:[' ',link.title,/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]}):/*#__PURE__*/_jsxs(NavLink,{to:link.url,className:\"link\",onClick:()=>setActiveMobMenu(false),children:[link.title,/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]})},link.id))})]},item.id))})})});};export default NavDiscover;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "arrowIcon", "discoverMenuData", "NavLink", "jsx", "_jsx", "jsxs", "_jsxs", "NavDiscover", "_ref", "onClose", "setActiveMobMenu", "visible", "setVisible", "timer", "setTimeout", "clearTimeout", "className", "concat", "overlay", "show", "onClick", "children", "menuWrapper", "e", "stopPropagation", "content", "map", "item", "column", "title", "items", "link", "target_url", "href", "url", "target", "src", "alt", "to", "id"], "sources": ["/var/www/html/gwm.tj/src/layout/Navbar/components/discover/NavDiscover.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './navDiscover.module.css';\nimport arrowIcon from '../../../../asset/imgs/icons/arrow.svg';\n// data\nimport { discoverMenuData } from '../../../../asset/data/navbarData';\nimport { NavLink } from 'react-router-dom';\n\nconst NavDiscover = ({ onClose, setActiveMobMenu }) => {\n  const [visible, setVisible] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <div\n      className={`${styles.overlay} ${visible ? styles.show : ''}`}\n      onClick={() => onClose()}\n    >\n      <div className={styles.menuWrapper} onClick={(e) => e.stopPropagation()}>\n        {/* content  */}\n        <div className={styles.content}>\n          {discoverMenuData.map((item) => (\n            <div className={styles.column} key={item.id}>\n              <h4>{item.title}</h4>\n              <ul>\n                {item.items.map((link) => (\n                  <li key={link.id} onClick={() => onClose()}>\n                    {link.target_url ? (\n                      <a\n                        href={link.url}\n                        className=\"link\"\n                        onClick={() => setActiveMobMenu(false)}\n                        target=\"_blanck\"\n                      >\n                        {' '}\n                        {link.title}\n                        <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                      </a>\n                    ) : (\n                      <NavLink\n                        to={link.url}\n                        className=\"link\"\n                        onClick={() => setActiveMobMenu(false)}\n                      >\n                        {link.title}\n                        <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                      </NavLink>\n                    )}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NavDiscover;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,0BAA0B,CAC7C,MAAO,CAAAC,SAAS,KAAM,wCAAwC,CAC9D;AACA,OAASC,gBAAgB,KAAQ,mCAAmC,CACpE,OAASC,OAAO,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3C,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAmC,IAAlC,CAAEC,OAAO,CAAEC,gBAAiB,CAAC,CAAAF,IAAA,CAChD,KAAM,CAACG,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAE7CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgB,KAAK,CAAGC,UAAU,CAAC,IAAMF,UAAU,CAAC,IAAI,CAAC,CAAE,EAAE,CAAC,CACpD,MAAO,IAAMG,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN,mBACET,IAAA,QACEY,SAAS,IAAAC,MAAA,CAAKlB,MAAM,CAACmB,OAAO,MAAAD,MAAA,CAAIN,OAAO,CAAGZ,MAAM,CAACoB,IAAI,CAAG,EAAE,CAAG,CAC7DC,OAAO,CAAEA,CAAA,GAAMX,OAAO,CAAC,CAAE,CAAAY,QAAA,cAEzBjB,IAAA,QAAKY,SAAS,CAAEjB,MAAM,CAACuB,WAAY,CAACF,OAAO,CAAGG,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAAH,QAAA,cAEtEjB,IAAA,QAAKY,SAAS,CAAEjB,MAAM,CAAC0B,OAAQ,CAAAJ,QAAA,CAC5BpB,gBAAgB,CAACyB,GAAG,CAAEC,IAAI,eACzBrB,KAAA,QAAKU,SAAS,CAAEjB,MAAM,CAAC6B,MAAO,CAAAP,QAAA,eAC5BjB,IAAA,OAAAiB,QAAA,CAAKM,IAAI,CAACE,KAAK,CAAK,CAAC,cACrBzB,IAAA,OAAAiB,QAAA,CACGM,IAAI,CAACG,KAAK,CAACJ,GAAG,CAAEK,IAAI,eACnB3B,IAAA,OAAkBgB,OAAO,CAAEA,CAAA,GAAMX,OAAO,CAAC,CAAE,CAAAY,QAAA,CACxCU,IAAI,CAACC,UAAU,cACd1B,KAAA,MACE2B,IAAI,CAAEF,IAAI,CAACG,GAAI,CACflB,SAAS,CAAC,MAAM,CAChBI,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAAC,KAAK,CAAE,CACvCyB,MAAM,CAAC,SAAS,CAAAd,QAAA,EAEf,GAAG,CACHU,IAAI,CAACF,KAAK,cACXzB,IAAA,QAAKgC,GAAG,CAAEpC,SAAU,CAACqC,GAAG,CAAC,EAAE,CAACrB,SAAS,CAAC,UAAU,CAAE,CAAC,EAClD,CAAC,cAEJV,KAAA,CAACJ,OAAO,EACNoC,EAAE,CAAEP,IAAI,CAACG,GAAI,CACblB,SAAS,CAAC,MAAM,CAChBI,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAAC,KAAK,CAAE,CAAAW,QAAA,EAEtCU,IAAI,CAACF,KAAK,cACXzB,IAAA,QAAKgC,GAAG,CAAEpC,SAAU,CAACqC,GAAG,CAAC,EAAE,CAACrB,SAAS,CAAC,UAAU,CAAE,CAAC,EAC5C,CACV,EArBMe,IAAI,CAACQ,EAsBV,CACL,CAAC,CACA,CAAC,GA5B6BZ,IAAI,CAACY,EA6BpC,CACN,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}