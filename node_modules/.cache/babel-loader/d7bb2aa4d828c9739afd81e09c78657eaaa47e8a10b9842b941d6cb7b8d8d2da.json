{"ast": null, "code": "import { useEffect } from 'react';\nfunction useUnmountEffect(callback) {\n  return useEffect(() => () => callback(), []);\n}\nexport { useUnmountEffect };", "map": {"version": 3, "names": ["useEffect", "useUnmountEffect", "callback"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs"], "sourcesContent": ["import { useEffect } from 'react';\n\nfunction useUnmountEffect(callback) {\n    return useEffect(() => () => callback(), []);\n}\n\nexport { useUnmountEffect };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AAEjC,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAChC,OAAOF,SAAS,CAAC,MAAM,MAAME,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;AAChD;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}