{"ast": null, "code": "import _objectSpread from\"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState}from'react';import Select from'react-select';import styles from'./partsList.module.css';import{partsData}from'../../../../asset/data/accessoriesData';// Уникальные категории + \"Все\"\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const categories=['Все',...new Set(partsData.map(item=>item.category))];// Опции для Select\nconst options=categories.map(cat=>({value:cat,label:cat}));const PartsList=()=>{const[activeCategory,setActiveCategory]=useState('Все');const filteredParts=activeCategory!=='Все'?partsData.filter(item=>item.category===activeCategory):partsData;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:styles.selectWrapper,children:/*#__PURE__*/_jsx(Select,{classNamePrefix:\"react-select\",options:options,placeholder:\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\",value:{value:activeCategory,label:activeCategory},onChange:selectedOption=>setActiveCategory(selectedOption.value),isSearchable:false,styles:{control:provided=>_objectSpread(_objectSpread({},provided),{},{width:'100%',paddingLeft:'8px',height:'58px',borderRadius:'2px',border:'1px solid #8e8e93',fontSize:'14px',boxShadow:'none',backgroundColor:'#fff','&:hover':{borderColor:'#8e8e93'}}),container:provided=>_objectSpread(_objectSpread({},provided),{},{width:'100%'}),placeholder:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#888'}),singleValue:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#000'}),indicatorsContainer:provided=>_objectSpread(_objectSpread({},provided),{},{paddingRight:'10px'}),dropdownIndicator:provided=>_objectSpread(_objectSpread({},provided),{},{color:'#8e8e93'}),menu:provided=>_objectSpread(_objectSpread({},provided),{},{border:'1px solid #8e8e93',borderRadius:'2px',boxShadow:'none',marginTop:'4px',backgroundColor:'#fff'}),menuList:provided=>_objectSpread(_objectSpread({},provided),{},{padding:0}),option:(provided,state)=>_objectSpread(_objectSpread({},provided),{},{padding:'14px 16px',fontSize:'14px',backgroundColor:state.isFocused?'#000':'#fff',color:state.isFocused?'#fff':'#000',cursor:'pointer','&:active':{backgroundColor:'#000'}})}})}),/*#__PURE__*/_jsx(\"div\",{className:styles.cardsWrapper,children:filteredParts.map(car=>/*#__PURE__*/_jsxs(\"div\",{className:styles.card,children:[/*#__PURE__*/_jsx(\"img\",{src:car.img,alt:car.title}),/*#__PURE__*/_jsxs(\"div\",{className:styles.cardInfo,children:[/*#__PURE__*/_jsx(\"h4\",{children:car.title}),car.price&&/*#__PURE__*/_jsxs(\"p\",{className:styles.desc,children:[\"\\u043E\\u0442 \",car.price,\" \\u0441\\u043E\\u043C\"]})]}),car.inStok&&/*#__PURE__*/_jsx(\"div\",{children:car.inStok==='да'?/*#__PURE__*/_jsx(\"span\",{className:styles.inStok,children:\"\\u0412 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0438\"}):/*#__PURE__*/_jsx(\"span\",{className:styles.noStok,children:\"\\u041D\\u0435\\u0442 \\u0432 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0438\"})}),car.type&&/*#__PURE__*/_jsx(\"span\",{className:styles.label,children:car.type})]},car.id))})]});};export default PartsList;", "map": {"version": 3, "names": ["useState", "Select", "styles", "partsData", "jsx", "_jsx", "jsxs", "_jsxs", "categories", "Set", "map", "item", "category", "options", "cat", "value", "label", "PartsList", "activeCategory", "setActiveCategory", "filteredParts", "filter", "children", "className", "selectWrapper", "classNamePrefix", "placeholder", "onChange", "selectedOption", "isSearchable", "control", "provided", "_objectSpread", "width", "paddingLeft", "height", "borderRadius", "border", "fontSize", "boxShadow", "backgroundColor", "borderColor", "container", "color", "singleValue", "indicatorsContainer", "paddingRight", "dropdownIndicator", "menu", "marginTop", "menuList", "padding", "option", "state", "isFocused", "cursor", "cardsWrapper", "car", "card", "src", "img", "alt", "title", "cardInfo", "price", "desc", "inStok", "noStok", "type", "id"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/components/PartsList/PartsList.jsx"], "sourcesContent": ["import { useState } from 'react';\nimport Select from 'react-select';\nimport styles from './partsList.module.css';\nimport { partsData } from '../../../../asset/data/accessoriesData';\n\n// Уникальные категории + \"Все\"\nconst categories = ['Все', ...new Set(partsData.map((item) => item.category))];\n\n// Опции для Select\nconst options = categories.map((cat) => ({\n  value: cat,\n  label: cat,\n}));\n\nconst PartsList = () => {\n  const [activeCategory, setActiveCategory] = useState('Все');\n\n  const filteredParts =\n    activeCategory !== 'Все'\n      ? partsData.filter((item) => item.category === activeCategory)\n      : partsData;\n\n  return (\n    <div>\n      <div className={styles.selectWrapper}>\n        <Select\n          classNamePrefix=\"react-select\"\n          options={options}\n          placeholder=\"Модель авто\"\n          value={{ value: activeCategory, label: activeCategory }}\n          onChange={(selectedOption) => setActiveCategory(selectedOption.value)}\n          isSearchable={false}\n          styles={{\n            control: (provided) => ({\n              ...provided,\n              width: '100%',\n              paddingLeft: '8px',\n              height: '58px',\n              borderRadius: '2px',\n              border: '1px solid #8e8e93',\n              fontSize: '14px',\n              boxShadow: 'none',\n              backgroundColor: '#fff',\n              '&:hover': {\n                borderColor: '#8e8e93',\n              },\n            }),\n            container: (provided) => ({\n              ...provided,\n              width: '100%',\n            }),\n            placeholder: (provided) => ({\n              ...provided,\n              color: '#888',\n            }),\n            singleValue: (provided) => ({\n              ...provided,\n              color: '#000',\n            }),\n            indicatorsContainer: (provided) => ({\n              ...provided,\n              paddingRight: '10px',\n            }),\n            dropdownIndicator: (provided) => ({\n              ...provided,\n              color: '#8e8e93',\n            }),\n            menu: (provided) => ({\n              ...provided,\n              border: '1px solid #8e8e93',\n              borderRadius: '2px',\n              boxShadow: 'none',\n              marginTop: '4px',\n              backgroundColor: '#fff',\n            }),\n            menuList: (provided) => ({\n              ...provided,\n              padding: 0,\n            }),\n            option: (provided, state) => ({\n              ...provided,\n              padding: '14px 16px',\n              fontSize: '14px',\n              backgroundColor: state.isFocused ? '#000' : '#fff',\n              color: state.isFocused ? '#fff' : '#000',\n              cursor: 'pointer',\n              '&:active': {\n                backgroundColor: '#000',\n              },\n            }),\n          }}\n        />\n      </div>\n\n      <div className={styles.cardsWrapper}>\n        {filteredParts.map((car) => (\n          <div key={car.id} className={styles.card}>\n            <img src={car.img} alt={car.title} />\n            <div className={styles.cardInfo}>\n              <h4>{car.title}</h4>\n              {car.price && <p className={styles.desc}>от {car.price} сом</p>}\n            </div>\n            {car.inStok && (\n              <div>\n                {car.inStok === 'да' ? (\n                  <span className={styles.inStok}>В наличии</span>\n                ) : (\n                  <span className={styles.noStok}>Нет в наличии</span>\n                )}\n              </div>\n            )}\n            {car.type && <span className={styles.label}>{car.type}</span>}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default PartsList;\n"], "mappings": "yGAAA,OAASA,QAAQ,KAAQ,OAAO,CAChC,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,MAAM,KAAM,wBAAwB,CAC3C,OAASC,SAAS,KAAQ,wCAAwC,CAElE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,UAAU,CAAG,CAAC,KAAK,CAAE,GAAG,GAAI,CAAAC,GAAG,CAACN,SAAS,CAACO,GAAG,CAAEC,IAAI,EAAKA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAE9E;AACA,KAAM,CAAAC,OAAO,CAAGL,UAAU,CAACE,GAAG,CAAEI,GAAG,GAAM,CACvCC,KAAK,CAAED,GAAG,CACVE,KAAK,CAAEF,GACT,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAG,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAAoB,aAAa,CACjBF,cAAc,GAAK,KAAK,CACpBf,SAAS,CAACkB,MAAM,CAAEV,IAAI,EAAKA,IAAI,CAACC,QAAQ,GAAKM,cAAc,CAAC,CAC5Df,SAAS,CAEf,mBACEI,KAAA,QAAAe,QAAA,eACEjB,IAAA,QAAKkB,SAAS,CAAErB,MAAM,CAACsB,aAAc,CAAAF,QAAA,cACnCjB,IAAA,CAACJ,MAAM,EACLwB,eAAe,CAAC,cAAc,CAC9BZ,OAAO,CAAEA,OAAQ,CACjBa,WAAW,CAAC,+DAAa,CACzBX,KAAK,CAAE,CAAEA,KAAK,CAAEG,cAAc,CAAEF,KAAK,CAAEE,cAAe,CAAE,CACxDS,QAAQ,CAAGC,cAAc,EAAKT,iBAAiB,CAACS,cAAc,CAACb,KAAK,CAAE,CACtEc,YAAY,CAAE,KAAM,CACpB3B,MAAM,CAAE,CACN4B,OAAO,CAAGC,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACbD,QAAQ,MACXE,KAAK,CAAE,MAAM,CACbC,WAAW,CAAE,KAAK,CAClBC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,mBAAmB,CAC3BC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,MAAM,CACjBC,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,CACTC,WAAW,CAAE,SACf,CAAC,EACD,CACFC,SAAS,CAAGX,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACfD,QAAQ,MACXE,KAAK,CAAE,MAAM,EACb,CACFP,WAAW,CAAGK,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACjBD,QAAQ,MACXY,KAAK,CAAE,MAAM,EACb,CACFC,WAAW,CAAGb,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACjBD,QAAQ,MACXY,KAAK,CAAE,MAAM,EACb,CACFE,mBAAmB,CAAGd,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACzBD,QAAQ,MACXe,YAAY,CAAE,MAAM,EACpB,CACFC,iBAAiB,CAAGhB,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACvBD,QAAQ,MACXY,KAAK,CAAE,SAAS,EAChB,CACFK,IAAI,CAAGjB,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACVD,QAAQ,MACXM,MAAM,CAAE,mBAAmB,CAC3BD,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,MAAM,CACjBU,SAAS,CAAE,KAAK,CAChBT,eAAe,CAAE,MAAM,EACvB,CACFU,QAAQ,CAAGnB,QAAQ,EAAAC,aAAA,CAAAA,aAAA,IACdD,QAAQ,MACXoB,OAAO,CAAE,CAAC,EACV,CACFC,MAAM,CAAEA,CAACrB,QAAQ,CAAEsB,KAAK,GAAArB,aAAA,CAAAA,aAAA,IACnBD,QAAQ,MACXoB,OAAO,CAAE,WAAW,CACpBb,QAAQ,CAAE,MAAM,CAChBE,eAAe,CAAEa,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,MAAM,CAClDX,KAAK,CAAEU,KAAK,CAACC,SAAS,CAAG,MAAM,CAAG,MAAM,CACxCC,MAAM,CAAE,SAAS,CACjB,UAAU,CAAE,CACVf,eAAe,CAAE,MACnB,CAAC,EAEL,CAAE,CACH,CAAC,CACC,CAAC,cAENnC,IAAA,QAAKkB,SAAS,CAAErB,MAAM,CAACsD,YAAa,CAAAlC,QAAA,CACjCF,aAAa,CAACV,GAAG,CAAE+C,GAAG,eACrBlD,KAAA,QAAkBgB,SAAS,CAAErB,MAAM,CAACwD,IAAK,CAAApC,QAAA,eACvCjB,IAAA,QAAKsD,GAAG,CAAEF,GAAG,CAACG,GAAI,CAACC,GAAG,CAAEJ,GAAG,CAACK,KAAM,CAAE,CAAC,cACrCvD,KAAA,QAAKgB,SAAS,CAAErB,MAAM,CAAC6D,QAAS,CAAAzC,QAAA,eAC9BjB,IAAA,OAAAiB,QAAA,CAAKmC,GAAG,CAACK,KAAK,CAAK,CAAC,CACnBL,GAAG,CAACO,KAAK,eAAIzD,KAAA,MAAGgB,SAAS,CAAErB,MAAM,CAAC+D,IAAK,CAAA3C,QAAA,EAAC,eAAG,CAACmC,GAAG,CAACO,KAAK,CAAC,qBAAI,EAAG,CAAC,EAC5D,CAAC,CACLP,GAAG,CAACS,MAAM,eACT7D,IAAA,QAAAiB,QAAA,CACGmC,GAAG,CAACS,MAAM,GAAK,IAAI,cAClB7D,IAAA,SAAMkB,SAAS,CAAErB,MAAM,CAACgE,MAAO,CAAA5C,QAAA,CAAC,mDAAS,CAAM,CAAC,cAEhDjB,IAAA,SAAMkB,SAAS,CAAErB,MAAM,CAACiE,MAAO,CAAA7C,QAAA,CAAC,sEAAa,CAAM,CACpD,CACE,CACN,CACAmC,GAAG,CAACW,IAAI,eAAI/D,IAAA,SAAMkB,SAAS,CAAErB,MAAM,CAACc,KAAM,CAAAM,QAAA,CAAEmC,GAAG,CAACW,IAAI,CAAO,CAAC,GAfrDX,GAAG,CAACY,EAgBT,CACN,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}