{"ast": null, "code": "import _objectSpread from\"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useEffect,useState}from'react';import{useParams,Link}from'react-router-dom';import{getModelData}from'../../../../data/modelLoader';import styles from'../modelPage.module.css';import icon from'../../../../asset/imgs/icons/download.png';import AOS from'aos';import'aos/dist/aos.css';// Components\nimport Modal from'../components/Modal';import Specifications from'../components/Specifications';// SEO\nimport SEO from'../../../../hooks/useSEO';import Form from'../../../../components/Form/Form';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const DynamicModelPage=()=>{var _modelData$hero,_modelData$hero2,_modelData$sections$3;const{slug}=useParams();const[loading,setLoading]=useState(true);const[modelData,setModelData]=useState(null);const[selectedColor,setSelectedColor]=useState(null);const[modalContent,setModalContent]=useState(null);const[error,setError]=useState(null);useEffect(()=>{setLoading(true);// сбрасываем при смене slug\nAOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[slug]);useEffect(()=>{const loadModelData=()=>{setLoading(true);try{const data=getModelData(slug);if(!data){setError('Модель не найдена');setLoading(false);return;}setModelData(data);if(data.colors&&data.colors.length>0){setSelectedColor(data.colors[0]);}setError(null);}catch(err){console.error('Failed to load model data:',err);setError('Модель не найдена');}finally{setLoading(false);}};if(slug){loadModelData();}},[slug]);const handleColorChange=color=>{setSelectedColor(color);};const openModal=(title,desc)=>{setModalContent({title,desc});};if(error||!modelData){return/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u041A \\u0441\\u043E\\u0436\\u0430\\u043B\\u0435\\u043D\\u0438\\u044E, \\u0437\\u0430\\u043F\\u0440\\u0430\\u0448\\u0438\\u0432\\u0430\\u0435\\u043C\\u0430\\u044F \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u043D\\u0435 \\u043D\\u0430\\u0439\\u0434\\u0435\\u043D\\u0430.\"})]})})});}// Prepare data for specifications component\nconst tabData={tabsBtn:modelData.specifications.trims,tableBtn:modelData.specifications.categories};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(SEO,_objectSpread({},modelData.seo)),loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsxs(\"div\",{className:\"topmenu\",children:[/*#__PURE__*/_jsx(\"div\",{className:styles.header,style:{backgroundImage:modelData!==null&&modelData!==void 0&&(_modelData$hero=modelData.hero)!==null&&_modelData$hero!==void 0&&_modelData$hero.image?\"url(\".concat(modelData.hero.image,\")\"):'none',backgroundColor:modelData!==null&&modelData!==void 0&&(_modelData$hero2=modelData.hero)!==null&&_modelData$hero2!==void 0&&_modelData$hero2.image?'transparent':'#ccc',// светло-серый цвет\nbackgroundSize:'cover',backgroundPosition:'center'},children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.slideContent,children:[modelData.hero.engine&&/*#__PURE__*/_jsx(\"div\",{className:styles.innerNew,\"data-aos\":\"fade-up\",children:modelData.hero.engine}),modelData.hero.title&&/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:modelData.hero.title}),modelData.hero.price&&/*#__PURE__*/_jsx(\"h3\",{className:styles.price,\"data-aos\":\"fade-up\",children:modelData.hero.price}),modelData.hero.features&&/*#__PURE__*/_jsx(\"span\",{className:styles.about,\"data-aos\":\"fade-up\",children:modelData.hero.features[0]}),/*#__PURE__*/_jsx(Link,{to:\"/book-a-test-drive\",\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"button\",{className:\"button-black\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u0422\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432\"})})})]})})}),/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"small\",{children:\"\\u041F\\u0440\\u0438\\u043C\\u0435\\u043D\\u044F\\u044E\\u0442\\u0441\\u044F \\u043F\\u043E\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u044F. \\u0418\\u0437\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u044F \\u0442\\u043E\\u043B\\u044C\\u043A\\u043E \\u0434\\u043B\\u044F \\u0438\\u043B\\u043B\\u044E\\u0441\\u0442\\u0440\\u0430\\u0442\\u0438\\u0432\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043B\\u0435\\u0439. E&OE. \\u041E\\u0442\\u043A\\u0430\\u0437 \\u043E\\u0442 \\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438: \\u0432\\u0441\\u0435 \\u0446\\u0435\\u043D\\u044B \\u0438 \\u0445\\u0430\\u0440\\u0430\\u043A\\u0442\\u0435\\u0440\\u0438\\u0441\\u0442\\u0438\\u043A\\u0438 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0431\\u044B\\u0442\\u044C \\u0438\\u0437\\u043C\\u0435\\u043D\\u0435\\u043D\\u044B \\u0431\\u0435\\u0437 \\u043F\\u0440\\u0435\\u0434\\u0432\\u0430\\u0440\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0443\\u0432\\u0435\\u0434\\u043E\\u043C\\u043B\\u0435\\u043D\\u0438\\u044F. \\u0421\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u0431\\u043B\\u0438\\u0436\\u0430\\u0439\\u0448\\u0438\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u043E\\u043C \\u0434\\u043B\\u044F \\u043F\\u043E\\u043B\\u0443\\u0447\\u0435\\u043D\\u0438\\u044F \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u0446\\u0435\\u043D.\"})})}),/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:styles.colorTitle,style:{color:selectedColor===null||selectedColor===void 0?void 0:selectedColor.hex_code,fontWeight:'900'},\"data-aos\":\"fade-up\",children:selectedColor===null||selectedColor===void 0?void 0:selectedColor.name}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"img\",{src:selectedColor===null||selectedColor===void 0?void 0:selectedColor.image,alt:selectedColor===null||selectedColor===void 0?void 0:selectedColor.name,className:styles.colorImage})}),/*#__PURE__*/_jsx(\"div\",{className:styles.colorPickerWrapper,children:modelData.colors.map(color=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleColorChange(color),className:\"\".concat(styles.colorBtn,\" \").concat((selectedColor===null||selectedColor===void 0?void 0:selectedColor.id)===color.id?styles.active:''),style:{backgroundColor:color.hex_code},children:(selectedColor===null||selectedColor===void 0?void 0:selectedColor.id)===color.id?color.name:''},color.id))})]})}),/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"title\",children:[modelData.sections[0].title&&/*#__PURE__*/_jsx(\"h2\",{children:modelData.sections[0].title}),modelData.sections[0].description&&/*#__PURE__*/_jsx(\"p\",{children:modelData.sections[0].description})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.cardContnet,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.rightCard,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.top,children:[0,1,4].map(index=>{var _modelData$sections$;return/*#__PURE__*/_jsx(\"div\",{className:styles.box,children:((_modelData$sections$=modelData.sections[0].cards[index])===null||_modelData$sections$===void 0?void 0:_modelData$sections$.img)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[0].cards[index].img,alt:\"\"}),(modelData.sections[0].cards[index].modalTitle||modelData.sections[0].cards[index].modalText)&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[0].cards[index].modalTitle,modelData.sections[0].cards[index].modalText),children:\"+\"}),modelData.sections[0].cards[index].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[0].cards[index].modalTitle})})]})},index);})}),/*#__PURE__*/_jsx(\"div\",{className:styles.bottom,children:[2,3].map(index=>{var _modelData$sections$2;return/*#__PURE__*/_jsx(\"div\",{className:styles.box,children:((_modelData$sections$2=modelData.sections[0].cards[index])===null||_modelData$sections$2===void 0?void 0:_modelData$sections$2.img)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[0].cards[index].img,alt:\"\"}),(modelData.sections[0].cards[index].modalTitle||modelData.sections[0].cards[index].modalText)&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[0].cards[index].modalTitle,modelData.sections[0].cards[index].modalText),children:\"+\"}),modelData.sections[0].cards[index].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[0].cards[index].modalTitle})})]})},index);})})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.leftCard,children:((_modelData$sections$3=modelData.sections[0].cards[5])===null||_modelData$sections$3===void 0?void 0:_modelData$sections$3.img)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[0].cards[5].img,alt:\"\"}),(modelData.sections[0].cards[5].modalTitle||modelData.sections[0].cards[5].modalText)&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[0].cards[5].modalTitle,modelData.sections[0].cards[5].modalText),children:\"+\"}),modelData.sections[0].cards[5].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[0].cards[5].modalTitle})})]})})]})]})}),/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.flex,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.left,children:modelData.sections[1].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[1].img,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.right,children:[modelData.sections[1].title&&/*#__PURE__*/_jsx(\"h2\",{children:modelData.sections[1].title}),modelData.sections[1].description&&/*#__PURE__*/_jsx(\"p\",{children:modelData.sections[1].description})]})]})})}),/*#__PURE__*/_jsx(\"section\",{className:styles.section,children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[modelData.sections[2].title&&/*#__PURE__*/_jsx(\"h2\",{children:modelData.sections[2].title}),modelData.sections[2].description&&/*#__PURE__*/_jsx(\"p\",{children:modelData.sections[2].description})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.cardContnet,children:[modelData.sections[2].cards[0]&&/*#__PURE__*/_jsxs(\"div\",{className:styles.right,children:[modelData.sections[2].cards[0].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[2].cards[0].img,alt:\"\"}),modelData.sections[2].cards[0].modalText&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[2].cards[0].modalTitle,modelData.sections[2].cards[0].modalText),children:\"+\"}),modelData.sections[2].cards[0].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[2].cards[0].modalTitle})})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.middle,children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.top,children:[modelData.sections[2].cards[1].img&&/*#__PURE__*/_jsxs(\"div\",{className:styles.topRight,children:[modelData.sections[2].cards[1].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[2].cards[1].img,alt:\"\"}),modelData.sections[2].cards[1].modalText&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[2].cards[1].modalTitle,modelData.sections[2].cards[1].modalText),children:\"+\"}),modelData.sections[2].cards[1].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[2].cards[1].modalTitle})})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.topLeft,children:[modelData.sections[2].cards[2].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[2].cards[2].img,alt:\"\"}),modelData.sections[2].cards[2].modalText&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[2].cards[2].modalTitle,modelData.sections[2].cards[2].modalText),children:\"+\"}),modelData.sections[2].cards[2].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[2].cards[2].modalTitle})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.buttom,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.box,children:[modelData.sections[2].cards[3].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[2].cards[3].img,alt:\"\"}),modelData.sections[2].cards[3].modalText&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[2].cards[3].modalTitle,modelData.sections[2].cards[3].modalText),children:\"+\"}),modelData.sections[2].cards[3].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[2].cards[3].modalTitle})})]})})]}),modelData.sections[2].cards[4]&&/*#__PURE__*/_jsxs(\"div\",{className:styles.left,children:[modelData.sections[2].cards[4].img&&/*#__PURE__*/_jsx(\"img\",{src:modelData.sections[2].cards[4].img,alt:\"\"}),modelData.sections[2].cards[4].modalText&&/*#__PURE__*/_jsx(\"div\",{className:styles.plus,onClick:()=>openModal(modelData.sections[2].cards[4].modalTitle,modelData.sections[2].cards[4].modalText),children:\"+\"}),modelData.sections[2].cards[4].modalTitle&&/*#__PURE__*/_jsx(\"div\",{className:styles.overlay,children:/*#__PURE__*/_jsx(\"span\",{children:modelData.sections[2].cards[4].modalTitle})})]})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:styles.section,children:[/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"div\",{className:styles.contant,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.text,children:[modelData.sections[3].title&&/*#__PURE__*/_jsx(\"h2\",{children:modelData.sections[3].title}),modelData.sections[3].description&&/*#__PURE__*/_jsx(\"p\",{children:modelData.sections[3].description}),modelData.sections[3].brochure&&/*#__PURE__*/_jsxs(\"a\",{href:modelData.sections[3].brochure,className:styles.dowBtn,target:\"_blank\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u0421\\u041A\\u0410\\u0427\\u0410\\u0422\\u042C \\u0411\\u0420\\u041E\\u0428\\u042E\\u0420\\u0423\"}),/*#__PURE__*/_jsx(\"img\",{src:icon,alt:\"\"})]})]})})}),modelData.sections[3].img&&/*#__PURE__*/_jsx(\"div\",{className:styles.bgSection,style:{backgroundImage:\"url(\".concat(modelData.sections[3].img,\")\")},children:/*#__PURE__*/_jsx(\"h2\",{\"data-aos\":\"fade-up\",className:styles.title,children:modelData.sections[3].imgTitle})})]}),/*#__PURE__*/_jsx(Specifications,{title:modelData.specifications.title,desc:modelData.specifications.description,tabData:tabData,tableData:modelData.specifications.data}),/*#__PURE__*/_jsx(\"section\",{className:\"container\",children:/*#__PURE__*/_jsx(Form,{formType:\"purchase\",title:\"\\u041E\\u0441\\u0442\\u0430\\u0432\\u0438\\u0442\\u044C \\u0437\\u0430\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0430 \\u043F\\u043E\\u043A\\u0443\\u043F\\u043A\\u0443\",defaultModel:slug})}),modalContent&&/*#__PURE__*/_jsx(Modal,{title:modalContent.title,desc:modalContent.desc,onClose:()=>setModalContent(null)})]})]});};export default DynamicModelPage;", "map": {"version": 3, "names": ["useEffect", "useState", "useParams", "Link", "getModelData", "styles", "icon", "AOS", "Modal", "Specifications", "SEO", "Form", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "DynamicModelPage", "_modelData$hero", "_modelData$hero2", "_modelData$sections$3", "slug", "loading", "setLoading", "modelData", "setModelData", "selectedColor", "setSelectedColor", "modalContent", "setModalContent", "error", "setError", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "loadModelData", "data", "colors", "length", "err", "console", "handleColorChange", "color", "openModal", "title", "desc", "className", "children", "tabData", "tabsBtn", "specifications", "trims", "tableBtn", "categories", "_objectSpread", "seo", "header", "backgroundImage", "hero", "image", "concat", "backgroundColor", "backgroundSize", "backgroundPosition", "slideContent", "engine", "innerNew", "price", "features", "about", "to", "section", "colorTitle", "hex_code", "fontWeight", "name", "src", "alt", "colorImage", "colorPickerWrapper", "map", "onClick", "colorBtn", "id", "active", "sections", "description", "cardContnet", "rightCard", "top", "index", "_modelData$sections$", "box", "cards", "img", "modalTitle", "modalText", "plus", "overlay", "bottom", "_modelData$sections$2", "leftCard", "flex", "left", "right", "middle", "topRight", "topLeft", "buttom", "contant", "text", "brochure", "href", "dowBtn", "target", "bgSection", "imgTitle", "tableData", "formType", "defaultModel", "onClose"], "sources": ["/var/www/html/gwm.tj/src/pages/Models/Pages/DynamicModelPage/DynamicModelPage.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';\nimport { getModelData } from '../../../../data/modelLoader';\nimport styles from '../modelPage.module.css';\nimport icon from '../../../../asset/imgs/icons/download.png';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\n// Components\nimport Modal from '../components/Modal';\nimport Specifications from '../components/Specifications';\n\n// SEO\nimport SEO from '../../../../hooks/useSEO';\nimport Form from '../../../../components/Form/Form';\n\nconst DynamicModelPage = () => {\n  const { slug } = useParams();\n  const [loading, setLoading] = useState(true);\n  const [modelData, setModelData] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(null);\n  const [modalContent, setModalContent] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    setLoading(true); // сбрасываем при смене slug\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, [slug]);\n\n  useEffect(() => {\n    const loadModelData = () => {\n      setLoading(true);\n      try {\n        const data = getModelData(slug);\n        if (!data) {\n          setError('Модель не найдена');\n          setLoading(false);\n          return;\n        }\n        setModelData(data);\n        if (data.colors && data.colors.length > 0) {\n          setSelectedColor(data.colors[0]);\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load model data:', err);\n        setError('Модель не найдена');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (slug) {\n      loadModelData();\n    }\n  }, [slug]);\n\n  const handleColorChange = (color) => {\n    setSelectedColor(color);\n  };\n\n  const openModal = (title, desc) => {\n    setModalContent({ title, desc });\n  };\n\n  if (error || !modelData) {\n    return (\n      <div className=\"topmenu\">\n        <div className=\"container\">\n          <div className=\"content\">\n            <h1 className=\"title\">Модель не найдена</h1>\n            <p>К сожалению, запрашиваемая модель не найдена.</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Prepare data for specifications component\n  const tabData = {\n    tabsBtn: modelData.specifications.trims,\n    tableBtn: modelData.specifications.categories,\n  };\n\n  return (\n    <>\n      <SEO {...modelData.seo} />\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          {/* Hero Section */}\n          <div\n            className={styles.header}\n            style={{\n              backgroundImage: modelData?.hero?.image\n                ? `url(${modelData.hero.image})`\n                : 'none',\n              backgroundColor: modelData?.hero?.image ? 'transparent' : '#ccc', // светло-серый цвет\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n            }}\n          >\n            <div className=\"container\">\n              <div className={styles.slideContent}>\n                {modelData.hero.engine && (\n                  <div className={styles.innerNew} data-aos=\"fade-up\">\n                    {modelData.hero.engine}\n                  </div>\n                )}\n                {modelData.hero.title && (\n                  <h1 data-aos=\"fade-up\">{modelData.hero.title}</h1>\n                )}\n                {modelData.hero.price && (\n                  <h3 className={styles.price} data-aos=\"fade-up\">\n                    {modelData.hero.price}\n                  </h3>\n                )}\n                {modelData.hero.features && (\n                  <span className={styles.about} data-aos=\"fade-up\">\n                    {modelData.hero.features[0]}\n                  </span>\n                )}\n                <Link to=\"/book-a-test-drive\" data-aos=\"fade-up\">\n                  <button className=\"button-black\">\n                    <span>Тест-драйв</span>\n                  </button>\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* Disclaimer */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <small>\n                Применяются положения и условия. Изображения только для\n                иллюстративных целей. E&OE. Отказ от ответственности: все цены и\n                характеристики могут быть изменены без предварительного\n                уведомления. Свяжитесь с ближайшим дилером для получения\n                последних цен.\n              </small>\n            </div>\n          </section>\n          {/* Color Section */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <h2\n                className={styles.colorTitle}\n                style={{ color: selectedColor?.hex_code, fontWeight: '900' }}\n                data-aos=\"fade-up\"\n              >\n                {selectedColor?.name}\n              </h2>\n\n              <div>\n                <img\n                  src={selectedColor?.image}\n                  alt={selectedColor?.name}\n                  className={styles.colorImage}\n                />\n              </div>\n\n              <div className={styles.colorPickerWrapper}>\n                {modelData.colors.map((color) => (\n                  <button\n                    key={color.id}\n                    onClick={() => handleColorChange(color)}\n                    className={`${styles.colorBtn} ${\n                      selectedColor?.id === color.id ? styles.active : ''\n                    }`}\n                    style={{ backgroundColor: color.hex_code }}\n                  >\n                    {selectedColor?.id === color.id ? color.name : ''}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </section>\n\n          {/* Section 1 — динамически на основе modelData.sections[0] */}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className=\"title\">\n                {modelData.sections[0].title && (\n                  <h2>{modelData.sections[0].title}</h2>\n                )}\n                {modelData.sections[0].description && (\n                  <p>{modelData.sections[0].description}</p>\n                )}\n              </div>\n              <div className={styles.cardContnet}>\n                <div className={styles.rightCard}>\n                  <div className={styles.top}>\n                    {[0, 1, 4].map((index) => (\n                      <div key={index} className={styles.box}>\n                        {modelData.sections[0].cards[index]?.img && (\n                          <>\n                            <img\n                              src={modelData.sections[0].cards[index].img}\n                              alt=\"\"\n                            />\n                            {(modelData.sections[0].cards[index].modalTitle ||\n                              modelData.sections[0].cards[index].modalText) && (\n                              <div\n                                className={styles.plus}\n                                onClick={() =>\n                                  openModal(\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle,\n                                    modelData.sections[0].cards[index].modalText\n                                  )\n                                }\n                              >\n                                +\n                              </div>\n                            )}\n                            {modelData.sections[0].cards[index].modalTitle && (\n                              <div className={styles.overlay}>\n                                <span>\n                                  {\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle\n                                  }\n                                </span>\n                              </div>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                  <div className={styles.bottom}>\n                    {[2, 3].map((index) => (\n                      <div key={index} className={styles.box}>\n                        {modelData.sections[0].cards[index]?.img && (\n                          <>\n                            <img\n                              src={modelData.sections[0].cards[index].img}\n                              alt=\"\"\n                            />\n                            {(modelData.sections[0].cards[index].modalTitle ||\n                              modelData.sections[0].cards[index].modalText) && (\n                              <div\n                                className={styles.plus}\n                                onClick={() =>\n                                  openModal(\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle,\n                                    modelData.sections[0].cards[index].modalText\n                                  )\n                                }\n                              >\n                                +\n                              </div>\n                            )}\n                            {modelData.sections[0].cards[index].modalTitle && (\n                              <div className={styles.overlay}>\n                                <span>\n                                  {\n                                    modelData.sections[0].cards[index]\n                                      .modalTitle\n                                  }\n                                </span>\n                              </div>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                <div className={styles.leftCard}>\n                  {modelData.sections[0].cards[5]?.img && (\n                    <>\n                      <img src={modelData.sections[0].cards[5].img} alt=\"\" />\n                      {(modelData.sections[0].cards[5].modalTitle ||\n                        modelData.sections[0].cards[5].modalText) && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[0].cards[5].modalTitle,\n                              modelData.sections[0].cards[5].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[0].cards[5].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[0].cards[5].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </>\n                  )}\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* section 2 динамически на основе modelData.sections[1]/*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className={styles.flex}>\n                <div className={styles.left}>\n                  {modelData.sections[1].img && (\n                    <img src={modelData.sections[1].img} alt=\"\" />\n                  )}\n                </div>\n                <div className={styles.right}>\n                  {modelData.sections[1].title && (\n                    <h2>{modelData.sections[1].title}</h2>\n                  )}\n                  {modelData.sections[1].description && (\n                    <p>{modelData.sections[1].description}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* section 3 динамически на основе modelData.sections[2]*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div>\n                {modelData.sections[2].title && (\n                  <h2>{modelData.sections[2].title}</h2>\n                )}\n                {modelData.sections[2].description && (\n                  <p>{modelData.sections[2].description}</p>\n                )}\n              </div>\n              <div className={styles.cardContnet}>\n                {/* /right/ */}\n                {modelData.sections[2].cards[0] && (\n                  <div className={styles.right}>\n                    {modelData.sections[2].cards[0].img && (\n                      <img src={modelData.sections[2].cards[0].img} alt=\"\" />\n                    )}\n                    {modelData.sections[2].cards[0].modalText && (\n                      <div\n                        className={styles.plus}\n                        onClick={() =>\n                          openModal(\n                            modelData.sections[2].cards[0].modalTitle,\n                            modelData.sections[2].cards[0].modalText\n                          )\n                        }\n                      >\n                        +\n                      </div>\n                    )}\n                    {modelData.sections[2].cards[0].modalTitle && (\n                      <div className={styles.overlay}>\n                        <span>{modelData.sections[2].cards[0].modalTitle}</span>\n                      </div>\n                    )}\n                  </div>\n                )}\n                {/* /middle/ */}\n                <div className={styles.middle}>\n                  <div className={styles.top}>\n                    {modelData.sections[2].cards[1].img && (\n                      <div className={styles.topRight}>\n                        {modelData.sections[2].cards[1].img && (\n                          <img\n                            src={modelData.sections[2].cards[1].img}\n                            alt=\"\"\n                          />\n                        )}\n                        {modelData.sections[2].cards[1].modalText && (\n                          <div\n                            className={styles.plus}\n                            onClick={() =>\n                              openModal(\n                                modelData.sections[2].cards[1].modalTitle,\n                                modelData.sections[2].cards[1].modalText\n                              )\n                            }\n                          >\n                            +\n                          </div>\n                        )}\n                        {modelData.sections[2].cards[1].modalTitle && (\n                          <div className={styles.overlay}>\n                            <span>\n                              {modelData.sections[2].cards[1].modalTitle}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                    <div className={styles.topLeft}>\n                      {modelData.sections[2].cards[2].img && (\n                        <img src={modelData.sections[2].cards[2].img} alt=\"\" />\n                      )}\n                      {modelData.sections[2].cards[2].modalText && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[2].cards[2].modalTitle,\n                              modelData.sections[2].cards[2].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[2].cards[2].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[2].cards[2].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  <div className={styles.buttom}>\n                    <div className={styles.box}>\n                      {modelData.sections[2].cards[3].img && (\n                        <img src={modelData.sections[2].cards[3].img} alt=\"\" />\n                      )}\n                      {modelData.sections[2].cards[3].modalText && (\n                        <div\n                          className={styles.plus}\n                          onClick={() =>\n                            openModal(\n                              modelData.sections[2].cards[3].modalTitle,\n                              modelData.sections[2].cards[3].modalText\n                            )\n                          }\n                        >\n                          +\n                        </div>\n                      )}\n                      {modelData.sections[2].cards[3].modalTitle && (\n                        <div className={styles.overlay}>\n                          <span>\n                            {modelData.sections[2].cards[3].modalTitle}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                {/* left  */}\n                {modelData.sections[2].cards[4] && (\n                  <div className={styles.left}>\n                    {modelData.sections[2].cards[4].img && (\n                      <img src={modelData.sections[2].cards[4].img} alt=\"\" />\n                    )}\n                    {modelData.sections[2].cards[4].modalText && (\n                      <div\n                        className={styles.plus}\n                        onClick={() =>\n                          openModal(\n                            modelData.sections[2].cards[4].modalTitle,\n                            modelData.sections[2].cards[4].modalText\n                          )\n                        }\n                      >\n                        +\n                      </div>\n                    )}\n                    {modelData.sections[2].cards[4].modalTitle && (\n                      <div className={styles.overlay}>\n                        <span>{modelData.sections[2].cards[4].modalTitle}</span>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          </section>\n\n          {/* section 4 динамически на основе modelData.sections[3]/*/}\n          <section className={styles.section}>\n            <div className=\"container\">\n              <div className={styles.contant}>\n                <div className={styles.text}>\n                  {modelData.sections[3].title && (\n                    <h2>{modelData.sections[3].title}</h2>\n                  )}\n                  {modelData.sections[3].description && (\n                    <p>{modelData.sections[3].description}</p>\n                  )}\n                  {modelData.sections[3].brochure && (\n                    <a\n                      href={modelData.sections[3].brochure}\n                      className={styles.dowBtn}\n                      target=\"_blank\"\n                    >\n                      <span>СКАЧАТЬ БРОШЮРУ</span>\n                      <img src={icon} alt=\"\" />\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n            {modelData.sections[3].img && (\n              <div\n                className={styles.bgSection}\n                style={{\n                  backgroundImage: `url(${modelData.sections[3].img})`,\n                }}\n              >\n                <h2 data-aos=\"fade-up\" className={styles.title}>\n                  {modelData.sections[3].imgTitle}\n                </h2>\n              </div>\n            )}\n          </section>\n\n          {/* Specifications */}\n          <Specifications\n            title={modelData.specifications.title}\n            desc={modelData.specifications.description}\n            tabData={tabData}\n            tableData={modelData.specifications.data}\n          />\n\n          {/* Purchase request form */}\n          <section className=\"container\">\n            <Form\n              formType=\"purchase\"\n              title=\"Оставить запрос на покупку\"\n              defaultModel={slug}\n            />\n          </section>\n\n          {/* Modal */}\n          {modalContent && (\n            <Modal\n              title={modalContent.title}\n              desc={modalContent.desc}\n              onClose={() => setModalContent(null)}\n            />\n          )}\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default DynamicModelPage;\n"], "mappings": "yGAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OAASC,YAAY,KAAQ,8BAA8B,CAC3D,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,IAAI,KAAM,2CAA2C,CAC5D,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAEzB;AACA,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CAEzD;AACA,MAAO,CAAAC,GAAG,KAAM,0BAA0B,CAC1C,MAAO,CAAAC,IAAI,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpD,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAC7B,KAAM,CAAEC,IAAK,CAAC,CAAGpB,SAAS,CAAC,CAAC,CAC5B,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwB,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC0B,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC4B,YAAY,CAAEC,eAAe,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAExCD,SAAS,CAAC,IAAM,CACdwB,UAAU,CAAC,IAAI,CAAC,CAAE;AAClBjB,GAAG,CAAC0B,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BnB,UAAU,CAAC,KAAK,CAAC,CACjBc,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,CAACnB,IAAI,CAAC,CAAC,CAEVtB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6C,aAAa,CAAGA,CAAA,GAAM,CAC1BrB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAsB,IAAI,CAAG1C,YAAY,CAACkB,IAAI,CAAC,CAC/B,GAAI,CAACwB,IAAI,CAAE,CACTd,QAAQ,CAAC,mBAAmB,CAAC,CAC7BR,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACAE,YAAY,CAACoB,IAAI,CAAC,CAClB,GAAIA,IAAI,CAACC,MAAM,EAAID,IAAI,CAACC,MAAM,CAACC,MAAM,CAAG,CAAC,CAAE,CACzCpB,gBAAgB,CAACkB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAClC,CACAf,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,MAAOiB,GAAG,CAAE,CACZC,OAAO,CAACnB,KAAK,CAAC,4BAA4B,CAAEkB,GAAG,CAAC,CAChDjB,QAAQ,CAAC,mBAAmB,CAAC,CAC/B,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIF,IAAI,CAAE,CACRuB,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,CAACvB,IAAI,CAAC,CAAC,CAEV,KAAM,CAAA6B,iBAAiB,CAAIC,KAAK,EAAK,CACnCxB,gBAAgB,CAACwB,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGA,CAACC,KAAK,CAAEC,IAAI,GAAK,CACjCzB,eAAe,CAAC,CAAEwB,KAAK,CAAEC,IAAK,CAAC,CAAC,CAClC,CAAC,CAED,GAAIxB,KAAK,EAAI,CAACN,SAAS,CAAE,CACvB,mBACEZ,IAAA,QAAK2C,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1C,KAAA,QAAKyC,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB5C,IAAA,OAAI2C,SAAS,CAAC,OAAO,CAAAC,QAAA,CAAC,8FAAiB,CAAI,CAAC,cAC5C5C,IAAA,MAAA4C,QAAA,CAAG,6OAA6C,CAAG,CAAC,EACjD,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,KAAM,CAAAC,OAAO,CAAG,CACdC,OAAO,CAAElC,SAAS,CAACmC,cAAc,CAACC,KAAK,CACvCC,QAAQ,CAAErC,SAAS,CAACmC,cAAc,CAACG,UACrC,CAAC,CAED,mBACEhD,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,CAACH,GAAG,CAAAsD,aAAA,IAAKvC,SAAS,CAACwC,GAAG,CAAG,CAAC,CACzB1C,OAAO,cACNV,IAAA,QAAK2C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5C,IAAA,QAAK2C,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENzC,KAAA,QAAKyC,SAAS,CAAC,SAAS,CAAAC,QAAA,eAEtB5C,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAAC6D,MAAO,CACzB1B,KAAK,CAAE,CACL2B,eAAe,CAAE1C,SAAS,SAATA,SAAS,YAAAN,eAAA,CAATM,SAAS,CAAE2C,IAAI,UAAAjD,eAAA,WAAfA,eAAA,CAAiBkD,KAAK,QAAAC,MAAA,CAC5B7C,SAAS,CAAC2C,IAAI,CAACC,KAAK,MAC3B,MAAM,CACVE,eAAe,CAAE9C,SAAS,SAATA,SAAS,YAAAL,gBAAA,CAATK,SAAS,CAAE2C,IAAI,UAAAhD,gBAAA,WAAfA,gBAAA,CAAiBiD,KAAK,CAAG,aAAa,CAAG,MAAM,CAAE;AAClEG,cAAc,CAAE,OAAO,CACvBC,kBAAkB,CAAE,QACtB,CAAE,CAAAhB,QAAA,cAEF5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAACqE,YAAa,CAAAjB,QAAA,EACjChC,SAAS,CAAC2C,IAAI,CAACO,MAAM,eACpB9D,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuE,QAAS,CAAC,WAAS,SAAS,CAAAnB,QAAA,CAChDhC,SAAS,CAAC2C,IAAI,CAACO,MAAM,CACnB,CACN,CACAlD,SAAS,CAAC2C,IAAI,CAACd,KAAK,eACnBzC,IAAA,OAAI,WAAS,SAAS,CAAA4C,QAAA,CAAEhC,SAAS,CAAC2C,IAAI,CAACd,KAAK,CAAK,CAClD,CACA7B,SAAS,CAAC2C,IAAI,CAACS,KAAK,eACnBhE,IAAA,OAAI2C,SAAS,CAAEnD,MAAM,CAACwE,KAAM,CAAC,WAAS,SAAS,CAAApB,QAAA,CAC5ChC,SAAS,CAAC2C,IAAI,CAACS,KAAK,CACnB,CACL,CACApD,SAAS,CAAC2C,IAAI,CAACU,QAAQ,eACtBjE,IAAA,SAAM2C,SAAS,CAAEnD,MAAM,CAAC0E,KAAM,CAAC,WAAS,SAAS,CAAAtB,QAAA,CAC9ChC,SAAS,CAAC2C,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC,CACvB,CACP,cACDjE,IAAA,CAACV,IAAI,EAAC6E,EAAE,CAAC,oBAAoB,CAAC,WAAS,SAAS,CAAAvB,QAAA,cAC9C5C,IAAA,WAAQ2C,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC9B5C,IAAA,SAAA4C,QAAA,CAAM,yDAAU,CAAM,CAAC,CACjB,CAAC,CACL,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CAAC,cAGN5C,IAAA,YAAS2C,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,cACjC5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB5C,IAAA,UAAA4C,QAAA,CAAO,0wCAMP,CAAO,CAAC,CACL,CAAC,CACC,CAAC,cAEV5C,IAAA,YAAS2C,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,cACjC1C,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB5C,IAAA,OACE2C,SAAS,CAAEnD,MAAM,CAAC6E,UAAW,CAC7B1C,KAAK,CAAE,CAAEY,KAAK,CAAEzB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEwD,QAAQ,CAAEC,UAAU,CAAE,KAAM,CAAE,CAC7D,WAAS,SAAS,CAAA3B,QAAA,CAEjB9B,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE0D,IAAI,CAClB,CAAC,cAELxE,IAAA,QAAA4C,QAAA,cACE5C,IAAA,QACEyE,GAAG,CAAE3D,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE0C,KAAM,CAC1BkB,GAAG,CAAE5D,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE0D,IAAK,CACzB7B,SAAS,CAAEnD,MAAM,CAACmF,UAAW,CAC9B,CAAC,CACC,CAAC,cAEN3E,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACoF,kBAAmB,CAAAhC,QAAA,CACvChC,SAAS,CAACsB,MAAM,CAAC2C,GAAG,CAAEtC,KAAK,eAC1BvC,IAAA,WAEE8E,OAAO,CAAEA,CAAA,GAAMxC,iBAAiB,CAACC,KAAK,CAAE,CACxCI,SAAS,IAAAc,MAAA,CAAKjE,MAAM,CAACuF,QAAQ,MAAAtB,MAAA,CAC3B,CAAA3C,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEkE,EAAE,IAAKzC,KAAK,CAACyC,EAAE,CAAGxF,MAAM,CAACyF,MAAM,CAAG,EAAE,CAClD,CACHtD,KAAK,CAAE,CAAE+B,eAAe,CAAEnB,KAAK,CAAC+B,QAAS,CAAE,CAAA1B,QAAA,CAE1C,CAAA9B,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEkE,EAAE,IAAKzC,KAAK,CAACyC,EAAE,CAAGzC,KAAK,CAACiC,IAAI,CAAG,EAAE,EAP5CjC,KAAK,CAACyC,EAQL,CACT,CAAC,CACC,CAAC,EACH,CAAC,CACC,CAAC,cAGVhF,IAAA,YAAS2C,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,cACjC1C,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1C,KAAA,QAAKyC,SAAS,CAAC,OAAO,CAAAC,QAAA,EACnBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,eAC1BzC,IAAA,OAAA4C,QAAA,CAAKhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAK,CACtC,CACA7B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,eAChCnF,IAAA,MAAA4C,QAAA,CAAIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,CAAI,CAC1C,EACE,CAAC,cACNjF,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC4F,WAAY,CAAAxC,QAAA,eACjC1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC6F,SAAU,CAAAzC,QAAA,eAC/B5C,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAAC8F,GAAI,CAAA1C,QAAA,CACxB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACiC,GAAG,CAAEU,KAAK,OAAAC,oBAAA,oBACnBxF,IAAA,QAAiB2C,SAAS,CAAEnD,MAAM,CAACiG,GAAI,CAAA7C,QAAA,CACpC,EAAA4C,oBAAA,CAAA5E,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,UAAAC,oBAAA,iBAAlCA,oBAAA,CAAoCG,GAAG,gBACtCzF,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,QACEyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACI,GAAI,CAC5CjB,GAAG,CAAC,EAAE,CACP,CAAC,CACD,CAAC9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,EAC7ChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SAAS,gBAC5C7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,CACbhF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SACrC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,eAC5C5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CAEIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,CAEX,CAAC,CACJ,CACN,EACD,CACH,EAjCOL,KAkCL,CAAC,EACP,CAAC,CACC,CAAC,cACNvF,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACwG,MAAO,CAAApD,QAAA,CAC3B,CAAC,CAAC,CAAE,CAAC,CAAC,CAACiC,GAAG,CAAEU,KAAK,OAAAU,qBAAA,oBAChBjG,IAAA,QAAiB2C,SAAS,CAAEnD,MAAM,CAACiG,GAAI,CAAA7C,QAAA,CACpC,EAAAqD,qBAAA,CAAArF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,UAAAU,qBAAA,iBAAlCA,qBAAA,CAAoCN,GAAG,gBACtCzF,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,QACEyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACI,GAAI,CAC5CjB,GAAG,CAAC,EAAE,CACP,CAAC,CACD,CAAC9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,EAC7ChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SAAS,gBAC5C7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,CACbhF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACM,SACrC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAACK,UAAU,eAC5C5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CAEIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACH,KAAK,CAAC,CAC/BK,UAAU,CAEX,CAAC,CACJ,CACN,EACD,CACH,EAjCOL,KAkCL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,cACNvF,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAAC0G,QAAS,CAAAtD,QAAA,CAC7B,EAAApC,qBAAA,CAAAI,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,UAAAlF,qBAAA,iBAA9BA,qBAAA,CAAgCmF,GAAG,gBAClCzF,KAAA,CAAAE,SAAA,EAAAwC,QAAA,eACE5C,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CAAC,CACtD,CAAC9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,EACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,gBACxC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CACGhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACtC,CAAC,CACJ,CACN,EACD,CACH,CACE,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGV5F,IAAA,YAAS2C,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,cACjC5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC2G,IAAK,CAAAvD,QAAA,eAC1B5C,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAAC4G,IAAK,CAAAxD,QAAA,CACzBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG,eACxB3F,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CAC9C,CACE,CAAC,cACNxE,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC6G,KAAM,CAAAzD,QAAA,EAC1BhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,eAC1BzC,IAAA,OAAA4C,QAAA,CAAKhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAK,CACtC,CACA7B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,eAChCnF,IAAA,MAAA4C,QAAA,CAAIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,CAAI,CAC1C,EACE,CAAC,EACH,CAAC,CACH,CAAC,CACC,CAAC,cAGVnF,IAAA,YAAS2C,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,cACjC1C,KAAA,QAAKyC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1C,KAAA,QAAA0C,QAAA,EACGhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,eAC1BzC,IAAA,OAAA4C,QAAA,CAAKhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAK,CACtC,CACA7B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,eAChCnF,IAAA,MAAA4C,QAAA,CAAIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,CAAI,CAC1C,EACE,CAAC,cACNjF,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC4F,WAAY,CAAAxC,QAAA,EAEhChC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,eAC7BxF,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC6G,KAAM,CAAAzD,QAAA,EAC1BhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjC3F,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CACvD,CACA9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,eACvC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CAAOhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CAAO,CAAC,CACrD,CACN,EACE,CACN,cAED1F,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC8G,MAAO,CAAA1D,QAAA,eAC5B1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC8F,GAAI,CAAA1C,QAAA,EACxBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjCzF,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC+G,QAAS,CAAA3D,QAAA,EAC7BhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjC3F,IAAA,QACEyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CACxCjB,GAAG,CAAC,EAAE,CACP,CACF,CACA9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,eACvC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CACGhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACtC,CAAC,CACJ,CACN,EACE,CACN,cACD1F,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAACgH,OAAQ,CAAA5D,QAAA,EAC5BhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjC3F,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CACvD,CACA9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,eACvC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CACGhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACtC,CAAC,CACJ,CACN,EACE,CAAC,EACH,CAAC,cACN5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACiH,MAAO,CAAA7D,QAAA,cAC5B1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAACiG,GAAI,CAAA7C,QAAA,EACxBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjC3F,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CACvD,CACA9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,eACvC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CACGhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACtC,CAAC,CACJ,CACN,EACE,CAAC,CACH,CAAC,EACH,CAAC,CAELhF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,eAC7BxF,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAAC4G,IAAK,CAAAxD,QAAA,EACzBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,eACjC3F,IAAA,QAAKyE,GAAG,CAAE7D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACC,GAAI,CAACjB,GAAG,CAAC,EAAE,CAAE,CACvD,CACA9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SAAS,eACvC7F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACsG,IAAK,CACvBhB,OAAO,CAAEA,CAAA,GACPtC,SAAS,CACP5B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CACzChF,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACG,SACjC,CACD,CAAAjD,QAAA,CACF,GAED,CAAK,CACN,CACAhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,eACxC5F,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACuG,OAAQ,CAAAnD,QAAA,cAC7B5C,IAAA,SAAA4C,QAAA,CAAOhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACE,UAAU,CAAO,CAAC,CACrD,CACN,EACE,CACN,EACE,CAAC,EACH,CAAC,CACC,CAAC,cAGV1F,KAAA,YAASyC,SAAS,CAAEnD,MAAM,CAAC4E,OAAQ,CAAAxB,QAAA,eACjC5C,IAAA,QAAK2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB5C,IAAA,QAAK2C,SAAS,CAAEnD,MAAM,CAACkH,OAAQ,CAAA9D,QAAA,cAC7B1C,KAAA,QAAKyC,SAAS,CAAEnD,MAAM,CAACmH,IAAK,CAAA/D,QAAA,EACzBhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,eAC1BzC,IAAA,OAAA4C,QAAA,CAAKhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAK,CACtC,CACA7B,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,eAChCnF,IAAA,MAAA4C,QAAA,CAAIhC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACC,WAAW,CAAI,CAC1C,CACAvE,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAAC0B,QAAQ,eAC7B1G,KAAA,MACE2G,IAAI,CAAEjG,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAAC0B,QAAS,CACrCjE,SAAS,CAAEnD,MAAM,CAACsH,MAAO,CACzBC,MAAM,CAAC,QAAQ,CAAAnE,QAAA,eAEf5C,IAAA,SAAA4C,QAAA,CAAM,uFAAe,CAAM,CAAC,cAC5B5C,IAAA,QAAKyE,GAAG,CAAEhF,IAAK,CAACiF,GAAG,CAAC,EAAE,CAAE,CAAC,EACxB,CACJ,EACE,CAAC,CACH,CAAC,CACH,CAAC,CACL9D,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG,eACxB3F,IAAA,QACE2C,SAAS,CAAEnD,MAAM,CAACwH,SAAU,CAC5BrF,KAAK,CAAE,CACL2B,eAAe,QAAAG,MAAA,CAAS7C,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAACS,GAAG,KACnD,CAAE,CAAA/C,QAAA,cAEF5C,IAAA,OAAI,WAAS,SAAS,CAAC2C,SAAS,CAAEnD,MAAM,CAACiD,KAAM,CAAAG,QAAA,CAC5ChC,SAAS,CAACsE,QAAQ,CAAC,CAAC,CAAC,CAAC+B,QAAQ,CAC7B,CAAC,CACF,CACN,EACM,CAAC,cAGVjH,IAAA,CAACJ,cAAc,EACb6C,KAAK,CAAE7B,SAAS,CAACmC,cAAc,CAACN,KAAM,CACtCC,IAAI,CAAE9B,SAAS,CAACmC,cAAc,CAACoC,WAAY,CAC3CtC,OAAO,CAAEA,OAAQ,CACjBqE,SAAS,CAAEtG,SAAS,CAACmC,cAAc,CAACd,IAAK,CAC1C,CAAC,cAGFjC,IAAA,YAAS2C,SAAS,CAAC,WAAW,CAAAC,QAAA,cAC5B5C,IAAA,CAACF,IAAI,EACHqH,QAAQ,CAAC,UAAU,CACnB1E,KAAK,CAAC,+IAA4B,CAClC2E,YAAY,CAAE3G,IAAK,CACpB,CAAC,CACK,CAAC,CAGTO,YAAY,eACXhB,IAAA,CAACL,KAAK,EACJ8C,KAAK,CAAEzB,YAAY,CAACyB,KAAM,CAC1BC,IAAI,CAAE1B,YAAY,CAAC0B,IAAK,CACxB2E,OAAO,CAAEA,CAAA,GAAMpG,eAAe,CAAC,IAAI,CAAE,CACtC,CACF,EACE,CACN,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}