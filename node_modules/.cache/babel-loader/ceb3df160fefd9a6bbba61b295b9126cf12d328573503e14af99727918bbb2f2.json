{"ast": null, "code": "import { isPrimaryPointer } from 'motion-dom';\nfunction extractEventInfo(event) {\n  return {\n    point: {\n      x: event.pageX,\n      y: event.pageY\n    }\n  };\n}\nconst addPointerInfo = handler => {\n  return event => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\nexport { addPointerInfo, extractEventInfo };", "map": {"version": 3, "names": ["isPrimaryPointer", "extractEventInfo", "event", "point", "x", "pageX", "y", "pageY", "addPointerInfo", "handler"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/events/event-info.mjs"], "sourcesContent": ["import { isPrimaryPointer } from 'motion-dom';\n\nfunction extractEventInfo(event) {\n    return {\n        point: {\n            x: event.pageX,\n            y: event.pageY,\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\n\nexport { addPointerInfo, extractEventInfo };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC7B,OAAO;IACHC,KAAK,EAAE;MACHC,CAAC,EAAEF,KAAK,CAACG,KAAK;MACdC,CAAC,EAAEJ,KAAK,CAACK;IACb;EACJ,CAAC;AACL;AACA,MAAMC,cAAc,GAAIC,OAAO,IAAK;EAChC,OAAQP,KAAK,IAAKF,gBAAgB,CAACE,KAAK,CAAC,IAAIO,OAAO,CAACP,KAAK,EAAED,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACxF,CAAC;AAED,SAASM,cAAc,EAAEP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}