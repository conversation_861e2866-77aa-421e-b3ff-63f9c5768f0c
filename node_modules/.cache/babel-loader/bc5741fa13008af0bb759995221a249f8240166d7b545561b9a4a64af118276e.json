{"ast": null, "code": "const namedEdges = {\n  start: 0,\n  center: 0.5,\n  end: 1\n};\nfunction resolveEdge(edge, length) {\n  let inset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let delta = 0;\n  /**\n   * If we have this edge defined as a preset, replace the definition\n   * with the numerical value.\n   */\n  if (edge in namedEdges) {\n    edge = namedEdges[edge];\n  }\n  /**\n   * Handle unit values\n   */\n  if (typeof edge === \"string\") {\n    const asNumber = parseFloat(edge);\n    if (edge.endsWith(\"px\")) {\n      delta = asNumber;\n    } else if (edge.endsWith(\"%\")) {\n      edge = asNumber / 100;\n    } else if (edge.endsWith(\"vw\")) {\n      delta = asNumber / 100 * document.documentElement.clientWidth;\n    } else if (edge.endsWith(\"vh\")) {\n      delta = asNumber / 100 * document.documentElement.clientHeight;\n    } else {\n      edge = asNumber;\n    }\n  }\n  /**\n   * If the edge is defined as a number, handle as a progress value.\n   */\n  if (typeof edge === \"number\") {\n    delta = length * edge;\n  }\n  return inset + delta;\n}\nexport { namedEdges, resolveEdge };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "start", "center", "end", "resolveEdge", "edge", "length", "inset", "arguments", "undefined", "delta", "asNumber", "parseFloat", "endsWith", "document", "documentElement", "clientWidth", "clientHeight"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs"], "sourcesContent": ["const namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\nexport { namedEdges, resolveEdge };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE;AACT,CAAC;AACD,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAa;EAAA,IAAXC,KAAK,GAAAC,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC;EACxC,IAAIE,KAAK,GAAG,CAAC;EACb;AACJ;AACA;AACA;EACI,IAAIL,IAAI,IAAIL,UAAU,EAAE;IACpBK,IAAI,GAAGL,UAAU,CAACK,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;EACI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAMM,QAAQ,GAAGC,UAAU,CAACP,IAAI,CAAC;IACjC,IAAIA,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrBH,KAAK,GAAGC,QAAQ;IACpB,CAAC,MACI,IAAIN,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzBR,IAAI,GAAGM,QAAQ,GAAG,GAAG;IACzB,CAAC,MACI,IAAIN,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BH,KAAK,GAAIC,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACC,WAAW;IACnE,CAAC,MACI,IAAIX,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BH,KAAK,GAAIC,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACE,YAAY;IACpE,CAAC,MACI;MACDZ,IAAI,GAAGM,QAAQ;IACnB;EACJ;EACA;AACJ;AACA;EACI,IAAI,OAAON,IAAI,KAAK,QAAQ,EAAE;IAC1BK,KAAK,GAAGJ,MAAM,GAAGD,IAAI;EACzB;EACA,OAAOE,KAAK,GAAGG,KAAK;AACxB;AAEA,SAASV,UAAU,EAAEI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}