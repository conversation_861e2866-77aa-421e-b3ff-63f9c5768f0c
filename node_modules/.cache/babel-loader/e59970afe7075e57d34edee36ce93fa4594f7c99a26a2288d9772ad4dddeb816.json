{"ast": null, "code": "import { useRef as r, use<PERSON>emo as e } from \"react\";\nimport { createProxy as t } from \"@react-input/core\";\nimport a from \"./Mask.js\";\nfunction n() {\n  var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},\n    c = n.mask,\n    o = n.replacement,\n    m = n.showMask,\n    s = n.separate,\n    u = n.track,\n    p = n.modify,\n    i = r(null),\n    k = r({\n      mask: c,\n      replacement: o,\n      showMask: m,\n      separate: s,\n      track: u,\n      modify: p\n    });\n  return k.current.mask = c, k.current.replacement = o, k.current.showMask = m, k.current.separate = s, k.current.track = u, k.current.modify = p, e(function () {\n    return t(i, new a(k.current));\n  }, []);\n}\nexport { n as default };", "map": {"version": 3, "names": ["useRef", "r", "useMemo", "e", "createProxy", "t", "a", "n", "arguments", "length", "c", "mask", "o", "replacement", "m", "showMask", "s", "separate", "u", "track", "p", "modify", "i", "k", "current", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/@react-input/mask/module/useMask.js"], "sourcesContent": ["import{useRef as r,use<PERSON>emo as e}from\"react\";import{createProxy as t}from\"@react-input/core\";import a from\"./Mask.js\";function n(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},c=n.mask,o=n.replacement,m=n.showMask,s=n.separate,u=n.track,p=n.modify,i=r(null),k=r({mask:c,replacement:o,showMask:m,separate:s,track:u,modify:p});return k.current.mask=c,k.current.replacement=o,k.current.showMask=m,k.current.separate=s,k.current.track=u,k.current.modify=p,e((function(){return t(i,new a(k.current))}),[])}export{n as default};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,mBAAmB;AAAC,OAAOC,CAAC,MAAK,WAAW;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIA,CAAC,GAACC,SAAS,CAACC,MAAM,GAAC,CAAC,IAAE,KAAK,CAAC,KAAGD,SAAS,CAAC,CAAC,CAAC,GAACA,SAAS,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;IAACE,CAAC,GAACH,CAAC,CAACI,IAAI;IAACC,CAAC,GAACL,CAAC,CAACM,WAAW;IAACC,CAAC,GAACP,CAAC,CAACQ,QAAQ;IAACC,CAAC,GAACT,CAAC,CAACU,QAAQ;IAACC,CAAC,GAACX,CAAC,CAACY,KAAK;IAACC,CAAC,GAACb,CAAC,CAACc,MAAM;IAACC,CAAC,GAACrB,CAAC,CAAC,IAAI,CAAC;IAACsB,CAAC,GAACtB,CAAC,CAAC;MAACU,IAAI,EAACD,CAAC;MAACG,WAAW,EAACD,CAAC;MAACG,QAAQ,EAACD,CAAC;MAACG,QAAQ,EAACD,CAAC;MAACG,KAAK,EAACD,CAAC;MAACG,MAAM,EAACD;IAAC,CAAC,CAAC;EAAC,OAAOG,CAAC,CAACC,OAAO,CAACb,IAAI,GAACD,CAAC,EAACa,CAAC,CAACC,OAAO,CAACX,WAAW,GAACD,CAAC,EAACW,CAAC,CAACC,OAAO,CAACT,QAAQ,GAACD,CAAC,EAACS,CAAC,CAACC,OAAO,CAACP,QAAQ,GAACD,CAAC,EAACO,CAAC,CAACC,OAAO,CAACL,KAAK,GAACD,CAAC,EAACK,CAAC,CAACC,OAAO,CAACH,MAAM,GAACD,CAAC,EAACjB,CAAC,CAAE,YAAU;IAAC,OAAOE,CAAC,CAACiB,CAAC,EAAC,IAAIhB,CAAC,CAACiB,CAAC,CAACC,OAAO,CAAC,CAAC;EAAA,CAAC,EAAE,EAAE,CAAC;AAAA;AAAC,SAAOjB,CAAC,IAAIkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}