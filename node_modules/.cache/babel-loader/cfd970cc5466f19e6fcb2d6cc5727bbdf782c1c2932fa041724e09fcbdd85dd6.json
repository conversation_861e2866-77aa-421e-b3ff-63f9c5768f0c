{"ast": null, "code": "import { pxValues } from '../../waapi/utils/px-values.mjs';\nfunction applyPxDefaults(keyframes, name) {\n  for (let i = 0; i < keyframes.length; i++) {\n    if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n      keyframes[i] = keyframes[i] + \"px\";\n    }\n  }\n}\nexport { applyPxDefaults };", "map": {"version": 3, "names": ["px<PERSON><PERSON><PERSON>", "applyPxDefaults", "keyframes", "name", "i", "length", "has"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/keyframes/utils/apply-px-defaults.mjs"], "sourcesContent": ["import { pxValues } from '../../waapi/utils/px-values.mjs';\n\nfunction applyPxDefaults(keyframes, name) {\n    for (let i = 0; i < keyframes.length; i++) {\n        if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n            keyframes[i] = keyframes[i] + \"px\";\n        }\n    }\n}\n\nexport { applyPxDefaults };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iCAAiC;AAE1D,SAASC,eAAeA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAI,OAAOF,SAAS,CAACE,CAAC,CAAC,KAAK,QAAQ,IAAIJ,QAAQ,CAACM,GAAG,CAACH,IAAI,CAAC,EAAE;MACxDD,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,CAAC,GAAG,IAAI;IACtC;EACJ;AACJ;AAEA,SAASH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}