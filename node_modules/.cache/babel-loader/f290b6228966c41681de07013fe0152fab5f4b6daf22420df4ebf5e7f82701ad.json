{"ast": null, "code": "// for navFeatureData\nimport navFeatureIconWhite1 from '../imgs/icons/icon3-black.svg';\nimport navFeatureIconWhite2 from '../imgs/icons/icon1-black.svg';\nimport { RiGlobalLine } from 'react-icons/ri';\nimport { GrLanguage } from 'react-icons/gr';\nexport const menuData = [{\n  id: 1,\n  title: 'Модели',\n  dropMenu: true\n}, {\n  id: 2,\n  title: 'Предложение',\n  url: '/offers',\n  dropMenu: false\n}, {\n  id: 3,\n  title: 'Владельцам',\n  url: '/owners',\n  dropMenu: false\n}, {\n  id: 4,\n  title: 'Узнать больше',\n  dropMenu: true\n}];\nexport const navFeatureData = [{\n  id: 1,\n  title: 'Свяжитесь с нами',\n  icon: navFeatureIconWhite1,\n  url: '/contact'\n}, {\n  id: 2,\n  title: 'Записаться на тест-драйв',\n  icon: navFeatureIconWhite2,\n  url: '/book-a-test-drive'\n}\n// {\n//   id: 3,\n//   title: 'RU',\n//   dobleTitle: 'TJ',\n//   url: false,\n// },\n];\n\n// navbarData.js\n\nexport const discoverMenuData = [{\n  id: 1,\n  title: 'GWM Глобальный',\n  items: [{\n    id: 1,\n    title: 'Новости',\n    url: '/news-list'\n  }, {\n    id: 2,\n    title: 'О GWM',\n    url: '/about-gwm'\n  }, {\n    id: 3,\n    title: 'История',\n    url: '/about-gwm/history'\n  }, {\n    id: 4,\n    title: 'Контакты',\n    url: '/contact'\n  }, {\n    id: 5,\n    title: 'Карьера',\n    target_url: true,\n    url: 'https://job.vector.tj/'\n  }]\n}];", "map": {"version": 3, "names": ["navFeatureIconWhite1", "navFeatureIconWhite2", "RiGlobalLine", "GrLanguage", "menuData", "id", "title", "dropMenu", "url", "navFeatureData", "icon", "discoverMenuData", "items", "target_url"], "sources": ["/var/www/html/gwm.tj/src/asset/data/navbarData.js"], "sourcesContent": ["// for navFeatureData\nimport navFeatureIconWhite1 from '../imgs/icons/icon3-black.svg';\nimport navFeatureIconWhite2 from '../imgs/icons/icon1-black.svg';\nimport { RiGlobalLine } from 'react-icons/ri';\nimport { GrLanguage } from 'react-icons/gr';\n\nexport const menuData = [\n  {\n    id: 1,\n    title: 'Модели',\n    dropMenu: true,\n  },\n  {\n    id: 2,\n    title: 'Предложение',\n    url: '/offers',\n    dropMenu: false,\n  },\n  {\n    id: 3,\n    title: 'Владельцам',\n    url: '/owners',\n    dropMenu: false,\n  },\n  {\n    id: 4,\n    title: 'Узнать больше',\n    dropMenu: true,\n  },\n];\n\nexport const navFeatureData = [\n  {\n    id: 1,\n    title: 'Свяжитесь с нами',\n    icon: navFeatureIconWhite1,\n    url: '/contact',\n  },\n  {\n    id: 2,\n    title: 'Записаться на тест-драйв',\n    icon: navFeatureIconWhite2,\n    url: '/book-a-test-drive',\n  },\n  // {\n  //   id: 3,\n  //   title: 'RU',\n  //   dobleTitle: 'TJ',\n  //   url: false,\n  // },\n];\n\n// navbarData.js\n\nexport const discoverMenuData = [\n  {\n    id: 1,\n    title: 'GWM Глобальный',\n    items: [\n      {\n        id: 1,\n        title: 'Новости',\n        url: '/news-list',\n      },\n      {\n        id: 2,\n        title: 'О GWM',\n        url: '/about-gwm',\n      },\n      {\n        id: 3,\n        title: 'История',\n        url: '/about-gwm/history',\n      },\n      {\n        id: 4,\n        title: 'Контакты',\n        url: '/contact',\n      },\n      {\n        id: 5,\n        title: 'Карьера',\n        target_url: true,\n        url: 'https://job.vector.tj/',\n      },\n    ],\n  },\n];\n"], "mappings": "AAAA;AACA,OAAOA,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;AAE3C,OAAO,MAAMC,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,aAAa;EACpBE,GAAG,EAAE,SAAS;EACdD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBE,GAAG,EAAE,SAAS;EACdD,QAAQ,EAAE;AACZ,CAAC,EACD;EACEF,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,OAAO,MAAME,cAAc,GAAG,CAC5B;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,kBAAkB;EACzBI,IAAI,EAAEV,oBAAoB;EAC1BQ,GAAG,EAAE;AACP,CAAC,EACD;EACEH,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,0BAA0B;EACjCI,IAAI,EAAET,oBAAoB;EAC1BO,GAAG,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD;;AAED;;AAEA,OAAO,MAAMG,gBAAgB,GAAG,CAC9B;EACEN,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,gBAAgB;EACvBM,KAAK,EAAE,CACL;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,OAAO;IACdE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,UAAU;IACjBE,GAAG,EAAE;EACP,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,SAAS;IAChBO,UAAU,EAAE,IAAI;IAChBL,GAAG,EAAE;EACP,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}