{"ast": null, "code": "import _objectSpread from\"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * API utility functions for secure HTTP requests\n */// Get API base URL from environment variables\nconst API_BASE_URL=process.env.REACT_APP_API_BASE_URL||'https://api.gwm.tj/api/v1';/**\n * Get CSRF token from meta tag or cookie\n * @returns {string|null} CSRF token\n */const getCSRFToken=()=>{var _document$querySelect;// Try to get from meta tag first\nconst metaToken=(_document$querySelect=document.querySelector('meta[name=\"csrf-token\"]'))===null||_document$querySelect===void 0?void 0:_document$querySelect.getAttribute('content');if(metaToken)return metaToken;// Try to get from cookie\nconst cookies=document.cookie.split(';');for(let cookie of cookies){const[name,value]=cookie.trim().split('=');if(name==='XSRF-TOKEN'){return decodeURIComponent(value);}}return null;};/**\n * Create default headers for API requests\n * @returns {Object} Default headers\n */const getDefaultHeaders=()=>{const headers={'Content-Type':'application/json','X-Requested-With':'XMLHttpRequest'};const csrfToken=getCSRFToken();if(csrfToken){headers['X-CSRF-TOKEN']=csrfToken;}return headers;};/**\n * Generic API request function with error handling and security\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise} API response\n */const apiRequest=async function(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=\"\".concat(API_BASE_URL).concat(endpoint);const defaultOptions=_objectSpread({headers:getDefaultHeaders(),credentials:'same-origin'},options);// Merge headers\nif(options.headers){defaultOptions.headers=_objectSpread(_objectSpread({},defaultOptions.headers),options.headers);}try{const response=await fetch(url,defaultOptions);// Handle different HTTP status codes\nif(!response.ok){const errorData=await response.json().catch(()=>({}));switch(response.status){case 400:throw new Error(errorData.message||'Проверьте правильность заполнения формы');case 401:throw new Error('Ошибка авторизации. Обновите страницу и попробуйте снова');case 403:throw new Error('Доступ запрещен');case 404:throw new Error('Запрашиваемый ресурс не найден');case 422:throw new Error(errorData.message||'Ошибка валидации данных');case 429:throw new Error('Слишком много запросов. Попробуйте позже');case 500:throw new Error('Ошибка сервера. Попробуйте позже');case 502:case 503:case 504:throw new Error('Сервис временно недоступен. Попробуйте позже');default:throw new Error(\"\\u041E\\u0448\\u0438\\u0431\\u043A\\u0430 \".concat(response.status,\": \").concat(errorData.message||'Неизвестная ошибка'));}}const contentType=response.headers.get('content-type');if(contentType&&contentType.includes('application/json')){return await response.json();}return response;}catch(error){// Log error for debugging (in development)\nif(process.env.NODE_ENV==='development'){console.error('API Request Error:',{url,options:defaultOptions,error:error.message,timestamp:new Date().toISOString()});}// Re-throw the error for handling by the calling code\nthrow error;}};/**\n * API request with retry logic for network errors\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @param {number} maxRetries - Maximum number of retries\n * @returns {Promise} API response\n */const apiRequestWithRetry=async function(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};let maxRetries=arguments.length>2&&arguments[2]!==undefined?arguments[2]:2;let lastError;for(let attempt=0;attempt<=maxRetries;attempt++){try{return await apiRequest(endpoint,options);}catch(error){lastError=error;// Only retry on network errors, not on HTTP errors\nif(error.name==='TypeError'&&attempt<maxRetries){// Wait before retrying (exponential backoff)\nconst delay=Math.min(1000*Math.pow(2,attempt),5000);await new Promise(resolve=>setTimeout(resolve,delay));continue;}// Don't retry on HTTP errors or if max retries reached\nbreak;}}throw lastError;};/**\n * Fetch car models from API\n * @returns {Promise<Array>} Array of car models\n */export const fetchModels=async()=>{try{const response=await apiRequestWithRetry('/models');return response.models||[];}catch(error){console.error('Failed to fetch models:',error);throw new Error('Не удалось загрузить список моделей. Попробуйте обновить страницу.');}};/**\n * Submit feedback form to GWM API\n * @param {Object} formData - Form data to submit\n * @param {string} formData.firstName - User's first name\n * @param {string} formData.lastName - User's last name (optional)\n * @param {string} formData.phone - User's phone number\n * @param {string} formData.topic - Selected car model\n * @param {string} formData.message - User's message (optional)\n * @param {string} formData.formType - Type of form (default: 'contact')\n * @returns {Promise<Object>} API response\n */export const submitFeedback=async formData=>{try{// Prepare payload according to API specification\nconst payload={formid:generateFormId(),// Generate unique form ID\nform_name:formData.formType||'contact',// Form type identifier\nname:\"\".concat(formData.firstName).concat(formData.lastName?' '+formData.lastName:'').trim(),phone:formData.phone.replace(/[^\\d+]/g,''),model:formData.topic||'',// Selected car model\nmessage:formData.message||''// Optional message\n};console.log(payload);const response=await apiRequestWithRetry('/feedback',{method:'POST',headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify(payload)});return response;}catch(error){console.error('Failed to submit feedback:',error);throw error;// Re-throw to let the component handle the specific error message\n}};/**\n * Generate unique form ID for tracking\n * @returns {integer} Unique form ID\n */const generateFormId=()=>{const random=Math.floor(Math.random()*1000);// 3-digit random number (0 to 999)\nreturn random;};/**\n * Submit test drive booking\n * @param {Object} bookingData - Booking data to submit\n * @returns {Promise<Object>} API response\n */export const submitTestDriveBooking=async bookingData=>{try{const response=await apiRequestWithRetry('/test-drive',{method:'POST',body:JSON.stringify(bookingData)});return response;}catch(error){console.error('Failed to submit test drive booking:',error);throw error;}};/**\n * Fetch news articles\n * @param {Object} params - Query parameters\n * @returns {Promise<Array>} Array of news articles\n */export const fetchNews=async function(){let params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{const queryString=new URLSearchParams(params).toString();const endpoint=\"/news\".concat(queryString?\"?\".concat(queryString):'');const response=await apiRequestWithRetry(endpoint);return response.news||[];}catch(error){console.error('Failed to fetch news:',error);throw new Error('Не удалось загрузить новости. Попробуйте обновить страницу.');}};/**\n * Fetch single news article\n * @param {string} id - News article ID\n * @returns {Promise<Object>} News article\n */export const fetchNewsById=async id=>{try{const response=await apiRequestWithRetry(\"/news/\".concat(id));return response;}catch(error){console.error('Failed to fetch news article:',error);throw new Error('Не удалось загрузить статью. Попробуйте обновить страницу.');}};export default{fetchModels,submitFeedback,submitTestDriveBooking,fetchNews,fetchNewsById};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "getCSRFToken", "_document$querySelect", "metaToken", "document", "querySelector", "getAttribute", "cookies", "cookie", "split", "name", "value", "trim", "decodeURIComponent", "getDefaultHeaders", "headers", "csrfToken", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "url", "concat", "defaultOptions", "_objectSpread", "credentials", "response", "fetch", "ok", "errorData", "json", "catch", "status", "Error", "message", "contentType", "get", "includes", "error", "NODE_ENV", "console", "timestamp", "Date", "toISOString", "apiRequestWithRetry", "maxRetries", "lastError", "attempt", "delay", "Math", "min", "pow", "Promise", "resolve", "setTimeout", "fetchModels", "models", "submitFeedback", "formData", "payload", "formid", "generateFormId", "form_name", "formType", "firstName", "lastName", "phone", "replace", "model", "topic", "log", "method", "body", "JSON", "stringify", "random", "floor", "submitTestDriveBooking", "bookingData", "fetchNews", "params", "queryString", "URLSearchParams", "toString", "news", "fetchNewsById", "id"], "sources": ["/var/www/html/gwm.tj/src/utils/api.js"], "sourcesContent": ["/**\n * API utility functions for secure HTTP requests\n */\n\n// Get API base URL from environment variables\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'https://api.gwm.tj/api/v1';\n\n/**\n * Get CSRF token from meta tag or cookie\n * @returns {string|null} CSRF token\n */\nconst getCSRFToken = () => {\n  // Try to get from meta tag first\n  const metaToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n  if (metaToken) return metaToken;\n\n  // Try to get from cookie\n  const cookies = document.cookie.split(';');\n  for (let cookie of cookies) {\n    const [name, value] = cookie.trim().split('=');\n    if (name === 'XSRF-TOKEN') {\n      return decodeURIComponent(value);\n    }\n  }\n\n  return null;\n};\n\n/**\n * Create default headers for API requests\n * @returns {Object} Default headers\n */\nconst getDefaultHeaders = () => {\n  const headers = {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest',\n  };\n\n  const csrfToken = getCSRFToken();\n  if (csrfToken) {\n    headers['X-CSRF-TOKEN'] = csrfToken;\n  }\n\n  return headers;\n};\n\n/**\n * Generic API request function with error handling and security\n * @param {string} endpoint - API endpoint (without base URL)\n * @param {Object} options - Fetch options\n * @returns {Promise} API response\n */\nconst apiRequest = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  const defaultOptions = {\n    headers: getDefaultHeaders(),\n    credentials: 'same-origin', // Include cookies for CSRF protection\n    ...options,\n  };\n\n  // Merge headers\n  if (options.headers) {\n    defaultOptions.headers = { ...defaultOptions.headers, ...options.headers };\n  }\n\n  try {\n    const response = await fetch(url, defaultOptions);\n\n    // Handle different HTTP status codes\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n\n      switch (response.status) {\n        case 400:\n          throw new Error(errorData.message || 'Проверьте правильность заполнения формы');\n        case 401:\n          throw new Error('Ошибка авторизации. Обновите страницу и попробуйте снова');\n        case 403:\n          throw new Error('Доступ запрещен');\n        case 404:\n          throw new Error('Запрашиваемый ресурс не найден');\n        case 422:\n          throw new Error(errorData.message || 'Ошибка валидации данных');\n        case 429:\n          throw new Error('Слишком много запросов. Попробуйте позже');\n        case 500:\n          throw new Error('Ошибка сервера. Попробуйте позже');\n        case 502:\n        case 503:\n        case 504:\n          throw new Error('Сервис временно недоступен. Попробуйте позже');\n        default:\n          throw new Error(`Ошибка ${response.status}: ${errorData.message || 'Неизвестная ошибка'}`);\n      }\n    }\n\n    const contentType = response.headers.get('content-type');\n    if (contentType && contentType.includes('application/json')) {\n      return await response.json();\n    }\n\n    return response;\n  } catch (error) {\n    // Log error for debugging (in development)\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Request Error:', {\n        url,\n        options: defaultOptions,\n        error: error.message,\n        timestamp: new Date().toISOString(),\n      });\n    }\n\n    // Re-throw the error for handling by the calling code\n    throw error;\n  }\n};\n\n/**\n * API request with retry logic for network errors\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @param {number} maxRetries - Maximum number of retries\n * @returns {Promise} API response\n */\nconst apiRequestWithRetry = async (endpoint, options = {}, maxRetries = 2) => {\n  let lastError;\n\n  for (let attempt = 0; attempt <= maxRetries; attempt++) {\n    try {\n      return await apiRequest(endpoint, options);\n    } catch (error) {\n      lastError = error;\n\n      // Only retry on network errors, not on HTTP errors\n      if (error.name === 'TypeError' && attempt < maxRetries) {\n        // Wait before retrying (exponential backoff)\n        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);\n        await new Promise(resolve => setTimeout(resolve, delay));\n        continue;\n      }\n\n      // Don't retry on HTTP errors or if max retries reached\n      break;\n    }\n  }\n\n  throw lastError;\n};\n\n/**\n * Fetch car models from API\n * @returns {Promise<Array>} Array of car models\n */\nexport const fetchModels = async () => {\n  try {\n    const response = await apiRequestWithRetry('/models');\n    return response.models || [];\n  } catch (error) {\n    console.error('Failed to fetch models:', error);\n    throw new Error('Не удалось загрузить список моделей. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Submit feedback form to GWM API\n * @param {Object} formData - Form data to submit\n * @param {string} formData.firstName - User's first name\n * @param {string} formData.lastName - User's last name (optional)\n * @param {string} formData.phone - User's phone number\n * @param {string} formData.topic - Selected car model\n * @param {string} formData.message - User's message (optional)\n * @param {string} formData.formType - Type of form (default: 'contact')\n * @returns {Promise<Object>} API response\n */\nexport const submitFeedback = async (formData) => {\n  try {\n    // Prepare payload according to API specification\n    const payload = {\n      formid: generateFormId(), // Generate unique form ID\n      form_name: formData.formType || 'contact', // Form type identifier\n      name: `${formData.firstName}${formData.lastName ? ' ' + formData.lastName : ''}`.trim(),\n      phone: formData.phone.replace(/[^\\d+]/g, ''),\n      model: formData.topic || '', // Selected car model\n      message: formData.message || '', // Optional message\n    };\n\n    console.log(payload)\n\n    const response = await apiRequestWithRetry('/feedback', {\n      method: 'POST',\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(payload),\n    });\n\n    return response;\n  } catch (error) {\n    console.error('Failed to submit feedback:', error);\n    throw error; // Re-throw to let the component handle the specific error message\n  }\n};\n\n/**\n * Generate unique form ID for tracking\n * @returns {integer} Unique form ID\n */\nconst generateFormId = () => {\n  const random = Math.floor(Math.random() * 1000); // 3-digit random number (0 to 999)\n  return random;\n};\n\n\n/**\n * Submit test drive booking\n * @param {Object} bookingData - Booking data to submit\n * @returns {Promise<Object>} API response\n */\nexport const submitTestDriveBooking = async (bookingData) => {\n  try {\n    const response = await apiRequestWithRetry('/test-drive', {\n      method: 'POST',\n      body: JSON.stringify(bookingData),\n    });\n\n    return response;\n  } catch (error) {\n    console.error('Failed to submit test drive booking:', error);\n    throw error;\n  }\n};\n\n/**\n * Fetch news articles\n * @param {Object} params - Query parameters\n * @returns {Promise<Array>} Array of news articles\n */\nexport const fetchNews = async (params = {}) => {\n  try {\n    const queryString = new URLSearchParams(params).toString();\n    const endpoint = `/news${queryString ? `?${queryString}` : ''}`;\n\n    const response = await apiRequestWithRetry(endpoint);\n    return response.news || [];\n  } catch (error) {\n    console.error('Failed to fetch news:', error);\n    throw new Error('Не удалось загрузить новости. Попробуйте обновить страницу.');\n  }\n};\n\n/**\n * Fetch single news article\n * @param {string} id - News article ID\n * @returns {Promise<Object>} News article\n */\nexport const fetchNewsById = async (id) => {\n  try {\n    const response = await apiRequestWithRetry(`/news/${id}`);\n    return response;\n  } catch (error) {\n    console.error('Failed to fetch news article:', error);\n    throw new Error('Не удалось загрузить статью. Попробуйте обновить страницу.');\n  }\n};\n\nexport default {\n  fetchModels,\n  submitFeedback,\n  submitTestDriveBooking,\n  fetchNews,\n  fetchNewsById,\n};\n"], "mappings": "yGAAA;AACA;AACA,GAEA;AACA,KAAM,CAAAA,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAI,2BAA2B,CAEtF;AACA;AACA;AACA,GACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACzB;AACA,KAAM,CAAAC,SAAS,EAAAD,qBAAA,CAAGE,QAAQ,CAACC,aAAa,CAAC,yBAAyB,CAAC,UAAAH,qBAAA,iBAAjDA,qBAAA,CAAmDI,YAAY,CAAC,SAAS,CAAC,CAC5F,GAAIH,SAAS,CAAE,MAAO,CAAAA,SAAS,CAE/B;AACA,KAAM,CAAAI,OAAO,CAAGH,QAAQ,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAC1C,IAAK,GAAI,CAAAD,MAAM,GAAI,CAAAD,OAAO,CAAE,CAC1B,KAAM,CAACG,IAAI,CAAEC,KAAK,CAAC,CAAGH,MAAM,CAACI,IAAI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC,CAC9C,GAAIC,IAAI,GAAK,YAAY,CAAE,CACzB,MAAO,CAAAG,kBAAkB,CAACF,KAAK,CAAC,CAClC,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACA;AACA;AACA,GACA,KAAM,CAAAG,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,OAAO,CAAG,CACd,cAAc,CAAE,kBAAkB,CAClC,kBAAkB,CAAE,gBACtB,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGf,YAAY,CAAC,CAAC,CAChC,GAAIe,SAAS,CAAE,CACbD,OAAO,CAAC,cAAc,CAAC,CAAGC,SAAS,CACrC,CAEA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,KAAM,CAAAE,UAAU,CAAG,cAAAA,CAAOC,QAAQ,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC9C,KAAM,CAAAG,GAAG,IAAAC,MAAA,CAAM3B,YAAY,EAAA2B,MAAA,CAAGN,QAAQ,CAAE,CAExC,KAAM,CAAAO,cAAc,CAAAC,aAAA,EAClBX,OAAO,CAAED,iBAAiB,CAAC,CAAC,CAC5Ba,WAAW,CAAE,aAAa,EACvBR,OAAO,CACX,CAED;AACA,GAAIA,OAAO,CAACJ,OAAO,CAAE,CACnBU,cAAc,CAACV,OAAO,CAAAW,aAAA,CAAAA,aAAA,IAAQD,cAAc,CAACV,OAAO,EAAKI,OAAO,CAACJ,OAAO,CAAE,CAC5E,CAEA,GAAI,CACF,KAAM,CAAAa,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACN,GAAG,CAAEE,cAAc,CAAC,CAEjD;AACA,GAAI,CAACG,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAO,CAAC,CAAC,CAAC,CAAC,CAEzD,OAAQL,QAAQ,CAACM,MAAM,EACrB,IAAK,IAAG,CACN,KAAM,IAAI,CAAAC,KAAK,CAACJ,SAAS,CAACK,OAAO,EAAI,yCAAyC,CAAC,CACjF,IAAK,IAAG,CACN,KAAM,IAAI,CAAAD,KAAK,CAAC,0DAA0D,CAAC,CAC7E,IAAK,IAAG,CACN,KAAM,IAAI,CAAAA,KAAK,CAAC,iBAAiB,CAAC,CACpC,IAAK,IAAG,CACN,KAAM,IAAI,CAAAA,KAAK,CAAC,gCAAgC,CAAC,CACnD,IAAK,IAAG,CACN,KAAM,IAAI,CAAAA,KAAK,CAACJ,SAAS,CAACK,OAAO,EAAI,yBAAyB,CAAC,CACjE,IAAK,IAAG,CACN,KAAM,IAAI,CAAAD,KAAK,CAAC,0CAA0C,CAAC,CAC7D,IAAK,IAAG,CACN,KAAM,IAAI,CAAAA,KAAK,CAAC,kCAAkC,CAAC,CACrD,IAAK,IAAG,CACR,IAAK,IAAG,CACR,IAAK,IAAG,CACN,KAAM,IAAI,CAAAA,KAAK,CAAC,8CAA8C,CAAC,CACjE,QACE,KAAM,IAAI,CAAAA,KAAK,yCAAAX,MAAA,CAAWI,QAAQ,CAACM,MAAM,OAAAV,MAAA,CAAKO,SAAS,CAACK,OAAO,EAAI,oBAAoB,CAAE,CAAC,CAC9F,CACF,CAEA,KAAM,CAAAC,WAAW,CAAGT,QAAQ,CAACb,OAAO,CAACuB,GAAG,CAAC,cAAc,CAAC,CACxD,GAAID,WAAW,EAAIA,WAAW,CAACE,QAAQ,CAAC,kBAAkB,CAAC,CAAE,CAC3D,MAAO,MAAM,CAAAX,QAAQ,CAACI,IAAI,CAAC,CAAC,CAC9B,CAEA,MAAO,CAAAJ,QAAQ,CACjB,CAAE,MAAOY,KAAK,CAAE,CACd;AACA,GAAI1C,OAAO,CAACC,GAAG,CAAC0C,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACF,KAAK,CAAC,oBAAoB,CAAE,CAClCjB,GAAG,CACHJ,OAAO,CAAEM,cAAc,CACvBe,KAAK,CAAEA,KAAK,CAACJ,OAAO,CACpBO,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAL,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,KAAM,CAAAM,mBAAmB,CAAG,cAAAA,CAAO5B,QAAQ,CAAmC,IAAjC,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAA2B,UAAU,CAAA3B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CACvE,GAAI,CAAA4B,SAAS,CAEb,IAAK,GAAI,CAAAC,OAAO,CAAG,CAAC,CAAEA,OAAO,EAAIF,UAAU,CAAEE,OAAO,EAAE,CAAE,CACtD,GAAI,CACF,MAAO,MAAM,CAAAhC,UAAU,CAACC,QAAQ,CAAEC,OAAO,CAAC,CAC5C,CAAE,MAAOqB,KAAK,CAAE,CACdQ,SAAS,CAAGR,KAAK,CAEjB;AACA,GAAIA,KAAK,CAAC9B,IAAI,GAAK,WAAW,EAAIuC,OAAO,CAAGF,UAAU,CAAE,CACtD;AACA,KAAM,CAAAG,KAAK,CAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,CAAEJ,OAAO,CAAC,CAAE,IAAI,CAAC,CACzD,KAAM,IAAI,CAAAK,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAEL,KAAK,CAAC,CAAC,CACxD,SACF,CAEA;AACA,MACF,CACF,CAEA,KAAM,CAAAF,SAAS,CACjB,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAS,WAAW,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAA7B,QAAQ,CAAG,KAAM,CAAAkB,mBAAmB,CAAC,SAAS,CAAC,CACrD,MAAO,CAAAlB,QAAQ,CAAC8B,MAAM,EAAI,EAAE,CAC9B,CAAE,MAAOlB,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,IAAI,CAAAL,KAAK,CAAC,oEAAoE,CAAC,CACvF,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAwB,cAAc,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAChD,GAAI,CACF;AACA,KAAM,CAAAC,OAAO,CAAG,CACdC,MAAM,CAAEC,cAAc,CAAC,CAAC,CAAE;AAC1BC,SAAS,CAAEJ,QAAQ,CAACK,QAAQ,EAAI,SAAS,CAAE;AAC3CvD,IAAI,CAAE,GAAAc,MAAA,CAAGoC,QAAQ,CAACM,SAAS,EAAA1C,MAAA,CAAGoC,QAAQ,CAACO,QAAQ,CAAG,GAAG,CAAGP,QAAQ,CAACO,QAAQ,CAAG,EAAE,EAAGvD,IAAI,CAAC,CAAC,CACvFwD,KAAK,CAAER,QAAQ,CAACQ,KAAK,CAACC,OAAO,CAAC,SAAS,CAAE,EAAE,CAAC,CAC5CC,KAAK,CAAEV,QAAQ,CAACW,KAAK,EAAI,EAAE,CAAE;AAC7BnC,OAAO,CAAEwB,QAAQ,CAACxB,OAAO,EAAI,EAAI;AACnC,CAAC,CAEDM,OAAO,CAAC8B,GAAG,CAACX,OAAO,CAAC,CAEpB,KAAM,CAAAjC,QAAQ,CAAG,KAAM,CAAAkB,mBAAmB,CAAC,WAAW,CAAE,CACtD2B,MAAM,CAAE,MAAM,CACd1D,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACD2D,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACf,OAAO,CAC9B,CAAC,CAAC,CAEF,MAAO,CAAAjC,QAAQ,CACjB,CAAE,MAAOY,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,CAAAA,KAAK,CAAE;AACf,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,KAAM,CAAAuB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAc,MAAM,CAAG1B,IAAI,CAAC2B,KAAK,CAAC3B,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAG,IAAI,CAAC,CAAE;AACjD,MAAO,CAAAA,MAAM,CACf,CAAC,CAGD;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,sBAAsB,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC3D,GAAI,CACF,KAAM,CAAApD,QAAQ,CAAG,KAAM,CAAAkB,mBAAmB,CAAC,aAAa,CAAE,CACxD2B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACI,WAAW,CAClC,CAAC,CAAC,CAEF,MAAO,CAAApD,QAAQ,CACjB,CAAE,MAAOY,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAyC,SAAS,CAAG,cAAAA,CAAA,CAAuB,IAAhB,CAAAC,MAAM,CAAA9D,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACzC,GAAI,CACF,KAAM,CAAA+D,WAAW,CAAG,GAAI,CAAAC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC,CAC1D,KAAM,CAAAnE,QAAQ,SAAAM,MAAA,CAAW2D,WAAW,KAAA3D,MAAA,CAAO2D,WAAW,EAAK,EAAE,CAAE,CAE/D,KAAM,CAAAvD,QAAQ,CAAG,KAAM,CAAAkB,mBAAmB,CAAC5B,QAAQ,CAAC,CACpD,MAAO,CAAAU,QAAQ,CAAC0D,IAAI,EAAI,EAAE,CAC5B,CAAE,MAAO9C,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,IAAI,CAAAL,KAAK,CAAC,6DAA6D,CAAC,CAChF,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAoD,aAAa,CAAG,KAAO,CAAAC,EAAE,EAAK,CACzC,GAAI,CACF,KAAM,CAAA5D,QAAQ,CAAG,KAAM,CAAAkB,mBAAmB,UAAAtB,MAAA,CAAUgE,EAAE,CAAE,CAAC,CACzD,MAAO,CAAA5D,QAAQ,CACjB,CAAE,MAAOY,KAAK,CAAE,CACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,KAAM,IAAI,CAAAL,KAAK,CAAC,4DAA4D,CAAC,CAC/E,CACF,CAAC,CAED,cAAe,CACbsB,WAAW,CACXE,cAAc,CACdoB,sBAAsB,CACtBE,SAAS,CACTM,aACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}