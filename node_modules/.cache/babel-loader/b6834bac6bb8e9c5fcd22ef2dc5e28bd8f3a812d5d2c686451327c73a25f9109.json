{"ast": null, "code": "import { transformProps } from 'motion-dom';\nconst appearStoreId = (elementId, valueName) => {\n  const key = transformProps.has(valueName) ? \"transform\" : valueName;\n  return \"\".concat(elementId, \": \").concat(key);\n};\nexport { appearStoreId };", "map": {"version": 3, "names": ["transformProps", "appearStoreId", "elementId", "valueName", "key", "has", "concat"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/animation/optimized-appear/store-id.mjs"], "sourcesContent": ["import { transformProps } from 'motion-dom';\n\nconst appearStoreId = (elementId, valueName) => {\n    const key = transformProps.has(valueName) ? \"transform\" : valueName;\n    return `${elementId}: ${key}`;\n};\n\nexport { appearStoreId };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,YAAY;AAE3C,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK;EAC5C,MAAMC,GAAG,GAAGJ,cAAc,CAACK,GAAG,CAACF,SAAS,CAAC,GAAG,WAAW,GAAGA,SAAS;EACnE,UAAAG,MAAA,CAAUJ,SAAS,QAAAI,MAAA,CAAKF,GAAG;AAC/B,CAAC;AAED,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}