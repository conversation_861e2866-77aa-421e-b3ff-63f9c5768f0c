{"ast": null, "code": "// btn imsg\nimport news from'../imgs/icons/11.png';import loc from'../imgs/icons/21.png';import drive from'../imgs/icons/31.png';export const btnListData=[{id:1,title:'Новости',icon:news,url:'/news-list'},{id:2,title:'Дилерский центр',icon:loc,url:'/contact'},{id:3,title:'Акции',icon:drive,url:'/offers'},{id:4,title:'Тест-драйв',icon:drive,url:'/book-a-test-drive'}];export const footerMenuData=[{id:1,title:'GWM',items:[{id:1,title:'Все модели',url:'/models'},{id:2,title:'Внедорожник',url:'/models'},{id:3,title:'<PERSON><PERSON>тчбек',url:'/models'},{id:4,title:'Кроссовер',url:'/models'},{id:5,title:'Купе',url:'/models'}]},{id:2,title:'Выбор и покупка',items:[{id:1,title:'Тест-драйв',url:'/book-a-test-drive'},{id:2,title:'Акции',url:'/offers'},{id:3,title:'Дилерский центр',url:'/contact'}]},{id:3,title:'Откройте для себя GWM',items:[{id:1,title:'Новости',url:'/news-list'},{id:2,title:'О GWM',url:'/about-gwm'},{id:3,title:'История',url:'/about-gwm/history'},{id:4,title:'Контакты',url:'/contact'},{id:5,title:'Карьера',url:'https://job.vector.tj/'// url\n}]},{id:4,title:'Владельцам',items:[{id:1,title:'Обслуживание клиентов',url:'/owners/care'},{id:2,title:'Гарантия и обслуживание',url:'/owners/warranty'},{id:3,title:'Оригинальные детали',url:'/owners/accessories'}]}];", "map": {"version": 3, "names": ["news", "loc", "drive", "btnListData", "id", "title", "icon", "url", "footerMenuData", "items"], "sources": ["/var/www/html/gwm.tj/src/asset/data/footerData.js"], "sourcesContent": ["// btn imsg\nimport news from '../imgs/icons/11.png';\nimport loc from '../imgs/icons/21.png';\nimport drive from '../imgs/icons/31.png';\n\nexport const btnListData = [\n  {\n    id: 1,\n    title: 'Новости',\n    icon: news,\n    url: '/news-list',\n  },\n  {\n    id: 2,\n    title: 'Дилерский центр',\n    icon: loc,\n    url: '/contact',\n  },\n  {\n    id: 3,\n    title: 'Акции',\n    icon: drive,\n    url: '/offers',\n  },\n  {\n    id: 4,\n    title: 'Тест-драйв',\n    icon: drive,\n    url: '/book-a-test-drive',\n  },\n];\n\nexport const footerMenuData = [\n  {\n    id: 1,\n    title: 'GWM',\n    items: [\n      {\n        id: 1,\n        title: 'Все модели',\n        url: '/models',\n      },\n      {\n        id: 2,\n        title: 'Внедорожник',\n        url: '/models',\n      },\n      {\n        id: 3,\n        title: 'Хэтчбек',\n        url: '/models',\n      },\n      {\n        id: 4,\n        title: 'Кроссовер',\n        url: '/models',\n      },\n      {\n        id: 5,\n        title: 'Купе',\n        url: '/models',\n      },\n    ],\n  },\n  {\n    id: 2,\n    title: 'Выбор и покупка',\n    items: [\n      {\n        id: 1,\n        title: 'Тест-драйв',\n        url: '/book-a-test-drive',\n      },\n      {\n        id: 2,\n        title: 'Акции',\n        url: '/offers',\n      },\n      {\n        id: 3,\n        title: 'Дилерский центр',\n        url: '/contact',\n      },\n    ],\n  },\n  {\n    id: 3,\n    title: 'Откройте для себя GWM',\n    items: [\n      {\n        id: 1,\n        title: 'Новости',\n        url: '/news-list',\n      },\n      {\n        id: 2,\n        title: 'О GWM',\n        url: '/about-gwm',\n      },\n      {\n        id: 3,\n        title: 'История',\n        url: '/about-gwm/history',\n      },\n      {\n        id: 4,\n        title: 'Контакты',\n        url: '/contact',\n      },\n      {\n        id: 5,\n        title: 'Карьера',\n        url: 'https://job.vector.tj/', // url\n      },\n    ],\n  },\n  {\n    id: 4,\n    title: 'Владельцам',\n    items: [\n      {\n        id: 1,\n        title: 'Обслуживание клиентов',\n        url: '/owners/care',\n      },\n      {\n        id: 2,\n        title: 'Гарантия и обслуживание',\n        url: '/owners/warranty',\n      },\n      {\n        id: 3,\n        title: 'Оригинальные детали',\n        url: '/owners/accessories',\n      },\n    ],\n  },\n];\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,IAAI,KAAM,sBAAsB,CACvC,MAAO,CAAAC,GAAG,KAAM,sBAAsB,CACtC,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CAExC,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAEN,IAAI,CACVO,GAAG,CAAE,YACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBC,IAAI,CAAEL,GAAG,CACTM,GAAG,CAAE,UACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAEJ,KAAK,CACXK,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBC,IAAI,CAAEJ,KAAK,CACXK,GAAG,CAAE,oBACP,CAAC,CACF,CAED,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B,CACEJ,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,KAAK,CACZI,KAAK,CAAE,CACL,CACEL,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBE,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,aAAa,CACpBE,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,WAAW,CAClBE,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,MAAM,CACbE,GAAG,CAAE,SACP,CAAC,CAEL,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBI,KAAK,CAAE,CACL,CACEL,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBE,GAAG,CAAE,oBACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,OAAO,CACdE,GAAG,CAAE,SACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,iBAAiB,CACxBE,GAAG,CAAE,UACP,CAAC,CAEL,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,uBAAuB,CAC9BI,KAAK,CAAE,CACL,CACEL,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,YACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,OAAO,CACdE,GAAG,CAAE,YACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,oBACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,UAAU,CACjBE,GAAG,CAAE,UACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,wBAA0B;AACjC,CAAC,CAEL,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBI,KAAK,CAAE,CACL,CACEL,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,uBAAuB,CAC9BE,GAAG,CAAE,cACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,yBAAyB,CAChCE,GAAG,CAAE,kBACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,qBAAqB,CAC5BE,GAAG,CAAE,qBACP,CAAC,CAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}