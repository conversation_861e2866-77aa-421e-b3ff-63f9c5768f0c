{"ast": null, "code": "/**\n * SSR Window 5.0.0\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: February 12, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\nexport { getWindow as a, getDocument as g };", "map": {"version": 3, "names": ["isObject", "obj", "constructor", "Object", "extend", "target", "src", "noExtend", "keys", "filter", "key", "indexOf", "for<PERSON>ach", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window", "a", "g"], "sources": ["/var/www/html/gwm.tj/node_modules/swiper/shared/ssr-window.esm.mjs"], "sourcesContent": ["/**\n * SSR Window 5.0.0\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: February 12, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,aAAa,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,KAAKC,MAAM;AACtG;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC3B,IAAID,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,IAAIC,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,CAAC,CAAC;EACV;EACA,MAAMC,QAAQ,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;EAC1DJ,MAAM,CAACK,IAAI,CAACF,GAAG,CAAC,CAACG,MAAM,CAACC,GAAG,IAAIH,QAAQ,CAACI,OAAO,CAACD,GAAG,CAAC,GAAG,CAAC,CAAC,CAACE,OAAO,CAACF,GAAG,IAAI;IACvE,IAAI,OAAOL,MAAM,CAACK,GAAG,CAAC,KAAK,WAAW,EAAEL,MAAM,CAACK,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC,CAAC,KAAK,IAAIV,QAAQ,CAACM,GAAG,CAACI,GAAG,CAAC,CAAC,IAAIV,QAAQ,CAACK,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIP,MAAM,CAACK,IAAI,CAACF,GAAG,CAACI,GAAG,CAAC,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvJT,MAAM,CAACC,MAAM,CAACK,GAAG,CAAC,EAAEJ,GAAG,CAACI,GAAG,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ;AACA,MAAMI,WAAW,GAAG;EAClBC,IAAI,EAAE,CAAC,CAAC;EACRC,gBAAgBA,CAAA,EAAG,CAAC,CAAC;EACrBC,mBAAmBA,CAAA,EAAG,CAAC,CAAC;EACxBC,aAAa,EAAE;IACbC,IAAIA,CAAA,EAAG,CAAC,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EACDC,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI;EACb,CAAC;EACDC,gBAAgBA,CAAA,EAAG;IACjB,OAAO,EAAE;EACX,CAAC;EACDC,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI;EACb,CAAC;EACDC,WAAWA,CAAA,EAAG;IACZ,OAAO;MACLC,SAASA,CAAA,EAAG,CAAC;IACf,CAAC;EACH,CAAC;EACDC,aAAaA,CAAA,EAAG;IACd,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,CAAC,CAAC;MACTC,YAAYA,CAAA,EAAG,CAAC,CAAC;MACjBC,oBAAoBA,CAAA,EAAG;QACrB,OAAO,EAAE;MACX;IACF,CAAC;EACH,CAAC;EACDC,eAAeA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC;EACX,CAAC;EACDC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI;EACb,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV;AACF,CAAC;AACD,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,GAAG,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAG,CAAC,CAAC;EAC3DzC,MAAM,CAACwC,GAAG,EAAE9B,WAAW,CAAC;EACxB,OAAO8B,GAAG;AACZ;AACA,MAAME,SAAS,GAAG;EAChBD,QAAQ,EAAE/B,WAAW;EACrBiC,SAAS,EAAE;IACTC,SAAS,EAAE;EACb,CAAC;EACDd,QAAQ,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC;EACDO,OAAO,EAAE;IACPC,YAAYA,CAAA,EAAG,CAAC,CAAC;IACjBC,SAASA,CAAA,EAAG,CAAC,CAAC;IACdC,EAAEA,CAAA,EAAG,CAAC,CAAC;IACPC,IAAIA,CAAA,EAAG,CAAC;EACV,CAAC;EACDC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC,OAAO,IAAI;EACb,CAAC;EACDtC,gBAAgBA,CAAA,EAAG,CAAC,CAAC;EACrBC,mBAAmBA,CAAA,EAAG,CAAC,CAAC;EACxBsC,gBAAgBA,CAAA,EAAG;IACjB,OAAO;MACLC,gBAAgBA,CAAA,EAAG;QACjB,OAAO,EAAE;MACX;IACF,CAAC;EACH,CAAC;EACDC,KAAKA,CAAA,EAAG,CAAC,CAAC;EACVC,IAAIA,CAAA,EAAG,CAAC,CAAC;EACTC,MAAM,EAAE,CAAC,CAAC;EACVC,UAAUA,CAAA,EAAG,CAAC,CAAC;EACfC,YAAYA,CAAA,EAAG,CAAC,CAAC;EACjBC,UAAUA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC;EACX,CAAC;EACDC,qBAAqBA,CAACC,QAAQ,EAAE;IAC9B,IAAI,OAAOJ,UAAU,KAAK,WAAW,EAAE;MACrCI,QAAQ,CAAC,CAAC;MACV,OAAO,IAAI;IACb;IACA,OAAOJ,UAAU,CAACI,QAAQ,EAAE,CAAC,CAAC;EAChC,CAAC;EACDC,oBAAoBA,CAACC,EAAE,EAAE;IACvB,IAAI,OAAON,UAAU,KAAK,WAAW,EAAE;MACrC;IACF;IACAC,YAAY,CAACK,EAAE,CAAC;EAClB;AACF,CAAC;AACD,SAASC,SAASA,CAAA,EAAG;EACnB,MAAMC,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;EACvDjE,MAAM,CAACgE,GAAG,EAAEtB,SAAS,CAAC;EACtB,OAAOsB,GAAG;AACZ;AAEA,SAASD,SAAS,IAAIG,CAAC,EAAE3B,WAAW,IAAI4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}