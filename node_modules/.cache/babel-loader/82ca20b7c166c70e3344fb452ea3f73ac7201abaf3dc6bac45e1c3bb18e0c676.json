{"ast": null, "code": "import React,{useEffect,useState,useCallback}from'react';import styles from'./newsList.module.css';import AOS from'aos';import'aos/dist/aos.css';import arrowIcon from'../../../asset/imgs/icons/arrow.svg';import{useNavigate}from'react-router-dom';import Notification from'../../../components/Notification/Notification';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SkeletonCard=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(styles.card,\" \").concat(styles.skeletonCard),children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.img,\" \").concat(styles.skeleton_img)}),/*#__PURE__*/_jsxs(\"div\",{className:styles.title,children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.textLine)}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.textLine)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(styles.skeleton,\" \").concat(styles.linkLine)})]});const NewsList=()=>{const[pageLoading,setPageLoading]=useState(true);const[loading,setLoading]=useState(true);const[news,setNews]=useState([]);const navigate=useNavigate();const[notification,setNotification]=useState({message:'',type:''});const showNotification=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({message,type});setTimeout(()=>setNotification({message:'',type:''}),3000);};useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';setPageLoading(false);setLoading(true);fetch('https://api.gwm.tj/api/v1/news').then(res=>res.json()).then(json=>{setNews(json.news||[]);setLoading(false);document.body.style.overflow='visible';AOS.refresh();}).catch(err=>{setLoading(false);showNotification('Не удалось загрузить список. Возможно идет тех.работа','error');document.body.style.overflow='visible';});return()=>{document.body.style.overflow='visible';};},[]);const handleNavigation=useCallback(id=>{navigate(\"/news-list/\".concat(id));},[navigate]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Notification,{message:notification.message,type:notification.type}),pageLoading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"50\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"GWM \\u041D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0438\"})}),/*#__PURE__*/_jsx(\"i\",{className:\"redLine\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u0414\\u043E\\u0431\\u0440\\u043E \\u043F\\u043E\\u0436\\u0430\\u043B\\u043E\\u0432\\u0430\\u0442\\u044C \\u0432 GWM News! \\u0412\\u0430\\u0448 \\u043F\\u0443\\u043D\\u043A\\u0442 \\u043D\\u0430\\u0437\\u043D\\u0430\\u0447\\u0435\\u043D\\u0438\\u044F \\u0434\\u043B\\u044F \\u0432\\u0441\\u0435\\u0445 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u043E\\u0431\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u0439, \\u043E\\u0431\\u044A\\u044F\\u0432\\u043B\\u0435\\u043D\\u0438\\u0439 \\u0438 \\u0438\\u0434\\u0435\\u0439 \\u0438\\u0437 \\u043C\\u0438\\u0440\\u0430 GWM. \\u0418\\u0437\\u0443\\u0447\\u0438\\u0442\\u0435 \\u043D\\u0430\\u0448\\u0443 \\u043A\\u043E\\u043B\\u043B\\u0435\\u043A\\u0446\\u0438\\u044E \\u0441\\u0442\\u0430\\u0442\\u0435\\u0439, \\u043F\\u0440\\u0435\\u0441\\u0441-\\u0440\\u0435\\u043B\\u0438\\u0437\\u043E\\u0432 \\u0438 \\u0438\\u0441\\u0442\\u043E\\u0440\\u0438\\u0439, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0431\\u044B\\u0442\\u044C \\u0432 \\u043A\\u0443\\u0440\\u0441\\u0435 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u0438\\u043D\\u043D\\u043E\\u0432\\u0430\\u0446\\u0438\\u0439, \\u0441\\u043E\\u0431\\u044B\\u0442\\u0438\\u0439 \\u0438 \\u0430\\u043A\\u0446\\u0438\\u0439.\"})]}),/*#__PURE__*/_jsx(\"section\",{children:/*#__PURE__*/_jsx(\"div\",{className:styles.flexContent,children:loading?Array.from({length:8}).map((_,index)=>/*#__PURE__*/_jsx(SkeletonCard,{},index)):Array.isArray(news)&&news.length>0?news.map(item=>{var _item$title;const title=(item===null||item===void 0?void 0:(_item$title=item.title)===null||_item$title===void 0?void 0:_item$title.length)>60?item.title.slice(0,60).trim()+'...':item.title;return/*#__PURE__*/_jsxs(\"div\",{className:styles.card,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,children:(item===null||item===void 0?void 0:item.preview)&&/*#__PURE__*/_jsx(\"img\",{src:item.preview,alt:item.title||'Image'})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.title,children:[(item===null||item===void 0?void 0:item.created_at)&&/*#__PURE__*/_jsx(\"span\",{children:new Date(item.updated_at).toLocaleDateString('ru-RU')}),title&&/*#__PURE__*/_jsx(\"h2\",{children:title})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"link\",onClick:()=>handleNavigation(item.slug),children:[/*#__PURE__*/_jsx(\"b\",{children:\"\\u041F\\u043E\\u0434\\u0440\\u043E\\u0431\\u043D\\u0435\\u0435\"}),/*#__PURE__*/_jsx(\"img\",{src:arrowIcon,alt:\"\",className:\"linkIcon\"})]})]},item.id);}):/*#__PURE__*/_jsx(\"p\",{children:\"\\u041D\\u0435\\u0442 \\u043D\\u043E\\u0432\\u043E\\u0441\\u0442\\u0435\\u0439 \\u0434\\u043B\\u044F \\u043E\\u0442\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u044F.\"})})})]})})]});};export default NewsList;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "styles", "AOS", "arrowIcon", "useNavigate", "Notification", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SkeletonCard", "className", "concat", "card", "skeletonCard", "children", "img", "skeleton_img", "title", "skeleton", "textLine", "linkLine", "NewsList", "pageLoading", "setPageLoading", "loading", "setLoading", "news", "setNews", "navigate", "notification", "setNotification", "message", "type", "showNotification", "arguments", "length", "undefined", "setTimeout", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "fetch", "then", "res", "json", "refresh", "catch", "err", "handleNavigation", "id", "flexContent", "Array", "from", "map", "_", "index", "isArray", "item", "_item$title", "slice", "trim", "preview", "src", "alt", "created_at", "Date", "updated_at", "toLocaleDateString", "onClick", "slug"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/News/NewsList.jsx"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from 'react';\nimport styles from './newsList.module.css';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport arrowIcon from '../../../asset/imgs/icons/arrow.svg';\nimport { useNavigate } from 'react-router-dom';\nimport Notification from '../../../components/Notification/Notification';\n\nconst SkeletonCard = () => (\n  <div className={`${styles.card} ${styles.skeletonCard}`}>\n    <div className={`${styles.img} ${styles.skeleton_img}`}></div>\n    <div className={styles.title}>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n      <div className={`${styles.skeleton} ${styles.textLine}`}></div>\n    </div>\n    <div className={`${styles.skeleton} ${styles.linkLine}`}></div>\n  </div>\n);\n\nconst NewsList = () => {\n  const [pageLoading, setPageLoading] = useState(true);\n  const [loading, setLoading] = useState(true);\n  const [news, setNews] = useState([]);\n  const navigate = useNavigate();\n  const [notification, setNotification] = useState({ message: '', type: '' });\n\n  const showNotification = (message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  };\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    setPageLoading(false);\n    setLoading(true);\n\n    fetch('https://api.gwm.tj/api/v1/news')\n      .then((res) => res.json())\n      .then((json) => {\n        setNews(json.news || []);\n        setLoading(false);\n        document.body.style.overflow = 'visible';\n        AOS.refresh();\n      })\n      .catch((err) => {\n        setLoading(false);\n        showNotification(\n          'Не удалось загрузить список. Возможно идет тех.работа',\n          'error'\n        );\n        document.body.style.overflow = 'visible';\n      });\n\n    return () => {\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  const handleNavigation = useCallback(\n    (id) => {\n      navigate(`/news-list/${id}`);\n    },\n    [navigate]\n  );\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {pageLoading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"50\">\n                <strong>GWM Новости</strong>\n              </h1>\n              <i\n                className=\"redLine\"\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n              ></i>\n              <p data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Добро пожаловать в GWM News! Ваш пункт назначения для всех\n                последних обновлений, объявлений и идей из мира GWM. Изучите\n                нашу коллекцию статей, пресс-релизов и историй, чтобы быть в\n                курсе наших последних инноваций, событий и акций.\n              </p>\n            </div>\n            <section>\n              <div className={styles.flexContent}>\n                {loading ? (\n                  Array.from({ length: 8 }).map((_, index) => (\n                    <SkeletonCard key={index} />\n                  ))\n                ) : Array.isArray(news) && news.length > 0 ? (\n                  news.map((item) => {\n                    const title =\n                      item?.title?.length > 60\n                        ? item.title.slice(0, 60).trim() + '...'\n                        : item.title;\n                    return (\n                      <div className={styles.card} key={item.id}>\n                        <div className={styles.img}>\n                          {item?.preview && (\n                            <img\n                              src={item.preview}\n                              alt={item.title || 'Image'}\n                            />\n                          )}\n                        </div>\n                        <div className={styles.title}>\n                          {item?.created_at && (\n                            <span>\n                              {new Date(item.updated_at).toLocaleDateString(\n                                'ru-RU'\n                              )}\n                            </span>\n                          )}\n                          {title && <h2>{title}</h2>}\n                        </div>\n                        <div\n                          className=\"link\"\n                          onClick={() => handleNavigation(item.slug)}\n                        >\n                          <b>Подробнее</b>\n                          <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                        </div>\n                      </div>\n                    );\n                  })\n                ) : (\n                  <p>Нет новостей для отображения.</p>\n                )}\n              </div>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default NewsList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAC/D,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAC1C,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,MAAO,CAAAC,SAAS,KAAM,qCAAqC,CAC3D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,YAAY,KAAM,+CAA+C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEzE,KAAM,CAAAC,YAAY,CAAGA,CAAA,gBACnBH,KAAA,QAAKI,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACc,IAAI,MAAAD,MAAA,CAAIb,MAAM,CAACe,YAAY,CAAG,CAAAC,QAAA,eACtDV,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACiB,GAAG,MAAAJ,MAAA,CAAIb,MAAM,CAACkB,YAAY,CAAG,CAAM,CAAC,cAC9DV,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACmB,KAAM,CAAAH,QAAA,eAC3BV,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACqB,QAAQ,CAAG,CAAM,CAAC,cAC/Df,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACqB,QAAQ,CAAG,CAAM,CAAC,EAC5D,CAAC,cACNf,IAAA,QAAKM,SAAS,IAAAC,MAAA,CAAKb,MAAM,CAACoB,QAAQ,MAAAP,MAAA,CAAIb,MAAM,CAACsB,QAAQ,CAAG,CAAM,CAAC,EAC5D,CACN,CAED,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,IAAI,CAAEC,OAAO,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAAgC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC4B,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,CAAEmC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAE3E,KAAM,CAAAC,gBAAgB,CAAG,QAAAA,CAACF,OAAO,CAAuB,IAArB,CAAAC,IAAI,CAAAE,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACjDJ,eAAe,CAAC,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAC,CAClCK,UAAU,CAAC,IAAMP,eAAe,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACpE,CAAC,CAEDrC,SAAS,CAAC,IAAM,CACdI,GAAG,CAACuC,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvCvB,cAAc,CAAC,KAAK,CAAC,CACrBE,UAAU,CAAC,IAAI,CAAC,CAEhBsB,KAAK,CAAC,gCAAgC,CAAC,CACpCC,IAAI,CAAEC,GAAG,EAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEE,IAAI,EAAK,CACdvB,OAAO,CAACuB,IAAI,CAACxB,IAAI,EAAI,EAAE,CAAC,CACxBD,UAAU,CAAC,KAAK,CAAC,CACjBkB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CACxC/C,GAAG,CAACoD,OAAO,CAAC,CAAC,CACf,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,EAAK,CACd5B,UAAU,CAAC,KAAK,CAAC,CACjBQ,gBAAgB,CACd,uDAAuD,CACvD,OACF,CAAC,CACDU,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAC,CAEJ,MAAO,IAAM,CACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAQ,gBAAgB,CAAGzD,WAAW,CACjC0D,EAAE,EAAK,CACN3B,QAAQ,eAAAjB,MAAA,CAAe4C,EAAE,CAAE,CAAC,CAC9B,CAAC,CACD,CAAC3B,QAAQ,CACX,CAAC,CAED,mBACEtB,KAAA,CAAAE,SAAA,EAAAM,QAAA,eACEV,IAAA,CAACF,YAAY,EAAC6B,OAAO,CAAEF,YAAY,CAACE,OAAQ,CAACC,IAAI,CAAEH,YAAY,CAACG,IAAK,CAAE,CAAC,CACvEV,WAAW,cACVlB,IAAA,QAAKM,SAAS,CAAC,eAAe,CAAAI,QAAA,cAC5BV,IAAA,QAAKM,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENN,IAAA,QAAKM,SAAS,CAAC,SAAS,CAAAI,QAAA,cACtBR,KAAA,QAAKI,SAAS,CAAC,WAAW,CAAAI,QAAA,eACxBR,KAAA,QAAKI,SAAS,CAAC,SAAS,CAAAI,QAAA,eACtBV,IAAA,OAAIM,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAC,iBAAe,IAAI,CAAAI,QAAA,cAC1DV,IAAA,WAAAU,QAAA,CAAQ,gDAAW,CAAQ,CAAC,CAC1B,CAAC,cACLV,IAAA,MACEM,SAAS,CAAC,SAAS,CACnB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAClB,CAAC,cACLN,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAU,QAAA,CAAC,kmCAK3C,CAAG,CAAC,EACD,CAAC,cACNV,IAAA,YAAAU,QAAA,cACEV,IAAA,QAAKM,SAAS,CAAEZ,MAAM,CAAC0D,WAAY,CAAA1C,QAAA,CAChCU,OAAO,CACNiC,KAAK,CAACC,IAAI,CAAC,CAAEvB,MAAM,CAAE,CAAE,CAAC,CAAC,CAACwB,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,gBACrCzD,IAAA,CAACK,YAAY,IAAMoD,KAAQ,CAC5B,CAAC,CACAJ,KAAK,CAACK,OAAO,CAACpC,IAAI,CAAC,EAAIA,IAAI,CAACS,MAAM,CAAG,CAAC,CACxCT,IAAI,CAACiC,GAAG,CAAEI,IAAI,EAAK,KAAAC,WAAA,CACjB,KAAM,CAAA/C,KAAK,CACT,CAAA8C,IAAI,SAAJA,IAAI,kBAAAC,WAAA,CAAJD,IAAI,CAAE9C,KAAK,UAAA+C,WAAA,iBAAXA,WAAA,CAAa7B,MAAM,EAAG,EAAE,CACpB4B,IAAI,CAAC9C,KAAK,CAACgD,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAG,KAAK,CACtCH,IAAI,CAAC9C,KAAK,CAChB,mBACEX,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACc,IAAK,CAAAE,QAAA,eAC1BV,IAAA,QAAKM,SAAS,CAAEZ,MAAM,CAACiB,GAAI,CAAAD,QAAA,CACxB,CAAAiD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,OAAO,gBACZ/D,IAAA,QACEgE,GAAG,CAAEL,IAAI,CAACI,OAAQ,CAClBE,GAAG,CAAEN,IAAI,CAAC9C,KAAK,EAAI,OAAQ,CAC5B,CACF,CACE,CAAC,cACNX,KAAA,QAAKI,SAAS,CAAEZ,MAAM,CAACmB,KAAM,CAAAH,QAAA,EAC1B,CAAAiD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEO,UAAU,gBACflE,IAAA,SAAAU,QAAA,CACG,GAAI,CAAAyD,IAAI,CAACR,IAAI,CAACS,UAAU,CAAC,CAACC,kBAAkB,CAC3C,OACF,CAAC,CACG,CACP,CACAxD,KAAK,eAAIb,IAAA,OAAAU,QAAA,CAAKG,KAAK,CAAK,CAAC,EACvB,CAAC,cACNX,KAAA,QACEI,SAAS,CAAC,MAAM,CAChBgE,OAAO,CAAEA,CAAA,GAAMpB,gBAAgB,CAACS,IAAI,CAACY,IAAI,CAAE,CAAA7D,QAAA,eAE3CV,IAAA,MAAAU,QAAA,CAAG,wDAAS,CAAG,CAAC,cAChBV,IAAA,QAAKgE,GAAG,CAAEpE,SAAU,CAACqE,GAAG,CAAC,EAAE,CAAC3D,SAAS,CAAC,UAAU,CAAE,CAAC,EAChD,CAAC,GAzB0BqD,IAAI,CAACR,EA0BlC,CAAC,CAEV,CAAC,CAAC,cAEFnD,IAAA,MAAAU,QAAA,CAAG,4JAA6B,CAAG,CACpC,CACE,CAAC,CACC,CAAC,EACP,CAAC,CACH,CACN,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAO,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}