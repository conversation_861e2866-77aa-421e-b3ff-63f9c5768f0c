{"ast": null, "code": "function fillWildcards(keyframes) {\n  for (let i = 1; i < keyframes.length; i++) {\n    var _keyframes$i;\n    (_keyframes$i = keyframes[i]) !== null && _keyframes$i !== void 0 ? _keyframes$i : keyframes[i] = keyframes[i - 1];\n  }\n}\nexport { fillWildcards };", "map": {"version": 3, "names": ["fillWildcards", "keyframes", "i", "length", "_keyframes$i"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs"], "sourcesContent": ["function fillWildcards(keyframes) {\n    for (let i = 1; i < keyframes.length; i++) {\n        keyframes[i] ?? (keyframes[i] = keyframes[i - 1]);\n    }\n}\n\nexport { fillWildcards };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,SAAS,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAA,IAAAE,YAAA;IACvC,CAAAA,YAAA,GAAAH,SAAS,CAACC,CAAC,CAAC,cAAAE,YAAA,cAAAA,YAAA,GAAKH,SAAS,CAACC,CAAC,CAAC,GAAGD,SAAS,CAACC,CAAC,GAAG,CAAC,CAAC;EACpD;AACJ;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}