{"ast": null, "code": "import { observeTimeline } from 'motion-dom';\nimport { getTimeline } from './utils/get-timeline.mjs';\nfunction attachToAnimation(animation, options) {\n  const timeline = getTimeline(options);\n  return animation.attachTimeline({\n    timeline: options.target ? undefined : timeline,\n    observe: valueAnimation => {\n      valueAnimation.pause();\n      return observeTimeline(progress => {\n        valueAnimation.time = valueAnimation.duration * progress;\n      }, timeline);\n    }\n  });\n}\nexport { attachToAnimation };", "map": {"version": 3, "names": ["observeTimeline", "getTimeline", "attachToAnimation", "animation", "options", "timeline", "attachTimeline", "target", "undefined", "observe", "valueAnimation", "pause", "progress", "time", "duration"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs"], "sourcesContent": ["import { observeTimeline } from 'motion-dom';\nimport { getTimeline } from './utils/get-timeline.mjs';\n\nfunction attachToAnimation(animation, options) {\n    const timeline = getTimeline(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return observeTimeline((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\nexport { attachToAnimation };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,YAAY;AAC5C,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC3C,MAAMC,QAAQ,GAAGJ,WAAW,CAACG,OAAO,CAAC;EACrC,OAAOD,SAAS,CAACG,cAAc,CAAC;IAC5BD,QAAQ,EAAED,OAAO,CAACG,MAAM,GAAGC,SAAS,GAAGH,QAAQ;IAC/CI,OAAO,EAAGC,cAAc,IAAK;MACzBA,cAAc,CAACC,KAAK,CAAC,CAAC;MACtB,OAAOX,eAAe,CAAEY,QAAQ,IAAK;QACjCF,cAAc,CAACG,IAAI,GAAGH,cAAc,CAACI,QAAQ,GAAGF,QAAQ;MAC5D,CAAC,EAAEP,QAAQ,CAAC;IAChB;EACJ,CAAC,CAAC;AACN;AAEA,SAASH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}