{"ast": null, "code": "function getValueTransition(transition, key) {\n  var _ref, _transition$key;\n  return (_ref = (_transition$key = transition === null || transition === void 0 ? void 0 : transition[key]) !== null && _transition$key !== void 0 ? _transition$key : transition === null || transition === void 0 ? void 0 : transition[\"default\"]) !== null && _ref !== void 0 ? _ref : transition;\n}\nexport { getValueTransition };", "map": {"version": 3, "names": ["getValueTransition", "transition", "key", "_ref", "_transition$key"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs"], "sourcesContent": ["function getValueTransition(transition, key) {\n    return (transition?.[key] ??\n        transition?.[\"default\"] ??\n        transition);\n}\n\nexport { getValueTransition };\n"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,UAAU,EAAEC,GAAG,EAAE;EAAA,IAAAC,IAAA,EAAAC,eAAA;EACzC,QAAAD,IAAA,IAAAC,eAAA,GAAQH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAGC,GAAG,CAAC,cAAAE,eAAA,cAAAA,eAAA,GACrBH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,SAAS,CAAC,cAAAE,IAAA,cAAAA,IAAA,GACvBF,UAAU;AAClB;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}