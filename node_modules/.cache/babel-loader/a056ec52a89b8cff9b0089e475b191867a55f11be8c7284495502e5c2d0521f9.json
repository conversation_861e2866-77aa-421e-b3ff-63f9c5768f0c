{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"style\", \"value\", \"as\", \"onDrag\", \"layout\"];\nimport { jsx } from 'react/jsx-runtime';\nimport { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nfunction useDefaultMotionValue(value) {\n  let defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItemComponent(_ref, externalRef) {\n  let {\n      children,\n      style = {},\n      value,\n      as = \"li\",\n      onDrag,\n      layout = true\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const Component = useConstant(() => motion[as]);\n  const context = useContext(ReorderContext);\n  const point = {\n    x: useDefaultMotionValue(style.x),\n    y: useDefaultMotionValue(style.y)\n  };\n  const zIndex = useTransform([point.x, point.y], _ref2 => {\n    let [latestX, latestY] = _ref2;\n    return latestX || latestY ? 1 : \"unset\";\n  });\n  invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n  const {\n    axis,\n    registerItem,\n    updateOrder\n  } = context;\n  return jsx(Component, _objectSpread(_objectSpread({\n    drag: axis\n  }, props), {}, {\n    dragSnapToOrigin: true,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      x: point.x,\n      y: point.y,\n      zIndex\n    }),\n    layout: layout,\n    onDrag: (event, gesturePoint) => {\n      const {\n        velocity\n      } = gesturePoint;\n      velocity[axis] && updateOrder(value, point[axis].get(), velocity[axis]);\n      onDrag && onDrag(event, gesturePoint);\n    },\n    onLayoutMeasure: measured => registerItem(value, measured),\n    ref: externalRef,\n    ignoreStrict: true,\n    children: children\n  }));\n}\nconst ReorderItem = /*@__PURE__*/forwardRef(ReorderItemComponent);\nexport { ReorderItem, ReorderItemComponent };", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "jsx", "isMotionValue", "invariant", "forwardRef", "useContext", "ReorderContext", "motion", "useConstant", "useMotionValue", "useTransform", "useDefaultMotionValue", "value", "defaultValue", "arguments", "length", "undefined", "ReorderItemComponent", "_ref", "externalRef", "children", "style", "as", "onDrag", "layout", "props", "Component", "context", "point", "x", "y", "zIndex", "_ref2", "latestX", "latestY", "Boolean", "axis", "registerItem", "updateOrder", "drag", "dragSnapToO<PERSON>in", "event", "gesturePoint", "velocity", "get", "onLayoutMeasure", "measured", "ref", "ignoreStrict", "ReorderItem"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/Reorder/Item.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { isMotionValue } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\n\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n    return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItemComponent({ children, style = {}, value, as = \"li\", onDrag, layout = true, ...props }, externalRef) {\n    const Component = useConstant(() => motion[as]);\n    const context = useContext(ReorderContext);\n    const point = {\n        x: useDefaultMotionValue(style.x),\n        y: useDefaultMotionValue(style.y),\n    };\n    const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n    invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n    const { axis, registerItem, updateOrder } = context;\n    return (jsx(Component, { drag: axis, ...props, dragSnapToOrigin: true, style: { ...style, x: point.x, y: point.y, zIndex }, layout: layout, onDrag: (event, gesturePoint) => {\n            const { velocity } = gesturePoint;\n            velocity[axis] &&\n                updateOrder(value, point[axis].get(), velocity[axis]);\n            onDrag && onDrag(event, gesturePoint);\n        }, onLayoutMeasure: (measured) => registerItem(value, measured), ref: externalRef, ignoreStrict: true, children: children }));\n}\nconst ReorderItem = /*@__PURE__*/ forwardRef(ReorderItemComponent);\n\nexport { ReorderItem, ReorderItemComponent };\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AACb,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,0CAA0C;AACjE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAE5D,SAASC,qBAAqBA,CAACC,KAAK,EAAoB;EAAA,IAAlBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAClD,OAAOZ,aAAa,CAACU,KAAK,CAAC,GAAGA,KAAK,GAAGH,cAAc,CAACI,YAAY,CAAC;AACtE;AACA,SAASI,oBAAoBA,CAAAC,IAAA,EAA8EC,WAAW,EAAE;EAAA,IAA1F;MAAEC,QAAQ;MAAEC,KAAK,GAAG,CAAC,CAAC;MAAET,KAAK;MAAEU,EAAE,GAAG,IAAI;MAAEC,MAAM;MAAEC,MAAM,GAAG;IAAe,CAAC,GAAAN,IAAA;IAAPO,KAAK,GAAA1B,wBAAA,CAAAmB,IAAA,EAAAlB,SAAA;EACnG,MAAM0B,SAAS,GAAGlB,WAAW,CAAC,MAAMD,MAAM,CAACe,EAAE,CAAC,CAAC;EAC/C,MAAMK,OAAO,GAAGtB,UAAU,CAACC,cAAc,CAAC;EAC1C,MAAMsB,KAAK,GAAG;IACVC,CAAC,EAAElB,qBAAqB,CAACU,KAAK,CAACQ,CAAC,CAAC;IACjCC,CAAC,EAAEnB,qBAAqB,CAACU,KAAK,CAACS,CAAC;EACpC,CAAC;EACD,MAAMC,MAAM,GAAGrB,YAAY,CAAC,CAACkB,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC,EAAEE,KAAA;IAAA,IAAC,CAACC,OAAO,EAAEC,OAAO,CAAC,GAAAF,KAAA;IAAA,OAAKC,OAAO,IAAIC,OAAO,GAAG,CAAC,GAAG,OAAO;EAAA,EAAC;EACzG/B,SAAS,CAACgC,OAAO,CAACR,OAAO,CAAC,EAAE,+CAA+C,CAAC;EAC5E,MAAM;IAAES,IAAI;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGX,OAAO;EACnD,OAAQ1B,GAAG,CAACyB,SAAS,EAAA5B,aAAA,CAAAA,aAAA;IAAIyC,IAAI,EAAEH;EAAI,GAAKX,KAAK;IAAEe,gBAAgB,EAAE,IAAI;IAAEnB,KAAK,EAAAvB,aAAA,CAAAA,aAAA,KAAOuB,KAAK;MAAEQ,CAAC,EAAED,KAAK,CAACC,CAAC;MAAEC,CAAC,EAAEF,KAAK,CAACE,CAAC;MAAEC;IAAM,EAAE;IAAEP,MAAM,EAAEA,MAAM;IAAED,MAAM,EAAEA,CAACkB,KAAK,EAAEC,YAAY,KAAK;MACrK,MAAM;QAAEC;MAAS,CAAC,GAAGD,YAAY;MACjCC,QAAQ,CAACP,IAAI,CAAC,IACVE,WAAW,CAAC1B,KAAK,EAAEgB,KAAK,CAACQ,IAAI,CAAC,CAACQ,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACP,IAAI,CAAC,CAAC;MACzDb,MAAM,IAAIA,MAAM,CAACkB,KAAK,EAAEC,YAAY,CAAC;IACzC,CAAC;IAAEG,eAAe,EAAGC,QAAQ,IAAKT,YAAY,CAACzB,KAAK,EAAEkC,QAAQ,CAAC;IAAEC,GAAG,EAAE5B,WAAW;IAAE6B,YAAY,EAAE,IAAI;IAAE5B,QAAQ,EAAEA;EAAQ,EAAE,CAAC;AACpI;AACA,MAAM6B,WAAW,GAAG,aAAc7C,UAAU,CAACa,oBAAoB,CAAC;AAElE,SAASgC,WAAW,EAAEhC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}