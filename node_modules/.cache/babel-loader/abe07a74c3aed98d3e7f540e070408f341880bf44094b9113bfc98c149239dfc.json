{"ast": null, "code": "import React,{useEffect,useState}from'react';import Sidebar from'../../components/sidebar/Sidebar';import img from'../../../../asset/imgs/owners/approve.webp';import{Link}from'react-router-dom';import AOS from'aos';import'aos/dist/aos.css';import styles from'../../owners.module.css';import OwnersForm from'../../components/form/OwnersForm';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Care=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041E\\u0411\\u0421\\u041B\\u0423\\u0416\\u0418\\u0412\\u0410\\u041D\\u0418\\u0415 \\u041A\\u041B\\u0418\\u0415\\u041D\\u0422\\u041E\\u0412\"})}),/*#__PURE__*/_jsx(\"span\",{className:styles.underText,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:\"\\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u043C\\u0441\\u044F \\u043F\\u0440\\u0435\\u0434\\u043E\\u0441\\u0442\\u0430\\u0432\\u043B\\u044F\\u0442\\u044C \\u0432\\u0430\\u043C \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0441\\u0430\\u043C\\u043E\\u0433\\u043E \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u0433\\u043E \\u0443\\u0440\\u043E\\u0432\\u043D\\u044F.\"}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:styles.redLine}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[/*#__PURE__*/_jsx(\"img\",{src:img,alt:\"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",className:styles.banner}),/*#__PURE__*/_jsx(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"300\",children:/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0433\\u043E\\u0442\\u043E\\u0432\\u044B \\u043F\\u043E\\u043C\\u043E\\u0447\\u044C! \\u041B\\u044E\\u0431\\u0430\\u044F \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F, \\u043E\\u043F\\u0435\\u0440\\u0430\\u0442\\u0438\\u0432\\u043D\\u0430\\u044F \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C \\u0438\\u043B\\u0438 \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0430 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 \\u2014 \\u0432\\u0441\\u0435 \\u044D\\u0442\\u043E \\u043C\\u043E\\u0436\\u043D\\u043E \\u043F\\u043E\\u043B\\u0443\\u0447\\u0438\\u0442\\u044C, \\u043F\\u0440\\u043E\\u0441\\u0442\\u043E \\u043F\\u043E\\u0437\\u0432\\u043E\\u043D\\u0438\\u0432 \\u043F\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0443\",' ',/*#__PURE__*/_jsx(\"a\",{href:\"tel:6677\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"6677\"})}),' ',\"\\u0438\\u043B\\u0438 \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u0438\\u0432 \\u0437\\u0430\\u044F\\u0432\\u043A\\u0443\"]})})]}),/*#__PURE__*/_jsx(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"400\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u0412 \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u0438\\u0445 \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430\\u0445 GWM \\u0441 \\u0432\\u0430\\u043C\\u0438 \\u0441\\u0432\\u044F\\u0436\\u0443\\u0442\\u0441\\u044F, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0443\\u0431\\u0435\\u0434\\u0438\\u0442\\u044C\\u0441\\u044F \\u0432 \\u0432\\u0430\\u0448\\u0435\\u0439 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043F\\u043E\\u0441\\u043B\\u0435 \\u043F\\u043E\\u0441\\u0435\\u0449\\u0435\\u043D\\u0438\\u044F \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430. \\u041D\\u0430\\u0448\\u0438 \\u0434\\u0438\\u043B\\u0435\\u0440\\u044B \\u0433\\u043E\\u0440\\u0434\\u044F\\u0442\\u0441\\u044F \\u0441\\u0432\\u043E\\u0438\\u043C\\u0438 \\u0441\\u0442\\u0430\\u043D\\u0434\\u0430\\u0440\\u0442\\u0430\\u043C\\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044F \\u0438 \\u0445\\u043E\\u0442\\u0435\\u043B\\u0438 \\u0431\\u044B \\u0438\\u043C\\u0435\\u0442\\u044C \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u044C \\u0440\\u0435\\u0448\\u0438\\u0442\\u044C \\u043B\\u044E\\u0431\\u044B\\u0435 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u044B, \\u043A\\u043E\\u0442\\u043E\\u0440\\u044B\\u0435 \\u043C\\u043E\\u0433\\u0443\\u0442 \\u0443 \\u0432\\u0430\\u0441 \\u0432\\u043E\\u0437\\u043D\\u0438\\u043A\\u043D\\u0443\\u0442\\u044C. \\u0415\\u0441\\u043B\\u0438 \\u0443 \\u0432\\u0430\\u0441 \\u0435\\u0441\\u0442\\u044C \\u043A\\u0430\\u043A\\u0438\\u0435-\\u043B\\u0438\\u0431\\u043E \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441\\u044B \\u043F\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E \\u0438\\u043B\\u0438 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441\\u044B, \\u0432\\u044B\\u0437\\u044B\\u0432\\u0430\\u044E\\u0449\\u0438\\u0435 \\u0431\\u0435\\u0441\\u043F\\u043E\\u043A\\u043E\\u0439\\u0441\\u0442\\u0432\\u043E, \\u0441\\u0432\\u044F\\u0436\\u0438\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043C\\u0435\\u043D\\u0435\\u0434\\u0436\\u0435\\u0440\\u043E\\u043C \\u043F\\u043E \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u044E \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438 \\u043E\\u0431\\u044A\\u044F\\u0441\\u043D\\u0438\\u0442\\u0435 \\u0441\\u0432\\u043E\\u044E \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u0443. \\u0415\\u0441\\u043B\\u0438 \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0440\\u0435\\u0448\\u0435\\u043D \\u043D\\u0430 \\u044D\\u0442\\u043E\\u043C \\u044D\\u0442\\u0430\\u043F\\u0435, \\u0432\\u0430\\u043C \\u0441\\u043B\\u0435\\u0434\\u0443\\u0435\\u0442 \\u043F\\u043E\\u043F\\u0440\\u043E\\u0441\\u0438\\u0442\\u044C \\u043F\\u043E\\u0433\\u043E\\u0432\\u043E\\u0440\\u0438\\u0442\\u044C \\u0441 \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u0435\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438\\u043B\\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u0440\\u0435\\u043A\\u0442\\u043E\\u0440\\u043E\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430. \\u0415\\u0441\\u043B\\u0438 \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u043D\\u0435 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0440\\u0435\\u0448\\u0435\\u043D \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0438\\u0442\\u0435\\u043B\\u0435\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u043E\\u0433\\u043E \\u0446\\u0435\\u043D\\u0442\\u0440\\u0430 \\u0438\\u043B\\u0438 \\u0443\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u0440\\u0435\\u043A\\u0442\\u043E\\u0440\\u043E\\u043C, \\u0432\\u0430\\u043C \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u0442\\u0441\\u044F \\u043F\\u0435\\u0440\\u0435\\u0434\\u0430\\u0442\\u044C \\u0435\\u0433\\u043E \\u0432 \\u0441\\u043B\\u0443\\u0436\\u0431\\u0443 \\u043F\\u043E\\u0434\\u0434\\u0435\\u0440\\u0436\\u043A\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432 GWM. \\u0417\\u0430\\u0442\\u0435\\u043C \\u0432\\u0430\\u0448 \\u0432\\u043E\\u043F\\u0440\\u043E\\u0441 \\u0431\\u0443\\u0434\\u0435\\u0442 \\u0437\\u0430\\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D \\u0438 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D \\u0441\\u043E\\u0432\\u043C\\u0435\\u0441\\u0442\\u043D\\u043E \\u0441 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0443\\u044E\\u0449\\u0438\\u043C \\u0434\\u0438\\u043B\\u0435\\u0440\\u0441\\u043A\\u0438\\u043C \\u0446\\u0435\\u043D\\u0442\\u0440\\u043E\\u043C, \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0438\\u0432 \\u0432\\u0441\\u0435 \\u0443\\u0441\\u0438\\u043B\\u0438\\u044F \\u0434\\u043B\\u044F \\u0441\\u043A\\u043E\\u0440\\u0435\\u0439\\u0448\\u0435\\u0433\\u043E \\u0435\\u0433\\u043E \\u0440\\u0435\\u0448\\u0435\\u043D\\u0438\\u044F \\u043A \\u0432\\u0430\\u0448\\u0435\\u043C\\u0443 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u0438\\u044E.\"})}),/*#__PURE__*/_jsx(OwnersForm,{})]})})]})})})});};export default Care;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "Link", "AOS", "styles", "OwnersForm", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Care", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "underText", "redLine", "flexContent", "src", "alt", "banner", "textContent", "href"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Care/Care.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/approve.webp';\nimport { Link } from 'react-router-dom';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nimport styles from '../../owners.module.css';\nimport OwnersForm from '../../components/form/OwnersForm';\n\nconst Care = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false,\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ОБСЛУЖИВАНИЕ КЛИЕНТОВ</strong>\n                  </h1>\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    Мы всегда стремимся предоставлять вам обслуживание клиентов\n                    и качество самого высокого уровня.\n                  </span>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n                  <div\n                    className={styles.flexContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  >\n                    <img\n                      src={img}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <div\n                      className={styles.textContent}\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"300\"\n                    >\n                      <p>\n                        Мы всегда готовы помочь! Любая информация, оперативная\n                        помощь или поддержка клиентов — все это можно получить,\n                        просто позвонив по телефону{' '}\n                        <a href=\"tel:6677\">\n                          <strong>6677</strong>\n                        </a>{' '}\n                        или отправив заявку\n                      </p>\n                    </div>\n                  </div>\n                  <div\n                    className={styles.textContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"400\"\n                  >\n                    <p>\n                      В дилерских центрах GWM с вами свяжутся, чтобы убедиться в\n                      вашей удовлетворенности после посещения дилерского центра.\n                      Наши дилеры гордятся своими стандартами обслуживания и\n                      хотели бы иметь возможность решить любые проблемы, которые\n                      могут у вас возникнуть. Если у вас есть какие-либо вопросы\n                      по обслуживанию или вопросы, вызывающие беспокойство,\n                      свяжитесь с менеджером по обслуживанию дилерского центра и\n                      объясните свою проблему. Если ваш вопрос не будет решен на\n                      этом этапе, вам следует попросить поговорить с\n                      руководителем дилерского центра или управляющим директором\n                      дилерского центра. Если ваш вопрос не будет решен\n                      руководителем дилерского центра или управляющим\n                      директором, вам предлагается передать его в службу\n                      поддержки клиентов GWM. Затем ваш вопрос будет\n                      зарегистрирован и обработан совместно с соответствующим\n                      дилерским центром, приложив все усилия для скорейшего его\n                      решения к вашему удовлетворению.\n                    </p>\n                  </div>\n                  <OwnersForm />\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Care;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,GAAG,KAAM,4CAA4C,CAC5D,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAEzB,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,UAAU,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1D,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdK,GAAG,CAACY,IAAI,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,KACR,CAAC,CAAC,CAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAExB,MAAM,CAACyB,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACP,OAAO,GAAE,CAAC,cACXO,IAAA,SAAMqB,SAAS,CAAExB,MAAM,CAAC0B,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAExB,MAAM,CAAC2B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACpBpB,IAAA,WAAAoB,QAAA,CAAQ,2HAAqB,CAAQ,CAAC,CACpC,CAAC,cACLpB,IAAA,SACEqB,SAAS,CAAExB,MAAM,CAAC4B,SAAU,CAC5B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAL,QAAA,CACrB,0fAGD,CAAM,CAAC,cACPpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAExB,MAAM,CAAC6B,OAAQ,CACxB,CAAC,cACLxB,KAAA,QACEmB,SAAS,CAAExB,MAAM,CAAC8B,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAP,QAAA,eAEpBpB,IAAA,QACE4B,GAAG,CAAElC,GAAI,CACTmC,GAAG,CAAC,uGAAuB,CAC3BR,SAAS,CAAExB,MAAM,CAACiC,MAAO,CAC1B,CAAC,cACF9B,IAAA,QACEqB,SAAS,CAAExB,MAAM,CAACkC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAX,QAAA,cAEpBlB,KAAA,MAAAkB,QAAA,EAAG,gtBAG0B,CAAC,GAAG,cAC/BpB,IAAA,MAAGgC,IAAI,CAAC,UAAU,CAAAZ,QAAA,cAChBpB,IAAA,WAAAoB,QAAA,CAAQ,MAAI,CAAQ,CAAC,CACpB,CAAC,CAAC,GAAG,CAAC,0GAEX,EAAG,CAAC,CACD,CAAC,EACH,CAAC,cACNpB,IAAA,QACEqB,SAAS,CAAExB,MAAM,CAACkC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAX,QAAA,cAEpBpB,IAAA,MAAAoB,QAAA,CAAG,spJAkBH,CAAG,CAAC,CACD,CAAC,cACNpB,IAAA,CAACF,UAAU,GAAE,CAAC,EACX,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAO,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}