{"ast": null, "code": "// for navFeatureData\nimport navFeatureIconWhite1 from'../imgs/icons/icon3-black.svg';import navFeatureIconWhite2 from'../imgs/icons/icon1-black.svg';import{RiGlobalLine}from'react-icons/ri';import{GrLanguage}from'react-icons/gr';export const menuData=[{id:1,title:'Модели',dropMenu:true},{id:2,title:'Предложение',url:'/offers',dropMenu:false},{id:3,title:'Владельцам',url:'/owners',dropMenu:false},{id:4,title:'Узнать больше',dropMenu:true}];export const navFeatureData=[{id:1,title:'Свяжитесь с нами',icon:navFeatureIconWhite1,url:'/contact'},{id:2,title:'Записаться на тест-драйв',icon:navFeatureIconWhite2,url:'/book-a-test-drive'}// {\n//   id: 3,\n//   title: 'RU',\n//   dobleTitle: 'TJ',\n//   url: false,\n// },\n];// navbarData.js\nexport const discoverMenuData=[{id:1,title:'GWM Глобальный',items:[{id:1,title:'Новости',url:'/news-list'},{id:2,title:'О GWM',url:'/about-gwm'},{id:3,title:'История',url:'/about-gwm/history'},{id:4,title:'Контакты',url:'/contact'},{id:5,title:'Карьера',target_url:true,url:'https://job.vector.tj/'}]}];", "map": {"version": 3, "names": ["navFeatureIconWhite1", "navFeatureIconWhite2", "RiGlobalLine", "GrLanguage", "menuData", "id", "title", "dropMenu", "url", "navFeatureData", "icon", "discoverMenuData", "items", "target_url"], "sources": ["/var/www/html/gwm.tj/src/asset/data/navbarData.js"], "sourcesContent": ["// for navFeatureData\nimport navFeatureIconWhite1 from '../imgs/icons/icon3-black.svg';\nimport navFeatureIconWhite2 from '../imgs/icons/icon1-black.svg';\nimport { RiGlobalLine } from 'react-icons/ri';\nimport { GrLanguage } from 'react-icons/gr';\n\nexport const menuData = [\n  {\n    id: 1,\n    title: 'Модели',\n    dropMenu: true,\n  },\n  {\n    id: 2,\n    title: 'Предложение',\n    url: '/offers',\n    dropMenu: false,\n  },\n  {\n    id: 3,\n    title: 'Владельцам',\n    url: '/owners',\n    dropMenu: false,\n  },\n  {\n    id: 4,\n    title: 'Узнать больше',\n    dropMenu: true,\n  },\n];\n\nexport const navFeatureData = [\n  {\n    id: 1,\n    title: 'Свяжитесь с нами',\n    icon: navFeatureIconWhite1,\n    url: '/contact',\n  },\n  {\n    id: 2,\n    title: 'Записаться на тест-драйв',\n    icon: navFeatureIconWhite2,\n    url: '/book-a-test-drive',\n  },\n  // {\n  //   id: 3,\n  //   title: 'RU',\n  //   dobleTitle: 'TJ',\n  //   url: false,\n  // },\n];\n\n// navbarData.js\n\nexport const discoverMenuData = [\n  {\n    id: 1,\n    title: 'GWM Глобальный',\n    items: [\n      {\n        id: 1,\n        title: 'Новости',\n        url: '/news-list',\n      },\n      {\n        id: 2,\n        title: 'О GWM',\n        url: '/about-gwm',\n      },\n      {\n        id: 3,\n        title: 'История',\n        url: '/about-gwm/history',\n      },\n      {\n        id: 4,\n        title: 'Контакты',\n        url: '/contact',\n      },\n      {\n        id: 5,\n        title: 'Карьера',\n        target_url: true,\n        url: 'https://job.vector.tj/',\n      },\n    ],\n  },\n];\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,oBAAoB,KAAM,+BAA+B,CAChE,MAAO,CAAAC,oBAAoB,KAAM,+BAA+B,CAChE,OAASC,YAAY,KAAQ,gBAAgB,CAC7C,OAASC,UAAU,KAAQ,gBAAgB,CAE3C,MAAO,MAAM,CAAAC,QAAQ,CAAG,CACtB,CACEC,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,QAAQ,CACfC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,aAAa,CACpBE,GAAG,CAAE,SAAS,CACdD,QAAQ,CAAE,KACZ,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,YAAY,CACnBE,GAAG,CAAE,SAAS,CACdD,QAAQ,CAAE,KACZ,CAAC,CACD,CACEF,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,eAAe,CACtBC,QAAQ,CAAE,IACZ,CAAC,CACF,CAED,MAAO,MAAM,CAAAE,cAAc,CAAG,CAC5B,CACEJ,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,kBAAkB,CACzBI,IAAI,CAAEV,oBAAoB,CAC1BQ,GAAG,CAAE,UACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,0BAA0B,CACjCI,IAAI,CAAET,oBAAoB,CAC1BO,GAAG,CAAE,oBACP,CACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAED;AAEA,MAAO,MAAM,CAAAG,gBAAgB,CAAG,CAC9B,CACEN,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,gBAAgB,CACvBM,KAAK,CAAE,CACL,CACEP,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,YACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,OAAO,CACdE,GAAG,CAAE,YACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBE,GAAG,CAAE,oBACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,UAAU,CACjBE,GAAG,CAAE,UACP,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChBO,UAAU,CAAE,IAAI,CAChBL,GAAG,CAAE,wBACP,CAAC,CAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}