{"ast": null, "code": "import { useRef } from 'react';\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n  const ref = useRef(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\nexport { useConstant };", "map": {"version": 3, "names": ["useRef", "useConstant", "init", "ref", "current"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/use-constant.mjs"], "sourcesContent": ["import { useRef } from 'react';\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = useRef(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\nexport { useConstant };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EACvB,MAAMC,GAAG,GAAGH,MAAM,CAAC,IAAI,CAAC;EACxB,IAAIG,GAAG,CAACC,OAAO,KAAK,IAAI,EAAE;IACtBD,GAAG,CAACC,OAAO,GAAGF,IAAI,CAAC,CAAC;EACxB;EACA,OAAOC,GAAG,CAACC,OAAO;AACtB;AAEA,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}