{"ast": null, "code": "import _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"container\", \"target\", \"layoutEffect\"];\nimport { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), \"You have defined a \".concat(name, \" options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its `layoutEffect: false` option.\"));\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll() {\n  let _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    {\n      container,\n      target,\n      layoutEffect = true\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scroll((_progress, _ref2) => {\n      let {\n        x,\n        y\n      } = _ref2;\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, _objectSpread(_objectSpread({}, options), {}, {\n      container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n      target: (target === null || target === void 0 ? void 0 : target.current) || undefined\n    }));\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "warning", "useEffect", "scroll", "useConstant", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "concat", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "_ref", "arguments", "length", "undefined", "container", "target", "layoutEffect", "options", "_objectWithoutProperties", "_excluded", "values", "useLifecycleEffect", "_progress", "_ref2", "x", "y", "set", "progress", "_objectSpread", "JSON", "stringify", "offset"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scroll((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BP,OAAO,CAACQ,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,wBAAAC,MAAA,CAAwBJ,IAAI,gNAA+M,CAAC;AACpR;AACA,MAAMK,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,OAAO,EAAEd,WAAW,CAAC,CAAC,CAAC;EACvBe,eAAe,EAAEf,WAAW,CAAC,CAAC,CAAC;EAC/BgB,eAAe,EAAEhB,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASiB,SAASA,CAAA,EAA8D;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;IAA3D;MAAEG,SAAS;MAAEC,MAAM;MAAEC,YAAY,GAAG;IAAiB,CAAC,GAAAN,IAAA;IAATO,OAAO,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EACnE,MAAMC,MAAM,GAAGxB,WAAW,CAACQ,wBAAwB,CAAC;EACpD,MAAMiB,kBAAkB,GAAGL,YAAY,GACjCnB,yBAAyB,GACzBH,SAAS;EACf2B,kBAAkB,CAAC,MAAM;IACrBvB,UAAU,CAAC,QAAQ,EAAEiB,MAAM,CAAC;IAC5BjB,UAAU,CAAC,WAAW,EAAEgB,SAAS,CAAC;IAClC,OAAOnB,MAAM,CAAC,CAAC2B,SAAS,EAAAC,KAAA,KAAgB;MAAA,IAAd;QAAEC,CAAC;QAAEC;MAAG,CAAC,GAAAF,KAAA;MAC/BH,MAAM,CAACf,OAAO,CAACqB,GAAG,CAACF,CAAC,CAACtB,OAAO,CAAC;MAC7BkB,MAAM,CAACb,eAAe,CAACmB,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCP,MAAM,CAACd,OAAO,CAACoB,GAAG,CAACD,CAAC,CAACvB,OAAO,CAAC;MAC7BkB,MAAM,CAACZ,eAAe,CAACkB,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAAC,aAAA,CAAAA,aAAA,KACMX,OAAO;MACVH,SAAS,EAAE,CAAAA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEZ,OAAO,KAAIW,SAAS;MAC1CE,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEb,OAAO,KAAIW;IAAS,EACvC,CAAC;EACN,CAAC,EAAE,CAACC,SAAS,EAAEC,MAAM,EAAEc,IAAI,CAACC,SAAS,CAACb,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOX,MAAM;AACjB;AAEA,SAASX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}