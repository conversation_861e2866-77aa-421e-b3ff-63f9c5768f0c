{"ast": null, "code": "import { MotionValue, transformProps, acceleratedValues } from 'motion-dom';\nclass WillChangeMotionValue extends MotionValue {\n  constructor() {\n    super(...arguments);\n    this.isEnabled = false;\n  }\n  add(name) {\n    if (transformProps.has(name) || acceleratedValues.has(name)) {\n      this.isEnabled = true;\n      this.update();\n    }\n  }\n  update() {\n    this.set(this.isEnabled ? \"transform\" : \"auto\");\n  }\n}\nexport { WillChangeMotionValue };", "map": {"version": 3, "names": ["MotionValue", "transformProps", "acceleratedValues", "WillChangeMotionValue", "constructor", "arguments", "isEnabled", "add", "name", "has", "update", "set"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-will-change/WillChangeMotionValue.mjs"], "sourcesContent": ["import { MotionValue, transformProps, acceleratedValues } from 'motion-dom';\n\nclass WillChangeMotionValue extends MotionValue {\n    constructor() {\n        super(...arguments);\n        this.isEnabled = false;\n    }\n    add(name) {\n        if (transformProps.has(name) || acceleratedValues.has(name)) {\n            this.isEnabled = true;\n            this.update();\n        }\n    }\n    update() {\n        this.set(this.isEnabled ? \"transform\" : \"auto\");\n    }\n}\n\nexport { WillChangeMotionValue };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,YAAY;AAE3E,MAAMC,qBAAqB,SAASH,WAAW,CAAC;EAC5CI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;EAC1B;EACAC,GAAGA,CAACC,IAAI,EAAE;IACN,IAAIP,cAAc,CAACQ,GAAG,CAACD,IAAI,CAAC,IAAIN,iBAAiB,CAACO,GAAG,CAACD,IAAI,CAAC,EAAE;MACzD,IAAI,CAACF,SAAS,GAAG,IAAI;MACrB,IAAI,CAACI,MAAM,CAAC,CAAC;IACjB;EACJ;EACAA,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,GAAG,CAAC,IAAI,CAACL,SAAS,GAAG,WAAW,GAAG,MAAM,CAAC;EACnD;AACJ;AAEA,SAASH,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}