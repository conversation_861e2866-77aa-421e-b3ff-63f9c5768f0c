{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/layout/Navbar/components/models/NavModels.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useMemo } from 'react';\nimport styles from './navModels.module.css';\nimport { Link, NavLink } from 'react-router-dom';\nimport FilterSwiper from '../../../../components/FilterSlide/FilterSwiper';\nimport arrowIcon from '../../../../asset/imgs/icons/arrow.svg';\n\n// Добавляем импорт скелетона\nimport SkeletonCard from '../../../../components/SkeletonCard/SkeletonCard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst typeOptions = [{\n  id: 1,\n  title: 'Электрический'\n}, {\n  id: 2,\n  title: 'Гибрид'\n}, {\n  id: 3,\n  title: 'Бензин'\n}, {\n  id: 4,\n  title: 'Дизель'\n}];\nconst bodyTypeOptions = [{\n  id: 1,\n  title: 'Седан'\n}, {\n  id: 2,\n  title: 'Кроссовер'\n}, {\n  id: 3,\n  title: 'Внедорожник'\n}, {\n  id: 4,\n  title: 'Пикапы'\n}];\nconst noCarMessage = 'К сожалению, модель, которую вы ищете, в настоящее время недоступна. Попробуйте использовать другие критерии поиска.';\nconst NavModels = ({\n  onClose,\n  setActiveMobMenu\n}) => {\n  _s();\n  const [visible, setVisible] = useState(false);\n  const [cars, setCars] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeType, setActiveType] = useState(null);\n  const [activeBodyType, setActiveBodyType] = useState(null);\n  const [activeModel, setActiveModel] = useState('Все модели');\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n  useEffect(() => {\n    const timer = setTimeout(() => setVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n  useEffect(() => {\n    const fetchCars = async () => {\n      try {\n        const response = await fetch('https://api.gwm.tj/api/v1/models');\n        const data = await response.json();\n        setCars(data.models);\n      } catch (error) {} finally {\n        setLoading(false);\n      }\n    };\n    fetchCars();\n  }, []);\n  const filteredCars = useMemo(() => {\n    return cars.filter(car => {\n      var _typeOptions$find, _car$type, _bodyTypeOptions$find, _car$body_type;\n      const matchModel = activeModel === 'Все модели' || car.category === activeModel;\n      const matchType = !activeType || ((_typeOptions$find = typeOptions.find(t => t.id === activeType)) === null || _typeOptions$find === void 0 ? void 0 : _typeOptions$find.title.toLowerCase()) === ((_car$type = car.type) === null || _car$type === void 0 ? void 0 : _car$type.toLowerCase());\n      const matchBody = !activeBodyType || ((_bodyTypeOptions$find = bodyTypeOptions.find(b => b.id === activeBodyType)) === null || _bodyTypeOptions$find === void 0 ? void 0 : _bodyTypeOptions$find.title.toLowerCase()) === ((_car$body_type = car.body_type) === null || _car$body_type === void 0 ? void 0 : _car$body_type.toLowerCase());\n      return matchModel && matchType && matchBody;\n    });\n  }, [cars, activeModel, activeType, activeBodyType]);\n  const handelCar = () => {\n    onClose();\n    setActiveMobMenu(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.overlay} ${visible ? styles.show : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.content,\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: styles.sidebar,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.setting,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u041F\\u0430\\u0440\\u0430\\u043C\\u0435\\u0442\\u0440\\u044B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: styles.types,\n            children: typeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: activeType === item.id ? styles.active : '',\n              onClick: () => setActiveType(activeType === item.id ? null : item.id),\n              children: item.title\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: styles.bodyTypes,\n            children: bodyTypeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: activeBodyType === item.id ? styles.active : '',\n              onClick: () => setActiveBodyType(activeBodyType === item.id ? null : item.id),\n              children: item.title\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.services,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u0423\\u0441\\u043B\\u0443\\u0433\\u0438\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              onClick: onClose,\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/book-a-test-drive\",\n                className: \"link\",\n                children: [\"\\u0422\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432 \", /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: arrowIcon,\n                  alt: \"\",\n                  className: \"linkIcon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              onClick: onClose,\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/models\",\n                className: \"link\",\n                children: [\"\\u041D\\u0430\\u0448\\u0438 \\u043C\\u043E\\u0434\\u0435\\u043B\\u0438\", ' ', /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: arrowIcon,\n                  alt: \"\",\n                  className: \"linkIcon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: styles.main,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.filterBar,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.filterSelected,\n            onClick: () => setIsFilterOpen(prev => !prev),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0424\\u0438\\u043B\\u044C\\u0442\\u0440\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: isFilterOpen ? styles.iconMinus : styles.iconPlus,\n              children: isFilterOpen ? '-' : '+'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${styles.filterDropdownContainer} ${isFilterOpen ? styles.show : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.filterList,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: styles.filterItem,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: styles.setting,\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u041F\\u0430\\u0440\\u0430\\u043C\\u0435\\u0442\\u0440\\u044B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: styles.types,\n                    children: typeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: activeType === item.id ? styles.active : '',\n                      onClick: () => setActiveType(activeType === item.id ? null : item.id),\n                      children: item.title\n                    }, item.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: styles.bodyTypes,\n                    children: bodyTypeOptions.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                      className: activeBodyType === item.id ? styles.active : '',\n                      onClick: () => setActiveBodyType(activeBodyType === item.id ? null : item.id),\n                      children: item.title\n                    }, item.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterSwiper, {\n          activeModel: activeModel,\n          setActiveModel: setActiveModel,\n          cars: cars\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.grid,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: Array.from({\n              length: 6\n            }).map((_, index) => /*#__PURE__*/_jsxDEV(SkeletonCard, {}, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this))\n          }, void 0, false) : filteredCars.length > 0 ? filteredCars.map(car => /*#__PURE__*/_jsxDEV(Link, {\n            to: `models/${car.slug}`,\n            className: styles.card,\n            onClick: handelCar,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: car.preview_show,\n              alt: car.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: styles.cardInfo,\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: car.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this), car.in_stock && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: car.in_stock === 'Yes' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.inStok,\n                children: \"\\u0412 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0435\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: styles.noStok,\n                children: \"\\u041D\\u0435 \\u0432 \\u043D\\u0430\\u043B\\u0438\\u0447\\u0438\\u0438\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.label,\n              children: car.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this)]\n          }, car.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.noCarText,\n            children: noCarMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(NavModels, \"AMPhSE3yGL2NrQcWVLUO+ZlrTwI=\");\n_c = NavModels;\nexport default NavModels;\nvar _c;\n$RefreshReg$(_c, \"NavModels\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useMemo", "styles", "Link", "NavLink", "FilterSwiper", "arrowIcon", "SkeletonCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "typeOptions", "id", "title", "bodyTypeOptions", "noCarMessage", "NavModels", "onClose", "setActiveMobMenu", "_s", "visible", "setVisible", "cars", "setCars", "loading", "setLoading", "activeType", "setActiveType", "activeBodyType", "setActiveBodyType", "activeModel", "setActiveModel", "isFilterOpen", "setIsFilterOpen", "timer", "setTimeout", "clearTimeout", "fetchCars", "response", "fetch", "data", "json", "models", "error", "filteredCars", "filter", "car", "_typeOptions$find", "_car$type", "_bodyTypeOptions$find", "_car$body_type", "matchModel", "category", "matchType", "find", "t", "toLowerCase", "type", "matchBody", "b", "body_type", "handelCar", "className", "overlay", "show", "children", "content", "sidebar", "setting", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "types", "map", "item", "active", "onClick", "bodyTypes", "services", "to", "src", "alt", "main", "filterBar", "filterSelected", "prev", "iconMinus", "iconPlus", "filterDropdownContainer", "filterList", "filterItem", "grid", "Array", "from", "length", "_", "index", "slug", "card", "preview_show", "cardInfo", "in_stock", "inStok", "noStok", "label", "noCarText", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/layout/Navbar/components/models/NavModels.jsx"], "sourcesContent": ["import React, { useEffect, useState, useMemo } from 'react';\nimport styles from './navModels.module.css';\nimport { Link, NavLink } from 'react-router-dom';\nimport FilterSwiper from '../../../../components/FilterSlide/FilterSwiper';\nimport arrowIcon from '../../../../asset/imgs/icons/arrow.svg';\n\n// Добавляем импорт скелетона\nimport SkeletonCard from '../../../../components/SkeletonCard/SkeletonCard';\n\nconst typeOptions = [\n  { id: 1, title: 'Электрический' },\n  { id: 2, title: 'Гибрид' },\n  { id: 3, title: 'Бензин' },\n  { id: 4, title: 'Дизель' },\n];\n\nconst bodyTypeOptions = [\n  { id: 1, title: 'Седан' },\n  { id: 2, title: 'Кроссовер' },\n  { id: 3, title: 'Внедорожник' },\n  { id: 4, title: 'Пикапы' },\n];\n\nconst noCarMessage =\n  'К сожалению, модель, которую вы ищете, в настоящее время недоступна. Попробуйте использовать другие критерии поиска.';\n\nconst NavModels = ({ onClose, setActiveMobMenu }) => {\n  const [visible, setVisible] = useState(false);\n  const [cars, setCars] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeType, setActiveType] = useState(null);\n  const [activeBodyType, setActiveBodyType] = useState(null);\n  const [activeModel, setActiveModel] = useState('Все модели');\n  const [isFilterOpen, setIsFilterOpen] = useState(false);\n\n  useEffect(() => {\n    const timer = setTimeout(() => setVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    const fetchCars = async () => {\n      try {\n        const response = await fetch('https://api.gwm.tj/api/v1/models');\n        const data = await response.json();\n        setCars(data.models);\n      } catch (error) {\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCars();\n  }, []);\n\n  const filteredCars = useMemo(() => {\n    return cars.filter((car) => {\n      const matchModel =\n        activeModel === 'Все модели' || car.category === activeModel;\n\n      const matchType =\n        !activeType ||\n        typeOptions.find((t) => t.id === activeType)?.title.toLowerCase() ===\n          car.type?.toLowerCase();\n\n      const matchBody =\n        !activeBodyType ||\n        bodyTypeOptions\n          .find((b) => b.id === activeBodyType)\n          ?.title.toLowerCase() === car.body_type?.toLowerCase();\n\n      return matchModel && matchType && matchBody;\n    });\n  }, [cars, activeModel, activeType, activeBodyType]);\n\n  const handelCar = () => {\n    onClose();\n    setActiveMobMenu(false);\n  };\n\n  return (\n    <div className={`${styles.overlay} ${visible ? styles.show : ''}`}>\n      <div className={styles.content}>\n        {/* Сайдбар */}\n        <aside className={styles.sidebar}>\n          <div className={styles.setting}>\n            <h4>Параметры</h4>\n\n            <ul className={styles.types}>\n              {typeOptions.map((item) => (\n                <li\n                  key={item.id}\n                  className={activeType === item.id ? styles.active : ''}\n                  onClick={() =>\n                    setActiveType(activeType === item.id ? null : item.id)\n                  }\n                >\n                  {item.title}\n                </li>\n              ))}\n            </ul>\n\n            <ul className={styles.bodyTypes}>\n              {bodyTypeOptions.map((item) => (\n                <li\n                  key={item.id}\n                  className={activeBodyType === item.id ? styles.active : ''}\n                  onClick={() =>\n                    setActiveBodyType(\n                      activeBodyType === item.id ? null : item.id\n                    )\n                  }\n                >\n                  {item.title}\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div className={styles.services}>\n            <h4>Услуги</h4>\n            <ul>\n              <li onClick={onClose}>\n                <Link to=\"/book-a-test-drive\" className=\"link\">\n                  Тест-драйв <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                </Link>\n              </li>\n              <li onClick={onClose}>\n                <NavLink to=\"/models\" className=\"link\">\n                  Наши модели{' '}\n                  <img src={arrowIcon} alt=\"\" className=\"linkIcon\" />\n                </NavLink>\n              </li>\n            </ul>\n          </div>\n        </aside>\n\n        {/* Основной блок */}\n        <main className={styles.main}>\n          <div className={styles.filterBar}>\n            <div\n              className={styles.filterSelected}\n              onClick={() => setIsFilterOpen((prev) => !prev)}\n            >\n              <span>Фильтр</span>\n              <div\n                className={isFilterOpen ? styles.iconMinus : styles.iconPlus}\n              >\n                {isFilterOpen ? '-' : '+'}\n              </div>\n            </div>\n\n            <div\n              className={`${styles.filterDropdownContainer} ${\n                isFilterOpen ? styles.show : ''\n              }`}\n            >\n              <div className={styles.filterList}>\n                <div className={styles.filterItem}>\n                  <div className={styles.setting}>\n                    <h4>Параметры</h4>\n                    <ul className={styles.types}>\n                      {typeOptions.map((item) => (\n                        <li\n                          key={item.id}\n                          className={\n                            activeType === item.id ? styles.active : ''\n                          }\n                          onClick={() =>\n                            setActiveType(\n                              activeType === item.id ? null : item.id\n                            )\n                          }\n                        >\n                          {item.title}\n                        </li>\n                      ))}\n                    </ul>\n                    <ul className={styles.bodyTypes}>\n                      {bodyTypeOptions.map((item) => (\n                        <li\n                          key={item.id}\n                          className={\n                            activeBodyType === item.id ? styles.active : ''\n                          }\n                          onClick={() =>\n                            setActiveBodyType(\n                              activeBodyType === item.id ? null : item.id\n                            )\n                          }\n                        >\n                          {item.title}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <FilterSwiper\n            activeModel={activeModel}\n            setActiveModel={setActiveModel}\n            cars={cars}\n          />\n\n          <div className={styles.grid}>\n            {loading ? (\n              <>\n                {Array.from({ length: 6 }).map((_, index) => (\n                  <SkeletonCard key={index} />\n                ))}\n              </>\n            ) : filteredCars.length > 0 ? (\n              filteredCars.map((car) => (\n                <Link\n                  to={`models/${car.slug}`}\n                  key={car.id}\n                  className={styles.card}\n                  onClick={handelCar}\n                >\n                  <img src={car.preview_show} alt={car.title} />\n                  <div className={styles.cardInfo}>\n                    <h4>{car.title}</h4>\n                  </div>\n                  {car.in_stock && (\n                    <div>\n                      {car.in_stock === 'Yes' ? (\n                        <span className={styles.inStok}>В наличие</span>\n                      ) : (\n                        <span className={styles.noStok}>Не в наличии</span>\n                      )}\n                    </div>\n                  )}\n                  <span className={styles.label}>{car.type}</span>\n                </Link>\n              ))\n            ) : (\n              <div className={styles.noCarText}>{noCarMessage}</div>\n            )}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default NavModels;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC3D,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,SAASC,IAAI,EAAEC,OAAO,QAAQ,kBAAkB;AAChD,OAAOC,YAAY,MAAM,iDAAiD;AAC1E,OAAOC,SAAS,MAAM,wCAAwC;;AAE9D;AACA,OAAOC,YAAY,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5E,MAAMC,WAAW,GAAG,CAClB;EAAEC,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAgB,CAAC,EACjC;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,EAC1B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,CAC3B;AAED,MAAMC,eAAe,GAAG,CACtB;EAAEF,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACzB;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAY,CAAC,EAC7B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC/B;EAAED,EAAE,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAS,CAAC,CAC3B;AAED,MAAME,YAAY,GAChB,sHAAsH;AAExH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,YAAY,CAAC;EAC5D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACd,MAAMoC,KAAK,GAAGC,UAAU,CAAC,MAAMd,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACpD,OAAO,MAAMe,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAENpC,SAAS,CAAC,MAAM;IACd,MAAMuC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClClB,OAAO,CAACiB,IAAI,CAACE,MAAM,CAAC;MACtB,CAAC,CAAC,OAAOC,KAAK,EAAE,CAChB,CAAC,SAAS;QACRlB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,YAAY,GAAG5C,OAAO,CAAC,MAAM;IACjC,OAAOsB,IAAI,CAACuB,MAAM,CAAEC,GAAG,IAAK;MAAA,IAAAC,iBAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,cAAA;MAC1B,MAAMC,UAAU,GACdrB,WAAW,KAAK,YAAY,IAAIgB,GAAG,CAACM,QAAQ,KAAKtB,WAAW;MAE9D,MAAMuB,SAAS,GACb,CAAC3B,UAAU,IACX,EAAAqB,iBAAA,GAAApC,WAAW,CAAC2C,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3C,EAAE,KAAKc,UAAU,CAAC,cAAAqB,iBAAA,uBAA5CA,iBAAA,CAA8ClC,KAAK,CAAC2C,WAAW,CAAC,CAAC,QAAAR,SAAA,GAC/DF,GAAG,CAACW,IAAI,cAAAT,SAAA,uBAARA,SAAA,CAAUQ,WAAW,CAAC,CAAC;MAE3B,MAAME,SAAS,GACb,CAAC9B,cAAc,IACf,EAAAqB,qBAAA,GAAAnC,eAAe,CACZwC,IAAI,CAAEK,CAAC,IAAKA,CAAC,CAAC/C,EAAE,KAAKgB,cAAc,CAAC,cAAAqB,qBAAA,uBADvCA,qBAAA,CAEIpC,KAAK,CAAC2C,WAAW,CAAC,CAAC,QAAAN,cAAA,GAAKJ,GAAG,CAACc,SAAS,cAAAV,cAAA,uBAAbA,cAAA,CAAeM,WAAW,CAAC,CAAC;MAE1D,OAAOL,UAAU,IAAIE,SAAS,IAAIK,SAAS;IAC7C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpC,IAAI,EAAEQ,WAAW,EAAEJ,UAAU,EAAEE,cAAc,CAAC,CAAC;EAEnD,MAAMiC,SAAS,GAAGA,CAAA,KAAM;IACtB5C,OAAO,CAAC,CAAC;IACTC,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,oBACEV,OAAA;IAAKsD,SAAS,EAAE,GAAG7D,MAAM,CAAC8D,OAAO,IAAI3C,OAAO,GAAGnB,MAAM,CAAC+D,IAAI,GAAG,EAAE,EAAG;IAAAC,QAAA,eAChEzD,OAAA;MAAKsD,SAAS,EAAE7D,MAAM,CAACiE,OAAQ;MAAAD,QAAA,gBAE7BzD,OAAA;QAAOsD,SAAS,EAAE7D,MAAM,CAACkE,OAAQ;QAAAF,QAAA,gBAC/BzD,OAAA;UAAKsD,SAAS,EAAE7D,MAAM,CAACmE,OAAQ;UAAAH,QAAA,gBAC7BzD,OAAA;YAAAyD,QAAA,EAAI;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAElBhE,OAAA;YAAIsD,SAAS,EAAE7D,MAAM,CAACwE,KAAM;YAAAR,QAAA,EACzBtD,WAAW,CAAC+D,GAAG,CAAEC,IAAI,iBACpBnE,OAAA;cAEEsD,SAAS,EAAEpC,UAAU,KAAKiD,IAAI,CAAC/D,EAAE,GAAGX,MAAM,CAAC2E,MAAM,GAAG,EAAG;cACvDC,OAAO,EAAEA,CAAA,KACPlD,aAAa,CAACD,UAAU,KAAKiD,IAAI,CAAC/D,EAAE,GAAG,IAAI,GAAG+D,IAAI,CAAC/D,EAAE,CACtD;cAAAqD,QAAA,EAEAU,IAAI,CAAC9D;YAAK,GANN8D,IAAI,CAAC/D,EAAE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAELhE,OAAA;YAAIsD,SAAS,EAAE7D,MAAM,CAAC6E,SAAU;YAAAb,QAAA,EAC7BnD,eAAe,CAAC4D,GAAG,CAAEC,IAAI,iBACxBnE,OAAA;cAEEsD,SAAS,EAAElC,cAAc,KAAK+C,IAAI,CAAC/D,EAAE,GAAGX,MAAM,CAAC2E,MAAM,GAAG,EAAG;cAC3DC,OAAO,EAAEA,CAAA,KACPhD,iBAAiB,CACfD,cAAc,KAAK+C,IAAI,CAAC/D,EAAE,GAAG,IAAI,GAAG+D,IAAI,CAAC/D,EAC3C,CACD;cAAAqD,QAAA,EAEAU,IAAI,CAAC9D;YAAK,GARN8D,IAAI,CAAC/D,EAAE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENhE,OAAA;UAAKsD,SAAS,EAAE7D,MAAM,CAAC8E,QAAS;UAAAd,QAAA,gBAC9BzD,OAAA;YAAAyD,QAAA,EAAI;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACfhE,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAIqE,OAAO,EAAE5D,OAAQ;cAAAgD,QAAA,eACnBzD,OAAA,CAACN,IAAI;gBAAC8E,EAAE,EAAC,oBAAoB;gBAAClB,SAAS,EAAC,MAAM;gBAAAG,QAAA,GAAC,0DAClC,eAAAzD,OAAA;kBAAKyE,GAAG,EAAE5E,SAAU;kBAAC6E,GAAG,EAAC,EAAE;kBAACpB,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLhE,OAAA;cAAIqE,OAAO,EAAE5D,OAAQ;cAAAgD,QAAA,eACnBzD,OAAA,CAACL,OAAO;gBAAC6E,EAAE,EAAC,SAAS;gBAAClB,SAAS,EAAC,MAAM;gBAAAG,QAAA,GAAC,+DAC1B,EAAC,GAAG,eACfzD,OAAA;kBAAKyE,GAAG,EAAE5E,SAAU;kBAAC6E,GAAG,EAAC,EAAE;kBAACpB,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRhE,OAAA;QAAMsD,SAAS,EAAE7D,MAAM,CAACkF,IAAK;QAAAlB,QAAA,gBAC3BzD,OAAA;UAAKsD,SAAS,EAAE7D,MAAM,CAACmF,SAAU;UAAAnB,QAAA,gBAC/BzD,OAAA;YACEsD,SAAS,EAAE7D,MAAM,CAACoF,cAAe;YACjCR,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAEqD,IAAI,IAAK,CAACA,IAAI,CAAE;YAAArB,QAAA,gBAEhDzD,OAAA;cAAAyD,QAAA,EAAM;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBhE,OAAA;cACEsD,SAAS,EAAE9B,YAAY,GAAG/B,MAAM,CAACsF,SAAS,GAAGtF,MAAM,CAACuF,QAAS;cAAAvB,QAAA,EAE5DjC,YAAY,GAAG,GAAG,GAAG;YAAG;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YACEsD,SAAS,EAAE,GAAG7D,MAAM,CAACwF,uBAAuB,IAC1CzD,YAAY,GAAG/B,MAAM,CAAC+D,IAAI,GAAG,EAAE,EAC9B;YAAAC,QAAA,eAEHzD,OAAA;cAAKsD,SAAS,EAAE7D,MAAM,CAACyF,UAAW;cAAAzB,QAAA,eAChCzD,OAAA;gBAAKsD,SAAS,EAAE7D,MAAM,CAAC0F,UAAW;gBAAA1B,QAAA,eAChCzD,OAAA;kBAAKsD,SAAS,EAAE7D,MAAM,CAACmE,OAAQ;kBAAAH,QAAA,gBAC7BzD,OAAA;oBAAAyD,QAAA,EAAI;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBhE,OAAA;oBAAIsD,SAAS,EAAE7D,MAAM,CAACwE,KAAM;oBAAAR,QAAA,EACzBtD,WAAW,CAAC+D,GAAG,CAAEC,IAAI,iBACpBnE,OAAA;sBAEEsD,SAAS,EACPpC,UAAU,KAAKiD,IAAI,CAAC/D,EAAE,GAAGX,MAAM,CAAC2E,MAAM,GAAG,EAC1C;sBACDC,OAAO,EAAEA,CAAA,KACPlD,aAAa,CACXD,UAAU,KAAKiD,IAAI,CAAC/D,EAAE,GAAG,IAAI,GAAG+D,IAAI,CAAC/D,EACvC,CACD;sBAAAqD,QAAA,EAEAU,IAAI,CAAC9D;oBAAK,GAVN8D,IAAI,CAAC/D,EAAE;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAWV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACLhE,OAAA;oBAAIsD,SAAS,EAAE7D,MAAM,CAAC6E,SAAU;oBAAAb,QAAA,EAC7BnD,eAAe,CAAC4D,GAAG,CAAEC,IAAI,iBACxBnE,OAAA;sBAEEsD,SAAS,EACPlC,cAAc,KAAK+C,IAAI,CAAC/D,EAAE,GAAGX,MAAM,CAAC2E,MAAM,GAAG,EAC9C;sBACDC,OAAO,EAAEA,CAAA,KACPhD,iBAAiB,CACfD,cAAc,KAAK+C,IAAI,CAAC/D,EAAE,GAAG,IAAI,GAAG+D,IAAI,CAAC/D,EAC3C,CACD;sBAAAqD,QAAA,EAEAU,IAAI,CAAC9D;oBAAK,GAVN8D,IAAI,CAAC/D,EAAE;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAWV,CACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhE,OAAA,CAACJ,YAAY;UACX0B,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BT,IAAI,EAAEA;QAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEFhE,OAAA;UAAKsD,SAAS,EAAE7D,MAAM,CAAC2F,IAAK;UAAA3B,QAAA,EACzBzC,OAAO,gBACNhB,OAAA,CAAAE,SAAA;YAAAuD,QAAA,EACG4B,KAAK,CAACC,IAAI,CAAC;cAAEC,MAAM,EAAE;YAAE,CAAC,CAAC,CAACrB,GAAG,CAAC,CAACsB,CAAC,EAAEC,KAAK,kBACtCzF,OAAA,CAACF,YAAY,MAAM2F,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC5B;UAAC,gBACF,CAAC,GACD5B,YAAY,CAACmD,MAAM,GAAG,CAAC,GACzBnD,YAAY,CAAC8B,GAAG,CAAE5B,GAAG,iBACnBtC,OAAA,CAACN,IAAI;YACH8E,EAAE,EAAE,UAAUlC,GAAG,CAACoD,IAAI,EAAG;YAEzBpC,SAAS,EAAE7D,MAAM,CAACkG,IAAK;YACvBtB,OAAO,EAAEhB,SAAU;YAAAI,QAAA,gBAEnBzD,OAAA;cAAKyE,GAAG,EAAEnC,GAAG,CAACsD,YAAa;cAAClB,GAAG,EAAEpC,GAAG,CAACjC;YAAM;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9ChE,OAAA;cAAKsD,SAAS,EAAE7D,MAAM,CAACoG,QAAS;cAAApC,QAAA,eAC9BzD,OAAA;gBAAAyD,QAAA,EAAKnB,GAAG,CAACjC;cAAK;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACL1B,GAAG,CAACwD,QAAQ,iBACX9F,OAAA;cAAAyD,QAAA,EACGnB,GAAG,CAACwD,QAAQ,KAAK,KAAK,gBACrB9F,OAAA;gBAAMsD,SAAS,EAAE7D,MAAM,CAACsG,MAAO;gBAAAtC,QAAA,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEhDhE,OAAA;gBAAMsD,SAAS,EAAE7D,MAAM,CAACuG,MAAO;gBAAAvC,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACnD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eACDhE,OAAA;cAAMsD,SAAS,EAAE7D,MAAM,CAACwG,KAAM;cAAAxC,QAAA,EAAEnB,GAAG,CAACW;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAjB3C1B,GAAG,CAAClC,EAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBP,CACP,CAAC,gBAEFhE,OAAA;YAAKsD,SAAS,EAAE7D,MAAM,CAACyG,SAAU;YAAAzC,QAAA,EAAElD;UAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACtD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CA5NIH,SAAS;AAAA2F,EAAA,GAAT3F,SAAS;AA8Nf,eAAeA,SAAS;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}