{"ast": null, "code": "var _jsxFileName = \"/var/www/html/gwm.tj/src/components/Form/Form.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../components/Notification/Notification';\nimport { sanitizeAndValidateForm, formRateLimiter } from '../../utils/validation';\nimport { fetchModels, submitFeedback } from '../../utils/api';\n\n/**\n * Contact form component with validation and security features\n * @component\n * @param {Object} props - Component props\n * @param {string} props.formType - Type of form ('contact', 'test-drive', 'service', etc.)\n * @param {string} props.title - Form title (optional)\n * @param {string} props.defaultModel - Default selected model (optional)\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Form = ({\n  formType = 'contact',\n  title,\n  defaultModel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false\n  });\n  const [notification, setNotification] = useState({\n    message: '',\n    type: ''\n  });\n  const [carOptions, setCarOptions] = useState([]);\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({\n      message,\n      type\n    });\n    setTimeout(() => setNotification({\n      message: '',\n      type: ''\n    }), 3000);\n  }, []);\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: {\n      _: /\\d/\n    }\n  });\n\n  /**\n   * Fetch car models on component mount and set default model\n   */\n  useEffect(() => {\n    const loadCarModels = async () => {\n      try {\n        const models = await fetchModels();\n        const formattedOptions = models.map(model => ({\n          value: model.slug || model.title,\n          label: model.title\n        }));\n        setCarOptions(formattedOptions);\n\n        // Set default model if provided\n        if (defaultModel) {\n          const defaultOption = formattedOptions.find(option => option.value === defaultModel || option.label === defaultModel);\n          if (defaultOption) {\n            setFormData(prev => ({\n              ...prev,\n              topic: defaultOption\n            }));\n          }\n        }\n      } catch (error) {\n        showNotification(error.message, 'error');\n      }\n    };\n    loadCarModels();\n  }, [showNotification, defaultModel]);\n\n  /**\n   * Handle input changes and clear field-specific errors\n   * @param {Event} e - Input change event\n   */\n  const handleChange = useCallback(e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    const newValue = type === 'checkbox' ? checked : value;\n    setFormData(prev => ({\n      ...prev,\n      [name]: newValue\n    }));\n\n    // Clear field-specific error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  }, [formErrors]);\n\n  /**\n   * Handle select dropdown changes\n   * @param {Object} selectedOption - Selected option from react-select\n   */\n  const handleSelectChange = useCallback(selectedOption => {\n    setFormData(prev => ({\n      ...prev,\n      topic: selectedOption\n    }));\n\n    // Clear topic error when user selects an option\n    if (formErrors.topic) {\n      setFormErrors(prev => ({\n        ...prev,\n        topic: ''\n      }));\n    }\n  }, [formErrors.topic]);\n\n  /**\n   * Handle form submission with validation and security checks\n   * @param {Event} e - Form submit event\n   */\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification('Слишком много попыток отправки. Подождите минуту.', 'error');\n      return;\n    }\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n    try {\n      var _sanitizedData$topic;\n      // Validate and sanitize form data\n      const {\n        data: sanitizedData,\n        errors,\n        isValid\n      } = sanitizeAndValidateForm(formData);\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: ((_sanitizedData$topic = sanitizedData.topic) === null || _sanitizedData$topic === void 0 ? void 0 : _sanitizedData$topic.value) || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: formType // Include form type for API\n      };\n\n      // Submit form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification('Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.', 'success');\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false\n      });\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Notification, {\n      message: notification.message,\n      type: notification.type\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), title && /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: styles.formTitle,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: styles.form,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"firstName\",\n            className: styles.label,\n            children: [\"\\u0418\\u043C\\u044F\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.required,\n              \"aria-label\": \"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"firstName\",\n            type: \"text\",\n            name: \"firstName\",\n            value: formData.firstName,\n            className: `${styles.input} ${formErrors.firstName ? styles.inputError : ''}`,\n            required: true,\n            onChange: handleChange,\n            minLength: \"2\",\n            maxLength: \"50\",\n            \"aria-describedby\": formErrors.firstName ? 'firstName-error' : undefined,\n            \"aria-invalid\": formErrors.firstName ? 'true' : 'false',\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), formErrors.firstName && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"firstName-error\",\n            className: styles.errorMessage,\n            role: \"alert\",\n            children: formErrors.firstName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"lastName\",\n            className: styles.label,\n            children: \"\\u0424\\u0430\\u043C\\u0438\\u043B\\u0438\\u044F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"lastName\",\n            type: \"text\",\n            name: \"lastName\",\n            value: formData.lastName,\n            className: `${styles.input} ${formErrors.lastName ? styles.inputError : ''}`,\n            onChange: handleChange,\n            minLength: \"2\",\n            maxLength: \"50\",\n            \"aria-describedby\": formErrors.lastName ? 'lastName-error' : undefined,\n            \"aria-invalid\": formErrors.lastName ? 'true' : 'false',\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), formErrors.lastName && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"lastName-error\",\n            className: styles.errorMessage,\n            role: \"alert\",\n            children: formErrors.lastName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"phone\",\n            className: styles.label,\n            children: [\"\\u041D\\u043E\\u043C\\u0435\\u0440 \\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043B\\u0435\\u0444\\u043E\\u043D\\u0430\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.required,\n              \"aria-label\": \"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"phone\",\n            type: \"tel\",\n            name: \"phone\",\n            value: formData.phone,\n            className: `${styles.input} ${formErrors.phone ? styles.inputError : ''}`,\n            required: true,\n            ref: phoneRef,\n            onChange: handleChange,\n            placeholder: \"+992 XXX-XX-XX-XX\",\n            \"aria-describedby\": formErrors.phone ? 'phone-error' : 'phone-help',\n            \"aria-invalid\": formErrors.phone ? 'true' : 'false',\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"phone-help\",\n            className: styles.helpText,\n            children: \"\\u0424\\u043E\\u0440\\u043C\\u0430\\u0442: +992 XXX-XX-XX-XX\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), formErrors.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"phone-error\",\n            className: styles.errorMessage,\n            role: \"alert\",\n            children: formErrors.phone\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"message\",\n            className: styles.label,\n            children: \"\\u0412\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"message\",\n            name: \"message\",\n            value: formData.message,\n            className: `${styles.input} ${styles.textarea} ${formErrors.message ? styles.inputError : ''}`,\n            onChange: handleChange,\n            maxLength: \"1000\",\n            rows: \"1\",\n            placeholder: \"\\u0412\\u0432\\u0435\\u0434\\u0438\\u0442\\u0435 \\u0432\\u0430\\u0448\\u0435 \\u0441\\u043E\\u043E\\u0431\\u0449\\u0435\\u043D\\u0438\\u0435 (\\u043D\\u0435\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E)\",\n            \"aria-describedby\": formErrors.message ? 'message-error' : 'message-help',\n            \"aria-invalid\": formErrors.message ? 'true' : 'false',\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"message-help\",\n            className: styles.helpText,\n            children: \"\\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0443\\u043C 1000 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), formErrors.message && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"message-error\",\n            className: styles.errorMessage,\n            role: \"alert\",\n            children: formErrors.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.row,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: styles.inputGroup,\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"topic\",\n            className: styles.label,\n            children: [\"\\u041C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.required,\n              \"aria-label\": \"\\u043E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u043E\\u043B\\u0435\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.selectWrapper,\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              inputId: \"topic\",\n              classNamePrefix: \"react-select\",\n              options: carOptions,\n              placeholder: \"\\u0412\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\",\n              onChange: handleSelectChange,\n              value: formData.topic,\n              isSearchable: false,\n              isDisabled: isSubmitting,\n              menuPortalTarget: document.body,\n              \"aria-describedby\": formErrors.topic ? 'topic-error' : undefined,\n              \"aria-invalid\": formErrors.topic ? 'true' : 'false',\n              styles: {\n                container: provided => ({\n                  ...provided,\n                  width: '100%'\n                }),\n                control: (provided, state) => ({\n                  ...provided,\n                  width: '100%',\n                  paddingLeft: '8px',\n                  height: '58px',\n                  borderRadius: '2px',\n                  border: `1px solid ${state.isFocused ? '#d7000f' : '#8e8e93'}`,\n                  fontSize: '16px',\n                  backgroundColor: '#fff',\n                  boxShadow: 'none',\n                  zIndex: 1,\n                  transition: 'all 0.2s ease',\n                  '&:hover': {\n                    borderColor: state.isFocused ? '#d7000f' : '#8e8e93'\n                  }\n                }),\n                placeholder: provided => ({\n                  ...provided,\n                  color: '#888'\n                }),\n                singleValue: provided => ({\n                  ...provided,\n                  color: '#000'\n                }),\n                indicatorsContainer: provided => ({\n                  ...provided,\n                  paddingRight: '10px'\n                }),\n                dropdownIndicator: provided => ({\n                  ...provided,\n                  color: '#8e8e93'\n                }),\n                menuPortal: base => ({\n                  ...base,\n                  zIndex: 100\n                }),\n                menu: provided => ({\n                  ...provided,\n                  border: '1px solid #8e8e93',\n                  borderRadius: '2px',\n                  boxShadow: 'none',\n                  marginTop: '4px',\n                  backgroundColor: '#fff',\n                  zIndex: 100\n                }),\n                menuList: provided => ({\n                  ...provided,\n                  padding: 0\n                }),\n                option: (provided, state) => ({\n                  ...provided,\n                  padding: '14px 16px',\n                  fontSize: '14px',\n                  backgroundColor: state.isFocused ? '#f2f2f2' : '#fff',\n                  color: '#000',\n                  cursor: 'pointer',\n                  '&:active': {\n                    backgroundColor: '#e6e6e6'\n                  }\n                })\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), formErrors.topic && /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"topic-error\",\n              className: styles.errorMessage,\n              role: \"alert\",\n              children: formErrors.topic\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.checkboxGroup,\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: styles.checkboxWrapper,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            name: \"consent\",\n            required: true,\n            checked: formData.consent,\n            onChange: handleChange,\n            className: styles.checkbox,\n            \"aria-describedby\": formErrors.consent ? 'consent-error' : 'consent-help',\n            \"aria-invalid\": formErrors.consent ? 'true' : 'false',\n            disabled: isSubmitting\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.checkboxLabel,\n            children: \"\\u042F \\u0434\\u0430\\u044E \\u0441\\u043E\\u0433\\u043B\\u0430\\u0441\\u0438\\u0435 \\u043D\\u0430 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u0443 \\u043C\\u043E\\u0438\\u0445 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"consent-help\",\n          className: styles.helpText,\n          children: \"\\u041E\\u0431\\u044F\\u0437\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E \\u0434\\u043B\\u044F \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043A\\u0438 \\u0444\\u043E\\u0440\\u043C\\u044B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), formErrors.consent && /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"consent-error\",\n          className: styles.errorMessage,\n          role: \"alert\",\n          children: formErrors.consent\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: `${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`,\n        disabled: isSubmitting,\n        \"aria-describedby\": \"submit-help\",\n        children: btnLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: styles.spinner,\n            \"aria-hidden\": \"true\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), \"\\u041E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043A\\u0430...\"]\n        }, void 0, true) : 'Отправить'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        id: \"submit-help\",\n        className: styles.helpText,\n        children: \"\\u041D\\u0430\\u0436\\u0438\\u043C\\u0430\\u044F \\u043A\\u043D\\u043E\\u043F\\u043A\\u0443, \\u0432\\u044B \\u0441\\u043E\\u0433\\u043B\\u0430\\u0448\\u0430\\u0435\\u0442\\u0435\\u0441\\u044C \\u0441 \\u043E\\u0431\\u0440\\u0430\\u0431\\u043E\\u0442\\u043A\\u043E\\u0439 \\u043F\\u0435\\u0440\\u0441\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0445 \\u0434\\u0430\\u043D\\u043D\\u044B\\u0445\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Form, \"p1tSlM0KfeReiJ9VFFv+UEWwYLg=\", false, function () {\n  return [useMask];\n});\n_c = Form;\nexport default Form;\nvar _c;\n$RefreshReg$(_c, \"Form\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Select", "styles", "useMask", "Notification", "sanitizeAndValidateForm", "formRateLimiter", "fetchModels", "submitFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Form", "formType", "title", "defaultModel", "_s", "formData", "setFormData", "firstName", "lastName", "phone", "topic", "message", "consent", "notification", "setNotification", "type", "carOptions", "setCarOptions", "btnLoading", "setBtnLoading", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "showNotification", "setTimeout", "phoneRef", "mask", "replacement", "_", "loadCarModels", "models", "formattedOptions", "map", "model", "value", "slug", "label", "defaultOption", "find", "option", "prev", "error", "handleChange", "e", "name", "checked", "target", "newValue", "handleSelectChange", "selectedOption", "handleSubmit", "preventDefault", "isAllowed", "_sanitizedData$topic", "data", "sanitizedData", "errors", "<PERSON><PERSON><PERSON><PERSON>", "payload", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "formTitle", "onSubmit", "form", "row", "inputGroup", "htmlFor", "required", "id", "input", "inputError", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "undefined", "disabled", "errorMessage", "role", "ref", "placeholder", "helpText", "textarea", "rows", "selectWrapper", "inputId", "classNamePrefix", "options", "isSearchable", "isDisabled", "menuPortalTarget", "document", "body", "container", "provided", "width", "control", "state", "paddingLeft", "height", "borderRadius", "border", "isFocused", "fontSize", "backgroundColor", "boxShadow", "zIndex", "transition", "borderColor", "color", "singleValue", "indicatorsContainer", "paddingRight", "dropdownIndicator", "menuPortal", "base", "menu", "marginTop", "menuList", "padding", "cursor", "checkboxGroup", "checkboxWrapper", "checkbox", "checkboxLabel", "button", "buttonLoading", "spinner", "_c", "$RefreshReg$"], "sources": ["/var/www/html/gwm.tj/src/components/Form/Form.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport Select from 'react-select';\nimport styles from './form.module.css';\nimport { useMask } from '@react-input/mask';\nimport Notification from '../../components/Notification/Notification';\nimport {\n  sanitizeAndValidateForm,\n  formRateLimiter,\n} from '../../utils/validation';\nimport { fetchModels, submitFeedback } from '../../utils/api';\n\n/**\n * Contact form component with validation and security features\n * @component\n * @param {Object} props - Component props\n * @param {string} props.formType - Type of form ('contact', 'test-drive', 'service', etc.)\n * @param {string} props.title - Form title (optional)\n * @param {string} props.defaultModel - Default selected model (optional)\n */\nconst Form = ({ formType = 'contact', title, defaultModel }) => {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    phone: '',\n    topic: null,\n    message: '',\n    consent: false,\n  });\n\n  const [notification, setNotification] = useState({ message: '', type: '' });\n  const [carOptions, setCarOptions] = useState([]);\n  const [btnLoading, setBtnLoading] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Shows notification message to user\n   * @param {string} message - Message to display\n   * @param {('success'|'error'|'warning')} type - Type of notification\n   */\n  const showNotification = useCallback((message, type = 'success') => {\n    setNotification({ message, type });\n    setTimeout(() => setNotification({ message: '', type: '' }), 3000);\n  }, []);\n\n  const phoneRef = useMask({\n    mask: '+992 ___-__-__-__',\n    replacement: { _: /\\d/ },\n  });\n\n  /**\n   * Fetch car models on component mount and set default model\n   */\n  useEffect(() => {\n    const loadCarModels = async () => {\n      try {\n        const models = await fetchModels();\n        const formattedOptions = models.map((model) => ({\n          value: model.slug || model.title,\n          label: model.title,\n        }));\n        setCarOptions(formattedOptions);\n\n        // Set default model if provided\n        if (defaultModel) {\n          const defaultOption = formattedOptions.find(\n            (option) =>\n              option.value === defaultModel || option.label === defaultModel\n          );\n          if (defaultOption) {\n            setFormData((prev) => ({\n              ...prev,\n              topic: defaultOption,\n            }));\n          }\n        }\n      } catch (error) {\n        showNotification(error.message, 'error');\n      }\n    };\n\n    loadCarModels();\n  }, [showNotification, defaultModel]);\n\n  /**\n   * Handle input changes and clear field-specific errors\n   * @param {Event} e - Input change event\n   */\n  const handleChange = useCallback(\n    (e) => {\n      const { name, value, type, checked } = e.target;\n      const newValue = type === 'checkbox' ? checked : value;\n\n      setFormData((prev) => ({\n        ...prev,\n        [name]: newValue,\n      }));\n\n      // Clear field-specific error when user starts typing\n      if (formErrors[name]) {\n        setFormErrors((prev) => ({\n          ...prev,\n          [name]: '',\n        }));\n      }\n    },\n    [formErrors]\n  );\n\n  /**\n   * Handle select dropdown changes\n   * @param {Object} selectedOption - Selected option from react-select\n   */\n  const handleSelectChange = useCallback(\n    (selectedOption) => {\n      setFormData((prev) => ({\n        ...prev,\n        topic: selectedOption,\n      }));\n\n      // Clear topic error when user selects an option\n      if (formErrors.topic) {\n        setFormErrors((prev) => ({\n          ...prev,\n          topic: '',\n        }));\n      }\n    },\n    [formErrors.topic]\n  );\n\n  /**\n   * Handle form submission with validation and security checks\n   * @param {Event} e - Form submit event\n   */\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Prevent double submission\n    if (isSubmitting) return;\n\n    // Check rate limiting\n    if (!formRateLimiter.isAllowed()) {\n      showNotification(\n        'Слишком много попыток отправки. Подождите минуту.',\n        'error'\n      );\n      return;\n    }\n\n    setIsSubmitting(true);\n    setBtnLoading(true);\n    setFormErrors({});\n\n    try {\n      // Validate and sanitize form data\n      const {\n        data: sanitizedData,\n        errors,\n        isValid,\n      } = sanitizeAndValidateForm(formData);\n\n      if (!isValid) {\n        setFormErrors(errors);\n        showNotification('Пожалуйста, исправьте ошибки в форме', 'error');\n        return;\n      }\n\n      // Prepare payload for API\n      const payload = {\n        firstName: sanitizedData.firstName,\n        lastName: sanitizedData.lastName,\n        phone: sanitizedData.phone,\n        topic: sanitizedData.topic?.value || '',\n        message: sanitizedData.message,\n        consent: sanitizedData.consent,\n        formType: formType, // Include form type for API\n      };\n\n      // Submit form\n      await submitFeedback(payload);\n\n      // Success\n      showNotification(\n        'Форма успешно отправлена! Мы свяжемся с вами в ближайшее время.',\n        'success'\n      );\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        topic: null,\n        message: '',\n        consent: false,\n      });\n      setFormErrors({});\n    } catch (error) {\n      showNotification(error.message, 'error');\n    } finally {\n      setBtnLoading(false);\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <>\n      <Notification message={notification.message} type={notification.type} />\n      {title && <h2 className={styles.formTitle}>{title}</h2>}\n      <form onSubmit={handleSubmit} className={styles.form}>\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"firstName\" className={styles.label}>\n              Имя{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <input\n              id=\"firstName\"\n              type=\"text\"\n              name=\"firstName\"\n              value={formData.firstName}\n              className={`${styles.input} ${formErrors.firstName ? styles.inputError : ''}`}\n              required\n              onChange={handleChange}\n              minLength=\"2\"\n              maxLength=\"50\"\n              aria-describedby={\n                formErrors.firstName ? 'firstName-error' : undefined\n              }\n              aria-invalid={formErrors.firstName ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            {formErrors.firstName && (\n              <div\n                id=\"firstName-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.firstName}\n              </div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"lastName\" className={styles.label}>\n              Фамилия\n            </label>\n            <input\n              id=\"lastName\"\n              type=\"text\"\n              name=\"lastName\"\n              value={formData.lastName}\n              className={`${styles.input} ${formErrors.lastName ? styles.inputError : ''}`}\n              onChange={handleChange}\n              minLength=\"2\"\n              maxLength=\"50\"\n              aria-describedby={\n                formErrors.lastName ? 'lastName-error' : undefined\n              }\n              aria-invalid={formErrors.lastName ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            {formErrors.lastName && (\n              <div\n                id=\"lastName-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.lastName}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"phone\" className={styles.label}>\n              Номер мобильного телефона{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <input\n              id=\"phone\"\n              type=\"tel\"\n              name=\"phone\"\n              value={formData.phone}\n              className={`${styles.input} ${formErrors.phone ? styles.inputError : ''}`}\n              required\n              ref={phoneRef}\n              onChange={handleChange}\n              placeholder=\"+992 XXX-XX-XX-XX\"\n              aria-describedby={formErrors.phone ? 'phone-error' : 'phone-help'}\n              aria-invalid={formErrors.phone ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <div id=\"phone-help\" className={styles.helpText}>\n              Формат: +992 XXX-XX-XX-XX\n            </div>\n            {formErrors.phone && (\n              <div\n                id=\"phone-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.phone}\n              </div>\n            )}\n          </div>\n\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"message\" className={styles.label}>\n              Ваше сообщение\n            </label>\n            <textarea\n              id=\"message\"\n              name=\"message\"\n              value={formData.message}\n              className={`${styles.input} ${styles.textarea} ${formErrors.message ? styles.inputError : ''}`}\n              onChange={handleChange}\n              maxLength=\"1000\"\n              rows=\"1\"\n              placeholder=\"Введите ваше сообщение (необязательно)\"\n              aria-describedby={\n                formErrors.message ? 'message-error' : 'message-help'\n              }\n              aria-invalid={formErrors.message ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <div id=\"message-help\" className={styles.helpText}>\n              Максимум 1000 символов\n            </div>\n            {formErrors.message && (\n              <div\n                id=\"message-error\"\n                className={styles.errorMessage}\n                role=\"alert\"\n              >\n                {formErrors.message}\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className={styles.row}>\n          <div className={styles.inputGroup}>\n            <label htmlFor=\"topic\" className={styles.label}>\n              Модель автомобиля{' '}\n              <span className={styles.required} aria-label=\"обязательное поле\">\n                *\n              </span>\n            </label>\n            <div className={styles.selectWrapper}>\n              <Select\n                inputId=\"topic\"\n                classNamePrefix=\"react-select\"\n                options={carOptions}\n                placeholder=\"Выберите модель автомобиля\"\n                onChange={handleSelectChange}\n                value={formData.topic}\n                isSearchable={false}\n                isDisabled={isSubmitting}\n                menuPortalTarget={document.body}\n                aria-describedby={formErrors.topic ? 'topic-error' : undefined}\n                aria-invalid={formErrors.topic ? 'true' : 'false'}\n                styles={{\n                  container: (provided) => ({\n                    ...provided,\n                    width: '100%',\n                  }),\n\n                  control: (provided, state) => ({\n                    ...provided,\n                    width: '100%',\n                    paddingLeft: '8px',\n                    height: '58px',\n                    borderRadius: '2px',\n                    border: `1px solid ${\n                      state.isFocused ? '#d7000f' : '#8e8e93'\n                    }`,\n                    fontSize: '16px',\n                    backgroundColor: '#fff',\n                    boxShadow: 'none',\n                    zIndex: 1,\n                    transition: 'all 0.2s ease',\n                    '&:hover': {\n                      borderColor: state.isFocused ? '#d7000f' : '#8e8e93',\n                    },\n                  }),\n\n                  placeholder: (provided) => ({\n                    ...provided,\n                    color: '#888',\n                  }),\n\n                  singleValue: (provided) => ({\n                    ...provided,\n                    color: '#000',\n                  }),\n\n                  indicatorsContainer: (provided) => ({\n                    ...provided,\n                    paddingRight: '10px',\n                  }),\n\n                  dropdownIndicator: (provided) => ({\n                    ...provided,\n                    color: '#8e8e93',\n                  }),\n\n                  menuPortal: (base) => ({\n                    ...base,\n                    zIndex: 100,\n                  }),\n\n                  menu: (provided) => ({\n                    ...provided,\n                    border: '1px solid #8e8e93',\n                    borderRadius: '2px',\n                    boxShadow: 'none',\n                    marginTop: '4px',\n                    backgroundColor: '#fff',\n                    zIndex: 100,\n                  }),\n\n                  menuList: (provided) => ({\n                    ...provided,\n                    padding: 0,\n                  }),\n\n                  option: (provided, state) => ({\n                    ...provided,\n                    padding: '14px 16px',\n                    fontSize: '14px',\n                    backgroundColor: state.isFocused ? '#f2f2f2' : '#fff',\n                    color: '#000',\n                    cursor: 'pointer',\n                    '&:active': {\n                      backgroundColor: '#e6e6e6',\n                    },\n                  }),\n                }}\n              />\n              {formErrors.topic && (\n                <div\n                  id=\"topic-error\"\n                  className={styles.errorMessage}\n                  role=\"alert\"\n                >\n                  {formErrors.topic}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className={styles.checkboxGroup}>\n          <label className={styles.checkboxWrapper}>\n            <input\n              type=\"checkbox\"\n              name=\"consent\"\n              required\n              checked={formData.consent}\n              onChange={handleChange}\n              className={styles.checkbox}\n              aria-describedby={\n                formErrors.consent ? 'consent-error' : 'consent-help'\n              }\n              aria-invalid={formErrors.consent ? 'true' : 'false'}\n              disabled={isSubmitting}\n            />\n            <span className={styles.checkboxLabel}>\n              Я даю согласие на обработку моих персональных данных\n            </span>\n          </label>\n          <div id=\"consent-help\" className={styles.helpText}>\n            Обязательно для отправки формы\n          </div>\n          {formErrors.consent && (\n            <div\n              id=\"consent-error\"\n              className={styles.errorMessage}\n              role=\"alert\"\n            >\n              {formErrors.consent}\n            </div>\n          )}\n        </div>\n\n        <button\n          type=\"submit\"\n          className={`${styles.button} ${isSubmitting ? styles.buttonLoading : ''}`}\n          disabled={isSubmitting}\n          aria-describedby=\"submit-help\"\n        >\n          {btnLoading ? (\n            <>\n              <span className={styles.spinner} aria-hidden=\"true\"></span>\n              Отправка...\n            </>\n          ) : (\n            'Отправить'\n          )}\n        </button>\n        <div id=\"submit-help\" className={styles.helpText}>\n          Нажимая кнопку, вы соглашаетесь с обработкой персональных данных\n        </div>\n      </form>\n    </>\n  );\n};\n\nexport default Form;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,4CAA4C;AACrE,SACEC,uBAAuB,EACvBC,eAAe,QACV,wBAAwB;AAC/B,SAASC,WAAW,EAAEC,cAAc,QAAQ,iBAAiB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,IAAI,GAAGA,CAAC;EAAEC,QAAQ,GAAG,SAAS;EAAEC,KAAK;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC;IAAE0B,OAAO,EAAE,EAAE;IAAEI,IAAI,EAAE;EAAG,CAAC,CAAC;EAC3E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;AACF;AACA;AACA;AACA;EACE,MAAMuC,gBAAgB,GAAGrC,WAAW,CAAC,CAACwB,OAAO,EAAEI,IAAI,GAAG,SAAS,KAAK;IAClED,eAAe,CAAC;MAAEH,OAAO;MAAEI;IAAK,CAAC,CAAC;IAClCU,UAAU,CAAC,MAAMX,eAAe,CAAC;MAAEH,OAAO,EAAE,EAAE;MAAEI,IAAI,EAAE;IAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,QAAQ,GAAGpC,OAAO,CAAC;IACvBqC,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;MAAEC,CAAC,EAAE;IAAK;EACzB,CAAC,CAAC;;EAEF;AACF;AACA;EACE3C,SAAS,CAAC,MAAM;IACd,MAAM4C,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMrC,WAAW,CAAC,CAAC;QAClC,MAAMsC,gBAAgB,GAAGD,MAAM,CAACE,GAAG,CAAEC,KAAK,KAAM;UAC9CC,KAAK,EAAED,KAAK,CAACE,IAAI,IAAIF,KAAK,CAAChC,KAAK;UAChCmC,KAAK,EAAEH,KAAK,CAAChC;QACf,CAAC,CAAC,CAAC;QACHe,aAAa,CAACe,gBAAgB,CAAC;;QAE/B;QACA,IAAI7B,YAAY,EAAE;UAChB,MAAMmC,aAAa,GAAGN,gBAAgB,CAACO,IAAI,CACxCC,MAAM,IACLA,MAAM,CAACL,KAAK,KAAKhC,YAAY,IAAIqC,MAAM,CAACH,KAAK,KAAKlC,YACtD,CAAC;UACD,IAAImC,aAAa,EAAE;YACjBhC,WAAW,CAAEmC,IAAI,KAAM;cACrB,GAAGA,IAAI;cACP/B,KAAK,EAAE4B;YACT,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdlB,gBAAgB,CAACkB,KAAK,CAAC/B,OAAO,EAAE,OAAO,CAAC;MAC1C;IACF,CAAC;IAEDmB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACN,gBAAgB,EAAErB,YAAY,CAAC,CAAC;;EAEpC;AACF;AACA;AACA;EACE,MAAMwC,YAAY,GAAGxD,WAAW,CAC7ByD,CAAC,IAAK;IACL,MAAM;MAAEC,IAAI;MAAEV,KAAK;MAAEpB,IAAI;MAAE+B;IAAQ,CAAC,GAAGF,CAAC,CAACG,MAAM;IAC/C,MAAMC,QAAQ,GAAGjC,IAAI,KAAK,UAAU,GAAG+B,OAAO,GAAGX,KAAK;IAEtD7B,WAAW,CAAEmC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAACI,IAAI,GAAGG;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI5B,UAAU,CAACyB,IAAI,CAAC,EAAE;MACpBxB,aAAa,CAAEoB,IAAI,KAAM;QACvB,GAAGA,IAAI;QACP,CAACI,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EACD,CAACzB,UAAU,CACb,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAM6B,kBAAkB,GAAG9D,WAAW,CACnC+D,cAAc,IAAK;IAClB5C,WAAW,CAAEmC,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP/B,KAAK,EAAEwC;IACT,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI9B,UAAU,CAACV,KAAK,EAAE;MACpBW,aAAa,CAAEoB,IAAI,KAAM;QACvB,GAAGA,IAAI;QACP/B,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EACD,CAACU,UAAU,CAACV,KAAK,CACnB,CAAC;;EAED;AACF;AACA;AACA;EACE,MAAMyC,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI9B,YAAY,EAAE;;IAElB;IACA,IAAI,CAAC7B,eAAe,CAAC4D,SAAS,CAAC,CAAC,EAAE;MAChC7B,gBAAgB,CACd,mDAAmD,EACnD,OACF,CAAC;MACD;IACF;IAEAD,eAAe,CAAC,IAAI,CAAC;IACrBJ,aAAa,CAAC,IAAI,CAAC;IACnBE,aAAa,CAAC,CAAC,CAAC,CAAC;IAEjB,IAAI;MAAA,IAAAiC,oBAAA;MACF;MACA,MAAM;QACJC,IAAI,EAAEC,aAAa;QACnBC,MAAM;QACNC;MACF,CAAC,GAAGlE,uBAAuB,CAACa,QAAQ,CAAC;MAErC,IAAI,CAACqD,OAAO,EAAE;QACZrC,aAAa,CAACoC,MAAM,CAAC;QACrBjC,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,CAAC;QACjE;MACF;;MAEA;MACA,MAAMmC,OAAO,GAAG;QACdpD,SAAS,EAAEiD,aAAa,CAACjD,SAAS;QAClCC,QAAQ,EAAEgD,aAAa,CAAChD,QAAQ;QAChCC,KAAK,EAAE+C,aAAa,CAAC/C,KAAK;QAC1BC,KAAK,EAAE,EAAA4C,oBAAA,GAAAE,aAAa,CAAC9C,KAAK,cAAA4C,oBAAA,uBAAnBA,oBAAA,CAAqBnB,KAAK,KAAI,EAAE;QACvCxB,OAAO,EAAE6C,aAAa,CAAC7C,OAAO;QAC9BC,OAAO,EAAE4C,aAAa,CAAC5C,OAAO;QAC9BX,QAAQ,EAAEA,QAAQ,CAAE;MACtB,CAAC;;MAED;MACA,MAAMN,cAAc,CAACgE,OAAO,CAAC;;MAE7B;MACAnC,gBAAgB,CACd,iEAAiE,EACjE,SACF,CAAC;;MAED;MACAlB,WAAW,CAAC;QACVC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE;MACX,CAAC,CAAC;MACFS,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdlB,gBAAgB,CAACkB,KAAK,CAAC/B,OAAO,EAAE,OAAO,CAAC;IAC1C,CAAC,SAAS;MACRQ,aAAa,CAAC,KAAK,CAAC;MACpBI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE1B,OAAA,CAAAE,SAAA;IAAA6D,QAAA,gBACE/D,OAAA,CAACN,YAAY;MAACoB,OAAO,EAAEE,YAAY,CAACF,OAAQ;MAACI,IAAI,EAAEF,YAAY,CAACE;IAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvE9D,KAAK,iBAAIL,OAAA;MAAIoE,SAAS,EAAE5E,MAAM,CAAC6E,SAAU;MAAAN,QAAA,EAAE1D;IAAK;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvDnE,OAAA;MAAMsE,QAAQ,EAAEhB,YAAa;MAACc,SAAS,EAAE5E,MAAM,CAAC+E,IAAK;MAAAR,QAAA,gBACnD/D,OAAA;QAAKoE,SAAS,EAAE5E,MAAM,CAACgF,GAAI;QAAAT,QAAA,gBACzB/D,OAAA;UAAKoE,SAAS,EAAE5E,MAAM,CAACiF,UAAW;UAAAV,QAAA,gBAChC/D,OAAA;YAAO0E,OAAO,EAAC,WAAW;YAACN,SAAS,EAAE5E,MAAM,CAACgD,KAAM;YAAAuB,QAAA,GAAC,oBAC/C,EAAC,GAAG,eACP/D,OAAA;cAAMoE,SAAS,EAAE5E,MAAM,CAACmF,QAAS;cAAC,cAAW,mGAAmB;cAAAZ,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRnE,OAAA;YACE4E,EAAE,EAAC,WAAW;YACd1D,IAAI,EAAC,MAAM;YACX8B,IAAI,EAAC,WAAW;YAChBV,KAAK,EAAE9B,QAAQ,CAACE,SAAU;YAC1B0D,SAAS,EAAE,GAAG5E,MAAM,CAACqF,KAAK,IAAItD,UAAU,CAACb,SAAS,GAAGlB,MAAM,CAACsF,UAAU,GAAG,EAAE,EAAG;YAC9EH,QAAQ;YACRI,QAAQ,EAAEjC,YAAa;YACvBkC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC,IAAI;YACd,oBACE1D,UAAU,CAACb,SAAS,GAAG,iBAAiB,GAAGwE,SAC5C;YACD,gBAAc3D,UAAU,CAACb,SAAS,GAAG,MAAM,GAAG,OAAQ;YACtDyE,QAAQ,EAAE1D;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACD5C,UAAU,CAACb,SAAS,iBACnBV,OAAA;YACE4E,EAAE,EAAC,iBAAiB;YACpBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;YAC/BC,IAAI,EAAC,OAAO;YAAAtB,QAAA,EAEXxC,UAAU,CAACb;UAAS;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnE,OAAA;UAAKoE,SAAS,EAAE5E,MAAM,CAACiF,UAAW;UAAAV,QAAA,gBAChC/D,OAAA;YAAO0E,OAAO,EAAC,UAAU;YAACN,SAAS,EAAE5E,MAAM,CAACgD,KAAM;YAAAuB,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnE,OAAA;YACE4E,EAAE,EAAC,UAAU;YACb1D,IAAI,EAAC,MAAM;YACX8B,IAAI,EAAC,UAAU;YACfV,KAAK,EAAE9B,QAAQ,CAACG,QAAS;YACzByD,SAAS,EAAE,GAAG5E,MAAM,CAACqF,KAAK,IAAItD,UAAU,CAACZ,QAAQ,GAAGnB,MAAM,CAACsF,UAAU,GAAG,EAAE,EAAG;YAC7EC,QAAQ,EAAEjC,YAAa;YACvBkC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC,IAAI;YACd,oBACE1D,UAAU,CAACZ,QAAQ,GAAG,gBAAgB,GAAGuE,SAC1C;YACD,gBAAc3D,UAAU,CAACZ,QAAQ,GAAG,MAAM,GAAG,OAAQ;YACrDwE,QAAQ,EAAE1D;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EACD5C,UAAU,CAACZ,QAAQ,iBAClBX,OAAA;YACE4E,EAAE,EAAC,gBAAgB;YACnBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;YAC/BC,IAAI,EAAC,OAAO;YAAAtB,QAAA,EAEXxC,UAAU,CAACZ;UAAQ;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAKoE,SAAS,EAAE5E,MAAM,CAACgF,GAAI;QAAAT,QAAA,gBACzB/D,OAAA;UAAKoE,SAAS,EAAE5E,MAAM,CAACiF,UAAW;UAAAV,QAAA,gBAChC/D,OAAA;YAAO0E,OAAO,EAAC,OAAO;YAACN,SAAS,EAAE5E,MAAM,CAACgD,KAAM;YAAAuB,QAAA,GAAC,8IACrB,EAAC,GAAG,eAC7B/D,OAAA;cAAMoE,SAAS,EAAE5E,MAAM,CAACmF,QAAS;cAAC,cAAW,mGAAmB;cAAAZ,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRnE,OAAA;YACE4E,EAAE,EAAC,OAAO;YACV1D,IAAI,EAAC,KAAK;YACV8B,IAAI,EAAC,OAAO;YACZV,KAAK,EAAE9B,QAAQ,CAACI,KAAM;YACtBwD,SAAS,EAAE,GAAG5E,MAAM,CAACqF,KAAK,IAAItD,UAAU,CAACX,KAAK,GAAGpB,MAAM,CAACsF,UAAU,GAAG,EAAE,EAAG;YAC1EH,QAAQ;YACRW,GAAG,EAAEzD,QAAS;YACdkD,QAAQ,EAAEjC,YAAa;YACvByC,WAAW,EAAC,mBAAmB;YAC/B,oBAAkBhE,UAAU,CAACX,KAAK,GAAG,aAAa,GAAG,YAAa;YAClE,gBAAcW,UAAU,CAACX,KAAK,GAAG,MAAM,GAAG,OAAQ;YAClDuE,QAAQ,EAAE1D;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFnE,OAAA;YAAK4E,EAAE,EAAC,YAAY;YAACR,SAAS,EAAE5E,MAAM,CAACgG,QAAS;YAAAzB,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL5C,UAAU,CAACX,KAAK,iBACfZ,OAAA;YACE4E,EAAE,EAAC,aAAa;YAChBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;YAC/BC,IAAI,EAAC,OAAO;YAAAtB,QAAA,EAEXxC,UAAU,CAACX;UAAK;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnE,OAAA;UAAKoE,SAAS,EAAE5E,MAAM,CAACiF,UAAW;UAAAV,QAAA,gBAChC/D,OAAA;YAAO0E,OAAO,EAAC,SAAS;YAACN,SAAS,EAAE5E,MAAM,CAACgD,KAAM;YAAAuB,QAAA,EAAC;UAElD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnE,OAAA;YACE4E,EAAE,EAAC,SAAS;YACZ5B,IAAI,EAAC,SAAS;YACdV,KAAK,EAAE9B,QAAQ,CAACM,OAAQ;YACxBsD,SAAS,EAAE,GAAG5E,MAAM,CAACqF,KAAK,IAAIrF,MAAM,CAACiG,QAAQ,IAAIlE,UAAU,CAACT,OAAO,GAAGtB,MAAM,CAACsF,UAAU,GAAG,EAAE,EAAG;YAC/FC,QAAQ,EAAEjC,YAAa;YACvBmC,SAAS,EAAC,MAAM;YAChBS,IAAI,EAAC,GAAG;YACRH,WAAW,EAAC,6MAAwC;YACpD,oBACEhE,UAAU,CAACT,OAAO,GAAG,eAAe,GAAG,cACxC;YACD,gBAAcS,UAAU,CAACT,OAAO,GAAG,MAAM,GAAG,OAAQ;YACpDqE,QAAQ,EAAE1D;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFnE,OAAA;YAAK4E,EAAE,EAAC,cAAc;YAACR,SAAS,EAAE5E,MAAM,CAACgG,QAAS;YAAAzB,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL5C,UAAU,CAACT,OAAO,iBACjBd,OAAA;YACE4E,EAAE,EAAC,eAAe;YAClBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;YAC/BC,IAAI,EAAC,OAAO;YAAAtB,QAAA,EAEXxC,UAAU,CAACT;UAAO;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAKoE,SAAS,EAAE5E,MAAM,CAACgF,GAAI;QAAAT,QAAA,eACzB/D,OAAA;UAAKoE,SAAS,EAAE5E,MAAM,CAACiF,UAAW;UAAAV,QAAA,gBAChC/D,OAAA;YAAO0E,OAAO,EAAC,OAAO;YAACN,SAAS,EAAE5E,MAAM,CAACgD,KAAM;YAAAuB,QAAA,GAAC,mGAC7B,EAAC,GAAG,eACrB/D,OAAA;cAAMoE,SAAS,EAAE5E,MAAM,CAACmF,QAAS;cAAC,cAAW,mGAAmB;cAAAZ,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRnE,OAAA;YAAKoE,SAAS,EAAE5E,MAAM,CAACmG,aAAc;YAAA5B,QAAA,gBACnC/D,OAAA,CAACT,MAAM;cACLqG,OAAO,EAAC,OAAO;cACfC,eAAe,EAAC,cAAc;cAC9BC,OAAO,EAAE3E,UAAW;cACpBoE,WAAW,EAAC,oJAA4B;cACxCR,QAAQ,EAAE3B,kBAAmB;cAC7Bd,KAAK,EAAE9B,QAAQ,CAACK,KAAM;cACtBkF,YAAY,EAAE,KAAM;cACpBC,UAAU,EAAEvE,YAAa;cACzBwE,gBAAgB,EAAEC,QAAQ,CAACC,IAAK;cAChC,oBAAkB5E,UAAU,CAACV,KAAK,GAAG,aAAa,GAAGqE,SAAU;cAC/D,gBAAc3D,UAAU,CAACV,KAAK,GAAG,MAAM,GAAG,OAAQ;cAClDrB,MAAM,EAAE;gBACN4G,SAAS,EAAGC,QAAQ,KAAM;kBACxB,GAAGA,QAAQ;kBACXC,KAAK,EAAE;gBACT,CAAC,CAAC;gBAEFC,OAAO,EAAEA,CAACF,QAAQ,EAAEG,KAAK,MAAM;kBAC7B,GAAGH,QAAQ;kBACXC,KAAK,EAAE,MAAM;kBACbG,WAAW,EAAE,KAAK;kBAClBC,MAAM,EAAE,MAAM;kBACdC,YAAY,EAAE,KAAK;kBACnBC,MAAM,EAAE,aACNJ,KAAK,CAACK,SAAS,GAAG,SAAS,GAAG,SAAS,EACvC;kBACFC,QAAQ,EAAE,MAAM;kBAChBC,eAAe,EAAE,MAAM;kBACvBC,SAAS,EAAE,MAAM;kBACjBC,MAAM,EAAE,CAAC;kBACTC,UAAU,EAAE,eAAe;kBAC3B,SAAS,EAAE;oBACTC,WAAW,EAAEX,KAAK,CAACK,SAAS,GAAG,SAAS,GAAG;kBAC7C;gBACF,CAAC,CAAC;gBAEFtB,WAAW,EAAGc,QAAQ,KAAM;kBAC1B,GAAGA,QAAQ;kBACXe,KAAK,EAAE;gBACT,CAAC,CAAC;gBAEFC,WAAW,EAAGhB,QAAQ,KAAM;kBAC1B,GAAGA,QAAQ;kBACXe,KAAK,EAAE;gBACT,CAAC,CAAC;gBAEFE,mBAAmB,EAAGjB,QAAQ,KAAM;kBAClC,GAAGA,QAAQ;kBACXkB,YAAY,EAAE;gBAChB,CAAC,CAAC;gBAEFC,iBAAiB,EAAGnB,QAAQ,KAAM;kBAChC,GAAGA,QAAQ;kBACXe,KAAK,EAAE;gBACT,CAAC,CAAC;gBAEFK,UAAU,EAAGC,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACPT,MAAM,EAAE;gBACV,CAAC,CAAC;gBAEFU,IAAI,EAAGtB,QAAQ,KAAM;kBACnB,GAAGA,QAAQ;kBACXO,MAAM,EAAE,mBAAmB;kBAC3BD,YAAY,EAAE,KAAK;kBACnBK,SAAS,EAAE,MAAM;kBACjBY,SAAS,EAAE,KAAK;kBAChBb,eAAe,EAAE,MAAM;kBACvBE,MAAM,EAAE;gBACV,CAAC,CAAC;gBAEFY,QAAQ,EAAGxB,QAAQ,KAAM;kBACvB,GAAGA,QAAQ;kBACXyB,OAAO,EAAE;gBACX,CAAC,CAAC;gBAEFnF,MAAM,EAAEA,CAAC0D,QAAQ,EAAEG,KAAK,MAAM;kBAC5B,GAAGH,QAAQ;kBACXyB,OAAO,EAAE,WAAW;kBACpBhB,QAAQ,EAAE,MAAM;kBAChBC,eAAe,EAAEP,KAAK,CAACK,SAAS,GAAG,SAAS,GAAG,MAAM;kBACrDO,KAAK,EAAE,MAAM;kBACbW,MAAM,EAAE,SAAS;kBACjB,UAAU,EAAE;oBACVhB,eAAe,EAAE;kBACnB;gBACF,CAAC;cACH;YAAE;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD5C,UAAU,CAACV,KAAK,iBACfb,OAAA;cACE4E,EAAE,EAAC,aAAa;cAChBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;cAC/BC,IAAI,EAAC,OAAO;cAAAtB,QAAA,EAEXxC,UAAU,CAACV;YAAK;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAKoE,SAAS,EAAE5E,MAAM,CAACwI,aAAc;QAAAjE,QAAA,gBACnC/D,OAAA;UAAOoE,SAAS,EAAE5E,MAAM,CAACyI,eAAgB;UAAAlE,QAAA,gBACvC/D,OAAA;YACEkB,IAAI,EAAC,UAAU;YACf8B,IAAI,EAAC,SAAS;YACd2B,QAAQ;YACR1B,OAAO,EAAEzC,QAAQ,CAACO,OAAQ;YAC1BgE,QAAQ,EAAEjC,YAAa;YACvBsB,SAAS,EAAE5E,MAAM,CAAC0I,QAAS;YAC3B,oBACE3G,UAAU,CAACR,OAAO,GAAG,eAAe,GAAG,cACxC;YACD,gBAAcQ,UAAU,CAACR,OAAO,GAAG,MAAM,GAAG,OAAQ;YACpDoE,QAAQ,EAAE1D;UAAa;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACFnE,OAAA;YAAMoE,SAAS,EAAE5E,MAAM,CAAC2I,aAAc;YAAApE,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACRnE,OAAA;UAAK4E,EAAE,EAAC,cAAc;UAACR,SAAS,EAAE5E,MAAM,CAACgG,QAAS;UAAAzB,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACL5C,UAAU,CAACR,OAAO,iBACjBf,OAAA;UACE4E,EAAE,EAAC,eAAe;UAClBR,SAAS,EAAE5E,MAAM,CAAC4F,YAAa;UAC/BC,IAAI,EAAC,OAAO;UAAAtB,QAAA,EAEXxC,UAAU,CAACR;QAAO;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA;QACEkB,IAAI,EAAC,QAAQ;QACbkD,SAAS,EAAE,GAAG5E,MAAM,CAAC4I,MAAM,IAAI3G,YAAY,GAAGjC,MAAM,CAAC6I,aAAa,GAAG,EAAE,EAAG;QAC1ElD,QAAQ,EAAE1D,YAAa;QACvB,oBAAiB,aAAa;QAAAsC,QAAA,EAE7B1C,UAAU,gBACTrB,OAAA,CAAAE,SAAA;UAAA6D,QAAA,gBACE/D,OAAA;YAAMoE,SAAS,EAAE5E,MAAM,CAAC8I,OAAQ;YAAC,eAAY;UAAM;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,uDAE7D;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACTnE,OAAA;QAAK4E,EAAE,EAAC,aAAa;QAACR,SAAS,EAAE5E,MAAM,CAACgG,QAAS;QAAAzB,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAAC5D,EAAA,CA9eIJ,IAAI;EAAA,QA0BSV,OAAO;AAAA;AAAA8I,EAAA,GA1BpBpI,IAAI;AAgfV,eAAeA,IAAI;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}