{"ast": null, "code": "/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n/* globals __webpack_hash__ */\nif (module.hot) {\n  /** @type {undefined|string} */\n  var lastHash;\n  var upToDate = function upToDate() {\n    return /** @type {string} */lastHash.indexOf(__webpack_hash__) >= 0;\n  };\n  var log = require(\"./log\");\n  var check = function check() {\n    module.hot.check(true).then(function (updatedModules) {\n      if (!updatedModules) {\n        log(\"warning\", \"[HMR] Cannot find update. \" + (typeof window !== \"undefined\" ? \"Need to do a full reload!\" : \"Please reload manually!\"));\n        log(\"warning\", \"[HMR] (Probably because of restarting the webpack-dev-server)\");\n        if (typeof window !== \"undefined\") {\n          window.location.reload();\n        }\n        return;\n      }\n      if (!upToDate()) {\n        check();\n      }\n      require(\"./log-apply-result\")(updatedModules, updatedModules);\n      if (upToDate()) {\n        log(\"info\", \"[HMR] App is up to date.\");\n      }\n    }).catch(function (err) {\n      var status = module.hot.status();\n      if ([\"abort\", \"fail\"].indexOf(status) >= 0) {\n        log(\"warning\", \"[HMR] Cannot apply update. \" + (typeof window !== \"undefined\" ? \"Need to do a full reload!\" : \"Please reload manually!\"));\n        log(\"warning\", \"[HMR] \" + log.formatError(err));\n        if (typeof window !== \"undefined\") {\n          window.location.reload();\n        }\n      } else {\n        log(\"warning\", \"[HMR] Update failed: \" + log.formatError(err));\n      }\n    });\n  };\n  var hotEmitter = require(\"./emitter\");\n  hotEmitter.on(\"webpackHotUpdate\", function (currentHash) {\n    lastHash = currentHash;\n    if (!upToDate() && module.hot.status() === \"idle\") {\n      log(\"info\", \"[HMR] Checking for updates on the server...\");\n      check();\n    }\n  });\n  log(\"info\", \"[HMR] Waiting for update signal from WDS...\");\n} else {\n  throw new Error(\"[HMR] Hot Module Replacement is disabled.\");\n}", "map": {"version": 3, "names": ["module", "hot", "lastHash", "upToDate", "indexOf", "__webpack_hash__", "log", "require", "check", "then", "updatedModules", "window", "location", "reload", "catch", "err", "status", "formatError", "hotEmitter", "on", "currentHash", "Error"], "sources": ["/var/www/html/gwm.tj/node_modules/webpack/hot/dev-server.js"], "sourcesContent": ["/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n*/\n/* globals __webpack_hash__ */\nif (module.hot) {\n\t/** @type {undefined|string} */\n\tvar lastHash;\n\tvar upToDate = function upToDate() {\n\t\treturn /** @type {string} */ (lastHash).indexOf(__webpack_hash__) >= 0;\n\t};\n\tvar log = require(\"./log\");\n\tvar check = function check() {\n\t\tmodule.hot\n\t\t\t.check(true)\n\t\t\t.then(function (updatedModules) {\n\t\t\t\tif (!updatedModules) {\n\t\t\t\t\tlog(\n\t\t\t\t\t\t\"warning\",\n\t\t\t\t\t\t\"[HMR] Cannot find update. \" +\n\t\t\t\t\t\t\t(typeof window !== \"undefined\"\n\t\t\t\t\t\t\t\t? \"Need to do a full reload!\"\n\t\t\t\t\t\t\t\t: \"Please reload manually!\")\n\t\t\t\t\t);\n\t\t\t\t\tlog(\n\t\t\t\t\t\t\"warning\",\n\t\t\t\t\t\t\"[HMR] (Probably because of restarting the webpack-dev-server)\"\n\t\t\t\t\t);\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\twindow.location.reload();\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!upToDate()) {\n\t\t\t\t\tcheck();\n\t\t\t\t}\n\n\t\t\t\trequire(\"./log-apply-result\")(updatedModules, updatedModules);\n\n\t\t\t\tif (upToDate()) {\n\t\t\t\t\tlog(\"info\", \"[HMR] App is up to date.\");\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(function (err) {\n\t\t\t\tvar status = module.hot.status();\n\t\t\t\tif ([\"abort\", \"fail\"].indexOf(status) >= 0) {\n\t\t\t\t\tlog(\n\t\t\t\t\t\t\"warning\",\n\t\t\t\t\t\t\"[HMR] Cannot apply update. \" +\n\t\t\t\t\t\t\t(typeof window !== \"undefined\"\n\t\t\t\t\t\t\t\t? \"Need to do a full reload!\"\n\t\t\t\t\t\t\t\t: \"Please reload manually!\")\n\t\t\t\t\t);\n\t\t\t\t\tlog(\"warning\", \"[HMR] \" + log.formatError(err));\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\twindow.location.reload();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlog(\"warning\", \"[HMR] Update failed: \" + log.formatError(err));\n\t\t\t\t}\n\t\t\t});\n\t};\n\tvar hotEmitter = require(\"./emitter\");\n\thotEmitter.on(\"webpackHotUpdate\", function (currentHash) {\n\t\tlastHash = currentHash;\n\t\tif (!upToDate() && module.hot.status() === \"idle\") {\n\t\t\tlog(\"info\", \"[HMR] Checking for updates on the server...\");\n\t\t\tcheck();\n\t\t}\n\t});\n\tlog(\"info\", \"[HMR] Waiting for update signal from WDS...\");\n} else {\n\tthrow new Error(\"[HMR] Hot Module Replacement is disabled.\");\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,MAAM,CAACC,GAAG,EAAE;EACf;EACA,IAAIC,QAAQ;EACZ,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IAClC,OAAO,qBAAuBD,QAAQ,CAAEE,OAAO,CAACC,gBAAgB,CAAC,IAAI,CAAC;EACvE,CAAC;EACD,IAAIC,GAAG,GAAGC,OAAO,CAAC,OAAO,CAAC;EAC1B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC5BR,MAAM,CAACC,GAAG,CACRO,KAAK,CAAC,IAAI,CAAC,CACXC,IAAI,CAAC,UAAUC,cAAc,EAAE;MAC/B,IAAI,CAACA,cAAc,EAAE;QACpBJ,GAAG,CACF,SAAS,EACT,4BAA4B,IAC1B,OAAOK,MAAM,KAAK,WAAW,GAC3B,2BAA2B,GAC3B,yBAAyB,CAC9B,CAAC;QACDL,GAAG,CACF,SAAS,EACT,+DACD,CAAC;QACD,IAAI,OAAOK,MAAM,KAAK,WAAW,EAAE;UAClCA,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QACzB;QACA;MACD;MAEA,IAAI,CAACV,QAAQ,CAAC,CAAC,EAAE;QAChBK,KAAK,CAAC,CAAC;MACR;MAEAD,OAAO,CAAC,oBAAoB,CAAC,CAACG,cAAc,EAAEA,cAAc,CAAC;MAE7D,IAAIP,QAAQ,CAAC,CAAC,EAAE;QACfG,GAAG,CAAC,MAAM,EAAE,0BAA0B,CAAC;MACxC;IACD,CAAC,CAAC,CACDQ,KAAK,CAAC,UAAUC,GAAG,EAAE;MACrB,IAAIC,MAAM,GAAGhB,MAAM,CAACC,GAAG,CAACe,MAAM,CAAC,CAAC;MAChC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAACZ,OAAO,CAACY,MAAM,CAAC,IAAI,CAAC,EAAE;QAC3CV,GAAG,CACF,SAAS,EACT,6BAA6B,IAC3B,OAAOK,MAAM,KAAK,WAAW,GAC3B,2BAA2B,GAC3B,yBAAyB,CAC9B,CAAC;QACDL,GAAG,CAAC,SAAS,EAAE,QAAQ,GAAGA,GAAG,CAACW,WAAW,CAACF,GAAG,CAAC,CAAC;QAC/C,IAAI,OAAOJ,MAAM,KAAK,WAAW,EAAE;UAClCA,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QACzB;MACD,CAAC,MAAM;QACNP,GAAG,CAAC,SAAS,EAAE,uBAAuB,GAAGA,GAAG,CAACW,WAAW,CAACF,GAAG,CAAC,CAAC;MAC/D;IACD,CAAC,CAAC;EACJ,CAAC;EACD,IAAIG,UAAU,GAAGX,OAAO,CAAC,WAAW,CAAC;EACrCW,UAAU,CAACC,EAAE,CAAC,kBAAkB,EAAE,UAAUC,WAAW,EAAE;IACxDlB,QAAQ,GAAGkB,WAAW;IACtB,IAAI,CAACjB,QAAQ,CAAC,CAAC,IAAIH,MAAM,CAACC,GAAG,CAACe,MAAM,CAAC,CAAC,KAAK,MAAM,EAAE;MAClDV,GAAG,CAAC,MAAM,EAAE,6CAA6C,CAAC;MAC1DE,KAAK,CAAC,CAAC;IACR;EACD,CAAC,CAAC;EACFF,GAAG,CAAC,MAAM,EAAE,6CAA6C,CAAC;AAC3D,CAAC,MAAM;EACN,MAAM,IAAIe,KAAK,CAAC,2CAA2C,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}