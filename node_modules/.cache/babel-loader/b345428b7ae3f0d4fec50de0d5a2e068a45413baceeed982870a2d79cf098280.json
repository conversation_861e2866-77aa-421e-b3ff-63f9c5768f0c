{"ast": null, "code": "import React,{Suspense}from'react';import{BrowserRouter,Routes,Route}from'react-router-dom';import{HelmetProvider}from'react-helmet-async';// components\nimport Layout from'./layout/Layout';// layout (navigation {page} footer)\nimport ErrorBoundary from'./components/ErrorBoundary/ErrorBoundary';import GoogleAnalytics from'./components/GoogleAnalytics';import Home from'./pages/Home/Home';import Models from'./pages/Models/Models';import NewsList from'./pages/Discover/News/NewsList';import NewsPage from'./pages/Discover/News/NewsPage.jsx/NewsPage';import About from'./pages/Discover/About/About';import History from'./pages/Discover/About/History/History';import Owners from'./pages/Owners/Owners';import Warranty from'./pages/Owners/Pages/Warranty/Warranty';import Assistance from'./pages/Owners/Pages/Assistance/Assistance';import Service from'./pages/Owners/Pages/Service/Service';import Parts from'./pages/Owners/Pages/Parts/Parts';import Accessories from'./pages/Owners/Pages/Accessories/Accessories';import ServicePlan from'./pages/Owners/Pages/ServicePlan/ServicePlan';import Table from'./pages/Owners/Pages/Vehicle-reference-table/Table';import Care from'./pages/Owners/Pages/Care/Care';import Offers from'./pages/Offer/Offers';import Privacy from'./pages/Privacy/Privacy';import Contact from'./pages/Discover/Contact/Contact';import BookTestDrive from'./pages/Book-a-test-drive/Book-a-test-dieve';import NoPage from'./pages/NoPage/NoPage';import DynamicModelPage from'./pages/Models/Pages/DynamicModelPage/DynamicModelPage';import OfferPage from'./pages/Offer/OfferPage/OfferPage';// Loading component\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingSpinner=()=>/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})});function App(){return/*#__PURE__*/_jsx(HelmetProvider,{children:/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsxs(BrowserRouter,{children:[/*#__PURE__*/_jsx(GoogleAnalytics,{measurementId:process.env.REACT_APP_GA_MEASUREMENT_ID}),/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(LoadingSpinner,{}),children:/*#__PURE__*/_jsx(Routes,{children:/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Layout,{}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"models\",element:/*#__PURE__*/_jsx(Models,{})}),/*#__PURE__*/_jsx(Route,{path:\"about-gwm\",element:/*#__PURE__*/_jsx(About,{})}),/*#__PURE__*/_jsx(Route,{path:\"news-list\",element:/*#__PURE__*/_jsx(NewsList,{})}),/*#__PURE__*/_jsx(Route,{path:\"news-list/:id\",element:/*#__PURE__*/_jsx(NewsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"about-gwm/history\",element:/*#__PURE__*/_jsx(History,{})}),/*#__PURE__*/_jsx(Route,{path:\"contact\",element:/*#__PURE__*/_jsx(Contact,{})}),/*#__PURE__*/_jsx(Route,{path:\"offers\",element:/*#__PURE__*/_jsx(Offers,{})}),/*#__PURE__*/_jsx(Route,{path:\"offers/:id\",element:/*#__PURE__*/_jsx(OfferPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"book-a-test-drive\",element:/*#__PURE__*/_jsx(BookTestDrive,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners\",element:/*#__PURE__*/_jsx(Owners,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/warranty\",element:/*#__PURE__*/_jsx(Warranty,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/road-assistance\",element:/*#__PURE__*/_jsx(Assistance,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/service\",element:/*#__PURE__*/_jsx(Service,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/service-plans\",element:/*#__PURE__*/_jsx(ServicePlan,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/parts\",element:/*#__PURE__*/_jsx(Parts,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/accessories\",element:/*#__PURE__*/_jsx(Accessories,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/care\",element:/*#__PURE__*/_jsx(Care,{})}),/*#__PURE__*/_jsx(Route,{path:\"owners/vehicle-reference-table\",element:/*#__PURE__*/_jsx(Table,{})}),/*#__PURE__*/_jsx(Route,{path:\"privacy\",element:/*#__PURE__*/_jsx(Privacy,{})}),/*#__PURE__*/_jsx(Route,{path:\"models/:slug\",element:/*#__PURE__*/_jsx(DynamicModelPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(NoPage,{})})]})})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Layout", "Error<PERSON>ou<PERSON><PERSON>", "GoogleAnalytics", "Home", "Models", "NewsList", "NewsPage", "About", "History", "Owners", "Warranty", "Assistance", "Service", "Parts", "Accessories", "ServicePlan", "Table", "Care", "Offers", "Privacy", "Contact", "BookTestDrive", "NoPage", "DynamicModelPage", "OfferPage", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSpinner", "className", "children", "App", "measurementId", "process", "env", "REACT_APP_GA_MEASUREMENT_ID", "fallback", "path", "element", "index"], "sources": ["/var/www/html/gwm.tj/src/App.js"], "sourcesContent": ["import React, { Suspense } from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport { HelmetProvider } from 'react-helmet-async';\n// components\nimport Layout from './layout/Layout'; // layout (navigation {page} footer)\nimport ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';\nimport GoogleAnalytics from './components/GoogleAnalytics';\n\nimport Home from './pages/Home/Home';\nimport Models from './pages/Models/Models';\nimport NewsList from './pages/Discover/News/NewsList';\nimport NewsPage from './pages/Discover/News/NewsPage.jsx/NewsPage';\nimport About from './pages/Discover/About/About';\nimport History from './pages/Discover/About/History/History';\nimport Owners from './pages/Owners/Owners';\nimport Warranty from './pages/Owners/Pages/Warranty/Warranty';\nimport Assistance from './pages/Owners/Pages/Assistance/Assistance';\nimport Service from './pages/Owners/Pages/Service/Service';\nimport Parts from './pages/Owners/Pages/Parts/Parts';\nimport Accessories from './pages/Owners/Pages/Accessories/Accessories';\nimport ServicePlan from './pages/Owners/Pages/ServicePlan/ServicePlan';\nimport Table from './pages/Owners/Pages/Vehicle-reference-table/Table';\nimport Care from './pages/Owners/Pages/Care/Care';\nimport Offers from './pages/Offer/Offers';\nimport Privacy from './pages/Privacy/Privacy';\nimport Contact from './pages/Discover/Contact/Contact';\nimport BookTestDrive from './pages/Book-a-test-drive/Book-a-test-dieve';\nimport NoPage from './pages/NoPage/NoPage';\nimport DynamicModelPage from './pages/Models/Pages/DynamicModelPage/DynamicModelPage';\nimport OfferPage from './pages/Offer/OfferPage/OfferPage';\n\n// Loading component\nconst LoadingSpinner = () => (\n  <div className=\"loaderWrapper\">\n    <div className=\"loaderPage\"></div>\n  </div>\n);\n\nfunction App() {\n  return (\n    <HelmetProvider>\n      <ErrorBoundary>\n        <BrowserRouter>\n          <GoogleAnalytics\n            measurementId={process.env.REACT_APP_GA_MEASUREMENT_ID}\n          />\n          <Suspense fallback={<LoadingSpinner />}>\n            <Routes>\n              <Route path=\"/\" element={<Layout />}>\n                {/* home  page*/}\n                <Route index element={<Home />} />\n                {/* models list page */}\n                <Route path=\"models\" element={<Models />} />\n                {/* model page  */}\n                {/* <Route path=\"models/:id\" element={<ModelPage />} /> */}\n                {/* about  page*/}\n                <Route path=\"about-gwm\" element={<About />} />\n                {/* news list  page*/}\n                <Route path=\"news-list\" element={<NewsList />} />\n                {/* news  page*/}\n                <Route path=\"news-list/:id\" element={<NewsPage />} />\n\n                {/* history  page*/}\n                <Route path=\"about-gwm/history\" element={<History />} />\n                {/* contact page  */}\n                <Route path=\"contact\" element={<Contact />} />\n                {/* offer page  */}\n                <Route path=\"offers\" element={<Offers />} />\n                <Route path=\"offers/:id\" element={<OfferPage />} />\n                {/* test drive  */}\n                <Route path=\"book-a-test-drive\" element={<BookTestDrive />} />\n                {/* owners pages*/}\n                <Route path=\"owners\" element={<Owners />} />\n                <Route path=\"owners/warranty\" element={<Warranty />} />\n                <Route path=\"owners/road-assistance\" element={<Assistance />} />\n                <Route path=\"owners/service\" element={<Service />} />\n                <Route path=\"owners/service-plans\" element={<ServicePlan />} />\n                <Route path=\"owners/parts\" element={<Parts />} />\n                <Route path=\"owners/accessories\" element={<Accessories />} />\n                <Route path=\"owners/care\" element={<Care />} />\n                <Route\n                  path=\"owners/vehicle-reference-table\"\n                  element={<Table />}\n                />\n                {/* privacy page*/}\n                <Route path=\"privacy\" element={<Privacy />} />\n                {/* cars page - Dynamic routes */}\n                <Route path=\"models/:slug\" element={<DynamicModelPage />} />\n                {/* no page - MUST BE LAST */}\n                <Route path=\"*\" element={<NoPage />} />\n              </Route>\n            </Routes>\n          </Suspense>\n        </BrowserRouter>\n      </ErrorBoundary>\n    </HelmetProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,aAAa,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAC/D,OAASC,cAAc,KAAQ,oBAAoB,CACnD;AACA,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CAAE;AACtC,MAAO,CAAAC,aAAa,KAAM,0CAA0C,CACpE,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAE1D,MAAO,CAAAC,IAAI,KAAM,mBAAmB,CACpC,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,MAAO,CAAAC,QAAQ,KAAM,6CAA6C,CAClE,MAAO,CAAAC,KAAK,KAAM,8BAA8B,CAChD,MAAO,CAAAC,OAAO,KAAM,wCAAwC,CAC5D,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,wCAAwC,CAC7D,MAAO,CAAAC,UAAU,KAAM,4CAA4C,CACnE,MAAO,CAAAC,OAAO,KAAM,sCAAsC,CAC1D,MAAO,CAAAC,KAAK,KAAM,kCAAkC,CACpD,MAAO,CAAAC,WAAW,KAAM,8CAA8C,CACtE,MAAO,CAAAC,WAAW,KAAM,8CAA8C,CACtE,MAAO,CAAAC,KAAK,KAAM,oDAAoD,CACtE,MAAO,CAAAC,IAAI,KAAM,gCAAgC,CACjD,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,aAAa,KAAM,6CAA6C,CACvE,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAC1C,MAAO,CAAAC,gBAAgB,KAAM,wDAAwD,CACrF,MAAO,CAAAC,SAAS,KAAM,mCAAmC,CAEzD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,gBACrBH,IAAA,QAAKI,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BL,IAAA,QAAKI,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CACN,CAED,QAAS,CAAAE,GAAGA,CAAA,CAAG,CACb,mBACEN,IAAA,CAAC3B,cAAc,EAAAgC,QAAA,cACbL,IAAA,CAACzB,aAAa,EAAA8B,QAAA,cACZH,KAAA,CAAChC,aAAa,EAAAmC,QAAA,eACZL,IAAA,CAACxB,eAAe,EACd+B,aAAa,CAAEC,OAAO,CAACC,GAAG,CAACC,2BAA4B,CACxD,CAAC,cACFV,IAAA,CAAC/B,QAAQ,EAAC0C,QAAQ,cAAEX,IAAA,CAACG,cAAc,GAAE,CAAE,CAAAE,QAAA,cACrCL,IAAA,CAAC7B,MAAM,EAAAkC,QAAA,cACLH,KAAA,CAAC9B,KAAK,EAACwC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEb,IAAA,CAAC1B,MAAM,GAAE,CAAE,CAAA+B,QAAA,eAElCL,IAAA,CAAC5B,KAAK,EAAC0C,KAAK,MAACD,OAAO,cAAEb,IAAA,CAACvB,IAAI,GAAE,CAAE,CAAE,CAAC,cAElCuB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEb,IAAA,CAACtB,MAAM,GAAE,CAAE,CAAE,CAAC,cAI5CsB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEb,IAAA,CAACnB,KAAK,GAAE,CAAE,CAAE,CAAC,cAE9CmB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEb,IAAA,CAACrB,QAAQ,GAAE,CAAE,CAAE,CAAC,cAEjDqB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEb,IAAA,CAACpB,QAAQ,GAAE,CAAE,CAAE,CAAC,cAGrDoB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEb,IAAA,CAAClB,OAAO,GAAE,CAAE,CAAE,CAAC,cAExDkB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEb,IAAA,CAACN,OAAO,GAAE,CAAE,CAAE,CAAC,cAE9CM,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEb,IAAA,CAACR,MAAM,GAAE,CAAE,CAAE,CAAC,cAC5CQ,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEb,IAAA,CAACF,SAAS,GAAE,CAAE,CAAE,CAAC,cAEnDE,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEb,IAAA,CAACL,aAAa,GAAE,CAAE,CAAE,CAAC,cAE9DK,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEb,IAAA,CAACjB,MAAM,GAAE,CAAE,CAAE,CAAC,cAC5CiB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEb,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAE,CAAC,cACvDgB,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAAEb,IAAA,CAACf,UAAU,GAAE,CAAE,CAAE,CAAC,cAChEe,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEb,IAAA,CAACd,OAAO,GAAE,CAAE,CAAE,CAAC,cACrDc,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEb,IAAA,CAACX,WAAW,GAAE,CAAE,CAAE,CAAC,cAC/DW,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEb,IAAA,CAACb,KAAK,GAAE,CAAE,CAAE,CAAC,cACjDa,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEb,IAAA,CAACZ,WAAW,GAAE,CAAE,CAAE,CAAC,cAC7DY,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEb,IAAA,CAACT,IAAI,GAAE,CAAE,CAAE,CAAC,cAC/CS,IAAA,CAAC5B,KAAK,EACJwC,IAAI,CAAC,gCAAgC,CACrCC,OAAO,cAAEb,IAAA,CAACV,KAAK,GAAE,CAAE,CACpB,CAAC,cAEFU,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEb,IAAA,CAACP,OAAO,GAAE,CAAE,CAAE,CAAC,cAE9CO,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEb,IAAA,CAACH,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAE5DG,IAAA,CAAC5B,KAAK,EAACwC,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEb,IAAA,CAACJ,MAAM,GAAE,CAAE,CAAE,CAAC,EAClC,CAAC,CACF,CAAC,CACD,CAAC,EACE,CAAC,CACH,CAAC,CACF,CAAC,CAErB,CAEA,cAAe,CAAAU,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}