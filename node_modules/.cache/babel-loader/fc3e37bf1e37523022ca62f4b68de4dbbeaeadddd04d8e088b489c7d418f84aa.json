{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nfunction useForceUpdate() {\n  const isMounted = useIsMounted();\n  const [forcedRenderCount, setForcedRenderCount] = useState(0);\n  const forceRender = useCallback(() => {\n    isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n  }, [forcedRenderCount]);\n  /**\n   * Defer this to the end of the next animation frame in case there are multiple\n   * synchronous calls.\n   */\n  const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n  return [deferredForceRender, forcedRenderCount];\n}\nexport { useForceUpdate };", "map": {"version": 3, "names": ["frame", "useState", "useCallback", "useIsMounted", "useForceUpdate", "isMounted", "forcedRenderCount", "setForcedRenderCount", "forceRender", "current", "deferred<PERSON><PERSON><PERSON><PERSON><PERSON>", "postRender"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/utils/use-force-update.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\n\nfunction useForceUpdate() {\n    const isMounted = useIsMounted();\n    const [forcedRenderCount, setForcedRenderCount] = useState(0);\n    const forceRender = useCallback(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = useCallback(() => frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\nexport { useForceUpdate };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,YAAY,QAAQ,sBAAsB;AAEnD,SAASC,cAAcA,CAAA,EAAG;EACtB,MAAMC,SAAS,GAAGF,YAAY,CAAC,CAAC;EAChC,MAAM,CAACG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAMO,WAAW,GAAGN,WAAW,CAAC,MAAM;IAClCG,SAAS,CAACI,OAAO,IAAIF,oBAAoB,CAACD,iBAAiB,GAAG,CAAC,CAAC;EACpE,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EACvB;AACJ;AACA;AACA;EACI,MAAMI,mBAAmB,GAAGR,WAAW,CAAC,MAAMF,KAAK,CAACW,UAAU,CAACH,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAC3F,OAAO,CAACE,mBAAmB,EAAEJ,iBAAiB,CAAC;AACnD;AAEA,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}