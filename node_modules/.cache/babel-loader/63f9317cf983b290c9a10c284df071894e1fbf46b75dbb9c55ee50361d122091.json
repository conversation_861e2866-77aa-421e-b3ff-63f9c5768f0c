{"ast": null, "code": "function r(r, e) {\n  return new Proxy(r, {\n    set: function (n, t, u) {\n      return \"current\" === t && (u !== r.current && (null !== r.current && e.unregister(r.current), null !== u && e.register(u)), n[t] = u, !0);\n    }\n  });\n}\nexport { r as default };", "map": {"version": 3, "names": ["r", "e", "Proxy", "set", "n", "t", "u", "current", "unregister", "register", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/@react-input/core/module/createProxy.js"], "sourcesContent": ["function r(r,e){return new Proxy(r,{set:function(n,t,u){return\"current\"===t&&(u!==r.current&&(null!==r.current&&e.unregister(r.current),null!==u&&e.register(u)),n[t]=u,!0)}})}export{r as default};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,OAAO,IAAIC,KAAK,CAACF,CAAC,EAAC;IAACG,GAAG,EAAC,SAAAA,CAASC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;MAAC,OAAM,SAAS,KAAGD,CAAC,KAAGC,CAAC,KAAGN,CAAC,CAACO,OAAO,KAAG,IAAI,KAAGP,CAAC,CAACO,OAAO,IAAEN,CAAC,CAACO,UAAU,CAACR,CAAC,CAACO,OAAO,CAAC,EAAC,IAAI,KAAGD,CAAC,IAAEL,CAAC,CAACQ,QAAQ,CAACH,CAAC,CAAC,CAAC,EAACF,CAAC,CAACC,CAAC,CAAC,GAACC,CAAC,EAAC,CAAC,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}