{"ast": null, "code": "import { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll({\n  container,\n  target,\n  layoutEffect = true,\n  ...options\n} = {}) {\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scroll((_progress, {\n      x,\n      y\n    }) => {\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, {\n      ...options,\n      container: container?.current || undefined,\n      target: target?.current || undefined\n    });\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "warning", "useEffect", "scroll", "useConstant", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "container", "target", "layoutEffect", "options", "values", "useLifecycleEffect", "_progress", "x", "y", "set", "progress", "undefined", "JSON", "stringify", "offset"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { warning } from 'motion-utils';\nimport { useEffect } from 'react';\nimport { scroll } from '../render/dom/scroll/index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scroll((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,YAAY;AACxC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,QAAQ,gCAAgC;AACvD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BP,OAAO,CAACQ,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,EAAE,sBAAsBH,IAAI,+MAA+M,CAAC;AACpR;AACA,MAAMI,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEZ,WAAW,CAAC,CAAC,CAAC;EACvBa,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,eAAe,EAAEd,WAAW,CAAC,CAAC,CAAC;EAC/Be,eAAe,EAAEf,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASgB,SAASA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC,YAAY,GAAG,IAAI;EAAE,GAAGC;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5E,MAAMC,MAAM,GAAGjB,WAAW,CAACO,wBAAwB,CAAC;EACpD,MAAMW,kBAAkB,GAAGH,YAAY,GACjCd,yBAAyB,GACzBH,SAAS;EACfoB,kBAAkB,CAAC,MAAM;IACrBhB,UAAU,CAAC,QAAQ,EAAEY,MAAM,CAAC;IAC5BZ,UAAU,CAAC,WAAW,EAAEW,SAAS,CAAC;IAClC,OAAOd,MAAM,CAAC,CAACoB,SAAS,EAAE;MAAEC,CAAC;MAAEC;IAAG,CAAC,KAAK;MACpCJ,MAAM,CAACT,OAAO,CAACc,GAAG,CAACF,CAAC,CAACd,OAAO,CAAC;MAC7BW,MAAM,CAACP,eAAe,CAACY,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCN,MAAM,CAACR,OAAO,CAACa,GAAG,CAACD,CAAC,CAACf,OAAO,CAAC;MAC7BW,MAAM,CAACN,eAAe,CAACW,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAE;MACC,GAAGP,OAAO;MACVH,SAAS,EAAEA,SAAS,EAAEP,OAAO,IAAIkB,SAAS;MAC1CV,MAAM,EAAEA,MAAM,EAAER,OAAO,IAAIkB;IAC/B,CAAC,CAAC;EACN,CAAC,EAAE,CAACX,SAAS,EAAEC,MAAM,EAAEW,IAAI,CAACC,SAAS,CAACV,OAAO,CAACW,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOV,MAAM;AACjB;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}