{"ast": null, "code": "import React,{useEffect,useState}from'react';import Sidebar from'../../components/sidebar/Sidebar';import img from'../../../../asset/imgs/owners/road-assistance.webp';import AOS from'aos';import'aos/dist/aos.css';import styles from'../../owners.module.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Assistance=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsxs(\"div\",{className:styles.layout,children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsx(\"main\",{className:styles.main,children:/*#__PURE__*/_jsxs(\"div\",{className:styles.mainContainer,children:[/*#__PURE__*/_jsx(\"h1\",{\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u041F\\u041E\\u041C\\u041E\\u0429\\u042C \\u041D\\u0410 \\u0414\\u041E\\u0420\\u041E\\u0413\\u0410\\u0425\"})}),/*#__PURE__*/_jsxs(\"span\",{className:styles.underText,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:[\"\\u041A\\u0420\\u0423\\u0413\\u041B\\u041E\\u0421\\u0423\\u0422\\u041E\\u0427\\u041D\\u0410\\u042F \\u041F\\u041E\\u041C\\u041E\\u0429\\u042C \\u041D\\u0410 \\u0414\\u041E\\u0420\\u041E\\u0413\\u0410\\u0425 \\u041F\\u041E \\u0422\\u0415\\u041B\\u0415\\u0424\\u041E\\u041D\\u0423 \",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"a\",{href:\"tel:6677\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"6677\"})})]}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",className:styles.redLine}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[/*#__PURE__*/_jsx(\"img\",{src:img,alt:\"\\u0411\\u0430\\u043D\\u043D\\u0435\\u0440 \\u0432\\u043B\\u0430\\u0434\\u0435\\u043B\\u044C\\u0446\\u0435\\u0432 GWM\",className:styles.banner}),/*#__PURE__*/_jsx(\"div\",{className:styles.textContent,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"300\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u041C\\u044B \\u0432\\u0441\\u0435\\u0433\\u0434\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u043C\\u0441\\u044F \\u043A \\u0442\\u043E\\u043C\\u0443, \\u0447\\u0442\\u043E\\u0431\\u044B \\u0432\\u044B \\u043D\\u0430\\u0441\\u043B\\u0430\\u0436\\u0434\\u0430\\u043B\\u0438\\u0441\\u044C \\u0432\\u043E\\u0436\\u0434\\u0435\\u043D\\u0438\\u0435\\u043C \\u0431\\u0435\\u0437 \\u0441\\u0442\\u0440\\u0435\\u0441\\u0441\\u0430, \\u0438 \\u0432 \\u0441\\u043B\\u0443\\u0447\\u0430\\u0435 \\u043B\\u044E\\u0431\\u043E\\u0439 \\u043D\\u0435\\u043F\\u0440\\u0435\\u0434\\u0432\\u0438\\u0434\\u0435\\u043D\\u043D\\u043E\\u0439 \\u043F\\u043E\\u043B\\u043E\\u043C\\u043A\\u0438 \\u0438\\u043B\\u0438 \\u043B\\u044E\\u0431\\u043E\\u0433\\u043E \\u0434\\u043E\\u0440\\u043E\\u0436\\u043D\\u043E\\u0433\\u043E \\u0438\\u043D\\u0446\\u0438\\u0434\\u0435\\u043D\\u0442\\u0430, \\u0441\\u0432\\u044F\\u0437\\u0430\\u043D\\u043D\\u043E\\u0433\\u043E \\u0441 \\u043F\\u043E\\u043A\\u0440\\u044B\\u0442\\u0438\\u0435\\u043C, \\u043C\\u044B \\u0431\\u0443\\u0434\\u0435\\u043C \\u0441\\u0442\\u0440\\u0435\\u043C\\u0438\\u0442\\u044C\\u0441\\u044F \\u043F\\u043E\\u043C\\u043E\\u0447\\u044C \\u0432\\u0430\\u043C \\u0438 \\u0432\\u0430\\u0448\\u0438\\u043C \\u0443\\u0432\\u0430\\u0436\\u0430\\u0435\\u043C\\u044B\\u043C \\u043F\\u0430\\u0441\\u0441\\u0430\\u0436\\u0438\\u0440\\u0430\\u043C \\u0434\\u043E\\u0431\\u0440\\u0430\\u0442\\u044C\\u0441\\u044F \\u0434\\u043E \\u043C\\u0435\\u0441\\u0442\\u0430 \\u043D\\u0430\\u0437\\u043D\\u0430\\u0447\\u0435\\u043D\\u0438\\u044F \\u043A\\u0430\\u043A \\u043C\\u043E\\u0436\\u043D\\u043E \\u0431\\u044B\\u0441\\u0442\\u0440\\u0435\\u0435, \\u0431\\u0435\\u0437\\u043E\\u043F\\u0430\\u0441\\u043D\\u0435\\u0435 \\u0438 \\u0431\\u0435\\u0437 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C.\"})})]})]})})]})})})});};export default Assistance;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Sidebar", "img", "AOS", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Assistance", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "layout", "main", "mainContainer", "underText", "href", "redLine", "flexContent", "src", "alt", "banner", "textContent"], "sources": ["/var/www/html/gwm.tj/src/pages/Owners/Pages/Assistance/Assistance.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport Sidebar from '../../components/sidebar/Sidebar';\nimport img from '../../../../asset/imgs/owners/road-assistance.webp';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\n\nimport styles from '../../owners.module.css';\n\nconst Assistance = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({\n      duration: 500,\n      once: false,\n    });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className={styles.layout}>\n              <Sidebar />\n              <main className={styles.main}>\n                <div className={styles.mainContainer}>\n                  <h1 data-aos=\"fade-up\">\n                    <strong>ПОМОЩЬ НА ДОРОГАХ</strong>\n                  </h1>\n                  <span\n                    className={styles.underText}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"100\"\n                  >\n                    КРУГЛОСУТОЧНАЯ ПОМОЩЬ НА ДОРОГАХ ПО ТЕЛЕФОНУ <br />\n                    <a href=\"tel:6677\">\n                      <strong>6677</strong>\n                    </a>\n                  </span>\n                  <i\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"150\"\n                    className={styles.redLine}\n                  ></i>\n                  <div\n                    className={styles.flexContent}\n                    data-aos=\"fade-up\"\n                    data-aos-delay=\"200\"\n                  >\n                    <img\n                      src={img}\n                      alt=\"Баннер владельцев GWM\"\n                      className={styles.banner}\n                    />\n                    <div\n                      className={styles.textContent}\n                      data-aos=\"fade-up\"\n                      data-aos-delay=\"300\"\n                    >\n                      <p>\n                        Мы всегда стремимся к тому, чтобы вы наслаждались\n                        вождением без стресса, и в случае любой непредвиденной\n                        поломки или любого дорожного инцидента, связанного с\n                        покрытием, мы будем стремиться помочь вам и вашим\n                        уважаемым пассажирам добраться до места назначения как\n                        можно быстрее, безопаснее и без проблем.\n                      </p>\n                      {/* \n                      <p>Предлагаемые услуги:</p>\n                      <ul>\n                        <li>Буксировка</li>\n                        <li>Безопасность на обочине дороги.</li>\n                        <li>\n                          Замена спущенных шин и помощь в замене шины (замена\n                          шины за счет клиента)\n                        </li>\n                        <li>\n                          Услуга по разблокировке ключей. (замена ключей за счет\n                          клиента)\n                        </li>\n                      </ul> \n                      */}\n                    </div>\n                  </div>\n                </div>\n              </main>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Assistance;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,OAAO,KAAM,kCAAkC,CACtD,MAAO,CAAAC,GAAG,KAAM,oDAAoD,CACpE,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CAEzB,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdI,GAAG,CAACW,IAAI,CAAC,CACPC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,KACR,CAAC,CAAC,CAEFC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBpB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBlB,KAAA,QAAKmB,SAAS,CAAEvB,MAAM,CAACwB,MAAO,CAAAF,QAAA,eAC5BpB,IAAA,CAACL,OAAO,GAAE,CAAC,cACXK,IAAA,SAAMqB,SAAS,CAAEvB,MAAM,CAACyB,IAAK,CAAAH,QAAA,cAC3BlB,KAAA,QAAKmB,SAAS,CAAEvB,MAAM,CAAC0B,aAAc,CAAAJ,QAAA,eACnCpB,IAAA,OAAI,WAAS,SAAS,CAAAoB,QAAA,cACpBpB,IAAA,WAAAoB,QAAA,CAAQ,8FAAiB,CAAQ,CAAC,CAChC,CAAC,cACLlB,KAAA,SACEmB,SAAS,CAAEvB,MAAM,CAAC2B,SAAU,CAC5B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAL,QAAA,EACrB,kPAC8C,cAAApB,IAAA,QAAK,CAAC,cACnDA,IAAA,MAAG0B,IAAI,CAAC,UAAU,CAAAN,QAAA,cAChBpB,IAAA,WAAAoB,QAAA,CAAQ,MAAI,CAAQ,CAAC,CACpB,CAAC,EACA,CAAC,cACPpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAEvB,MAAM,CAAC6B,OAAQ,CACxB,CAAC,cACLzB,KAAA,QACEmB,SAAS,CAAEvB,MAAM,CAAC8B,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAR,QAAA,eAEpBpB,IAAA,QACE6B,GAAG,CAAEjC,GAAI,CACTkC,GAAG,CAAC,uGAAuB,CAC3BT,SAAS,CAAEvB,MAAM,CAACiC,MAAO,CAC1B,CAAC,cACF/B,IAAA,QACEqB,SAAS,CAAEvB,MAAM,CAACkC,WAAY,CAC9B,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAZ,QAAA,cAEpBpB,IAAA,MAAAoB,QAAA,CAAG,uiDAOH,CAAG,CAAC,CAgBD,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAf,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}