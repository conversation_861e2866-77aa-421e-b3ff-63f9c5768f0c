{"ast": null, "code": "import React,{useEffect,useState}from'react';import styles from'./about.module.css';// animation\nimport AOS from'aos';import'aos/dist/aos.css';//\nimport img_1 from'../../../asset/imgs/about/1.webp';import img_2 from'../../../asset/imgs/about/5-pc.webp';import img_3 from'../../../asset/imgs/about/3.webp';import img_4 from'../../../asset/imgs/about/6-1.webp';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const About=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';// Отключаем скролл при загрузке\nconst timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';// Возвращаем скролл после загрузки\n},300);// 1 секунда для имитации загрузки\nreturn()=>{clearTimeout(timer);document.body.style.overflow='visible';// На случай размонтирования компонента\n};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0412\\u0421\\u0415 \\u041E GWM\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",className:\"redLine\"}),/*#__PURE__*/_jsx(\"p\",{className:\"underText\",\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"GWM \\u2014 \\u044D\\u0442\\u043E \\u0433\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0430\\u044F \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044F, \\u0447\\u0435\\u0439 \\u0431\\u0438\\u0437\\u043D\\u0435\\u0441 \\u0432\\u043A\\u043B\\u044E\\u0447\\u0430\\u0435\\u0442 \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0438 \\u0434\\u0435\\u0442\\u0430\\u043B\\u0435\\u0439, \\u041D\\u0418\\u041E\\u041A\\u0420, \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E, \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u0438 \\u0438 \\u043E\\u0431\\u0441\\u043B\\u0443\\u0436\\u0438\\u0432\\u0430\\u043D\\u0438\\u0435. \\u0421\\u043E\\u0441\\u0442\\u043E\\u0438\\u0442 \\u0438\\u0437 \\u0442\\u0430\\u043A\\u0438\\u0445 \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432, \\u043A\\u0430\\u043A HAVAL, ORA, STEED, TANK, P300 \\u0438 P500.\"}),/*#__PURE__*/_jsxs(\"div\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"+ 173,2 \\u043C\\u043B\\u0440\\u0434 \\u0434\\u043E\\u043B\\u043B\\u0430\\u0440\\u043E\\u0432 \\u0421\\u0428\\u0410 \\u043E\\u043F\\u0435\\u0440\\u0430\\u0446\\u0438\\u043E\\u043D\\u043D\\u043E\\u0439 \\u043F\\u0440\\u0438\\u0431\\u044B\\u043B\\u0438 \\u0432 2023 \\u0433\\u043E\\u0434\\u0443\"}),/*#__PURE__*/_jsx(\"p\",{children:\"+ 8 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0434\\u0440\\u044F\\u0434 \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436\\u0430 \\u0431\\u043E\\u043B\\u0435\\u0435 1 \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D\\u0430 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439\"}),/*#__PURE__*/_jsx(\"p\",{children:\"+ 14 \\u043C\\u0438\\u043B\\u043B\\u0438\\u043E\\u043D\\u043E\\u0432 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u0435\\u0439 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"}),/*#__PURE__*/_jsx(\"p\",{children:\"+ 7000 \\u0434\\u0438\\u0441\\u0442\\u0440\\u0438\\u0431\\u044C\\u044E\\u0442\\u043E\\u0440\\u0441\\u043A\\u0438\\u0445 \\u0438 \\u0441\\u0435\\u0440\\u0432\\u0438\\u0441\\u043D\\u044B\\u0445 \\u0446\\u0435\\u043D\\u0442\\u0440\\u043E\\u0432 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"}),/*#__PURE__*/_jsx(\"p\",{children:\"+ 90 000 \\u0441\\u043E\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u043A\\u043E\\u0432 \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443\"}),/*#__PURE__*/_jsx(\"p\",{children:\"+ 170 \\u0441\\u0442\\u0440\\u0430\\u043D \\u0438 \\u0440\\u0435\\u0433\\u0438\\u043E\\u043D\\u043E\\u0432-\\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0435\\u0440\\u043E\\u0432\"})]})]}),/*#__PURE__*/_jsxs(\"section\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:/*#__PURE__*/_jsx(\"img\",{src:img_1,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.text,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"250\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u041F\\u043E\\u0447\\u0435\\u043C\\u0443 GWM?\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0421 \\u043D\\u043E\\u0432\\u043E\\u0439 \\u044D\\u043D\\u0435\\u0440\\u0433\\u0438\\u0435\\u0439 \\u0438 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C\\u0438 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u043C\\u0438 \\u043C\\u044B \\u043D\\u0430\\u0434\\u0435\\u0435\\u043C\\u0441\\u044F \\u0441\\u043E\\u0437\\u0434\\u0430\\u0442\\u044C \\u0434\\u043B\\u044F \\u0432\\u0430\\u0441 \\u0446\\u0435\\u043D\\u043D\\u044B\\u0439 \\u043E\\u043F\\u044B\\u0442.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0418\\u043C\\u0435\\u044F \\u0434\\u043E\\u043B\\u0433\\u043E\\u0441\\u0440\\u043E\\u0447\\u043D\\u0443\\u044E \\u0446\\u0435\\u043B\\u044C \\u0441\\u043E\\u043A\\u0440\\u0430\\u0442\\u0438\\u0442\\u044C \\u0432\\u044B\\u0431\\u0440\\u043E\\u0441\\u044B \\u0443\\u0433\\u043B\\u0435\\u0440\\u043E\\u0434\\u0430 \\u0434\\u043E \\u043D\\u0443\\u043B\\u044F, \\u043C\\u044B \\u0441\\u043E\\u0437\\u0434\\u0430\\u043B\\u0438 \\u043C\\u043E\\u0434\\u0435\\u043B\\u044C \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0430 \\u0433\\u0438\\u0431\\u0440\\u0438\\u0434\\u043D\\u043E\\u0439, \\u044D\\u043B\\u0435\\u043A\\u0442\\u0440\\u0438\\u0447\\u0435\\u0441\\u043A\\u043E\\u0439 \\u0438 \\u0432\\u043E\\u0434\\u043E\\u0440\\u043E\\u0434\\u043D\\u043E\\u0439 \\u044D\\u043D\\u0435\\u0440\\u0433\\u0438\\u0438, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0438\\u0442\\u044C \\u0431\\u043E\\u043B\\u0435\\u0435 \\u0447\\u0438\\u0441\\u0442\\u044B\\u0435 \\u0438 \\u0438\\u043D\\u0442\\u0435\\u043B\\u043B\\u0435\\u043A\\u0442\\u0443\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044F\\u043C \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:/*#__PURE__*/_jsx(\"img\",{src:img_2,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.text,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"250\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0422\\u0432\\u043E\\u0440\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0431\\u0435\\u0437 \\u0433\\u0440\\u0430\\u043D\\u0438\\u0446\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u041C\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u043C \\u0432\\u0430\\u043C \\u043B\\u0443\\u0447\\u0448\\u0438\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438 \\u0441 \\u043F\\u043E\\u043C\\u043E\\u0449\\u044C\\u044E \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F, \\u0438\\u0441\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u043D\\u0438\\u0439 \\u0438 \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u043E\\u043A.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0428\\u0442\\u0430\\u0431-\\u043A\\u0432\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430 GWM \\u043D\\u0430\\u0445\\u043E\\u0434\\u0438\\u0442\\u0441\\u044F \\u0432 \\u041A\\u0438\\u0442\\u0430\\u0435, \\u0430 \\u0435\\u0435 \\u0433\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 R&D-\\u043E\\u0442\\u0434\\u0435\\u043B \\u043E\\u0445\\u0432\\u0430\\u0442\\u044B\\u0432\\u0430\\u0435\\u0442 \\u0415\\u0432\\u0440\\u043E\\u043F\\u0443, \\u0410\\u0437\\u0438\\u044E, \\u0421\\u0435\\u0432\\u0435\\u0440\\u043D\\u0443\\u044E \\u0438 \\u042E\\u0436\\u043D\\u0443\\u044E \\u0410\\u043C\\u0435\\u0440\\u0438\\u043A\\u0443. R&D-\\u0446\\u0435\\u043D\\u0442\\u0440\\u044B \\u0442\\u0430\\u043A\\u0436\\u0435 \\u0431\\u044B\\u043B\\u0438 \\u0441\\u043E\\u0437\\u0434\\u0430\\u043D\\u044B \\u0432 \\u042F\\u043F\\u043E\\u043D\\u0438\\u0438, \\u0421\\u0428\\u0410, \\u0413\\u0435\\u0440\\u043C\\u0430\\u043D\\u0438\\u0438, \\u0418\\u043D\\u0434\\u0438\\u0438, \\u0410\\u0432\\u0441\\u0442\\u0440\\u0438\\u0438 \\u0438 \\u042E\\u0436\\u043D\\u043E\\u0439 \\u041A\\u043E\\u0440\\u0435\\u0435.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:/*#__PURE__*/_jsx(\"img\",{src:img_3,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.text,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"250\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0413\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0439 \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u044B\\u0439 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0412 \\u041A\\u0438\\u0442\\u0430\\u0435 GWM \\u0438\\u043C\\u0435\\u0435\\u0442 10 \\u0431\\u0430\\u0437 \\u043F\\u043E \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0443 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0446\\u0438\\u043A\\u043B\\u0430. \\u041D\\u0430 \\u0437\\u0430\\u0440\\u0443\\u0431\\u0435\\u0436\\u043D\\u043E\\u043C \\u0440\\u044B\\u043D\\u043A\\u0435 GWM \\u043E\\u0442\\u043A\\u0440\\u044B\\u043B\\u0430 \\u0437\\u0430\\u0432\\u043E\\u0434\\u044B \\u043F\\u043E \\u043F\\u0440\\u043E\\u0438\\u0437\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0443 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u0446\\u0438\\u043A\\u043B\\u0430 \\u0432 \\u0422\\u0430\\u0438\\u043B\\u0430\\u043D\\u0434\\u0435 \\u0438 \\u0411\\u0440\\u0430\\u0437\\u0438\\u043B\\u0438\\u0438, \\u0430 \\u0442\\u0430\\u043A\\u0436\\u0435 \\u043D\\u0435\\u0441\\u043A\\u043E\\u043B\\u044C\\u043A\\u043E \\u0437\\u0430\\u0432\\u043E\\u0434\\u043E\\u0432 KD \\u0432 \\u042D\\u043A\\u0432\\u0430\\u0434\\u043E\\u0440\\u0435, \\u041F\\u0430\\u043A\\u0438\\u0441\\u0442\\u0430\\u043D\\u0435 \\u0438 \\u0434\\u0440\\u0443\\u0433\\u0438\\u0445 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0430\\u0445.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:styles.flexContent,children:[/*#__PURE__*/_jsx(\"div\",{className:styles.img,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:/*#__PURE__*/_jsx(\"img\",{src:img_4,alt:\"\"})}),/*#__PURE__*/_jsxs(\"div\",{className:styles.text,\"data-aos\":\"fade-up\",\"data-aos-delay\":\"250\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u0413\\u043B\\u043E\\u0431\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u043C\\u0431\\u0438\\u0446\\u0438\\u0438, \\u043D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u0430\\u044F \\u043C\\u0438\\u0441\\u0441\\u0438\\u044F\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u041D\\u0430\\u0448\\u0430 \\u0446\\u0435\\u043B\\u044C \\u2014 \\u043F\\u0440\\u043E\\u0434\\u043E\\u043B\\u0436\\u0430\\u0442\\u044C \\u0440\\u0430\\u0441\\u0448\\u0438\\u0440\\u044F\\u0442\\u044C \\u0433\\u0440\\u0430\\u043D\\u0438\\u0446\\u044B \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0435\\u0439 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F. \\u0410\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u044B\\u0439 \\u0440\\u044B\\u043D\\u043E\\u043A \\u2014 \\u043E\\u0434\\u0438\\u043D \\u0438\\u0437 \\u0441\\u0430\\u043C\\u044B\\u0445 \\u043A\\u043E\\u043D\\u043A\\u0443\\u0440\\u0435\\u043D\\u0442\\u043D\\u044B\\u0445 \\u0432 \\u043C\\u0438\\u0440\\u0435, \\u0438 \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438 \\u0434\\u043B\\u044F \\u043D\\u043E\\u0432\\u043E\\u0433\\u043E \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430, \\u0440\\u043E\\u0436\\u0434\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0441\\u043F\\u0435\\u0446\\u0438\\u0430\\u043B\\u0438\\u0437\\u0430\\u0446\\u0438\\u0435\\u0439, \\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\\u0442\\u044C \\u0441\\u0432\\u043E\\u0439 \\u0441\\u043B\\u0435\\u0434 \\u043D\\u0438\\u043A\\u043E\\u0433\\u0434\\u0430 \\u043D\\u0435 \\u0431\\u044B\\u043B\\u0438 \\u0431\\u043E\\u043B\\u044C\\u0448\\u0438\\u043C\\u0438.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u041F\\u043E \\u043C\\u0435\\u0440\\u0435 \\u0440\\u0430\\u0437\\u0432\\u0438\\u0442\\u0438\\u044F \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0439 \\u043B\\u044E\\u0434\\u0438 \\u0438\\u0449\\u0443\\u0442 \\u043D\\u043E\\u0432\\u044B\\u0435 \\u0440\\u0435\\u0448\\u0435\\u043D\\u0438\\u044F \\u0434\\u043B\\u044F \\u043B\\u0443\\u0447\\u0448\\u0435\\u0433\\u043E \\u0431\\u0443\\u0434\\u0443\\u0449\\u0435\\u0433\\u043E. \\u041C\\u044B \\u0437\\u043D\\u0430\\u0435\\u043C, \\u0447\\u0442\\u043E \\u043B\\u044E\\u0431\\u043E\\u0432\\u044C \\u043A \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044F\\u043C \\u0441\\u043E\\u043F\\u0440\\u044F\\u0436\\u0435\\u043D\\u0430 \\u0441 \\u0432\\u044B\\u0441\\u043E\\u043A\\u0438\\u043C\\u0438 \\u043E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u044F\\u043C\\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u0438 \\u043D\\u0430\\u0434\\u0435\\u0436\\u043D\\u043E\\u0441\\u0442\\u0438. \\u0412\\u043E\\u0442 \\u043F\\u043E\\u0447\\u0435\\u043C\\u0443 GWM \\u0441\\u043E\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0430\\u0435\\u0442 \\u0441 \\u043B\\u0443\\u0447\\u0448\\u0438\\u043C\\u0438 \\u0438\\u043D\\u0436\\u0435\\u043D\\u0435\\u0440\\u0430\\u043C\\u0438 \\u0438 \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0435\\u0440\\u0430\\u043C\\u0438, \\u0447\\u0442\\u043E\\u0431\\u044B \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0442\\u044C \\u044D\\u043B\\u0435\\u0433\\u0430\\u043D\\u0442\\u043D\\u044B\\u0435, \\u0444\\u0443\\u043D\\u043A\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0438, \\u0438\\u0434\\u0435\\u0430\\u043B\\u044C\\u043D\\u043E \\u043F\\u043E\\u0434\\u0445\\u043E\\u0434\\u044F\\u0449\\u0438\\u0435 \\u0434\\u043B\\u044F \\u0440\\u0430\\u0437\\u043D\\u044B\\u0445 \\u0441\\u0442\\u0438\\u043B\\u0435\\u0439 \\u0436\\u0438\\u0437\\u043D\\u0438 \\u0438 \\u0443\\u0441\\u043B\\u043E\\u0432\\u0438\\u0439\"})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:styles.text,children:[/*#__PURE__*/_jsx(\"h2\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u041F\\u043E\\u0447\\u0435\\u0441\\u0442\\u0438 \\u0438 \\u043D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B\"}),/*#__PURE__*/_jsxs(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:[\"\\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u0434\\u0438\\u0437\\u0430\\u0439\\u043D\\u0443, \\u043F\\u0440\\u043E\\u0435\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044E, \\u0432\\u043E\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442\\u044F\\u043C \\u043F\\u043E\\u043B\\u043D\\u043E\\u0433\\u043E \\u043F\\u0440\\u0438\\u0432\\u043E\\u0434\\u0430 \\u0438 \\u043B\\u0443\\u0447\\u0448\\u0435\\u043C\\u0443 \\u0441\\u043E\\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u044E \\u0446\\u0435\\u043D\\u044B \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u044F GWM \\u043D\\u0430 \\u043F\\u0440\\u043E\\u0442\\u044F\\u0436\\u0435\\u043D\\u0438\\u0438 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0445 \\u043B\\u0435\\u0442 \\u043F\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u043D\\u043E \\u043F\\u043E\\u043B\\u0443\\u0447\\u0430\\u043B\\u0430 \\u043F\\u0440\\u0438\\u0437\\u043D\\u0430\\u043D\\u0438\\u0435 \\u0438 \\u043D\\u0430\\u0433\\u0440\\u0430\\u0434\\u044B \\u043F\\u043E \\u0432\\u0441\\u0435\\u043C\\u0443 \\u043C\\u0438\\u0440\\u0443. \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043C\\u0430\\u0448\\u0438\\u043D\\u043E\\u0441\\u0442\\u0440\\u043E\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 500 \\u043B\\u0443\\u0447\\u0448\\u0438\\u0445 \\u043F\\u0440\\u043E\\u043C\\u044B\\u0448\\u043B\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0439 \\u041A\\u0438\\u0442\\u0430\\u044F \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 \\u041B\\u0443\\u0447\\u0448\\u0430\\u044F \\u0438\\u0437 \\u043A\\u0440\\u0443\\u043F\\u043D\\u0435\\u0439\\u0448\\u0438\\u0445 \\u043B\\u0438\\u0441\\u0442\\u0438\\u043D\\u0433\\u0443\\u0435\\u043C\\u044B\\u0445 \\u043A\\u043E\\u043C\\u043F\\u0430\\u043D\\u0438\\u0439 \\u0410\\u0437\\u0438\\u0430\\u0442\\u0441\\u043A\\u043E-\\u0422\\u0438\\u0445\\u043E\\u043E\\u043A\\u0435\\u0430\\u043D\\u0441\\u043A\\u043E\\u0433\\u043E \\u0440\\u0435\\u0433\\u0438\\u043E\\u043D\\u0430 \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 Forbes Global 2000 \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 Fortune China 500s \\u2022 \\u0411\\u0440\\u0435\\u043D\\u0434 \\u0432\\u0445\\u043E\\u0434\\u0438\\u0442 \\u0432 \\u0441\\u043E\\u0442\\u043D\\u044E \\u0441\\u0430\\u043C\\u044B\\u0445 \\u0446\\u0435\\u043D\\u043D\\u044B\\u0445 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u0445 \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u0432 \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 \\u041F\\u0440\\u0438\\u0437\\u043D\\u0430\\u043D \\xAB\\u0420\\u0435\\u043A\\u043E\\u043C\\u0435\\u043D\\u0434\\u0443\\u0435\\u043C\\u044B\\u043C \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u043C \\u0431\\u0440\\u0435\\u043D\\u0434\\u043E\\u043C\\xBB \\u041A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u043E\\u0439 \\u0442\\u043E\\u0440\\u0433\\u043E\\u0432\\u043E\\u0439 \\u043F\\u0430\\u043B\\u0430\\u0442\\u043E\\u0439 \\u043F\\u043E \\u0438\\u043C\\u043F\\u043E\\u0440\\u0442\\u0443 \\u0438 \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0443 \\u043C\\u0430\\u0448\\u0438\\u043D\\u043E\\u0441\\u0442\\u0440\\u043E\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0438 \\u044D\\u043B\\u0435\\u043A\\u0442\\u0440\\u043E\\u043D\\u043D\\u043E\\u0439 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438\",' ',/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 \\u041D\\u0430\\u0437\\u0432\\u0430\\u043D \\xAB\\u041D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u044B\\u043C \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0435\\u0440\\u043E\\u043C \\u0442\\u0440\\u0430\\u043D\\u0441\\u043F\\u043E\\u0440\\u0442\\u043D\\u044B\\u0445 \\u0441\\u0440\\u0435\\u0434\\u0441\\u0442\\u0432\\xBB \\u041C\\u0438\\u043D\\u0438\\u0441\\u0442\\u0435\\u0440\\u0441\\u0442\\u0432\\u043E\\u043C \\u0442\\u043E\\u0440\\u0433\\u043E\\u0432\\u043B\\u0438 \\u0438 \\u041D\\u0430\\u0446\\u0438\\u043E\\u043D\\u0430\\u043B\\u044C\\u043D\\u043E\\u0439 \\u043A\\u043E\\u043C\\u0438\\u0441\\u0441\\u0438\\u0435\\u0439 \\u043F\\u043E \\u0440\\u0430\\u0437\\u0432\\u0438\\u0442\\u0438\\u044E \\u0438 \\u0440\\u0435\\u0444\\u043E\\u0440\\u043C\\u0430\\u043C \",/*#__PURE__*/_jsx(\"br\",{}),\" \\u2022 \\u041F\\u0435\\u0440\\u0432\\u043E\\u0435 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u043E\\u0435 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u044C\\u043D\\u043E\\u0435 \\u043F\\u0440\\u0435\\u0434\\u043F\\u0440\\u0438\\u044F\\u0442\\u0438\\u0435, \\u0441\\u0442\\u0430\\u0432\\u0448\\u0435\\u0435 \\u0447\\u043B\\u0435\\u043D\\u043E\\u043C \\u0421\\u043E\\u0432\\u0435\\u0442\\u0430 \\u043F\\u043E \\u0432\\u043E\\u0434\\u043E\\u0440\\u043E\\u0434\\u0443\"]})]}),/*#__PURE__*/_jsxs(\"section\",{className:styles.text,children:[/*#__PURE__*/_jsx(\"h2\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"150\",children:\"\\u0423\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"200\",children:\"\\u0412 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0438\\u0435 \\u0433\\u043E\\u0434\\u044B GWM \\u0443\\u0434\\u0435\\u043B\\u044F\\u0435\\u0442 \\u043F\\u0435\\u0440\\u0432\\u043E\\u0441\\u0442\\u0435\\u043F\\u0435\\u043D\\u043D\\u043E\\u0435 \\u0432\\u043D\\u0438\\u043C\\u0430\\u043D\\u0438\\u0435 \\u0443\\u0434\\u043E\\u0432\\u043B\\u0435\\u0442\\u0432\\u043E\\u0440\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432, \\u043F\\u043E\\u0432\\u044B\\u0448\\u0430\\u044F \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u0441\\u0432\\u043E\\u0438\\u0445 \\u0443\\u0441\\u043B\\u0443\\u0433 \\u0432 \\u0441\\u043E\\u043E\\u0442\\u0432\\u0435\\u0442\\u0441\\u0442\\u0432\\u0438\\u0438 \\u0441 \\u0438\\u043D\\u0434\\u0438\\u0432\\u0438\\u0434\\u0443\\u0430\\u043B\\u044C\\u043D\\u043E\\u0441\\u0442\\u044C\\u044E \\u0441\\u0432\\u043E\\u0435\\u0433\\u043E \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430 \\u0438 \\u0441\\u0442\\u0440\\u0435\\u043C\\u044F\\u0441\\u044C \\u043F\\u0440\\u0435\\u0432\\u0437\\u043E\\u0439\\u0442\\u0438 \\u043E\\u0436\\u0438\\u0434\\u0430\\u043D\\u0438\\u044F \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432. \\u042D\\u0442\\u0430 \\u043F\\u0440\\u0435\\u0434\\u0430\\u043D\\u043D\\u043E\\u0441\\u0442\\u044C \\u0434\\u0435\\u043B\\u0443 \\u0441\\u043F\\u043E\\u0441\\u043E\\u0431\\u0441\\u0442\\u0432\\u043E\\u0432\\u0430\\u043B\\u0430 \\u0443\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u043B\\u0435\\u043D\\u0438\\u044E \\u043F\\u0440\\u043E\\u0447\\u043D\\u044B\\u0445 \\u0441\\u0432\\u044F\\u0437\\u0435\\u0439 \\u0441 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u0430\\u043C\\u0438 \\u0438 \\u0443\\u0441\\u0438\\u043B\\u0438\\u043B\\u0430 \\u0435\\u0435 \\u0441\\u0442\\u0440\\u0435\\u043C\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043A \\u0441\\u043E\\u0432\\u0435\\u0440\\u0448\\u0435\\u043D\\u0441\\u0442\\u0432\\u0443.\"}),/*#__PURE__*/_jsx(\"p\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"250\",children:\"\\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u0439 \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0435\\u043D\\u043D\\u043E\\u0439 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0435 GWM \\u0441\\u0442\\u0430\\u043B\\u0430 \\u043B\\u0438\\u0434\\u0435\\u0440\\u043E\\u043C \\u043F\\u043E \\u043E\\u0431\\u044A\\u0435\\u043C\\u0443 \\u043F\\u0440\\u043E\\u0434\\u0430\\u0436 \\u0438 \\u0438\\u043C\\u0438\\u0434\\u0436\\u0443 \\u0431\\u0440\\u0435\\u043D\\u0434\\u0430. \\u0415\\u0435 \\u0441\\u0443\\u0431\\u0431\\u0440\\u0435\\u043D\\u0434\\u044B \\u043D\\u0435\\u0443\\u043A\\u043B\\u043E\\u043D\\u043D\\u043E \\u043F\\u0440\\u043E\\u0434\\u0432\\u0438\\u0433\\u0430\\u044E\\u0442\\u0441\\u044F \\u043A \\u0440\\u044B\\u043D\\u043A\\u0443 \\u0432\\u044B\\u0441\\u043E\\u043A\\u043E\\u0433\\u043E \\u043A\\u043B\\u0430\\u0441\\u0441\\u0430, \\u0434\\u0432\\u0438\\u0436\\u0438\\u043C\\u044B\\u0435 \\u043F\\u043E\\u0441\\u0442\\u043E\\u044F\\u043D\\u043D\\u044B\\u043C\\u0438 \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u0447\\u0435\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0438\\u043D\\u043D\\u043E\\u0432\\u0430\\u0446\\u0438\\u044F\\u043C\\u0438 \\u0438 \\u043F\\u043E\\u0432\\u044B\\u0448\\u0435\\u043D\\u0438\\u0435\\u043C \\u0446\\u0435\\u043D\\u043D\\u043E\\u0441\\u0442\\u0438 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0442\\u0430. \\u0411\\u043B\\u0430\\u0433\\u043E\\u0434\\u0430\\u0440\\u044F \\u043D\\u0435\\u0437\\u0430\\u0432\\u0438\\u0441\\u0438\\u043C\\u044B\\u043C \\u043E\\u0441\\u043D\\u043E\\u0432\\u043D\\u044B\\u043C \\u0442\\u0435\\u0445\\u043D\\u043E\\u043B\\u043E\\u0433\\u0438\\u044F\\u043C \\u0438 \\u0438\\u0441\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435\\u043B\\u044C\\u043D\\u043E\\u043C\\u0443 \\u0441\\u043E\\u043E\\u0442\\u043D\\u043E\\u0448\\u0435\\u043D\\u0438\\u044E \\u0446\\u0435\\u043D\\u044B \\u0438 \\u043A\\u0430\\u0447\\u0435\\u0441\\u0442\\u0432\\u0430 GWM \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0435\\u0442 \\u0448\\u0438\\u0440\\u043E\\u043A\\u0438\\u0439 \\u0430\\u0441\\u0441\\u043E\\u0440\\u0442\\u0438\\u043C\\u0435\\u043D\\u0442 \\u043F\\u0440\\u043E\\u0434\\u0443\\u043A\\u0446\\u0438\\u0438, \\u0440\\u0430\\u0437\\u0440\\u0430\\u0431\\u043E\\u0442\\u0430\\u043D\\u043D\\u043E\\u0439 \\u0441 \\u0443\\u0447\\u0435\\u0442\\u043E\\u043C \\u043C\\u0435\\u043D\\u044F\\u044E\\u0449\\u0438\\u0445\\u0441\\u044F \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u043D\\u043E\\u0441\\u0442\\u0435\\u0439 \\u0441\\u0432\\u043E\\u0438\\u0445 \\u043A\\u043B\\u0438\\u0435\\u043D\\u0442\\u043E\\u0432.\"})]})]})})});};export default About;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styles", "AOS", "img_1", "img_2", "img_3", "img_4", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "About", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "flexContent", "img", "src", "alt", "text"], "sources": ["/var/www/html/gwm.tj/src/pages/Discover/About/About.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styles from './about.module.css';\n// animation\nimport A<PERSON> from 'aos';\nimport 'aos/dist/aos.css';\n//\nimport img_1 from '../../../asset/imgs/about/1.webp';\nimport img_2 from '../../../asset/imgs/about/5-pc.webp';\nimport img_3 from '../../../asset/imgs/about/3.webp';\nimport img_4 from '../../../asset/imgs/about/6-1.webp';\n\nconst About = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden'; // Отключаем скролл при загрузке\n\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible'; // Возвращаем скролл после загрузки\n    }, 300); // 1 секунда для имитации загрузки\n\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible'; // На случай размонтирования компонента\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\" data-aos-delay=\"100\">\n                <strong>ВСЕ О GWM</strong>\n              </h1>\n              <i\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n                className=\"redLine\"\n              ></i>\n              <p className=\"underText\" data-aos=\"fade-up\" data-aos-delay=\"150\">\n                GWM — это глобальная интеллектуальная технологическая компания,\n                чей бизнес включает проектирование автомобилей и деталей, НИОКР,\n                производство, продажи и обслуживание. Состоит из таких брендов,\n                как HAVAL, ORA, STEED, TANK, P300 и P500.\n              </p>\n\n              <div data-aos=\"fade-up\" data-aos-delay=\"200\">\n                <p>\n                  + 173,2 млрд долларов США операционной прибыли в 2023 году\n                </p>\n                <p>+ 8 лет подряд продажа более 1 миллиона автомобилей</p>\n                <p>+ 14 миллионов пользователей по всему миру</p>\n                <p>+ 7000 дистрибьюторских и сервисных центров по всему миру</p>\n                <p>+ 90 000 сотрудников по всему миру</p>\n                <p>+ 170 стран и регионов-экспортеров</p>\n              </div>\n            </div>\n            <section>\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_1} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Почему GWM?</h2>\n                  <p>\n                    С новой энергией и интеллектуальными технологиями мы\n                    надеемся создать для вас ценный опыт.\n                  </p>\n                  <p>\n                    Имея долгосрочную цель сократить выбросы углерода до нуля,\n                    мы создали модель дизайна гибридной, электрической и\n                    водородной энергии, чтобы предложить более чистые и\n                    интеллектуальные автомобили пользователям по всему миру.\n                  </p>\n                </div>\n              </div>\n              {/* 2 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_2} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Творчество без границ</h2>\n                  <p>\n                    Мы предлагаем вам лучшие автомобили с помощью\n                    проектирования, исследований и разработок.\n                  </p>\n                  <p>\n                    Штаб-квартира GWM находится в Китае, а ее глобальный\n                    R&D-отдел охватывает Европу, Азию, Северную и Южную Америку.\n                    R&D-центры также были созданы в Японии, США, Германии,\n                    Индии, Австрии и Южной Корее.\n                  </p>\n                </div>\n              </div>\n              {/* 3 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_3} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Глобальный производственный дизайн</h2>\n\n                  <p>\n                    В Китае GWM имеет 10 баз по производству автомобилей полного\n                    цикла. На зарубежном рынке GWM открыла заводы по\n                    производству автомобилей полного цикла в Таиланде и\n                    Бразилии, а также несколько заводов KD в Эквадоре, Пакистане\n                    и других странах.\n                  </p>\n                </div>\n              </div>\n              {/* 3 */}\n              <div className={styles.flexContent}>\n                <div\n                  className={styles.img}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"200\"\n                >\n                  <img src={img_4} alt=\"\" />\n                </div>\n                <div\n                  className={styles.text}\n                  data-aos=\"fade-up\"\n                  data-aos-delay=\"250\"\n                >\n                  <h2>Глобальные амбиции, национальная миссия</h2>\n\n                  <p>\n                    Наша цель — продолжать расширять границы возможностей\n                    автомобиля. Автомобильный рынок — один из самых конкурентных\n                    в мире, и возможности для нового бренда, рожденного\n                    специализацией, оставить свой след никогда не были большими.\n                  </p>\n                  <p>\n                    По мере развития технологий люди ищут новые решения для\n                    лучшего будущего. Мы знаем, что любовь к автомобилям\n                    сопряжена с высокими ожиданиями качества и надежности. Вот\n                    почему GWM сотрудничает с лучшими инженерами и дизайнерами,\n                    чтобы предлагать элегантные, функциональные автомобили,\n                    идеально подходящие для разных стилей жизни и условий\n                  </p>\n                </div>\n              </div>\n            </section>\n            <section className={styles.text}>\n              <h2 data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Почести и награды\n              </h2>\n              <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                Благодаря дизайну, проектированию, возможностям полного привода\n                и лучшему соотношению цены и качества компания GWM на протяжении\n                последних лет постоянно получала признание и награды по всему\n                миру. <br /> • 500 лучших предприятий Китая <br /> • 500 лучших\n                машиностроительных предприятий Китая <br /> • 500 лучших\n                промышленных предприятий Китая <br /> • Лучшая из крупнейших\n                листингуемых компаний Азиатско-Тихоокеанского региона <br /> •\n                Forbes Global 2000 <br /> • Fortune China 500s • Бренд входит в\n                сотню самых ценных китайских брендов <br /> • Признан\n                «Рекомендуемым экспортным брендом» Китайской торговой палатой по\n                импорту и экспорту машиностроительной и электронной продукции{' '}\n                <br /> • Назван «Национальным экспортером транспортных средств»\n                Министерством торговли и Национальной комиссией по развитию и\n                реформам <br /> • Первое китайское автомобильное предприятие,\n                ставшее членом Совета по водороду\n              </p>\n            </section>\n            <section className={styles.text}>\n              <h2 data-aos=\"fade-up\" data-aos-delay=\"150\">\n                Удовлетворенность клиентов\n              </h2>\n              <p data-aos=\"fade-up\" data-aos-delay=\"200\">\n                В последние годы GWM уделяет первостепенное внимание\n                удовлетворенности клиентов, повышая качество своих услуг в\n                соответствии с индивидуальностью своего бренда и стремясь\n                превзойти ожидания клиентов. Эта преданность делу способствовала\n                установлению прочных связей с клиентами и усилила ее стремление\n                к совершенству.\n              </p>\n              <p data-aos=\"fade-up\" data-aos-delay=\"250\">\n                Благодаря последовательной высококачественной работе GWM стала\n                лидером по объему продаж и имиджу бренда. Ее суббренды неуклонно\n                продвигаются к рынку высокого класса, движимые постоянными\n                технологическими инновациями и повышением ценности продукта.\n                Благодаря независимым основным технологиям и исключительному\n                соотношению цены и качества GWM предлагает широкий ассортимент\n                продукции, разработанной с учетом меняющихся потребностей своих\n                клиентов.\n              </p>\n            </section>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default About;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,oBAAoB,CACvC;AACA,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB;AACA,MAAO,CAAAC,KAAK,KAAM,kCAAkC,CACpD,MAAO,CAAAC,KAAK,KAAM,qCAAqC,CACvD,MAAO,CAAAC,KAAK,KAAM,kCAAkC,CACpD,MAAO,CAAAC,KAAK,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdG,GAAG,CAACc,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CAExCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAAE;AAEzC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAAE;AAC5C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBlB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBlB,KAAA,QAAKmB,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBpB,IAAA,OAAIqB,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAD,QAAA,cAC3DpB,IAAA,WAAAoB,QAAA,CAAQ,+BAAS,CAAQ,CAAC,CACxB,CAAC,cACLpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAC,SAAS,CACjB,CAAC,cACLrB,IAAA,MAAGqB,SAAS,CAAC,WAAW,CAAC,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAD,QAAA,CAAC,qiCAKjE,CAAG,CAAC,cAEJlB,KAAA,QAAK,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAkB,QAAA,eAC1CpB,IAAA,MAAAoB,QAAA,CAAG,+PAEH,CAAG,CAAC,cACJpB,IAAA,MAAAoB,QAAA,CAAG,6PAAmD,CAAG,CAAC,cAC1DpB,IAAA,MAAAoB,QAAA,CAAG,iNAA0C,CAAG,CAAC,cACjDpB,IAAA,MAAAoB,QAAA,CAAG,uRAAyD,CAAG,CAAC,cAChEpB,IAAA,MAAAoB,QAAA,CAAG,kJAAkC,CAAG,CAAC,cACzCpB,IAAA,MAAAoB,QAAA,CAAG,iKAAkC,CAAG,CAAC,EACtC,CAAC,EACH,CAAC,cACNlB,KAAA,YAAAkB,QAAA,eACElB,KAAA,QAAKmB,SAAS,CAAE5B,MAAM,CAAC6B,WAAY,CAAAF,QAAA,eACjCpB,IAAA,QACEqB,SAAS,CAAE5B,MAAM,CAAC8B,GAAI,CACtB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAH,QAAA,cAEpBpB,IAAA,QAAKwB,GAAG,CAAE7B,KAAM,CAAC8B,GAAG,CAAC,EAAE,CAAE,CAAC,CACvB,CAAC,cACNvB,KAAA,QACEmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CACvB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAN,QAAA,eAEpBpB,IAAA,OAAAoB,QAAA,CAAI,2CAAW,CAAI,CAAC,cACpBpB,IAAA,MAAAoB,QAAA,CAAG,6dAGH,CAAG,CAAC,cACJpB,IAAA,MAAAoB,QAAA,CAAG,+oCAKH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENlB,KAAA,QAAKmB,SAAS,CAAE5B,MAAM,CAAC6B,WAAY,CAAAF,QAAA,eACjCpB,IAAA,QACEqB,SAAS,CAAE5B,MAAM,CAAC8B,GAAI,CACtB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAH,QAAA,cAEpBpB,IAAA,QAAKwB,GAAG,CAAE5B,KAAM,CAAC6B,GAAG,CAAC,EAAE,CAAE,CAAC,CACvB,CAAC,cACNvB,KAAA,QACEmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CACvB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAN,QAAA,eAEpBpB,IAAA,OAAAoB,QAAA,CAAI,sHAAqB,CAAI,CAAC,cAC9BpB,IAAA,MAAAoB,QAAA,CAAG,sdAGH,CAAG,CAAC,cACJpB,IAAA,MAAAoB,QAAA,CAAG,i7BAKH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENlB,KAAA,QAAKmB,SAAS,CAAE5B,MAAM,CAAC6B,WAAY,CAAAF,QAAA,eACjCpB,IAAA,QACEqB,SAAS,CAAE5B,MAAM,CAAC8B,GAAI,CACtB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAH,QAAA,cAEpBpB,IAAA,QAAKwB,GAAG,CAAE3B,KAAM,CAAC4B,GAAG,CAAC,EAAE,CAAE,CAAC,CACvB,CAAC,cACNvB,KAAA,QACEmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CACvB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAN,QAAA,eAEpBpB,IAAA,OAAAoB,QAAA,CAAI,oMAAkC,CAAI,CAAC,cAE3CpB,IAAA,MAAAoB,QAAA,CAAG,wqCAMH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAENlB,KAAA,QAAKmB,SAAS,CAAE5B,MAAM,CAAC6B,WAAY,CAAAF,QAAA,eACjCpB,IAAA,QACEqB,SAAS,CAAE5B,MAAM,CAAC8B,GAAI,CACtB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAH,QAAA,cAEpBpB,IAAA,QAAKwB,GAAG,CAAE1B,KAAM,CAAC2B,GAAG,CAAC,EAAE,CAAE,CAAC,CACvB,CAAC,cACNvB,KAAA,QACEmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CACvB,WAAS,SAAS,CAClB,iBAAe,KAAK,CAAAN,QAAA,eAEpBpB,IAAA,OAAAoB,QAAA,CAAI,wNAAuC,CAAI,CAAC,cAEhDpB,IAAA,MAAAoB,QAAA,CAAG,qqCAKH,CAAG,CAAC,cACJpB,IAAA,MAAAoB,QAAA,CAAG,0tDAOH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACC,CAAC,cACVlB,KAAA,YAASmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CAAAN,QAAA,eAC9BpB,IAAA,OAAI,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,8FAE5C,CAAI,CAAC,cACLlB,KAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAkB,QAAA,EAAC,ggCAInC,cAAApB,IAAA,QAAK,CAAC,sJAAgC,cAAAA,IAAA,QAAK,CAAC,mQACb,cAAAA,IAAA,QAAK,CAAC,+NACZ,cAAAA,IAAA,QAAK,CAAC,qaACiB,cAAAA,IAAA,QAAK,CAAC,8BACzC,cAAAA,IAAA,QAAK,CAAC,qTACY,cAAAA,IAAA,QAAK,CAAC,quBAEkB,CAAC,GAAG,cACjEA,IAAA,QAAK,CAAC,qrBAEG,cAAAA,IAAA,QAAK,CAAC,4aAEjB,EAAG,CAAC,EACG,CAAC,cACVE,KAAA,YAASmB,SAAS,CAAE5B,MAAM,CAACiC,IAAK,CAAAN,QAAA,eAC9BpB,IAAA,OAAI,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,yJAE5C,CAAI,CAAC,cACLpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,koDAO3C,CAAG,CAAC,cACJpB,IAAA,MAAG,WAAS,SAAS,CAAC,iBAAe,KAAK,CAAAoB,QAAA,CAAC,+zEAS3C,CAAG,CAAC,EACG,CAAC,EACP,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAf,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}