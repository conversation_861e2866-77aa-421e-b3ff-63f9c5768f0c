{"ast": null, "code": "import { _ as r } from \"./helpers-C8k3UfPS.js\";\nimport { useCallback as t } from \"react\";\nfunction e(e, n) {\n  return t(function (t) {\n    e.current = t, \"function\" == typeof n ? n(t) : \"object\" === r(n) && null !== n && (n.current = t);\n  }, [e, n]);\n}\nexport { e as default };", "map": {"version": 3, "names": ["_", "r", "useCallback", "t", "e", "n", "current", "default"], "sources": ["/var/www/html/gwm.tj/node_modules/@react-input/core/module/useConnectedRef.js"], "sourcesContent": ["import{_ as r}from\"./helpers-C8k3UfPS.js\";import{useCallback as t}from\"react\";function e(e,n){return t((function(t){e.current=t,\"function\"==typeof n?n(t):\"object\"===r(n)&&null!==n&&(n.current=t)}),[e,n])}export{e as default};\n"], "mappings": "AAAA,SAAOA,CAAC,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,OAAO;AAAC,SAASC,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAAE,UAASA,CAAC,EAAC;IAACC,CAAC,CAACE,OAAO,GAACH,CAAC,EAAC,UAAU,IAAE,OAAOE,CAAC,GAACA,CAAC,CAACF,CAAC,CAAC,GAAC,QAAQ,KAAGF,CAAC,CAACI,CAAC,CAAC,IAAE,IAAI,KAAGA,CAAC,KAAGA,CAAC,CAACC,OAAO,GAACH,CAAC,CAAC;EAAA,CAAC,EAAE,CAACC,CAAC,EAACC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}