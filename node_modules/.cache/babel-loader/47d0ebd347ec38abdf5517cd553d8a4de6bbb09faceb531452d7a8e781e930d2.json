{"ast": null, "code": "import { clamp } from '../clamp.mjs';\nfunction steps(numSteps, direction = \"end\") {\n  return progress => {\n    progress = direction === \"end\" ? Math.min(progress, 0.999) : Math.max(progress, 0.001);\n    const expanded = progress * numSteps;\n    const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n    return clamp(0, 1, rounded / numSteps);\n  };\n}\nexport { steps };", "map": {"version": 3, "names": ["clamp", "steps", "numSteps", "direction", "progress", "Math", "min", "max", "expanded", "rounded", "floor", "ceil"], "sources": ["/var/www/html/gwm.tj/node_modules/motion-utils/dist/es/easing/steps.mjs"], "sourcesContent": ["import { clamp } from '../clamp.mjs';\n\nfunction steps(numSteps, direction = \"end\") {\n    return (progress) => {\n        progress =\n            direction === \"end\"\n                ? Math.min(progress, 0.999)\n                : Math.max(progress, 0.001);\n        const expanded = progress * numSteps;\n        const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n        return clamp(0, 1, rounded / numSteps);\n    };\n}\n\nexport { steps };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,cAAc;AAEpC,SAASC,KAAKA,CAACC,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAE;EACxC,OAAQC,QAAQ,IAAK;IACjBA,QAAQ,GACJD,SAAS,KAAK,KAAK,GACbE,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAE,KAAK,CAAC,GACzBC,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAE,KAAK,CAAC;IACnC,MAAMI,QAAQ,GAAGJ,QAAQ,GAAGF,QAAQ;IACpC,MAAMO,OAAO,GAAGN,SAAS,KAAK,KAAK,GAAGE,IAAI,CAACK,KAAK,CAACF,QAAQ,CAAC,GAAGH,IAAI,CAACM,IAAI,CAACH,QAAQ,CAAC;IAChF,OAAOR,KAAK,CAAC,CAAC,EAAE,CAAC,EAAES,OAAO,GAAGP,QAAQ,CAAC;EAC1C,CAAC;AACL;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}