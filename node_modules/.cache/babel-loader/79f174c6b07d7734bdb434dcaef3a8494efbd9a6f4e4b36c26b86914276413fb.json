{"ast": null, "code": "import React,{useEffect,useState}from'react';import AOS from'aos';import'aos/dist/aos.css';import{FaMapMarkerAlt,FaClock,FaPhoneAlt}from'react-icons/fa';import Form from'../../components/Form/Form';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const BookTestDrive=()=>{const[loading,setLoading]=useState(true);useEffect(()=>{AOS.init({duration:500,once:false});window.scrollTo(0,0);document.body.style.overflow='hidden';const timer=setTimeout(()=>{setLoading(false);document.body.style.overflow='visible';},300);return()=>{clearTimeout(timer);document.body.style.overflow='visible';};},[]);return/*#__PURE__*/_jsx(_Fragment,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"loaderWrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loaderPage\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"topmenu\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"title\",\"data-aos\":\"fade-up\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"\\u0417\\u0430\\u043F\\u0438\\u0441\\u0430\\u0442\\u044C\\u0441\\u044F \\u043D\\u0430 \\u0422\\u0415\\u0421\\u0422-\\u0414\\u0420\\u0410\\u0419\\u0412\"})}),/*#__PURE__*/_jsx(\"i\",{\"data-aos\":\"fade-up\",\"data-aos-delay\":\"100\",className:\"redLine\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"\\u0413\\u043E\\u0442\\u043E\\u0432\\u044B \\u043B\\u0438 \\u0432\\u044B \\u0438\\u0441\\u043F\\u044B\\u0442\\u0430\\u0442\\u044C \\u043E\\u0441\\u0442\\u0440\\u044B\\u0435 \\u043E\\u0449\\u0443\\u0449\\u0435\\u043D\\u0438\\u044F \\u043E\\u0442 \\u043B\\u0438\\u043D\\u0435\\u0439\\u043A\\u0438 GWM? \",/*#__PURE__*/_jsx(\"br\",{}),\"\\u0417\\u0430\\u0431\\u0440\\u043E\\u043D\\u0438\\u0440\\u0443\\u0439\\u0442\\u0435 \\u0442\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432 \\u0443\\u0436\\u0435 \\u0441\\u0435\\u0433\\u043E\\u0434\\u043D\\u044F, \\u0441\\u044F\\u0434\\u044C\\u0442\\u0435 \\u0437\\u0430 \\u0440\\u0443\\u043B\\u044C \\u043E\\u0434\\u043D\\u043E\\u0433\\u043E \\u0438\\u043B\\u0438 \\u043D\\u0435\\u0441\\u043A\\u043E\\u043B\\u044C\\u043A\\u0438\\u0445 \\u043D\\u0430\\u0448\\u0438\\u0445 \\u0430\\u0432\\u0442\\u043E\\u043C\\u043E\\u0431\\u0438\\u043B\\u0435\\u0439 \\u0438 \\u043E\\u0442\\u043F\\u0440\\u0430\\u0432\\u043B\\u044F\\u0439\\u0442\\u0435\\u0441\\u044C \\u0432 \\u0441\\u043B\\u0435\\u0434\\u0443\\u044E\\u0449\\u0435\\u0435 \\u043F\\u0440\\u0438\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435 \\u0441 GWM.\"]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Form,{formType:\"test-drive\",title:\"\\u0417\\u0430\\u043F\\u043E\\u043B\\u043D\\u0438\\u0442\\u0435 \\u0444\\u043E\\u0440\\u043C\\u0443 \\u0434\\u043B\\u044F \\u0437\\u0430\\u043F\\u0438\\u0441\\u0438 \\u043D\\u0430 \\u0442\\u0435\\u0441\\u0442-\\u0434\\u0440\\u0430\\u0439\\u0432\"})]})})});};export default BookTestDrive;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "AOS", "FaMapMarkerAlt", "FaClock", "FaPhoneAlt", "Form", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "BookTestDrive", "loading", "setLoading", "init", "duration", "once", "window", "scrollTo", "document", "body", "style", "overflow", "timer", "setTimeout", "clearTimeout", "children", "className", "formType", "title"], "sources": ["/var/www/html/gwm.tj/src/pages/Book-a-test-drive/Book-a-test-dieve.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport AOS from 'aos';\nimport 'aos/dist/aos.css';\nimport { FaMapMarkerAlt, FaClock, FaPhoneAlt } from 'react-icons/fa';\nimport Form from '../../components/Form/Form';\n\nconst BookTestDrive = () => {\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    AOS.init({ duration: 500, once: false });\n    window.scrollTo(0, 0);\n    document.body.style.overflow = 'hidden';\n    const timer = setTimeout(() => {\n      setLoading(false);\n      document.body.style.overflow = 'visible';\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n      document.body.style.overflow = 'visible';\n    };\n  }, []);\n\n  return (\n    <>\n      {loading ? (\n        <div className=\"loaderWrapper\">\n          <div className=\"loaderPage\"></div>\n        </div>\n      ) : (\n        <div className=\"topmenu\">\n          <div className=\"container\">\n            <div className=\"content\">\n              <h1 className=\"title\" data-aos=\"fade-up\">\n                <strong>Записаться на ТЕСТ-ДРАЙВ</strong>\n              </h1>\n              <i\n                data-aos=\"fade-up\"\n                data-aos-delay=\"100\"\n                className=\"redLine\"\n              ></i>\n            </div>\n\n            <p>\n              Готовы ли вы испытать острые ощущения от линейки GWM? <br />\n              Забронируйте тест-драйв уже сегодня, сядьте за руль одного или\n              нескольких наших автомобилей и отправляйтесь в следующее\n              приключение с GWM.\n            </p>\n            <br />\n            <Form\n              formType=\"test-drive\"\n              title=\"Заполните форму для записи на тест-драйв\"\n            />\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default BookTestDrive;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,GAAG,KAAM,KAAK,CACrB,MAAO,kBAAkB,CACzB,OAASC,cAAc,CAAEC,OAAO,CAAEC,UAAU,KAAQ,gBAAgB,CACpE,MAAO,CAAAC,IAAI,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE9C,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdE,GAAG,CAACc,IAAI,CAAC,CAAEC,QAAQ,CAAE,GAAG,CAAEC,IAAI,CAAE,KAAM,CAAC,CAAC,CACxCC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACvC,KAAM,CAAAC,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7BX,UAAU,CAAC,KAAK,CAAC,CACjBM,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CAAE,GAAG,CAAC,CACP,MAAO,IAAM,CACXG,YAAY,CAACF,KAAK,CAAC,CACnBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,SAAS,CAC1C,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEhB,IAAA,CAAAI,SAAA,EAAAgB,QAAA,CACGd,OAAO,cACNN,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5BpB,IAAA,QAAKqB,SAAS,CAAC,YAAY,CAAM,CAAC,CAC/B,CAAC,cAENrB,IAAA,QAAKqB,SAAS,CAAC,SAAS,CAAAD,QAAA,cACtBlB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBlB,KAAA,QAAKmB,SAAS,CAAC,SAAS,CAAAD,QAAA,eACtBpB,IAAA,OAAIqB,SAAS,CAAC,OAAO,CAAC,WAAS,SAAS,CAAAD,QAAA,cACtCpB,IAAA,WAAAoB,QAAA,CAAQ,mIAAwB,CAAQ,CAAC,CACvC,CAAC,cACLpB,IAAA,MACE,WAAS,SAAS,CAClB,iBAAe,KAAK,CACpBqB,SAAS,CAAC,SAAS,CACjB,CAAC,EACF,CAAC,cAENnB,KAAA,MAAAkB,QAAA,EAAG,qQACqD,cAAApB,IAAA,QAAK,CAAC,usBAI9D,EAAG,CAAC,cACJA,IAAA,QAAK,CAAC,cACNA,IAAA,CAACF,IAAI,EACHwB,QAAQ,CAAC,YAAY,CACrBC,KAAK,CAAC,oNAA0C,CACjD,CAAC,EACC,CAAC,CACH,CACN,CACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAlB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}