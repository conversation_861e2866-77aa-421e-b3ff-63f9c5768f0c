{"ast": null, "code": "\"use client\";\n\nimport _objectSpread from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/var/www/html/gwm.tj/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"as\", \"axis\", \"onReorder\", \"values\"];\nimport { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\nfunction ReorderGroupComponent(_ref, externalRef) {\n  let {\n      children,\n      as = \"ul\",\n      axis = \"y\",\n      onReorder,\n      values\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const Component = useConstant(() => motion[as]);\n  const order = [];\n  const isReordering = useRef(false);\n  invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n  const context = {\n    axis,\n    registerItem: (value, layout) => {\n      // If the entry was already added, update it rather than adding it again\n      const idx = order.findIndex(entry => value === entry.value);\n      if (idx !== -1) {\n        order[idx].layout = layout[axis];\n      } else {\n        order.push({\n          value: value,\n          layout: layout[axis]\n        });\n      }\n      order.sort(compareMin);\n    },\n    updateOrder: (item, offset, velocity) => {\n      if (isReordering.current) return;\n      const newOrder = checkReorder(order, item, offset, velocity);\n      if (order !== newOrder) {\n        isReordering.current = true;\n        onReorder(newOrder.map(getValue).filter(value => values.indexOf(value) !== -1));\n      }\n    }\n  };\n  useEffect(() => {\n    isReordering.current = false;\n  });\n  return jsx(Component, _objectSpread(_objectSpread({}, props), {}, {\n    ref: externalRef,\n    ignoreStrict: true,\n    children: jsx(ReorderContext.Provider, {\n      value: context,\n      children: children\n    })\n  }));\n}\nconst ReorderGroup = /*@__PURE__*/forwardRef(ReorderGroupComponent);\nfunction getValue(item) {\n  return item.value;\n}\nfunction compareMin(a, b) {\n  return a.layout.min - b.layout.min;\n}\nexport { ReorderGroup, ReorderGroupComponent };", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "jsx", "invariant", "forwardRef", "useRef", "useEffect", "ReorderContext", "motion", "useConstant", "check<PERSON>eor<PERSON>", "ReorderGroupComponent", "_ref", "externalRef", "children", "as", "axis", "onReorder", "values", "props", "Component", "order", "isReordering", "Boolean", "context", "registerItem", "value", "layout", "idx", "findIndex", "entry", "push", "sort", "compareMin", "updateOrder", "item", "offset", "velocity", "current", "newOrder", "map", "getValue", "filter", "indexOf", "ref", "ignoreStrict", "Provider", "ReorderGroup", "a", "b", "min"], "sources": ["/var/www/html/gwm.tj/node_modules/framer-motion/dist/es/components/Reorder/Group.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { invariant } from 'motion-utils';\nimport { forwardRef, useRef, useEffect } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/components/motion/proxy.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { checkReorder } from './utils/check-reorder.mjs';\n\nfunction ReorderGroupComponent({ children, as = \"ul\", axis = \"y\", onReorder, values, ...props }, externalRef) {\n    const Component = useConstant(() => motion[as]);\n    const order = [];\n    const isReordering = useRef(false);\n    invariant(Boolean(values), \"Reorder.Group must be provided a values prop\");\n    const context = {\n        axis,\n        registerItem: (value, layout) => {\n            // If the entry was already added, update it rather than adding it again\n            const idx = order.findIndex((entry) => value === entry.value);\n            if (idx !== -1) {\n                order[idx].layout = layout[axis];\n            }\n            else {\n                order.push({ value: value, layout: layout[axis] });\n            }\n            order.sort(compareMin);\n        },\n        updateOrder: (item, offset, velocity) => {\n            if (isReordering.current)\n                return;\n            const newOrder = checkReorder(order, item, offset, velocity);\n            if (order !== newOrder) {\n                isReordering.current = true;\n                onReorder(newOrder\n                    .map(getValue)\n                    .filter((value) => values.indexOf(value) !== -1));\n            }\n        },\n    };\n    useEffect(() => {\n        isReordering.current = false;\n    });\n    return (jsx(Component, { ...props, ref: externalRef, ignoreStrict: true, children: jsx(ReorderContext.Provider, { value: context, children: children }) }));\n}\nconst ReorderGroup = /*@__PURE__*/ forwardRef(ReorderGroupComponent);\nfunction getValue(item) {\n    return item.value;\n}\nfunction compareMin(a, b) {\n    return a.layout.min - b.layout.min;\n}\n\nexport { ReorderGroup, ReorderGroupComponent };\n"], "mappings": "AAAA,YAAY;;AAAC,OAAAA,aAAA;AAAA,OAAAC,wBAAA;AAAA,MAAAC,SAAA;AACb,SAASC,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACrD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,0CAA0C;AACjE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,qBAAqBA,CAAAC,IAAA,EAAmEC,WAAW,EAAE;EAAA,IAA/E;MAAEC,QAAQ;MAAEC,EAAE,GAAG,IAAI;MAAEC,IAAI,GAAG,GAAG;MAAEC,SAAS;MAAEC;IAAiB,CAAC,GAAAN,IAAA;IAAPO,KAAK,GAAAnB,wBAAA,CAAAY,IAAA,EAAAX,SAAA;EACzF,MAAMmB,SAAS,GAAGX,WAAW,CAAC,MAAMD,MAAM,CAACO,EAAE,CAAC,CAAC;EAC/C,MAAMM,KAAK,GAAG,EAAE;EAChB,MAAMC,YAAY,GAAGjB,MAAM,CAAC,KAAK,CAAC;EAClCF,SAAS,CAACoB,OAAO,CAACL,MAAM,CAAC,EAAE,8CAA8C,CAAC;EAC1E,MAAMM,OAAO,GAAG;IACZR,IAAI;IACJS,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC7B;MACA,MAAMC,GAAG,GAAGP,KAAK,CAACQ,SAAS,CAAEC,KAAK,IAAKJ,KAAK,KAAKI,KAAK,CAACJ,KAAK,CAAC;MAC7D,IAAIE,GAAG,KAAK,CAAC,CAAC,EAAE;QACZP,KAAK,CAACO,GAAG,CAAC,CAACD,MAAM,GAAGA,MAAM,CAACX,IAAI,CAAC;MACpC,CAAC,MACI;QACDK,KAAK,CAACU,IAAI,CAAC;UAAEL,KAAK,EAAEA,KAAK;UAAEC,MAAM,EAAEA,MAAM,CAACX,IAAI;QAAE,CAAC,CAAC;MACtD;MACAK,KAAK,CAACW,IAAI,CAACC,UAAU,CAAC;IAC1B,CAAC;IACDC,WAAW,EAAEA,CAACC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACrC,IAAIf,YAAY,CAACgB,OAAO,EACpB;MACJ,MAAMC,QAAQ,GAAG7B,YAAY,CAACW,KAAK,EAAEc,IAAI,EAAEC,MAAM,EAAEC,QAAQ,CAAC;MAC5D,IAAIhB,KAAK,KAAKkB,QAAQ,EAAE;QACpBjB,YAAY,CAACgB,OAAO,GAAG,IAAI;QAC3BrB,SAAS,CAACsB,QAAQ,CACbC,GAAG,CAACC,QAAQ,CAAC,CACbC,MAAM,CAAEhB,KAAK,IAAKR,MAAM,CAACyB,OAAO,CAACjB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACDpB,SAAS,CAAC,MAAM;IACZgB,YAAY,CAACgB,OAAO,GAAG,KAAK;EAChC,CAAC,CAAC;EACF,OAAQpC,GAAG,CAACkB,SAAS,EAAArB,aAAA,CAAAA,aAAA,KAAOoB,KAAK;IAAEyB,GAAG,EAAE/B,WAAW;IAAEgC,YAAY,EAAE,IAAI;IAAE/B,QAAQ,EAAEZ,GAAG,CAACK,cAAc,CAACuC,QAAQ,EAAE;MAAEpB,KAAK,EAAEF,OAAO;MAAEV,QAAQ,EAAEA;IAAS,CAAC;EAAC,EAAE,CAAC;AAC9J;AACA,MAAMiC,YAAY,GAAG,aAAc3C,UAAU,CAACO,qBAAqB,CAAC;AACpE,SAAS8B,QAAQA,CAACN,IAAI,EAAE;EACpB,OAAOA,IAAI,CAACT,KAAK;AACrB;AACA,SAASO,UAAUA,CAACe,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,CAACrB,MAAM,CAACuB,GAAG,GAAGD,CAAC,CAACtB,MAAM,CAACuB,GAAG;AACtC;AAEA,SAASH,YAAY,EAAEpC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}