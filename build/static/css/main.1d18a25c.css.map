{"version": 3, "file": "static/css/main.1d18a25c.css", "mappings": "AAAA,MAEE,iBAAkB,CAClB,iBAAkB,CAIlB,uBAAwB,CACxB,6BAA8B,CAC9B,4BAA6B,CAC7B,6BAA8B,CAE9B,0BAA2B,CAC3B,wBAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,2BAA4B,CAC5B,wBAA4B,CAE5B,uBAAwB,CACxB,6BAA8B,CAC9B,qBAAsB,CACtB,2BAA4B,CAC5B,uBAAwB,CACxB,6BAA8B,CAC9B,oBAAqB,CACrB,0BAA2B,CAG3B,wJAAkK,CAClK,sFAA4F,CAE5F,sBAAuB,CAEvB,uBAAwB,CAExB,qBAAsB,CAEtB,uBAAwB,CAExB,sBAAuB,CAEvB,sBAAuB,CAEvB,wBAAyB,CAEzB,uBAAwB,CAExB,oBAAqB,CAGrB,uBAAwB,CACxB,wBAAyB,CACzB,wBAAyB,CACzB,0BAA2B,CAC3B,sBAAuB,CACvB,2BAA4B,CAC5B,uBAAwB,CAExB,wBAAyB,CACzB,wBAAyB,CACzB,wBAAyB,CACzB,2BAA4B,CAC5B,qBAAsB,CAGtB,WAAY,CACZ,iBAAkB,CAElB,gBAAiB,CAEjB,iBAAkB,CAElB,cAAe,CAEf,iBAAkB,CAElB,gBAAiB,CAEjB,cAAe,CAEf,iBAAkB,CAElB,eAAgB,CAEhB,eAAgB,CAEhB,eAAgB,CAEhB,eAAgB,CAEhB,eAAgB,CAIhB,kBAAmB,CACnB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CAErB,sBAAuB,CACvB,2BAA4B,CAE5B,0BAA2B,CAE3B,yBAA0B,CAE1B,0BAA2B,CAE3B,wBAAyB,CAEzB,2BAA4B,CAG5B,iCAA4C,CAC5C,6DAAkF,CAClF,+DAAoF,CACpF,iEAAsF,CACtF,wCAAmD,CACnD,0CAAqD,CAGrD,mCAAoC,CACpC,mCAAoC,CACpC,mCAAoC,CAGpC,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,uBAAwB,CACxB,cAAe,CACf,gBAAiB,CACjB,gBAAiB,CAGjB,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,sBAAuB,CACvB,uBAAwB,CAGxB,oBAAqB,CACrB,oBAAqB,CACrB,oBAAqB,CACrB,qBAAsB,CACtB,sBACF,CCpJA,WACE,iBAAkB,CAClB,mBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,wFACF,CAEA,WACE,iBAAkB,CAClB,mBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,qFACF,CAEA,WACE,iBAAkB,CAClB,mBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,0FACF,CAEA,WACE,iBAAkB,CAClB,mBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,sFACF,CAIA,WAIE,iBAAkB,CAHlB,0BAA2B,CAC3B,iBAAkB,CAClB,mBAAoB,CAEpB,oFACF,CAEA,WAIE,iBAAkB,CAHlB,0BAA2B,CAC3B,iBAAkB,CAClB,mBAAoB,CAEpB,sFACF,CAEA,WAIE,iBAAkB,CAHlB,0BAA2B,CAC3B,iBAAkB,CAClB,mBAAoB,CAEpB,oFACF,CAEA,WAIE,iBAAkB,CAHlB,0BAA2B,CAC3B,iBAAkB,CAClB,mBAAoB,CAEpB,oFACF,CAEA,WAIE,iBAAkB,CAHlB,0BAA2B,CAC3B,iBAAkB,CAClB,mBAAoB,CAEpB,sFACF,CAEA,WAGE,sBAAuB,CACvB,uBAAwB,CAHxB,mCAAoC,CAIpC,uBAAwB,CACxB,mBAAmB,CAJnB,kBAKF,CAEA,iBAIE,qBAAsB,CACtB,QAAS,CACT,SACF,CAEA,KAOE,kCAAmC,CACnC,iCAAkC,CAFlC,yCAA0C,CAD1C,8BAA+B,CAH/B,mCAAoC,CACpC,+BAAgC,CAChC,qCAAsC,CAHtC,QAAS,CAQT,iCACF,CAEA,KACE,uEAEF,CAEA,GACE,eACF,CAEA,WAEE,WAAY,CADZ,SAEF,CAEA,EAEE,wBAAyB,CADzB,oBAEF,CAEA,iBAEE,SACF,CAEA,QAEE,0BAA2B,CAE3B,kBAAmB,CAHnB,cAIF,CAEA,GAEE,eACF,CAEA,MAJE,wBAQF,CAJA,GAEE,cAAe,CACf,kBACF,CAEA,EACE,cAAe,CACf,eACF,CAEA,YACE,mCAAoC,CACpC,YACF,CAEA,8DAEE,UAAY,CACZ,cACF,CAGA,4BAME,kBAAmB,CAUnB,qBAAsB,CAFtB,WAAY,CAXZ,iBAAkB,CAKlB,kBAAuB,CAOvB,UAAW,CANX,cAAe,CAGf,6BAA+B,CAC/B,eAAgB,CAXhB,wBAeF,CAEA,cACE,qBAAsB,CACtB,UACF,CAEA,sCAQE,oBAAqB,CAGrB,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAHjB,iBAAkB,CAClB,iBAAkB,CANlB,uBAAwB,CAGxB,uBAOF,CAEA,kDAGE,kEAAqD,CACrD,uBAAwB,CACxB,2BAA4B,CAC5B,uBAAwB,CAJxB,UAAW,CAKX,WAAY,CASZ,SAAU,CAPV,iBAAkB,CAClB,OAAQ,CACR,OAAQ,CAGR,yCAA0C,CAK1C,2BAA6B,CAX7B,UAAW,CAOX,SAKF,CAEA,oBACE,kBACF,CAEA,oBACE,iCACF,CAEA,kDAIE,0BACF,CAEA,8DAEE,SACF,CAGA,MAME,kBAAmB,CAEnB,UAAW,CAEX,cAAe,CAPf,YAAa,CAIb,OAAQ,CAER,oBAEF,CAEA,UAKE,6BAA+B,CAJ/B,UAMF,CAGA,sBAGE,yBACF,CAEA,SACE,eACF,CAGA,eAME,kBAAmB,CAKnB,eAAgB,CARhB,YAAa,CAOb,YAAa,CADb,sBAAuB,CAIvB,YACF,CAEA,YAOE,kCAAoC,CAHpC,mCAA0B,CAC1B,iBAAkB,CADlB,yBAA0B,CAF1B,WAAY,CADZ,UAOF,CASA,gBACE,GAEE,uBACF,CACF,CAGA,KACE,iBACF,CAEA,SACE,gBACF,CAQA,WAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAEjB,kBACF,CAEA,OACE,cAAe,CAEf,eACF,CAEA,gBAJE,kBAUF,CANA,SAGE,mCAAoC,CACpC,aAAc,CAFd,UAAW,CADX,WAKF,CAEA,qCACE,EACE,cACF,CACF,CAEA,qCACE,OACE,cAAe,CACf,kBACF,CAEA,WACE,cAAe,CACf,kBAAmB,CACnB,UACF,CACF,CAEA,0BACE,WACE,gBACF,CACF,CAEA,oCACE,WACE,SACF,CAEA,SACE,cACF,CACF,CAEA,yBACE,EACE,cACF,CAMA,OACE,cAAe,CACf,kBACF,CAEA,WACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,SACE,eACF,CACF,CCpaA,MACE,kBAAmB,CACnB,kBAAmB,CACnB,0BAAsC,CACtC,2BAAqC,CACrC,yBAAkC,CAClC,uBAAwB,CACxB,qBACF,CAEA,gBACE,wBAA6B,CAO7B,UAAyB,CAAzB,wBAAyB,CAFzB,MAAO,CAGP,cAAiB,CAPjB,cAAe,CAKf,OAAQ,CAJR,KAAM,CAaN,mDAEiB,CAdjB,UAAW,CACX,WAcF,CAEA,kCACE,qBAAoC,CAApC,mCAAoC,CACpC,+BAAgD,CAAhD,+CAAgD,CAChD,UAAyB,CAAzB,wBACF,CAEA,sBAEE,WAAY,CADZ,SAEF,CAEA,kBASE,kBAAmB,CANnB,YAAa,CAGb,6BAIF,CAEA,iBAME,kBAAmB,CAHnB,YAAa,CAIb,QACF,CAEA,sBAIE,YAAa,CAFb,gBAAiB,CACjB,UAEF,CAEA,wBAGE,aAAc,CADd,gBAAiB,CAIjB,kBAAmB,CAFnB,WAGF,CAEA,0DAEE,gBACF,CAEA,wDAEE,gBACF,CAEA,qBAME,kBAAmB,CAHnB,YAAa,CAIb,QAAS,CACT,eAAgB,CAChB,QAAS,CACT,SACF,CAEA,qBAUE,kBAAmB,CANnB,aAAc,CASd,cAAe,CANf,YAAa,CALb,cAAe,CACf,eAAgB,CAQhB,OAAQ,CACR,oBAAqB,CAXrB,wBAaF,CAEA,2BACE,aAAyB,CAAzB,wBAAyB,CAGzB,yBACF,CAEA,kBACE,cAAe,CACf,eAAgB,CAIhB,6BAIF,CAEA,oBAGE,yBACF,CAEA,kBAGE,YAAa,CACb,QACF,CAEA,wBAaE,qBAAoC,CAApC,mCAAoC,CACpC,iBAAkB,CALlB,aAAc,CAMd,cAAe,CALf,cAAe,CACf,eAAgB,CAChB,eAIF,CAEA,kDAXE,kBAAmB,CAHnB,YAAa,CAIb,OAAQ,CAPR,iBA2BF,CAVA,0BAQE,UAEF,CAEA,0DACE,qBAAoC,CAApC,mCACF,CAEA,kDACE,SAAU,CAIV,8BAAgC,CAHhC,kBAIF,CAEA,oBAOE,qBAAoC,CAApC,mCAAoC,CAKpC,iBAAkB,CASlB,8BAAyC,CAbzC,UAAyB,CAAzB,wBAAyB,CAEzB,cAAe,CACf,eAAgB,CARhB,QAAS,CAWT,SAAU,CALV,gBAAiB,CARjB,iBAAkB,CAClB,QAAS,CAIT,yBAA6B,CAY7B,uBAAyB,CAHzB,iBAAkB,CAFlB,kBAAmB,CAMnB,WAGF,CAEA,0BAQE,gBAAoE,CAApE,6BAAoE,CAApE,iDAAoE,CALpE,WAAY,CAFZ,UAAW,CAGX,QAAS,CACT,gBAAiB,CAHjB,iBAOF,CAEA,wBAIE,gBAAiB,CAHjB,WAAY,CAMZ,oBAAsB,CALtB,UAMF,CAEA,0DAEE,gBACF,CAMA,4CACE,YACF,CAEA,oCACE,qBAEE,cAAe,CADf,eAEF,CAEA,sBACE,cACF,CAEA,wBACE,YACF,CACF,CAEA,oCACE,gBACE,cACF,CAEA,iBAME,kBAAmB,CAHnB,YAAa,CAIb,QACF,CAEA,sBACE,UACF,CAEA,oBACE,YACF,CAEA,wBACE,cACF,CAEA,qBACE,YACF,CAEA,oBAUE,eAA8B,CAA9B,6BAA8B,CAE9B,cAAe,CAXf,QAAU,CACV,MAAS,CAST,eAAgB,CARhB,cAAe,CACf,KAAQ,CAGR,gDAAyD,CACzD,UAAW,CACX,WAIF,CAEA,oCACE,WACF,CAEA,yBACE,YAAa,CACb,MAAS,CACT,iBAAkB,CAClB,KAAQ,CACR,UAAW,CACX,UACF,CAEA,qBAIE,eAA8B,CAA9B,6BAA8B,CAC9B,+BAAiD,CAAjD,gDAAiD,CACjD,cAAiB,CALjB,iBAAkB,CAClB,UAAW,CACX,UAIF,CAEA,4BAME,kBAAmB,CAHnB,YAAa,CAIb,QACF,CAEA,wBAIE,wBAOF,CAEA,qDAVE,kBAAmB,CAMnB,iBAAkB,CAJlB,cAAe,CAGf,mBAAoB,CAEpB,WAcF,CAXA,6BAIE,qBAAoC,CAApC,mCAOF,CAEA,2BAEE,UAAyB,CAAzB,wBAAyB,CADzB,gBAEF,CAEA,2BAGE,YAAa,CAIb,qBAAsB,CAGtB,0BACF,CAEA,8BACE,UAAyB,CAAzB,wBAAyB,CACzB,kBAAmB,CAEnB,gBAAiB,CADjB,UAEF,CAEA,mDAGE,kBAAmB,CASnB,UAAyB,CAAzB,wBAAyB,CANzB,YAAa,CAIb,cAAe,CACf,eAAgB,CAFhB,6BAIF,CAEA,0BAGE,YAAa,CAEb,cAAe,CAGf,0BAA2B,CAC3B,eACF,CAEA,kDAIE,wBAAwC,CAAxC,uCAAwC,CACxC,iBAAkB,CAJlB,UAAyB,CAAzB,wBAAyB,CAMzB,cAAe,CACf,gBAAiB,CANjB,kBAAmB,CAInB,YAAa,CAHb,UAMF,CAEA,kBAGE,YAAa,CACb,QACF,CACF,CCvbA,MACE,0BAA2B,CAC3B,uBAAwB,CACxB,uBAAwB,CACxB,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,qBAAsB,CACtB,qBAAsB,CACtB,kBAAmB,CACnB,6BACF,CAGA,0BAME,qBAAyC,CAAzC,wCAAyC,CAFzC,4BAA6C,CAA7C,4CAA6C,CAH7C,QAAS,CACT,MAAS,CAOT,eAAgB,CANhB,cAAe,CAEf,QAAS,CAOT,0BAA4B,CAL5B,UAAW,CACX,WAKF,CAEA,gDACE,wBACF,CAGA,0BAGE,YAAa,CACb,WACF,CAGA,0BACE,8BAA+C,CAA/C,8CAA+C,CAG/C,YAAa,CAIb,qBAAsB,CAEtB,aAAc,CACd,eAAgB,CAChB,YAAa,CACb,WACF,CAEA,2DAEE,aAAc,CACd,gBAAiB,CACjB,iBACF,CAEA,yBACE,iCAAkD,CAAlD,iDAAkD,CAKlD,YAAa,CAIb,qBAAsB,CARtB,iBAAkB,CAClB,kBAQF,CAEA,2DAEE,sBAA6B,CAC7B,iBAAkB,CAClB,cAAe,CAIf,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAJjB,iBAAkB,CAClB,WAAY,CAKZ,iBAAkB,CAJlB,UAKF,CAEA,uEAEE,qBAAmC,CAAnC,kCACF,CAEA,2GAEE,qBAAyC,CAAzC,wCAAyC,CACzC,UAAwB,CAAxB,uBAAwB,CACxB,iBACF,CAEA,yHAEE,kBAA8B,CAA9B,6BAA8B,CAC9B,iBAAkB,CAClB,UAAW,CACX,UAAW,CACX,QAAS,CACT,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,SACF,CAEA,2BACE,eACF,CAEA,iCAIE,cAAe,CADf,YAAa,CAKb,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAJjB,iBAAkB,CAClB,eAAgB,CAChB,UAIF,CAEA,mCACE,UAAwB,CAAxB,uBACF,CAGA,uBAGE,WAAY,CAEZ,eAAgB,CADhB,YAEF,CAEA,uBAGE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,uBAKE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAH5D,eAKF,CAEA,uBASE,eAAmC,CAAnC,kCAAmC,CARnC,qBAAoC,CAApC,mCAAoC,CAEpC,kBAAmB,CADnB,YAAa,CAQb,iBAAkB,CAHlB,8BAIF,CAEA,6BAEE,+BAA+C,CAA/C,8CACF,CAEA,2BAEE,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,2BACE,eACF,CACA,8BACE,UAAW,CACX,cACF,CACA,wBAME,UACF,CAEA,0EAPE,UAAwB,CAAxB,uBAAwB,CADxB,cAAe,CAEf,eAAgB,CAChB,iBAAkB,CAClB,QAYF,CARA,kDAOE,SACF,CAEA,gEASE,wBAAoC,CAApC,mCAAoC,CAJpC,iBAAkB,CAHlB,UAAW,CACX,UAAW,CAIX,UAAW,CACX,iBAAkB,CAFlB,OAAQ,CAFR,SAMF,CAEA,gCACE,wBAAoC,CAApC,mCACF,CAEA,uBAGE,UAAwB,CAAxB,uBAAwB,CADxB,cAAe,CADf,eAGF,CAEA,4BAKE,UAAwB,CAAxB,uBAAwB,CAHxB,cAAe,CAEf,eAAgB,CAHhB,YAAa,CAEb,iBAIF,CAEA,6BACE,YACF,CAGA,oCACE,0BACE,YACF,CAEA,0BAIE,+BAAgD,CAAhD,+CAAgD,CADhD,eAAgB,CADhB,QAAS,CAGT,QAAS,CAJT,QAAS,CAOT,0BACF,CAEA,gDACE,yBACF,CAEA,uBACE,eACF,CAEA,6BAEE,sCAAuC,CACvC,iBAAkB,CAFlB,aAAc,CAId,kBAAmB,CADnB,aAEF,CAEA,iCASE,kBAAmB,CACnB,UAAwB,CAAxB,uBAAwB,CAPxB,YAAa,CAGb,6BAA8B,CAK9B,cACF,CAEA,sCACE,cAAe,CACf,eAAgB,CAChB,gBACF,CAEA,uDAEE,cAAe,CACf,gBACF,CAEA,2CAQE,QAAS,CAJT,SAAU,CAKV,eAAgB,CANhB,0BAA2B,CAI3B,uBAGF,CAEA,iEAKE,WAAY,CADZ,SAAU,CADV,uBAGF,CACF,CC1TA,WACE,wBAA2B,CAG3B,iBAAkB,CADlB,eAAgB,CADhB,4rEAGF,CAEA,MACE,4BAKF,CACA,MAEE,aAAc,CACd,gBAAiB,CACjB,iBAAkB,CAHlB,iBAAkB,CAIlB,SACF,CACA,QASE,aAAc,CAJd,eAAgB,CAJhB,gBAAiB,CACjB,iBAAkB,CAElB,eAAgB,CAEhB,SAAU,CAHV,iBAAkB,CAKlB,SAEF,CACA,iCACE,qBACF,CACA,gBAQE,kBAAuB,CAHvB,YAAa,CAFb,WAAY,CAFZ,iBAAkB,CAKlB,6BAA8B,CAC9B,+BAAqF,CAArF,mFAAqF,CALrF,UAAW,CAEX,SAKF,CACA,wEAGE,uBACF,CACA,mBACE,kBACF,CACA,iBACE,kBACF,CACA,cAME,aAAc,CALd,aAAc,CAEd,WAAY,CACZ,iBAAkB,CAClB,6BAA8B,CAH9B,UAKF,CACA,8BACE,iBACF,CAEA,oDAEE,WACF,CACA,mCACE,sBAAuB,CACvB,oCACF,CACA,sCAEE,kCAAmC,CAC3B,0BAA2B,CAFnC,uBAGF,CAEA,2CACE,kBACF,CACA,2BACE,2BACF,CACA,WACE,kBACF,CACA,wDAEE,2BACF,CAEA,iCAIE,uBAAwB,CAHxB,aAAc,CACd,oBAIF,CACA,oDACE,YACF,CACA,+CACE,6BACF,CACA,mDACE,4BACF,CACA,iDACE,4BACF,CACA,kDACE,qBACF,CACA,gEACE,sBACF,CACA,wDACE,UAAW,CACX,aAAc,CACd,UACF,CACA,+DACE,+BAAgC,CAChC,uBACF,CACA,6FACE,yDAAyD,CAAzD,wDACF,CACA,0EACE,WAAY,CACZ,cAAe,CACf,yCACF,CACA,2FACE,0DAAwD,CAAxD,uDACF,CACA,wEAGE,0CAA2C,CAD3C,aAAc,CADd,UAGF,CAGA,sLAcE,WAAY,CAHZ,MAAO,CAIP,mBAAoB,CALpB,iBAAkB,CAElB,KAAM,CACN,UAAW,CAGX,UACF,CACA,gCACE,oBACF,CACA,qCACE,wDACF,CACA,sCACE,uDACF,CACA,oCACE,sDACF,CACA,uCACE,wDACF,CACA,uBAWE,wBAA0E,CAE1E,wEAA6B,CAD7B,iBAAkB,CAClB,0BAA6B,CAH7B,qBAAsB,CARtB,WAAY,CAEZ,QAAS,CAET,iBAAkB,CAClB,gBAAiB,CAJjB,iBAAkB,CAElB,OAAQ,CAIR,oBAAqB,CARrB,UAAW,CAOX,UAMF,CACA,+HAEE,kDACF,CACA,6BACE,6BACF,CACA,6BACE,6BACF,CACA,iCACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CClOA,mBAcE,kBAAmB,CAOnB,6BAAoC,CAKpC,qBAAsB,CAvBtB,aAAc,CACd,cAAe,CAOf,YAAa,CALb,aAAc,CALd,cAAe,CAOf,eAAgB,CAehB,WAAY,CANZ,sBAAuB,CAfvB,eAAgB,CAsBhB,eAAgB,CAjBhB,cAAe,CAYf,iBAAkB,CADlB,iBAAkB,CAIlB,wBAAyB,CAQzB,uBAAyB,CAHzB,kBAAmB,CAPnB,SAWF,CAEA,sBAEE,4BAA6B,CAD7B,UAEF,CAEA,yBAME,wBAAyB,CAJzB,QAAS,CAGT,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAAW,CAGX,SACF,CACA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,cACF,CACF,CAEA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,aACF,CACF,CC9DA,kCAeE,6DAA0C,CAd1C,qBAAsB,CACtB,kBAAmB,CAGnB,6BAAwC,CAGxC,YAAa,CAIb,qBAAsB,CACtB,QAAS,CAVT,YAaF,CAEA,2BAGE,kBAAmB,CACnB,iBAAkB,CAFlB,YAAa,CADb,UAIF,CAEA,2BAGE,kBAAmB,CACnB,iBAAkB,CAFlB,WAAY,CADZ,SAIF,CAEA,2BAGE,kBAAmB,CACnB,iBAAkB,CAFlB,WAAY,CADZ,SAIF,CAcA,qCACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CC9DA,MACE,2BAAsC,CACtC,uBAAyB,CACzB,6BAA8B,CAC9B,2BACF,CAEA,4BAME,oBAAkC,CAAlC,iCAAkC,CAelC,YAAa,CAhBb,yBAA0B,CAmB1B,sBAAuB,CArBvB,MAAO,CAKP,SAAU,CAiBV,SAAU,CAPV,mBAAoB,CAjBpB,cAAe,CACf,QAAS,CAST,yBAA0B,CAI1B,8CAAkD,CAXlD,UAAW,CAGX,WAmBF,CAEA,yBAIE,SAAU,CACV,mBAAoB,CAFpB,uBAGF,CAEA,gCAWE,qBAAyC,CAAzC,wCAAyC,CAEzC,4BAAgD,CAAhD,+CAAgD,CAVhD,YAAa,CAKb,0BAA2B,CAE3B,kBAAmB,CAJnB,sBAAuB,CAQvB,eAAgB,CAFhB,cAAiB,CALjB,UAQF,CAEA,4BAGE,cAAe,CACf,SAAU,CAFV,UAGF,CAEA,2BACE,kBAAmB,CACnB,iBACF,CAEA,kCACE,wBAA4C,CAA5C,2CAA4C,CAC5C,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,KAAM,CACN,SAAU,CACV,SACF,CAEA,8BAKE,aAA+B,CAA/B,8BAA+B,CAJ/B,cAAe,CAEf,eAAgB,CADhB,kBAAmB,CAInB,iBAAkB,CAFlB,wBAGF,CAEA,8BAOE,YAAa,CACb,QAAS,CAPT,eAAgB,CAEhB,QAAS,CACT,kBAKF,CAEA,8BAIE,cAAe,CAFf,cAAe,CACf,eAAgB,CAFhB,kBAAmB,CAMnB,oBACF,CAEA,oCACE,4BAWE,qBAAyC,CAAzC,wCAAyC,CAPzC,eAAgB,CADhB,QAAS,CAIT,QAAS,CAFT,MAAO,CAJP,cAAe,CACf,QAAS,CAQT,0BAA4B,CAJ5B,UAAW,CAMX,WACF,CAEA,gCACE,gBACF,CAEA,oDACE,yBACF,CAEA,8BACE,cACF,CAEA,8BAKE,qBAAsB,CACtB,QAAS,CALT,cAMF,CAEA,kCACE,YACF,CAEA,2BACE,kBAAmB,CACnB,iBACF,CAEA,4BACE,+BAAmD,CAAnD,kDACF,CACF,CCjKA,MACE,qBAAsB,CACtB,uBACF,CAEA,8BAME,kBAAmB,CAInB,qBAAuC,CAAvC,sCAAuC,CAPvC,YAAa,CAYb,QAAS,CANT,0BAA2B,CAI3B,MAAO,CAGP,eAAgB,CAChB,aAAc,CAKd,mBAAoB,CAXpB,cAAe,CACf,QAAS,CAQT,0BAA4B,CAC5B,iBAAkB,CAPlB,UAAW,CASX,WACF,CAEA,qDACE,WAAY,CAEZ,kBAAmB,CADnB,kBAEF,CAEA,mCAEE,eAGF,CAEA,2DANE,UAA8B,CAA9B,6BAA8B,CAE9B,cAAe,CACf,eAOF,CAGA,yBACE,8BACE,YACF,CACF,CCvDA,MACE,eAAgB,CAChB,iBAAkB,CAClB,qBAAsB,CACtB,6CAA+C,CAC/C,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,mBAAoB,CACpB,sBACF,CAEA,OACE,qBAAiC,CAAjC,gCAAiC,CACjC,UAAwB,CAAxB,uBACF,CACA,2BAQE,kBAAmB,CANnB,UAAY,CAGZ,YAAa,CAJb,cAAe,CAYf,WAAY,CAFZ,sBAAuB,CACvB,UAEF,CAEA,wBAOE,kBAAmB,CAHnB,mBAAoB,CAMpB,sBAAuB,CATvB,iBAUF,CAEA,uBAEE,mBAAiC,CAAjC,gCAAiC,CADjC,gBAA8B,CAA9B,6BAEF,CACA,wBAIE,cAAe,CAFf,WAAY,CACZ,kBAAmB,CAFnB,WAIF,CAGA,wBAKE,cAAe,CAQf,qBAAsB,CADtB,iBAEF,CACA,+CAPE,kBAAmB,CALnB,YAAa,CAQb,0BA0BF,CAtBA,uBAUE,wBAAoC,CAApC,mCAAoC,CACpC,iBAAkB,CAOlB,aAAc,CANd,aAAc,CAEd,oBAAqB,CADrB,kBAAmB,CAEnB,yBAA0B,CAM1B,oCACF,CACA,6BACE,wBACF,CACA,oBAME,kBAAmB,CAHnB,YAAa,CAMb,WAAY,CACZ,kBAAmB,CAHnB,iBAAkB,CAClB,UAGF,CACA,wBAKE,aAAc,CAHd,WAAY,CACZ,kBAAoB,CAFpB,UAKF,CACA,yBAGE,UAAwB,CAAxB,uBAAwB,CAFxB,iBAAmB,CACnB,eAEF,CACA,0BAEE,kEAAyD,CAEzD,uBAA2B,CAD3B,2BAA4B,CAE5B,uBAAwB,CAJxB,UAAW,CASX,WAAY,CAJZ,iBAAkB,CAClB,OAAQ,CACR,OAAQ,CAKR,wCAAyC,CAIzC,6BAA+B,CAR/B,UAAW,CAUX,SACF,CAEA,uDAGE,0CACF,CAEA,oBAGE,YAAa,CAEb,cAAe,CAGf,6BAA8B,CAC9B,UACF,CACA,6BAWE,sBAAuB,CARvB,YAAa,CAKb,WAAY,CAHZ,cAOF,CACA,wBAUE,sBAAuB,CAPvB,YAAa,CAIb,qBAAsB,CAQtB,aAAc,CAFd,sBAAuB,CAIvB,oBAAqB,CACrB,iBAAkB,CAFlB,sBAGF,CACA,6BACE,UAAwB,CAAxB,uBAAwB,CAExB,cAAe,CACf,eAAgB,CAFhB,sBAAuB,CAKvB,oBACF,CACA,yBAQE,WAAa,CACb,eACF,CACA,2BACE,UAAwB,CAAxB,uBAAwB,CACxB,iBAAmB,CACnB,eAAgB,CAChB,mBAAoB,CAGpB,oBACF,CACA,kEAEE,aAA8B,CAA9B,6BAA8B,CAC9B,YACF,CAGA,uBAQE,kBAAmB,CAOnB,+BAA4C,CAA5C,2CAA4C,CAD5C,4BAAyC,CAAzC,wCAAyC,CAXzC,YAAa,CAEb,cAAe,CAMf,6BAA8B,CAE9B,aAAc,CADd,mBAIF,CACA,sBAME,kBAAmB,CAHnB,YAAa,CAIb,WAAY,CAEZ,oBAAqB,CADrB,gBAEF,CACA,wBAGE,YACF,CACA,4BAEE,WAAY,CADZ,UAEF,CACA,2CAOE,kBAAmB,CAHnB,YAAa,CAKb,oBAAqB,CADrB,gBAEF,CACA,yBACE,UAAwB,CAAxB,uBAAwB,CACxB,iBAAmB,CACnB,eAAgB,CAGhB,yBACF,CACA,oBAEE,UAAwB,CAAxB,uBAAwB,CADxB,gBAEF,CACA,+BACE,wBACF,CACA,sBAEE,oBAAqB,CADrB,gBAEF,CACA,0BAEE,WAAY,CADZ,WAEF,CAGA,oBACE,cACF,CACA,yBAGE,UAAwB,CAAxB,uBAAwB,CAFxB,gBAAkB,CAGlB,eAAgB,CAFhB,oBAAqB,CAGrB,kBACF,CAEA,yBAGE,YAAa,CAIb,qBAAsB,CACtB,OACF,CAEA,4BAEE,YAAa,CAIb,SAAU,CALV,eAAgB,CAIhB,+CAEF,CAEA,oBACE,SACF,CAGA,yBACE,uBAEE,qBAAsB,CADtB,kBAEF,CAEA,6BAIE,qBACF,CAEA,wBAGE,eAAmB,CADnB,cAAe,CADf,UAGF,CAEA,6BAUE,kBAAmB,CACnB,cAAe,CAPf,YAAa,CASb,kBAAmB,CANnB,6BAA8B,CAO9B,eAAkB,CAFlB,gBAAkB,CAXlB,UAcF,CAEA,yBAEE,iBAAmB,CADnB,UAEF,CAEA,2BACE,cAAe,CACf,cACF,CAEA,wBAIE,qBAAsB,CACtB,aACF,CAEA,uBAEE,aAAc,CADd,UAEF,CAEA,uBAOE,sBAAuB,CAHvB,qBAAsB,CAItB,UACF,CAEA,2CAEE,cAAe,CACf,iBACF,CACF,CC9ZA,oCAGE,kBAAmB,CAGnB,wCAAyC,CAJzC,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAIjB,sBAEF,CAEA,qCAIE,yCAA0C,CAI1C,2DAA4D,CAH5D,qCAAsC,CAEtC,2BAA4B,CAN5B,eAAgB,CAKhB,uBAAwB,CAHxB,iBAAkB,CADlB,UAOF,CAEA,gCACE,wBAAyB,CAEzB,YAAa,CACb,sBAAuB,CAFvB,4BAGF,CAEA,iCAGE,8BAA+B,CAF/B,8BAA+B,CAC/B,mCAAoC,CAGpC,oCAAqC,CADrC,2BAEF,CAEA,oCAEE,8BAA+B,CAD/B,6BAA8B,CAG9B,qCAAsC,CADtC,2BAEF,CAEA,8BAKE,yCAA0C,CAE1C,2DAA4D,CAD5D,qCAAsC,CAJtC,8BAA+B,CAD/B,6BAA8B,CAE9B,2BAA4B,CAC5B,sBAIF,CAEA,mCAIE,wBAA6B,CAD7B,8BAA+B,CAF/B,mCAAoC,CACpC,6BAA8B,CAG9B,SACF,CAEA,oCACE,YAAa,CAGb,cAAe,CAFf,kBAAmB,CACnB,sBAAuB,CAEvB,4BACF,CAEA,6BAWE,kBAAmB,CAHnB,wCAA+C,CAN/C,qCAAsC,CAItC,cAAe,CAIf,mBAAoB,CALpB,mCAAoC,CAFpC,+BAAgC,CAChC,qCAAsC,CAQtC,sBAAuB,CAHvB,eAAgB,CARhB,qCAAsC,CAMtC,iCAMF,CAEA,mCACE,sCAAuC,CACvC,kBACF,CAEA,oCACE,qCAAsC,CAEtC,iCAAkC,CADlC,8BAEF,CAEA,0CACE,2CAA4C,CAC5C,uCAAwC,CAExC,2BAA4B,CAD5B,0BAEF,CAEA,2CAEE,2BAA4B,CAD5B,uBAEF,CAEA,sCACE,yCAA0C,CAE1C,qCAAsC,CADtC,8BAEF,CAEA,4CACE,yCAA0C,CAC1C,qCAAsC,CAEtC,2BAA4B,CAD5B,0BAEF,CAEA,6CAEE,2BAA4B,CAD5B,uBAEF,CAEA,mCAKE,wCAAyC,CAFzC,2DAA4D,CAC5D,qCAAsC,CAHtC,yBAA0B,CAC1B,eAIF,CAEA,0CAIE,yCAA0C,CAC1C,iEAAkE,CAFlE,cAAe,CADf,qCAAsC,CADtC,sBAAuB,CAKvB,iCACF,CAEA,gDACE,yCACF,CAEA,0CACE,sBACF,CAEA,6CAGE,8BAA+B,CAF/B,+BAAgC,CAChC,uCAAwC,CAExC,wCACF,CAEA,yDACE,YACF,CAEA,iCACE,yCAA0C,CAG1C,qCAAsC,CAFtC,8BAA+B,CAG/B,mCAAoC,CACpC,6BAA8B,CAC9B,qCAAsC,CAItC,wCAAyC,CAHzC,eAAgB,CALhB,sBAAuB,CAMvB,oBAAqB,CACrB,qBAEF,CAGA,yBACE,oCACE,sBACF,CAEA,qCACE,sBACF,CAEA,iCACE,8BACF,CAEA,oCACE,+BACF,CAEA,oCAEE,kBAAmB,CADnB,qBAEF,CAEA,6BAEE,eAAgB,CADhB,UAEF,CAEA,iCACE,6BACF,CACF,CAGA,mCACE,oCACE,yCACF,CAEA,qCACE,yCAA0C,CAC1C,qCACF,CAEA,iCACE,8BACF,CAEA,oCACE,8BACF,CAEA,8BACE,yCAA0C,CAC1C,qCAAsC,CACtC,8BACF,CAEA,mCACE,8BACF,CAEA,sCACE,yCAA0C,CAE1C,qCAAsC,CADtC,8BAEF,CAEA,4CACE,yCAA0C,CAC1C,qCACF,CAEA,mCACE,yCAA0C,CAC1C,qCACF,CAEA,0CACE,yCACF,CAEA,gDACE,yCACF,CAEA,6CACE,8BACF,CACF,CCvQA,0BAEE,WAAY,CADZ,iBAAkB,CAElB,UACF,CAEA,qBAGE,uBAA2B,CAD3B,qBAAsB,CADtB,WAAY,CAGZ,iBACF,CAEA,4BAKE,wBAAyB,CAJzB,UAAY,CAOZ,YAAa,CAEb,qBAAsB,CACtB,WAAY,CAEZ,6BAA8B,CAX9B,oBAYF,CAEA,+BACE,cAAe,CACf,eAAiB,CACjB,kBAAmB,CACnB,SACF,CAEA,6BAEE,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAFjB,kBAAmB,CAInB,SACF,CAEA,uBAGE,eAAiB,CAGjB,WAAY,CAEZ,iBAAkB,CAJlB,UAAY,CAGZ,cAAe,CAFf,eAAgB,CAJhB,eAAgB,CAChB,iBAOF,CAEA,iCAYE,kBAAmB,CAVnB,WAAY,CAOZ,YAAa,CAIb,OAAQ,CAVR,QAAS,CAFT,iBAAkB,CAKlB,0BAA2B,CAQ3B,UACF,CAEA,mBAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,iBAAkB,CAJlB,SAKF,CAEA,uBAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,eAAgB,CAGhB,uBAAyB,CAPzB,WAQF,CAEA,uBAKE,+CAAkC,CAHlC,mCAAoC,CADpC,WAAY,CAEZ,UAGF,CAYA,iCACE,GACE,OACF,CAEA,GACE,UACF,CACF,CAEA,wBAaE,kBAAmB,CATnB,eAAiB,CACjB,WAAY,CACZ,iBAAkB,CAClB,UAAY,CAUZ,cAAe,CAPf,YAAa,CAPb,WAAY,CAaZ,sBAAuB,CAfvB,gBAAiB,CACjB,UAgBF,CAEA,kDAEE,oBAAoC,CACpC,kBAAmB,CACnB,UAAW,CACX,cAAe,CACf,aAAc,CAOd,cAAe,CADf,eAAgB,CALhB,kBAAmB,CACnB,WAAY,CACZ,yBAA0B,CAE1B,iBAGF,CAEA,0BACE,oBACF,CAIA,6BACE,wBACF,CAEA,yHAOE,uDAA0C,CAH1C,kBAAmB,CACnB,iBAAkB,CAClB,kBAEF,CAEA,2BAEE,WAAY,CADZ,WAEF,CAEA,6BAEE,WAAY,CADZ,SAEF,CAEA,mCAEE,WAAY,CADZ,SAEF,CAEA,8BAEE,WAAY,CACZ,eAAgB,CAFhB,WAGF,CAEA,+BACE,GACE,UACF,CAEA,IACE,SACF,CAEA,GACE,UACF,CACF,CAEA,oCAKE,+CACE,YACF,CAEA,+BAEE,aAAc,CACd,iBAAkB,CAFlB,UAGF,CAEA,6BACE,SACF,CACF,CAEA,oCACE,6BACE,UACF,CAEA,kDAGE,cAAe,CADf,WAEF,CACF,CChPA,0BACE,cACF,CAEA,wBAUE,kBAAmB,CANnB,YAAa,CAGb,6BAA8B,CAN9B,kBAUF,CACA,2BACE,eACF,CAEA,0BASE,mBAAoB,CANpB,YAAa,CAQb,cAAe,CALf,sBAAuB,CAMvB,kBACF,CAEA,uBAeE,kBAAmB,CACnB,kBAAmB,CACjB,kBAAmB,CAdrB,YAAa,CAIb,kBAAmB,CAGnB,6BAA8B,CAQ5B,YAAa,CANf,UAOF,CAEA,sBAME,YAAa,CAHb,UAAS,CAOT,qBAAsB,CAGtB,6BACF,CAEA,yBACE,cAAe,CACf,eAAiB,CACjB,kBACF,CAEA,wBAIE,UAAW,CAHX,cAAe,CACf,eAAgB,CAChB,kBAAmB,CAEnB,eACF,CAEA,sBAYE,kBAAmB,CANnB,YAAa,CAHb,UAAS,CAMT,sBAAuB,CAKvB,gBAAiB,CADjB,eAEF,CAEA,0BAME,aAAc,CAHd,WAAY,CADZ,cAAe,CAGf,kBAAmB,CAJnB,UAMF,CAEA,8BACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CACf,cAAe,CAIf,eAAgB,CAHhB,eAAgB,CAChB,SAAU,CACV,yBAEF,CAEA,oCACE,oBACF,CAEA,2BAIE,0DAA0C,CAH1C,wBAAyB,CACzB,iBAGF,CAEA,gCAEE,WAAY,CACZ,kBAAmB,CAFnB,SAGF,CAEA,+BAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,iCAIE,kBAAmB,CAFnB,WAAY,CACZ,eAAgB,CAFhB,WAIF,CAEA,8BAGE,kBAAmB,CADnB,WAAY,CADZ,UAGF,CAcA,kCACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CAGA,oCACE,0BACE,aACF,CAEA,uBAQE,sBAAuB,CAJvB,qBAAsB,CAMtB,gBAAiB,CADjB,iBAAkB,CAJlB,gBAMF,CAEA,sBACE,WACF,CAEA,yBACE,cACF,CAEA,wBACE,cAAe,CACf,eACF,CAEA,sBAEE,kBAAmB,CADnB,gBAEF,CAEA,0BAEE,WAAY,CADZ,UAEF,CACF,CCjOA,mBAcE,kBAAmB,CAOnB,6BAAoC,CASpC,qBAAsB,CA3BtB,aAAc,CACd,cAAe,CAOf,YAAa,CAwBb,aAAc,CAlCd,cAAe,CAOf,eAAgB,CAehB,WAAY,CANZ,sBAAuB,CAfvB,eAAgB,CAuBhB,eAAgB,CADhB,eAAgB,CAEhB,cAAe,CAPf,iBAAkB,CADlB,iBAAkB,CAelB,sBAAuB,CAXvB,wBAAyB,CAgBzB,uBAAyB,CAPzB,kBAAmB,CAXnB,SAmBF,CAEA,sBAEE,4BAA6B,CAD7B,UAEF,CAEA,0BAME,wBAAyB,CAJzB,QAAS,CAGT,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAAW,CAGX,SACF,CACA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,cACF,CACF,CAEA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,aACF,CACF,CCvEA,0BACE,gBACF,CAEA,uBACE,sEAAkE,CAClE,uBAA2B,CAC3B,qBAAsB,CAEtB,kBAAmB,CADnB,YAEF,CAEA,0BAIE,wBAAyB,CACzB,qBAAsB,CAGtB,sBAAuB,CAGvB,YAAa,CAEb,qBAAsB,CACtB,WAAY,CACZ,6BAA8B,CAd9B,gBAAiB,CAejB,iBAAkB,CAClB,SACF,CAEA,6BACE,UAAW,CACX,cAAe,CACf,eACF,CAEA,oCACE,0BACE,aACF,CAEA,uBACE,yEAAqE,CACrE,YACF,CAEA,6BACE,cACF,CACF,CCnDA,wBACE,kBAAmB,CACnB,UACF,CACA,4BACE,cAAe,CAEf,eAAgB,CADhB,gBAEF,CAGA,yBAME,cAAe,CAQf,qBAAsB,CADtB,iBAAkB,CAZlB,eAcF,CACA,kDAPE,kBAAmB,CALnB,YAAa,CAQb,0BA0BF,CAtBA,yBAUE,qBAAsB,CACtB,iBAAkB,CAOlB,aAAc,CANd,aAAc,CAEd,oBAAqB,CADrB,kBAAmB,CAEnB,yBAA0B,CAM1B,oCACF,CAEA,qBAME,kBAAmB,CAHnB,YAAa,CAMb,WAAY,CACZ,kBAAmB,CAHnB,iBAAkB,CAClB,UAGF,CACA,yBAKE,aAAc,CAHd,WAAY,CACZ,kBAAoB,CAFpB,UAKF,CACA,0BAGE,UAAW,CAFX,iBAAmB,CACnB,eAEF,CACA,2BAEE,kEAAyD,CAEzD,uBAA2B,CAD3B,2BAA4B,CAE5B,uBAAwB,CAJxB,UAAW,CASX,WAAY,CAJZ,iBAAkB,CAClB,OAAQ,CACR,OAAQ,CAKR,wCAAyC,CAIzC,6BAA+B,CAR/B,UAAW,CAUX,SACF,CAEA,0DAGE,0CACF,CCnGA,0BAGE,WAAY,CACZ,eAAgB,CAHhB,iBAAkB,CAClB,UAGF,CAEA,gCACE,SACF,CAEA,+BAIE,WAAY,CADZ,MAAO,CAFP,cAAe,CACf,KAAM,CAGN,UAAW,CACX,SACF,CAEA,yBAGE,WAAY,CACZ,eAAgB,CAHhB,uBAAgB,CAAhB,eAAgB,CAChB,KAGF,CAEA,4BAEE,WAAY,CACZ,aAAc,CACd,iBAAkB,CAClB,yBAA2B,CAJ3B,SAKF,CAEA,kCACE,UACF,CAEA,+BAUE,kBAAmB,CAHnB,YAAa,CAJb,WAAY,CAUZ,sBAAuB,CATvB,eAAgB,CAHhB,iBAAkB,CAelB,iCAAmC,CAdnC,UAeF,CAEA,qCACE,eACF,CAEA,wBAKE,kBAAmB,CAHnB,WAAY,CAEZ,gBAAiB,CAHjB,UAKF,CAEA,8BACE,eACF,CAEA,uBAEE,UAAW,CACX,UAAY,CACZ,gBAAiB,CACjB,eAAgB,CAJhB,iBAAkB,CAMlB,iBAAkB,CADlB,SAEF,CAGA,0BACE,uBACE,cACF,CACF,CAGA,yBACE,uBAEE,UAAW,CADX,gBAEF,CAEA,qCACE,eACF,CACF,CAGA,yBACE,uBACE,cACF,CACF,CC9GA,2BACE,wBAAyB,CAEzB,eAAgB,CADhB,aAEF,CAEA,yBAGE,YAAa,CAEb,cAAe,CACf,OAAQ,CAGR,sBACF,CAEA,wBACE,qBAAsB,CAStB,iBAAkB,CAIlB,qBAAsB,CAVtB,YAAa,CAIb,kBAAmB,CAGnB,UAAW,CACX,qBAAsB,CAHtB,qBAAsB,CAMtB,SACF,CAGA,wBAGE,YAAa,CAIb,qBAAsB,CACtB,SACF,CAEA,2BACE,iBAAkB,CAGlB,eAAgB,CAFhB,kBAAmB,CACnB,iBAEF,CAEA,0BACE,eAAiB,CAEjB,eAAgB,CADhB,kBAEF,CAEA,0BAOE,kBAAmB,CAKnB,UAAW,CARX,mBAAoB,CAMpB,eAAiB,CADjB,eAAgB,CADhB,OAAQ,CAPR,eAAgB,CAUhB,oBAEF,CAEA,4BAEE,WAAY,CADZ,UAEF,CAGA,yBAUE,oBAAqB,CAHrB,YAAa,CAJb,QAAO,CAUP,wBAAyB,CATzB,iBAAkB,CAUlB,SACF,CAEA,6BAQE,qBAAsB,CANtB,WAAY,CADZ,cAAe,CAGf,kBAAmB,CACnB,iBAAkB,CAClB,OAGF,CAGA,gDACE,wBAIE,qBAAsB,CAEtB,WAAY,CACZ,aAAc,CAFd,UAGF,CAEA,wBACE,UACF,CAEA,yBAOE,kBAAmB,CAHnB,wBAAyB,CAIzB,cAAe,CAPf,UAQF,CAEA,6BAEE,YAAa,CAEb,kBAAmB,CAHnB,UAIF,CAEA,2BACE,gBAAiB,CACjB,mBACF,CAEA,0BACE,gBAAkB,CAClB,kBACF,CAEA,0BACE,gBAAkB,CAClB,eACF,CACF,CAGA,yBACE,wBAIE,qBAAsB,CAEtB,WAAY,CACZ,aAAc,CAFd,UAGF,CAEA,wBACE,UACF,CAEA,yBAOE,kBAAmB,CAHnB,wBAAyB,CAHzB,UAOF,CAEA,6BAEE,YAAa,CAEb,kBAAmB,CAHnB,UAIF,CAEA,2BACE,gBAAiB,CACjB,eAAgB,CAChB,mBACF,CAEA,0BACE,gBAAkB,CAClB,kBACF,CAEA,0BACE,eAAiB,CACjB,eACF,CACF,CChNA,qFAAqF,wBAAwB,CAAC,+EAA+E,kBAAkB,CAAC,uGAAuG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,uFAAuF,uBAAuB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,oBAAoB,CAAC,uFAAuF,wBAAwB,CAAC,iFAAiF,kBAAkB,CAAC,yGAAyG,qBAAqB,CAAC,yFAAyF,sBAAsB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,mBAAmB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,sBAAsB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,mBAAmB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,wBAAwB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,qBAAqB,CAAC,yFAAyF,yBAAyB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,sBAAsB,CAAC,yFAAyF,sBAAsB,CAAC,mFAAmF,kBAAkB,CAAC,2GAA2G,mBAAmB,CAAC,qFAAqF,wDAAwD,CAAC,iFAAiF,+BAA+B,CAAC,uFAAuF,kCAAkC,CAAC,yFAAyF,mCAAmC,CAAC,+FAA+F,sCAAsC,CAAC,iGAAiG,0DAA0D,CAAC,mGAAmG,4DAA4D,CAAC,yGAAyG,2DAA2D,CAAC,iGAAiG,wDAAwD,CAAC,mGAAmG,wDAAwD,CAAC,yGAAyG,yDAAyD,CAAC,iGAAiG,yDAAyD,CAAC,mGAAmG,wDAAwD,CAAC,yGAAyG,2DAA2D,CAAC,mGAAmG,yDAAyD,CAAC,qGAAqG,wDAAwD,CAAC,2GAA2G,2DAA2D,CAAC,mGAAmG,yDAAyD,CAAC,qGAAqG,wDAAwD,CAAC,2GAA2G,2DAA2D,CAAC,iCAAiC,SAAS,CAAC,qCAAqC,CAAC,6CAA6C,SAAS,CAAC,uBAAuB,CAAC,mBAAmB,gCAAgC,CAAC,qBAAqB,iCAAiC,CAAC,sBAAsB,iCAAiC,CAAC,qBAAqB,gCAAgC,CAAC,yBAAyB,qCAAqC,CAAC,wBAAwB,oCAAoC,CAAC,2BAA2B,sCAAsC,CAAC,0BAA0B,qCAAqC,CAAC,iCAAiC,SAAS,CAAC,qCAAqC,CAAC,6CAA6C,SAAS,CAAC,gCAAgC,CAAC,mBAAmB,mBAAmB,CAAC,sBAAsB,0CAA0C,CAAC,wBAAwB,2CAA2C,CAAC,yBAAyB,2CAA2C,CAAC,wBAAwB,0CAA0C,CAAC,oBAAoB,oBAAoB,CAAC,uBAAuB,2CAA2C,CAAC,yBAAyB,4CAA4C,CAAC,0BAA0B,4CAA4C,CAAC,yBAAyB,2CAA2C,CAAC,mCAAmC,6BAA6B,CAAC,+CAA+C,uBAAuB,CAAC,oBAAoB,+BAA+B,CAAC,sBAAsB,gCAAgC,CAAC,uBAAuB,gCAAgC,CAAC,sBAAsB,+BAA+B,CAAC,iCAAiC,kCAA0B,CAA1B,0BAA0B,CAAC,6BAA6B,CAAC,qBAAqB,8CAA8C,CAAC,iCAAiC,wCAAwC,CAAC,sBAAsB,6CAA6C,CAAC,kCAAkC,wCAAwC,CAAC,mBAAmB,8CAA8C,CAAC,+BAA+B,wCAAwC,CAAC,qBAAqB,6CAA6C,CAAC,iCAAiC,wCAAwC,CCAp8yB,kBAEE,6BAA8B,CAD9B,0BAEF,CAEA,iBACE,YAAa,CACb,kBAAmB,CACnB,4BAA6B,CAC7B,UACF,CAEA,wBAEE,YAAa,CADb,QAAO,CAEP,qBACF,CAEA,mBAGE,8BAA+B,CAE/B,aAAc,CAJd,6BAA8B,CAC9B,qCAAsC,CAEtC,4BAEF,CAEA,sBACE,wBAAyB,CACzB,mCACF,CAEA,mBAIE,yCAA0C,CAD1C,2DAA4D,CAE5D,qCAAsC,CAItC,qBAAsB,CARtB,QAAO,CAMP,mCAAoC,CADpC,+BAAgC,CAIhC,eAAgB,CARhB,qCAAsC,CAStC,iCAAkC,CAHlC,UAIF,CAEA,yBAEE,iCAAkC,CAClC,8BAA2C,CAF3C,YAGF,CAEA,4BACE,yCAA0C,CAC1C,8BAA+B,CAC/B,kBACF,CAMA,sDAHE,+BAMF,CAHA,8BAEE,8BACF,CAEA,sBAEE,mCAAoC,CADpC,eAEF,CAEA,2BACE,QAAO,CACP,iBACF,CAEA,0BAKE,kBAAmB,CAJnB,wBAAyB,CAGzB,YAAa,CAFb,6BAA8B,CAI9B,kBAAmB,CAHnB,yBAIF,CAEA,iCACE,WAAY,CACZ,6BACF,CAEA,uBACE,8BAA+B,CAC/B,6BAA8B,CAC9B,yBACF,CAEA,2BACE,uBACF,CAEA,mCACE,+BAAiC,CACjC,2BAA6B,CAE7B,cAAe,CADf,yBAEF,CAEA,uCACE,UACF,CAEA,wCACE,UACF,CAEA,6BAME,sBAAuB,CAHvB,YAAa,CAKb,cAAe,CADf,QAAS,CAET,kBAAmB,CACnB,eACF,CAEA,sBAIE,iBAAmB,CAFnB,WAAY,CACZ,cAAe,CAFf,UAIF,CAEA,oBACE,qBAAuB,CAGvB,WAAY,CACZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAiB,CAHjB,iBAAkB,CAOlB,oCACF,CAEA,0BACE,qBACF,CAEA,yBACE,kBACE,eACF,CACA,iBAIE,qBAAsB,CACtB,QACF,CAEA,mBAEE,WAAY,CACZ,0BAA2B,CAF3B,UAGF,CAEA,6BAOE,sBAAuB,CAHvB,qBAIF,CAEA,wBACE,4BACF,CAEA,0BACE,6BACF,CACF,CAGA,6BAGE,qCAAsC,CAFtC,cAAe,CACf,sBAAuB,CAEvB,iCACF,CAEA,mCACE,wCACF,CAEA,4BACE,sCAAuC,CACvC,kBACF,CAEA,4BAGE,8BAA+B,CAF/B,6BAA8B,CAC9B,qCAEF,CAEA,oBAUE,kBAAmB,CATnB,+CAAiD,CACjD,wCAA0C,CAO1C,YAAa,CAHb,6CAA+C,CAF/C,yCAA2C,CAC3C,+CAAiD,CAOjD,kBAAmB,CADnB,sBAAuB,CAHvB,eAAgB,CALhB,+CAAiD,CAIjD,UAMF,CAEA,yCACE,qDAAuD,CAEvD,2BAA4B,CAD5B,0BAEF,CAEA,6BACE,mDAAqD,CAGrD,eAAgB,CAFhB,kBAAmB,CACnB,cAEF,CAEA,qBAME,6CAAkC,CAFlC,sBAAkC,CAClC,iBAAkB,CADlB,6BAAkC,CAFlC,WAAY,CADZ,UAMF,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CC5PA,kCAaE,uDAAqC,CALrC,iBAAkB,CAQlB,qBAAsB,CALtB,UAAY,CAFZ,eAAgB,CANhB,QAAS,CAWT,cAAe,CAPf,iBAAkB,CANlB,cAAe,CAgBf,iBAAkB,CAflB,QAAS,CAIT,0BAA2B,CAY3B,qBAAsB,CARtB,YASF,CAGA,+BACE,wBACF,CAEA,2BACE,wBACF,CACA,0BAEE,cAAe,CADf,gBAAiB,CAEjB,qBACF,CAeA,yCACE,GACE,SAAU,CAEV,+BACF,CACA,GACE,SAAU,CAEV,yBACF,CACF,CAGA,yBACE,kCAGE,cAAe,CADf,iBAAkB,CADlB,wBAGF,CACF,CCpEA,yBAEE,0CAA2C,CAC3C,iBAAkB,CAFlB,aAAc,CAId,kBAAmB,CADnB,aAEF,CACA,uBAME,wBAAyB,CACzB,kBAAmB,CANnB,YAAa,CACb,cAAe,CACf,QAAS,CAKT,kBAAmB,CAJnB,YAAa,CACb,UAIF,CAEA,yBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,UACF,CACA,oBACE,8CAA+C,CAC/C,kBACF,CAEA,8BAGE,kBAAmB,CACnB,uBAAwB,CAHxB,YAAa,CACb,6BAA8B,CAG9B,cACF,CAEA,mCACE,cAAe,CACf,eAAgB,CAChB,gBACF,CAEA,iDAEE,cAAe,CACf,gBACF,CAEA,wCAIE,QAAS,CAFT,SAAU,CAGV,eAAgB,CAJhB,0BAA2B,CAE3B,uBAGF,CAEA,2DAGE,WAAY,CADZ,SAAU,CADV,uBAGF,CAEA,0BACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,0BACE,UACF,CAEA,0BACE,cAAe,CACf,gBAAiB,CACjB,kBACF,CAEA,8CAEE,YAAa,CACb,QACF,CAEA,oDAEE,sBAA6B,CAC7B,iBAAkB,CAClB,cAAe,CAGf,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAHjB,WAAY,CAKZ,iBAAkB,CAJlB,UAKF,CAEA,gEAEE,kCACF,CAEA,8FAEE,wCAAyC,CACzC,uBAAwB,CACxB,iBACF,CAEA,4GAEE,6BAA8B,CAC9B,iBAAkB,CAClB,UAAW,CACX,UAAW,CACX,QAAS,CACT,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,SACF,CAGA,oBAKE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAF5D,kBAAmB,CADnB,eAKF,CAEA,oBAKE,kCAAmC,CAJnC,mCAAoC,CAEpC,kBAAmB,CADnB,YAAa,CAIb,iBAAkB,CAFlB,8BAGF,CAEA,0BACE,8CACF,CAEA,wBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,wBACE,eACF,CAEA,2BACE,UAAW,CACX,cACF,CAEA,oBAGE,uBAAwB,CADxB,cAAe,CADf,eAGF,CAEA,qBAME,UACF,CAEA,iEAPE,uBAAwB,CADxB,cAAe,CAEf,eAAgB,CAChB,iBAAkB,CAClB,QAYF,CARA,4CAOE,SACF,CAEA,0DASE,mCAAoC,CAJpC,iBAAkB,CAHlB,UAAW,CACX,UAAW,CAIX,UAAW,CACX,iBAAkB,CAFlB,OAAQ,CAFR,SAMF,CAEA,6BACE,mCACF,CAEA,0BAKE,uBAAwB,CAHxB,cAAe,CAEf,eAAgB,CAHhB,YAAa,CAEb,iBAGF,CAEA,yBACE,YACF,CACA,qCACE,uBAME,wBAAyB,CACzB,kBAAmB,CANnB,YAAa,CACb,cAAe,CACf,QAAS,CAKT,kBAAmB,CAJnB,YAAa,CACb,UAIF,CACA,oBAEE,+CAAgD,CADhD,iBAAkB,CAElB,mBACF,CACF,CACA,oCACE,yBACE,aACF,CACA,2BACE,YACF,CACA,yBACE,YAAa,CACb,qBAAsB,CACtB,cAAe,CACf,QAAS,CACT,YAAa,CACb,UACF,CACA,4BACE,eACF,CACA,8CAEE,YAAa,CACb,qBAAsB,CACtB,QACF,CACF,CC7PA,6BAIE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,sBACE,qBAAsB,CACtB,kBAAmB,CAEnB,+BAA0C,CAG1C,YAAa,CAIb,qBAAsB,CAItB,YAAa,CADb,6BAA8B,CAE9B,YACF,CAEA,yBAKE,iBAAkB,CAHlB,YAAa,CAIb,kBAAmB,CAFnB,gBAAiB,CAHjB,UAMF,CAEA,uBAGE,WACF,CAEA,0BASE,oBAAqB,CACrB,2BAA4B,CAN5B,UAAW,CAIX,mBAAoB,CAPpB,cAAe,CACf,eAAgB,CAGhB,eAAgB,CAFhB,iBAAkB,CAGlB,eAAgB,CAKhB,eACF,CAEA,4BAEE,UAAW,CADX,cAEF,CAEA,qBAGE,YAAa,CAGb,wBACF,CAEA,4BAEE,qBAAsB,CAItB,WAAY,CACZ,iBAAkB,CAJlB,UAAW,CAKX,cAAe,CAHf,cAAe,CADf,eAAgB,CAHhB,gBAAiB,CAUjB,8BACF,CAEA,kCACE,qBACF,CAGA,8BAEE,yDACF,CAMA,wDAHE,wBAAyB,CACzB,iBAMF,CAJA,8BAGE,YACF,CAEA,0BACE,WAAY,CACZ,YAAa,CACb,SACF,CAEA,0BACE,WAAY,CAEZ,eAAgB,CADhB,SAEF,CAcA,iCACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CAGA,+BASE,kBAAmB,CAEnB,qBAAsB,CARtB,YAAa,CAOb,YAAa,CAJb,sBAAuB,CAUvB,MAAO,CAHP,cAAe,CAEf,KAAM,CADN,UAAW,CAFX,YAKF,CAEA,4BAOE,kDAAoC,CAHpC,qBAAsB,CACtB,iBAAkB,CADlB,qBAAsB,CAFtB,WAAY,CADZ,UAOF,CASA,gCACE,GAEE,uBACF,CACF,CCrLA,sBAEE,UAAW,CADX,eAAiB,CAEjB,kBACF,CAEA,8BAGE,kBAAmB,CADnB,eAAgB,CADhB,UAGF,CAEA,kCAGE,kBAAmB,CADnB,WAAY,CAGZ,gBAAiB,CAIjB,6BAA+B,CAR/B,UAUF,CAEA,wCAGE,qBACF,CAEA,0BACE,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAChB,aACF,CAEA,uBAGE,UAAW,CAFX,iBAAkB,CAClB,eAAgB,CAGhB,kBAAmB,CADnB,oBAEF,CAeA,kCACE,GACE,SAAU,CAEV,0BACF,CACA,GACE,SAAU,CAEV,uBACF,CACF,CAGA,yBACE,uBACE,gBACF,CAEA,iDAEE,cACF,CAEA,yBACE,iBACF,CACF,CCtFA,0BAME,kBAAmB,CAHnB,YAAa,CAUb,cAAe,CAHf,QAAS,CADT,6BAA8B,CAE9B,cAGF,CAEA,0CAIE,0BACF,CAEA,kBAGE,YAAa,CACb,eACF,CAEA,sBAGE,kBAAmB,CADnB,WAAY,CAGZ,gBAAiB,CAJjB,UAKF,CAEA,mBAGE,YACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,qBAIE,UAAW,CAHX,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,qCACE,sBACE,cACF,CAEA,qBACE,cACF,CACF,CAEA,yBACE,0BAIE,qBAAsB,CACtB,QAAS,CACT,cACF,CAEA,sBACE,cACF,CAEA,qBACE,cACF,CACF,CCnFA,6BAME,kBAAmB,CAHnB,YAAa,CAUb,cAAe,CAHf,QAAS,CADT,sBAAuB,CAMvB,aAAc,CADd,gBAAiB,CAHjB,iBAKF,CAEA,0BAYE,kBAAmB,CANnB,YAAa,CAHb,QAAS,CAMT,sBAAuB,CAIvB,eACF,CAEA,8BAGE,gBAAmB,CAGnB,kBAAmB,CAEnB,+BAAyC,CANzC,eAAgB,CAGhB,gBAAiB,CAJjB,UAQF,CAEA,0BAGE,QAAS,CACT,eACF,CAEA,6BACE,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,4BACE,cAAe,CACf,eACF,CAGA,mCAIE,qBAAsB,CAGtB,iBAAkB,CAJlB,UAAW,CAEX,kBAAmB,CACnB,eAAgB,CALhB,iBAAkB,CAClB,UAMF,CAEA,4BAEE,oBAAqB,CAIrB,iBAAkB,CALlB,WAAY,CAIZ,yBAEF,CAGA,yBACE,6BAIE,qBAAsB,CACtB,iBACF,CAEA,6BACE,cACF,CAEA,4BACE,cACF,CAEA,8BACE,cACF,CACF,CAEA,yBACE,6BACE,QAAS,CACT,iBACF,CAEA,6BACE,cACF,CAEA,4BACE,cAAe,CACf,YAAa,CACb,aACF,CAEA,8BACE,gBACF,CACF,CC7HA,MACE,6BAMF,CACA,wCAUE,kBAAmB,CAEnB,+BAAgE,CAAhE,8DAAgE,CAJhE,cAAe,CACf,YAAa,CAJb,WAAqC,CAArC,oCAAqC,CAMrC,sBAAuB,CALvB,gBAA2D,CAA3D,sDAA2D,CAJ3D,iBAAkB,CAClB,OAA6C,CAA7C,2CAA6C,CAC7C,UAAoD,CAApD,+CAAoD,CAGpD,UAMF,CACA,sFAGE,WAAY,CADZ,WAAa,CAEb,mBACF,CACA,kFAGE,WAAY,CADZ,SAAU,CAEV,mBACF,CACA,gGAEE,sBACF,CACA,gDAGE,WAAY,CACZ,kBAAmB,CACnB,uBAAwB,CAHxB,UAIF,CACA,wEAEE,wBACF,CACA,oDAEE,SAAiD,CAAjD,+CAAiD,CACjD,UACF,CAMA,oBACE,YACF,CAEA,oDAEE,wBAAyB,CACzB,cAAwC,CAAxC,uCAAwC,CAGxC,mBAAqB,CADrB,gBAAiB,CAEjB,aAAc,CAHd,6BAIF,CACA,gEAEE,cACF,CACA,oDAGE,SAAU,CADV,UAAkD,CAAlD,gDAEF,CACA,gEAEE,cACF,CCpFA,sBAGE,YACF,CAEA,oBAaE,sBAAuB,CANvB,YAAa,CAJb,QAAO,CAOP,sBAAuB,CAIvB,iBAAkB,CAVlB,wBAWF,CAEA,6BACE,UACF,CACA,gCACE,cAAe,CAEf,eAAgB,CADhB,kBAEF,CACA,yBAKE,aAAc,CAJd,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAGjB,kBAAmB,CAFnB,SAGF,CACA,uBAGE,mCAAoC,CACpC,aAAc,CAFd,UAAW,CAGX,kBAAmB,CAJnB,WAKF,CACA,sBAGE,kBAAmB,CADnB,WAAY,CAEZ,kBAAmB,CAEnB,gBAAiB,CALjB,UAMF,CAEA,6BACE,kBACF,CAEA,2BAME,kBAAmB,CAHnB,YAAa,CAOb,cAAe,CAHf,QAAS,CACT,eAGF,CAEA,iDAME,kBAAmB,CAHnB,QAAO,CAEP,YAAa,CADb,aAAc,CAId,gBACF,CAEA,sDAME,UAAW,CAHX,QAAO,CACP,cAAe,CACf,eAAgB,CAEhB,aACF,CACA,iCACE,iBAAkB,CAClB,gBACF,CAEA,uBAYE,kBAAmB,CATnB,YAAa,CAEb,cAAe,CACf,QAAS,CAGT,0BAA2B,CAI3B,UACF,CAEA,2BAQE,iBAAkB,CALlB,cAAe,CAEf,YAAa,CAEb,gBAAiB,CAHjB,UAKF,CAGA,4BAGE,qBAAsB,CACtB,iBAAkB,CAHlB,gBAAiB,CACjB,eAAgB,CAGhB,UACF,CAEA,2BAEE,wBAAyB,CADzB,UAEF,CAEA,4DAIE,4BAA6B,CAF7B,iBAAkB,CAClB,eAEF,CAEA,oCAGE,wBAAyB,CAIzB,4BAA6B,CAD7B,wBAAyB,CADzB,eAAgB,CAJhB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CAEN,SAIF,CAEA,4CACE,wBACF,CAEA,oCACE,wBACF,CAEA,qCACE,gCACE,cAAe,CACf,kBACF,CACA,yBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CACF,CAGA,yBACE,sBAIE,qBACF,CACA,oBACE,mBACF,CACA,2BAOE,mBAAoB,CAHpB,qBAIF,CAEA,uGAGE,WAAY,CADZ,cAEF,CAEA,iDACE,YACF,CAEA,2BACE,cACF,CACA,2BAGE,cAAe,CAEf,YAAa,CADb,UAEF,CACF,CAGA,yBACE,2BAIE,qBACF,CAEA,iDAEE,cACF,CAIA,gCACE,cAAe,CACf,kBACF,CACA,yBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CACA,oBACE,mBACF,CAEA,4DAGE,cAAe,CADf,gBAEF,CACF,CC3PA,wBACE,8CAA+C,CAG/C,YAAa,CAIb,qBAAsB,CAEtB,aAAc,CACd,eAAgB,CAChB,YAAa,CACb,WACF,CAEA,uBAEE,aAAc,CACd,gBAAiB,CACjB,iBACF,CAEA,sBAKE,YAAa,CAIb,qBAAsB,CARtB,iBAAkB,CAClB,kBAQF,CAEA,wBACE,sBAA6B,CAC7B,iBAAkB,CASlB,UAAW,CARX,cAAe,CAIf,cAAe,CAEf,eAAgB,CADhB,gBAAiB,CAJjB,iBAAkB,CAClB,WAAY,CAKZ,iBAAkB,CAJlB,UAMF,CAEA,8BACE,kCACF,CAEA,8CACE,wCAAyC,CACzC,uBAAwB,CACxB,iBACF,CAEA,qDACE,6BAA8B,CAC9B,iBAAkB,CAClB,UAAW,CACX,UAAW,CACX,QAAS,CACT,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,SACF,CAEA,0BACE,YACF,CACA,oCACE,wBACE,YACF,CACA,0BAEE,sCAAuC,CACvC,iBAAkB,CAFlB,aAAc,CAId,kBAAmB,CACnB,eAAgB,CAFhB,aAGF,CAEA,+BASE,kBAAmB,CACnB,uBAAwB,CAPxB,YAAa,CAGb,6BAA8B,CAK9B,cACF,CAEA,oCACE,cAAe,CACf,eAAgB,CAChB,gBACF,CAEA,mDAEE,cAAe,CACf,gBACF,CAEA,wCAQE,QAAS,CAJT,SAAU,CAKV,eAAgB,CANhB,0BAA2B,CAI3B,uBAGF,CAEA,4DAKE,WAAY,CADZ,SAAU,CADV,uBAGF,CACF,CCrIA,kBAEE,kBAAmB,CADnB,eAEF,CAEA,iBAGE,YAAa,CACb,QAAS,CACT,kBAAmB,CACnB,UACF,CAEA,mBAME,qBAAsB,CADtB,wBAAyB,CAEzB,iBAAkB,CAIlB,qBAAsB,CARtB,QAAO,CAKP,cAAe,CAIf,WAAY,CARZ,wBAAyB,CAKzB,UAIF,CAEA,2BAGE,QACF,CAEA,mCACE,+BAAiC,CACjC,2BAA6B,CAE7B,cAAe,CADf,yBAEF,CAEA,uCACE,UACF,CAEA,wCACE,UACF,CAEA,6BAME,sBAAuB,CAHvB,YAAa,CAKb,cAAe,CADf,QAAS,CAET,kBAAmB,CACnB,eACF,CAEA,sBAIE,iBAAmB,CAFnB,WAAY,CACZ,cAAe,CAFf,UAIF,CAEA,oBACE,qBAAuB,CAGvB,WAAY,CACZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAiB,CAHjB,iBAAkB,CAOlB,oCACF,CAEA,0BACE,qBACF,CAEA,yBACE,kBACE,eACF,CACA,iBAIE,qBAAsB,CACtB,QACF,CAEA,mBAEE,WAAY,CACZ,0BAA2B,CAF3B,UAGF,CAEA,6BAOE,sBAAuB,CAHvB,qBAIF,CACF,CClHA,gCAEE,qBAAgB,CAChB,SACF,CACA,+BAKE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAH5D,eAKF,CACA,uBASE,kCAAmC,CARnC,mCAAoC,CAEpC,kBAAmB,CADnB,YAAa,CAQb,iBAAkB,CAHlB,8BAIF,CAEA,6BAEE,8CACF,CAEA,2BAEE,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,2BACE,eACF,CACA,8BACE,UAAW,CACX,cACF,CACA,wBAME,UACF,CAEA,0EAPE,uBAAwB,CADxB,cAAe,CAEf,eAAgB,CAChB,iBAAkB,CAClB,QAYF,CARA,kDAOE,SACF,CAEA,gEASE,mCAAoC,CAJpC,iBAAkB,CAHlB,UAAW,CACX,UAAW,CAIX,UAAW,CACX,iBAAkB,CAFlB,OAAQ,CAFR,SAMF,CAEA,gCACE,mCACF,CAEA,uBAGE,uBAAwB,CADxB,cAAe,CADf,eAGF,CAEA,yBACE,gCAKE,qBAAgB,CAJhB,UACF,CAKF,CC7FA,2BAIE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,oBACE,qBAAsB,CACtB,kBAAmB,CAEnB,+BAA0C,CAG1C,YAAa,CAIb,qBAAsB,CAItB,YAAa,CADb,6BAA8B,CAE9B,YACF,CAEA,uBAKE,iBAAkB,CAHlB,YAAa,CAIb,kBAAmB,CAFnB,gBAAiB,CAHjB,UAMF,CAEA,qBAGE,WACF,CAEA,wBASE,oBAAqB,CACrB,2BAA4B,CAN5B,UAAW,CAIX,mBAAoB,CAPpB,cAAe,CACf,eAAgB,CAGhB,eAAgB,CAFhB,iBAAkB,CAGlB,eAAgB,CAKhB,eACF,CAEA,0BAEE,UAAW,CADX,cAEF,CAEA,mBAGE,YAAa,CAGb,wBACF,CAEA,0BAEE,qBAAsB,CAItB,WAAY,CACZ,iBAAkB,CAJlB,UAAW,CAKX,cAAe,CAHf,cAAe,CADf,eAAgB,CAHhB,gBAAiB,CAUjB,8BACF,CAEA,gCACE,qBACF,CAGA,4BAEE,uDACF,CAMA,oDAHE,wBAAyB,CACzB,iBAMF,CAJA,4BAGE,YACF,CAEA,wBACE,WAAY,CACZ,YAAa,CACb,SACF,CAEA,wBACE,WAAY,CAEZ,eAAgB,CADhB,SAEF,CAcA,+BACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CAGA,6BASE,kBAAmB,CAEnB,qBAAsB,CARtB,YAAa,CAOb,YAAa,CAJb,sBAAuB,CAUvB,MAAO,CAHP,cAAe,CAEf,KAAM,CADN,UAAW,CAFX,YAKF,CAEA,0BAOE,gDAAoC,CAHpC,qBAAsB,CACtB,iBAAkB,CADlB,qBAAsB,CAFtB,WAAY,CADZ,UAOF,CASA,8BACE,GAEE,uBACF,CACF,CCrLA,4DAEE,UACF,CACA,oBACE,qBAAsB,CAEtB,iBAAkB,CADlB,YAEF,CAEA,0BACE,yBAA0B,CAC1B,cACF,CAEA,iCAiBE,kBAAmB,CAhBnB,eAAgB,CAChB,WAAY,CAMZ,cAAe,CAGf,YAAa,CALb,cAAe,CACf,eAAiB,CAOjB,6BAA8B,CAV9B,cAAe,CACf,eAAgB,CAFhB,UAeF,CAEA,wCAKE,YAAa,CAJb,eAAgB,CAGhB,8BAEF,CAEA,4DACE,8BAAuB,CAAvB,sBACF,CAEA,iCAGE,UAAW,CADX,gBAAkB,CADlB,cAGF,CCnDA,+BAGE,YAAa,CAIb,qBAAsB,CACtB,QAAS,CACT,gBACF,CAEA,6BAME,wDAA0C,CAL1C,kBAAmB,CACnB,kBAAmB,CAEnB,kBAAmB,CADnB,YAIF,CAEA,6BAGE,eAAgB,CAChB,iBAAkB,CAFlB,WAAY,CAGZ,iBAAkB,CAJlB,UAKF,CAEA,8BAEE,kBAAmB,CACnB,iBAAkB,CAFlB,WAAY,CAGZ,YACF,CAEA,4BAME,wDAA0C,CAH1C,kBAAmB,CACnB,iBAAkB,CAFlB,YAAa,CADb,UAMF,CAcA,gCACE,GACE,wBACF,CACA,IACE,wBACF,CACA,GACE,wBACF,CACF,CAGA,yBACE,+BAOE,sBAAuB,CAHvB,kBAIF,CACF,CAEA,2BAME,YAAa,CAHb,QAAO,CAOP,qBAAsB,CACtB,QACF,CAEA,4BAGE,QAAO,CACP,eACF,CAEA,4BAKE,aAAS,CAHT,YAAa,CAGb,QAAS,CADT,yBAEF,CAEA,yBACE,4BAEE,yBACF,CACF,CAEA,yBACE,wBAAyB,CAEzB,kBAAmB,CADnB,YAAa,CAEb,eACF,CAEA,qBAEE,aAAc,CADd,cAAe,CAEf,iBACF,CAEA,+BAME,kBAAmB,CAHnB,YAAa,CAIb,iBACF,CAEA,4BAGE,aAAc,CAFd,cAAe,CACf,QAEF,CAEA,sDAGE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,iCACE,aACF,CAEA,2BAGE,qBAAsB,CAFtB,kBAAmB,CAGnB,YAAa,CAFb,eAGF,CAEA,oBAGE,QAAS,CADT,WAAY,CADZ,UAGF,CCzKA,kBAEE,6BAA8B,CAD9B,0BAEF,CAEA,iBACE,YAAa,CACb,kBAAmB,CACnB,4BAA6B,CAC7B,UACF,CAEA,wBAEE,YAAa,CADb,QAAO,CAEP,qBACF,CAEA,mBAGE,8BAA+B,CAE/B,aAAc,CAJd,6BAA8B,CAC9B,qCAAsC,CAEtC,4BAEF,CAEA,sBACE,wBAAyB,CACzB,mCACF,CAEA,mBAIE,yCAA0C,CAD1C,2DAA4D,CAE5D,qCAAsC,CAItC,qBAAsB,CARtB,QAAO,CAMP,mCAAoC,CADpC,+BAAgC,CAIhC,eAAgB,CARhB,qCAAsC,CAStC,iCAAkC,CAHlC,UAIF,CAEA,yBAEE,iCAAkC,CAClC,8BAA2C,CAF3C,YAGF,CAEA,4BACE,yCAA0C,CAC1C,8BAA+B,CAC/B,kBACF,CAMA,sDAHE,+BAMF,CAHA,8BAEE,8BACF,CAEA,sBAEE,mCAAoC,CADpC,eAEF,CAEA,2BACE,QAAO,CACP,iBACF,CAEA,0BAKE,kBAAmB,CAJnB,wBAAyB,CAGzB,YAAa,CAFb,6BAA8B,CAI9B,kBAAmB,CAHnB,yBAIF,CAEA,iCACE,WAAY,CACZ,6BACF,CAEA,sBACE,8BAA+B,CAC/B,6BAA8B,CAC9B,yBACF,CAEA,2BACE,uBACF,CAEA,mCACE,+BAAiC,CACjC,2BAA6B,CAE7B,cAAe,CADf,yBAEF,CAEA,uCACE,UACF,CAEA,wCACE,UACF,CAEA,6BAME,sBAAuB,CAHvB,YAAa,CAKb,cAAe,CADf,QAAS,CAET,kBAAmB,CACnB,eACF,CAEA,sBAIE,iBAAmB,CAFnB,WAAY,CACZ,cAAe,CAFf,UAIF,CAEA,oBACE,qBAAuB,CAGvB,WAAY,CACZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAiB,CAHjB,iBAAkB,CAOlB,oCACF,CAEA,0BACE,qBACF,CAEA,yBACE,kBACE,eACF,CACA,iBAIE,qBAAsB,CACtB,QACF,CAEA,mBAEE,WAAY,CACZ,0BAA2B,CAF3B,UAGF,CAEA,6BAOE,sBAAuB,CAHvB,qBAIF,CAEA,wBACE,4BACF,CAEA,0BACE,6BACF,CACF,CAGA,6BAGE,qCAAsC,CAFtC,cAAe,CACf,sBAAuB,CAEvB,iCACF,CAEA,mCACE,wCACF,CAEA,4BACE,sCAAuC,CACvC,kBACF,CAEA,2BAGE,8BAA+B,CAF/B,6BAA8B,CAC9B,qCAEF,CAEA,oBAUE,kBAAmB,CATnB,+CAAiD,CACjD,wCAA0C,CAO1C,YAAa,CAHb,6CAA+C,CAF/C,yCAA2C,CAC3C,+CAAiD,CAOjD,kBAAmB,CADnB,sBAAuB,CAHvB,eAAgB,CALhB,+CAAiD,CAIjD,UAMF,CAEA,yCACE,qDAAuD,CAEvD,2BAA4B,CAD5B,0BAEF,CAEA,6BACE,mDAAqD,CAGrD,eAAgB,CAFhB,kBAAmB,CACnB,cAEF,CAEA,qBAME,6CAAkC,CAFlC,sBAAkC,CAClC,iBAAkB,CADlB,6BAAkC,CAFlC,WAAY,CADZ,UAMF,CAEA,4BACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CChQA,mBACE,iEAAsD,CACtD,uBAA2B,CAE3B,2BAA4B,CAD5B,qBAAsB,CAMtB,kBAAmB,CAEnB,qBAAsB,CALtB,eAAgB,CAEhB,YAIF,CAGA,yBACE,mBAEE,kBAAmB,CACnB,eAAgB,CAFhB,YAGF,CACF,CAEA,yBACE,mBAIE,uBAAwB,CADxB,kBAAmB,CADnB,eAAgB,CADhB,YAIF,CACF,CC9BA,yBAGE,uBAA2B,CAD3B,qBAAsB,CAGtB,UAAW,CAJX,YAAa,CAGb,iBAEF,CAEA,0BACE,cACF,CAEA,+BAKE,wBAAyB,CAJzB,UAAY,CAKZ,YAAa,CACb,qBAAsB,CACtB,WAAY,CACZ,6BAA8B,CAP9B,oBAQF,CAEA,kCACE,cAAe,CACf,eAAiB,CACjB,SACF,CAEA,uDAEE,kBAAmB,CADnB,eAEF,CAEA,uDAEE,oBAAoC,CACpC,kBAAmB,CACnB,UAAW,CACX,cAAe,CACf,aAAc,CAKd,cAAe,CADf,eAAgB,CAHhB,kBAAmB,CACnB,WAAY,CACZ,yBAAkB,CAAlB,iBAGF,CAEA,4BACE,oBACF,CAGA,+BAIE,kBAAmB,CAEnB,UAAY,CAHZ,YAAa,CAFb,YAAa,CAIb,sBAAuB,CAHvB,iBAKF,CAEA,qBAOE,uBAA2B,CAD3B,qBAAsB,CAEtB,UACF,CAEA,iDANE,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAeF,CARA,4BAOE,oBAA8B,CAN9B,UAOF,CAEA,0BACE,iBAAkB,CAClB,SACF,CAEA,6BACE,cAAe,CACf,eAAiB,CACjB,kBACF,CAEA,4BACE,gBAAiB,CACjB,UACF,CAGA,+BAEE,kBAAmB,CADnB,cAEF,CAEA,+BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,4BACE,QACF,CAEA,+BAGE,UAAW,CAFX,gBAAiB,CACjB,kBAEF,CAEA,+BACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,2BAIE,sBAA6B,CAD7B,iBAAkB,CADlB,WAAY,CAIZ,uBAAyB,CALzB,UAMF,CAEA,4BAEE,UAAW,CADX,gBAAiB,CAEjB,eACF,CAEA,8BACE,QAAO,CACP,iBACF,CAEA,kCAEE,WAAY,CADZ,cAEF,CAGA,iCACE,cACF,CAEA,+CACE,kBACF,CAEA,0BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,2DACE,OACF,CAMA,sHACE,OACF,CAEA,2DACE,OACF,CAEA,8BACE,QACF,CAEA,iCAGE,UAAW,CAFX,gBAAiB,CACjB,kBAEF,CAEA,gCAGE,UAAW,CAFX,gBAAiB,CACjB,eAEF,CAEA,+BAEE,YAAa,CADb,QAAO,CAEP,QACF,CAEA,+BACE,QACF,CAEA,mCAGE,iBAAkB,CAClB,cAAe,CAFf,WAAY,CAGZ,6BAA+B,CAJ/B,UAKF,CAEA,yCACE,qBACF,CAGA,8BAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,8BACE,iBAAkB,CAClB,eACF,CAEA,kCAIE,cAAe,CAFf,YAAa,CACb,gBAAiB,CAEjB,6BAA+B,CAJ/B,UAKF,CAEA,wCACE,qBACF,CAGA,qCAEE,kBAAmB,CADnB,cAEF,CAEA,yBAIE,kBAAmB,CAGnB,iBAAkB,CANlB,mBAAoB,CAQpB,eAAgB,CANhB,QAAS,CAQT,eAAgB,CALhB,iBAAkB,CAElB,oBAAqB,CAErB,8BAEF,CAEA,+BACE,kBAAmB,CACnB,UACF,CAEA,6BAEE,WAAY,CADZ,UAEF,CAGA,iCACE,cACF,CAEA,8BACE,YAAa,CACb,QAAS,CAET,sBAAuB,CADvB,kBAEF,CAEA,yBAGE,eAAiB,CADjB,qBAAsB,CAEtB,UAAW,CAHX,iBAAkB,CAMlB,uBACF,CAEA,gFAGE,eAAiB,CADjB,iBAAmB,CAGnB,eAAgB,CADhB,UAEF,CAGA,8BAEE,WAAY,CADZ,UAEF,CAEA,qCAIE,qBAAuB,CACvB,iBAAkB,CAJlB,YAAa,CACb,OAAQ,CAKR,gBAAiB,CAJjB,WAAY,CAGZ,yBAAkB,CAAlB,iBAEF,CAEA,uDACE,cAAe,CAEf,eAAiB,CACjB,iBAAkB,CAFlB,wBAGF,CAEA,2BAME,kBAAmB,CAHnB,qBAAsB,CACtB,iBAAkB,CAOlB,qBAAsB,CAHtB,UAAW,CAEX,cAAe,CALf,YAAa,CAIb,cAAe,CAKf,eAAmB,CAZnB,WAAY,CAKZ,sBAAuB,CANvB,cAAe,CAYf,SAAU,CADV,uBAGF,CAEA,mDAGE,eAAiB,CAFjB,eAAgB,CAChB,cAEF,CAGA,8BACE,YAAa,CACb,QAAS,CACT,sBAAuB,CACvB,eACF,CAEA,kCAGE,iBAAkB,CADlB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAGA,4BAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,QAAS,CADT,eAEF,CAEA,sBACE,iBACF,CAGA,2BACE,QAAO,CACP,iBACF,CAOA,+CACE,YAAa,CACb,QACF,CAMA,uBAIE,kBAAmB,CAHnB,YAAa,CACb,cAAe,CACf,QAAS,CAET,sBAAuB,CACvB,eACF,CAEA,8CACE,YAAa,CACb,eACF,CAEA,kDAGE,kBAAmB,CADnB,WAAY,CADZ,UAGF,CAEA,+CACE,YACF,CAGA,wBAGE,iBAEF,CAEA,iDANE,QAAO,CAGP,6BAA8B,CAF9B,eAYF,CAPA,yBAEE,YAAa,CACb,qBAAsB,CACtB,QAGF,CAEA,uBACE,QAAO,CAGP,6BAA8B,CAF9B,eAAgB,CAChB,iBAEF,CAMA,sDACE,iBACF,CAGA,0BAEE,WAAY,CAEZ,UAAY,CACZ,cAAe,CAFf,SAAU,CAFV,iBAAkB,CAKlB,SACF,CAEA,uBAUE,kBAAmB,CAJnB,qBAAsB,CAEtB,iBAAkB,CADlB,UAAY,CAOZ,cAAe,CALf,YAAa,CAGb,cAAe,CACf,eAAiB,CARjB,WAAY,CAMZ,sBAAuB,CAVvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAET,UAAW,CAWX,SACF,CAGA,0BAIE,iBAEF,CAEA,2EALE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,QAQF,CAEA,wDACE,yBAAkB,CAAlB,iBACF,CAEA,oDAEE,kBAAmB,CADnB,UAEF,CAEA,yBAYE,kBAAmB,CAFnB,qBAAsB,CAFtB,WAAY,CANZ,iBAAkB,CAElB,kBAAuB,CAKvB,UAAW,CAJX,cAAe,CACf,6BAA+B,CAK/B,QAAS,CAJT,eAAgB,CANhB,wBAAyB,CAYzB,kBACF,CAEA,8BACE,aAAc,CAEd,eAAgB,CADhB,gBAEF,CAEA,+BACE,kBACF,CAKA,6BAOE,qBAAsB,CAJtB,wBAAyB,CACzB,iBAAkB,CAElB,sBAAuB,CAEvB,WAAY,CANZ,kBAAmB,CADnB,eAAgB,CAIhB,eAIF,CAEA,gCACE,0BAEF,CAEA,6BAEE,kBAAmB,CACnB,+BAAgC,CAFhC,YAGF,CAEA,yBAGE,gBAAuB,CAQvB,qBAAsB,CAPtB,WAAY,CAGZ,UAAW,CAFX,cAAe,CAJf,QAAO,CAKP,eAAgB,CAEhB,eAAgB,CANhB,iBAAkB,CAOlB,kBAAoB,CACpB,kBAEF,CAEA,yBACE,qBAAsB,CACtB,UACF,CAEA,6BAEE,YAAa,CACb,qBAAsB,CACtB,QAAS,CAHT,YAIF,CAEA,yBAIE,kBAAmB,CAFnB,4BAA6B,CAD7B,YAAa,CAEb,eAEF,CAEA,qCACE,eACF,CAEA,0BACE,QAAO,CAEP,cAAe,CADf,eAEF,CAEA,wBAGE,aAAc,CADd,UAAS,CADT,eAGF,CAGA,kCACE,YAAa,CACb,iBACF,CAEA,iCAGE,kBAAmB,CACnB,eAAmB,CAGnB,wBAAyB,CACzB,iBAAkB,CAHlB,UAAW,CAMX,cAAe,CAVf,YAAa,CASb,eAAgB,CARhB,6BAA8B,CAI9B,iBAAkB,CAMlB,iBAAkB,CAHlB,UAIF,CAEA,+BAOE,qBAAsB,CACtB,wBAAyB,CACzB,eAAkB,CAElB,8BAAwC,CAVxC,QAAS,CAIT,MAAO,CAOP,SAAU,CATV,iBAAkB,CAGlB,OAAQ,CAFR,oBAAqB,CASrB,UAAW,CAXX,UAYF,CAEA,qCAGE,qBAAsB,CAGtB,aAAc,CAJd,0BAAmB,CAAnB,kBAAmB,CADnB,SAAU,CAGV,iBAAkB,CAClB,SAEF,CAEA,+BAME,eAAgB,CAChB,WAAY,CAEZ,UAAW,CACX,cAAe,CATf,aAAc,CAOd,cAAe,CANf,SAAU,CAEV,iBAAkB,CAClB,eAAgB,CAQhB,yBAA2B,CAV3B,UAWF,CAEA,qCACE,kBACF,CAEA,4BAUE,kBAAmB,CAJnB,uBAA2B,CAC3B,2BAA4B,CAF5B,qBAAsB,CAGtB,YAAa,CAJb,YAAa,CAKb,sBAAuB,CARvB,eAAgB,CAChB,iBAAkB,CASlB,iBAAkB,CARlB,UASF,CAEA,oDACE,UAAY,CACZ,gBAAiB,CACjB,eAAgB,CAChB,gCACF,CAIA,iCAIE,mBAAoB,CAHpB,YAAa,CACb,QAAS,CACT,eAGF,CAGA,qDAIE,YAAa,CAFb,QAAW,CAGX,qBAAsB,CACtB,QACF,CAGA,4BAGE,kBAAmB,CADnB,YAAa,CADb,QAAW,CAGX,sBACF,CAGA,wDAGE,gBAAmB,CAEnB,iBAAkB,CAElB,aAAc,CADd,eAAgB,CAJhB,UAOF,CAEA,+BACE,WAEF,CAGA,gEAGE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAGA,2BACE,qBAAsB,CAEtB,iBAAkB,CAGlB,UAAW,CACX,aAAc,CAHd,cAAe,CACf,eAAiB,CAHjB,YAOF,CAGA,oCACE,6BACE,YACF,CAEA,kCACE,cACF,CAEA,mCACE,gBACF,CACF,CAEA,oCACE,kCAEE,aAAc,CACd,iBAAkB,CAFlB,UAGF,CACF,CAEA,oCASE,8EACE,qBACF,CACF,CAEA,yBACE,uBACE,qBACF,CAEA,+CAEE,aACF,CAEA,2BACE,gBACF,CAEA,0BACE,gBACF,CAEA,yBACE,qBACF,CACF,CAEA,oCACE,6BACE,eACF,CAEA,iDACE,QACF,CAEA,uDAGE,cAAe,CADf,WAEF,CAEA,0BAEE,gBAAmB,CADnB,gBAEF,CAEA,6BACE,cAAe,CACf,kBACF,CAEA,4BACE,kBACF,CAEA,uDACE,cACF,CACF,CAGA,+BAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,8BACE,kBAAmB,CACnB,kBAAmB,CAEnB,+BAAyC,CADzC,YAAa,CAEb,iDAGF,CAEA,oCAEE,+BAA0C,CAD1C,0BAEF,CAEA,iCACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,kBACF,CAEA,gCACE,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,QACF,CAEA,2BACE,cAAe,CACf,eAAgB,CAChB,kBAAmB,CACnB,UACF,CAGA,yBACE,+BAEE,QAAS,CADT,yBAA0B,CAE1B,eACF,CAEA,8BACE,YACF,CAEA,iCACE,cACF,CAEA,gCACE,cACF,CAEA,2BACE,cACF,CACF,CAEA,oCAME,+CACE,YAAa,CACb,qBAAsB,CACtB,QACF,CACF,CCx6BA,sBAME,kBAAmB,CAHnB,oBAA8B,CAE9B,YAAa,CAHb,OAAQ,CAKR,sBAAuB,CACvB,YAAa,CAPb,cAAe,CAGf,YAKF,CAEA,oBACE,eAAgB,CAChB,kBAAmB,CAKnB,4BAA0C,CAG1C,UAAW,CAFX,eAAgB,CALhB,eAAgB,CAMhB,eAAgB,CAJhB,YAAa,CACb,iBAAkB,CAFlB,UAOF,CAEA,mBACE,YAAa,CAGb,QAAS,CAFT,6BAA8B,CAC9B,UAEF,CACA,oBAUE,kBAAmB,CATnB,eAAgB,CAIhB,WAAY,CACZ,iBAAkB,CAJlB,UAAW,CAKX,cAAe,CAEf,YAAa,CANb,cAAe,CAKf,eAAiB,CAJjB,WAAY,CAOZ,sBAAuB,CACvB,YACF,CAEA,oBAIE,UAAW,CAHX,gBAAiB,CACjB,eAAiB,CACjB,oBAEF,CAEA,qBAIE,UAAW,CADX,cAAe,CADf,eAAgB,CADhB,kBAIF,CAGA,yBACE,oBACE,YACF,CAEA,oBACE,iBACF,CAEA,qBACE,gBACF,CAEA,oBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CACF,CC9EA,mBASE,kBAAmB,CAKnB,6BAAoC,CAIpC,qBAAsB,CAftB,aAAc,CACd,cAAe,CAIf,YAAa,CAHb,aAAc,CAJd,cAAe,CAMf,eAAgB,CAShB,WAAY,CANZ,sBAAuB,CARvB,eAAgB,CAehB,eAAgB,CAXhB,cAAe,CAMf,iBAAkB,CADlB,iBAAkB,CAIlB,wBAAyB,CAKzB,uBAAyB,CADzB,kBAAmB,CANnB,SAQF,CAEA,sBAEE,4BAA6B,CAD7B,UAEF,CAEA,yBAME,wBAAyB,CAJzB,QAAS,CAGT,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAAW,CAGX,SACF,CACA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,cACF,CACF,CAEA,oCACE,mBACE,cAAe,CACf,WAAY,CACZ,cAAe,CACf,aACF,CACF,CCrDA,wBAEE,UAAW,CADX,eAAiB,CAEjB,kBACF,CAEA,+BAGE,kBAAmB,CADnB,eAAgB,CADhB,UAGF,CAEA,mCAGE,kBAAmB,CADnB,WAAY,CAGZ,gBAAiB,CAIjB,6BAA+B,CAR/B,UAYF,CAEA,yCAGE,qBACF,CAEA,4BACE,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAChB,aACF,CAEA,uBAGE,UAAW,CAFX,iBAAkB,CAClB,eAAgB,CAGhB,kBAAmB,CADnB,oBAEF,CAeA,mCACE,GACE,SAAU,CAEV,0BACF,CACA,GACE,SAAU,CAEV,uBACF,CACF,CAGA,yBACE,wBACE,gBACF,CAEA,mDAEE,cACF,CAEA,0BACE,iBACF,CACF", "sources": ["asset/style/root.css", "index.css", "layout/Navbar/nav.module.css", "layout/Navbar/components/models/navModels.module.css", "../node_modules/swiper/swiper.css", "components/FilterSlide/filter.module.css", "components/SkeletonCard/SkeletonCard.module.css", "layout/Navbar/components/discover/navDiscover.module.css", "layout/Navbar/components/mobPrimaryBar/primaryBar.module.css", "layout/Footer/footer.module.css", "components/ErrorBoundary/ErrorBoundary.module.css", "pages/Home/components/header-slider/header.module.css", "pages/Home/components/Models/ourModels.module.css", "pages/Home/components/Models/FilterSlide/filter.module.css", "pages/Home/components/Image-card/ImageCard.module.css", "components/ToolBar/toolBar.module.css", "pages/Home/components/video-card/videoCard.module.css", "components/SimpleCard/simpleCard.module.css", "../node_modules/aos/dist/aos.css", "components/Form/form.module.css", "components/Notification/Notification.module.css", "pages/Models/models.module.css", "pages/Discover/News/newsList.module.css", "pages/Discover/News/NewsPage.jsx/newsPage.module.css", "pages/Discover/About/about.module.css", "pages/Discover/About/History/history.module.css", "../node_modules/swiper/modules/navigation.css", "pages/Owners/owners.module.css", "pages/Owners/components/sidebar/sidebar.module.css", "pages/Owners/components/form/form.module.css", "pages/Owners/components/PartsList/partsList.module.css", "pages/Offer/offers.module.css", "pages/Privacy/privacy.module.css", "pages/Discover/Contact/contact.module.css", "pages/Discover/Contact/form.module.css", "pages/NoPage/noPage.module.css", "pages/Models/Pages/modelPage.module.css", "pages/Models/Pages/components/modal.module.css", "pages/Models/Pages/components/FilterSlide/filter.module.css", "pages/Offer/OfferPage/offerPage.module.css"], "sourcesContent": [":root {\n  /* Legacy variables (keep for backward compatibility) */\n  --body-color: #fff;\n  --text-color: #000;\n  --theme-color: #d7000f;\n\n  /* Design System - Colors */\n  --color-primary: #d7000f;\n  --color-primary-light: #ff1a2b;\n  --color-primary-dark: #a50008;\n  --color-primary-hover: #b8000d;\n\n  --color-neutral-50: #fafafa;\n  --color-neutral-100: #ffffff;\n  --color-neutral-200: #f8f9fa;\n  --color-neutral-300: #e9ecef;\n  --color-neutral-400: #ced4da;\n  --color-neutral-500: #8e8e93;\n  --color-neutral-600: #6c757d;\n  --color-neutral-700: #495057;\n  --color-neutral-800: #343a40;\n  --color-neutral-900: #000000;\n\n  --color-success: #28a745;\n  --color-success-light: #d4edda;\n  --color-error: #dc3545;\n  --color-error-light: #f8d7da;\n  --color-warning: #ffc107;\n  --color-warning-light: #fff3cd;\n  --color-info: #17a2b8;\n  --color-info-light: #d1ecf1;\n\n  /* Typography */\n  --font-family-base: 'GWMSans', Libre Franklin, Libre <PERSON> Fallback, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n\n  --font-size-xs: 0.75rem;\n  /* 12px */\n  --font-size-sm: 0.875rem;\n  /* 14px */\n  --font-size-base: 1rem;\n  /* 16px */\n  --font-size-lg: 1.125rem;\n  /* 18px */\n  --font-size-xl: 1.25rem;\n  /* 20px */\n  --font-size-2xl: 1.5rem;\n  /* 24px */\n  --font-size-3xl: 1.875rem;\n  /* 30px */\n  --font-size-4xl: 2.25rem;\n  /* 36px */\n  --font-size-5xl: 3rem;\n  /* 48px */\n\n  --font-weight-light: 300;\n  --font-weight-normal: 400;\n  --font-weight-medium: 500;\n  --font-weight-semibold: 600;\n  --font-weight-bold: 700;\n  --font-weight-extrabold: 800;\n  --font-weight-black: 900;\n\n  --line-height-tight: 1.25;\n  --line-height-snug: 1.375;\n  --line-height-normal: 1.5;\n  --line-height-relaxed: 1.625;\n  --line-height-loose: 2;\n\n  /* Spacing */\n  --space-0: 0;\n  --space-1: 0.25rem;\n  /* 4px */\n  --space-2: 0.5rem;\n  /* 8px */\n  --space-3: 0.75rem;\n  /* 12px */\n  --space-4: 1rem;\n  /* 16px */\n  --space-5: 1.25rem;\n  /* 20px */\n  --space-6: 1.5rem;\n  /* 24px */\n  --space-8: 2rem;\n  /* 32px */\n  --space-10: 2.5rem;\n  /* 40px */\n  --space-12: 3rem;\n  /* 48px */\n  --space-16: 4rem;\n  /* 64px */\n  --space-20: 5rem;\n  /* 80px */\n  --space-24: 6rem;\n  /* 96px */\n  --space-32: 8rem;\n  /* 128px */\n\n  /* Borders */\n  --border-width-0: 0;\n  --border-width-1: 1px;\n  --border-width-2: 2px;\n  --border-width-4: 4px;\n  --border-width-8: 8px;\n\n  --border-radius-none: 0;\n  --border-radius-sm: 0.125rem;\n  /* 2px */\n  --border-radius-md: 0.25rem;\n  /* 4px */\n  --border-radius-lg: 0.5rem;\n  /* 8px */\n  --border-radius-xl: 0.75rem;\n  /* 12px */\n  --border-radius-2xl: 1rem;\n  /* 16px */\n  --border-radius-full: 9999px;\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);\n\n  /* Transitions */\n  --transition-fast: 150ms ease-in-out;\n  --transition-base: 250ms ease-in-out;\n  --transition-slow: 350ms ease-in-out;\n\n  /* Z-index scale */\n  --z-dropdown: 1000;\n  --z-sticky: 1020;\n  --z-fixed: 1030;\n  --z-modal-backdrop: 1040;\n  --z-modal: 1050;\n  --z-popover: 1060;\n  --z-tooltip: 1070;\n\n  /* Breakpoints (for use in media queries) */\n  --breakpoint-sm: 576px;\n  --breakpoint-md: 768px;\n  --breakpoint-lg: 992px;\n  --breakpoint-xl: 1200px;\n  --breakpoint-2xl: 1400px;\n\n  /* Container max-widths */\n  --container-sm: 540px;\n  --container-md: 720px;\n  --container-lg: 960px;\n  --container-xl: 1140px;\n  --container-2xl: 1320px;\n}", "@import url('./asset/style/root.css');\n\n\n/* Font loading with available TTF formats */\n/* Font for the english */\n@font-face {\n  font-display: swap;\n  font-family: 'GWMSans';\n  font-style: normal;\n  font-weight: 400;\n  src: url('./asset/font/librefranklin-regular.ttf') format('truetype');\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'GWMSans';\n  font-style: normal;\n  font-weight: 700;\n  src: url('./asset/font/librefranklin-bold.ttf') format('truetype');\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'GWMSans';\n  font-style: normal;\n  font-weight: 800;\n  src: url('./asset/font/librefranklin-extrabold.ttf') format('truetype');\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'GWMSans';\n  font-style: normal;\n  font-weight: 900;\n  src: url('./asset/font/librefranklin-black.ttf') format('truetype');\n}\n\n/* Font for the russian */\n\n@font-face {\n  font-family: <PERSON><PERSON> Franklin;\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url('./asset/font/09f6c2baa931b883-s.woff2') format(\"woff2\");\n}\n\n@font-face {\n  font-family: Libre Franklin;\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url('./asset/font/6b4fe0bff7871930-s.p.woff2') format(\"woff2\");\n}\n\n@font-face {\n  font-family: Libre Franklin;\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url('./asset/font/064ea76c844b54f3-s.woff2') format(\"woff2\");\n}\n\n@font-face {\n  font-family: Libre Franklin;\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url('./asset/font/b0fde133b3c9bd9c-s.woff2') format(\"woff2\");\n}\n\n@font-face {\n  font-family: Libre Franklin;\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url('./asset/font/92daa4d0098aa288-s.p.woff2') format(\"woff2\");\n}\n\n@font-face {\n  font-family: Libre Franklin Fallback;\n  src: local(\"Arial\");\n  ascent-override: 92.61%;\n  descent-override: 23.58%;\n  line-gap-override: 0.00%;\n  size-adjust: 104.31%\n}\n\n*,\n:after,\n:before {\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  margin: 0;\n  font-family: var(--font-family-base);\n  font-size: var(--font-size-base);\n  line-height: var(--line-height-normal);\n  color: var(--color-neutral-900);\n  background-color: var(--color-neutral-100);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizeLegibility;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\nli {\n  list-style: none;\n}\n\n.container {\n  width: 80%;\n  margin: auto;\n}\n\na {\n  text-decoration: none;\n  color: var(--theme-color);\n}\n\na:active,\na:hover {\n  outline: 0;\n}\n\nsection {\n  padding: 50px 0px;\n  height: -webkit-max-content;\n  height: -moz-max-content;\n  height: max-content;\n}\n\nh1 {\n  text-transform: uppercase;\n  font-weight: 800;\n}\n\nh2 {\n  text-transform: uppercase;\n  font-size: 32px;\n  margin-bottom: 16px;\n}\n\np {\n  font-size: 18px;\n  line-height: 1.6;\n}\n\ninput:focus {\n  border: 1px solid var(--theme-color);\n  outline: none;\n}\n\n.container .swiper-button-prev,\n.container .swiper-button-next {\n  color: black;\n  font-size: 13px;\n}\n\n/* button  */\n.button-black,\n.button-white {\n  padding: 9pt 9pt 9pt 24px;\n  border-radius: 3px;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-sizing: content-box;\n  box-sizing: content-box;\n  cursor: pointer;\n  display: -webkit-inline-box !important;\n  display: -ms-inline-flexbox !important;\n  display: inline-flex !important;\n  min-height: 24px;\n  border: none;\n  color: #fff;\n  background-color: #000;\n}\n\n.button-white {\n  background-color: #fff;\n  color: #000;\n}\n\n.button-black span,\n.button-white span {\n  -webkit-transform: translateX(0);\n  -ms-transform: translateX(0);\n  transform: translateX(0);\n  -webkit-transition: all 0.3s ease;\n  -o-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n  display: inline-block;\n  padding-right: 2pc;\n  position: relative;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 700;\n}\n\n.button-black span::after,\n.button-white span::after {\n  content: '';\n  background-image: url('./asset/imgs/icons/arrow.svg');\n  background-position: 50%;\n  background-repeat: no-repeat;\n  background-size: contain;\n  height: 24px;\n  width: 24px;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  -webkit-transform: translateY(-50%) rotate(-45deg);\n  -ms-transform: translateY(-50%) rotate(-45deg);\n  transform: translateY(-50%) rotate(-45deg);\n  z-index: 1;\n  opacity: 1;\n  -webkit-transition: opacity 0.3s ease;\n  -o-transition: opacity 0.3s ease;\n  transition: opacity 0.3s ease;\n}\n\n.button-black:hover {\n  background: #48484a;\n}\n\n.button-white:hover {\n  background: rgba(242, 242, 247, 0.922);\n}\n\n.button-black:hover span,\n.button-white:hover span {\n  -webkit-transform: translateX(10px);\n  -ms-transform: translateX(10px);\n  transform: translateX(10px);\n}\n\n.button-black:hover span::after,\n.button-white:hover span::after {\n  opacity: 0;\n}\n\n/* link  */\n.link {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 5px;\n  color: #000;\n  text-decoration: none;\n  cursor: pointer;\n}\n\n.linkIcon {\n  width: 20px;\n  -webkit-transition: -webkit-transform 0.3s ease;\n  transition: -webkit-transform 0.3s ease;\n  -o-transition: transform 0.3s ease;\n  transition: transform 0.3s ease;\n  transition: transform 0.3s ease, -webkit-transform 0.3s ease;\n}\n\n/* Анимация при наведении */\n.link:hover .linkIcon {\n  -webkit-transform: translateX(5px);\n  -ms-transform: translateX(5px);\n  transform: translateX(5px);\n}\n\n.topmenu {\n  margin-top: 70px;\n}\n\n/* page loader */\n.loaderWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  height: 100vh;\n  background: #fff;\n  /* или любой цвет */\n  z-index: 9999;\n}\n\n.loaderPage {\n  width: 60px;\n  height: 60px;\n  border: 6px solid var(--theme-color);\n  border-top: 6px solid #fff;\n  border-radius: 50%;\n  -webkit-animation: spin 0.8s linear infinite;\n  animation: spin 0.8s linear infinite;\n}\n\n@-webkit-keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n/* main  */\nmain {\n  overflow-y: hidden;\n}\n\n.content {\n  padding-top: 40px;\n}\n\n.title {\n  font-size: 60px;\n  margin-bottom: 40px;\n  font-weight: 700;\n}\n\n.underText {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 2rem;\n  display: block;\n  margin-bottom: 10px;\n}\n\n.title {\n  font-size: 60px;\n  margin-bottom: 40px;\n  font-weight: 700;\n}\n\n.redLine {\n  width: 100px;\n  height: 6px;\n  background-color: var(--theme-color);\n  display: block;\n  margin-bottom: 40px;\n}\n\n@media screen and (max-width: 1200px) {\n  p {\n    font-size: 16px;\n  }\n}\n\n@media screen and (max-width: 1080px) {\n  .title {\n    font-size: 35px;\n    margin-bottom: 40px;\n  }\n\n  .underText {\n    font-size: 18px;\n    line-height: 1.8rem;\n    width: 100%;\n  }\n}\n\n@media (min-width: 1536px) {\n  .container {\n    max-width: 1536px;\n  }\n}\n\n@media screen and (max-width: 900px) {\n  .container {\n    width: 90%;\n  }\n\n  .content {\n    padding: 20px 0px;\n  }\n}\n\n@media (max-width: 768px) {\n  p {\n    font-size: 1rem;\n  }\n\n  .underText {\n    width: 100%;\n  }\n\n  .title {\n    font-size: 30px;\n    margin-bottom: 12px;\n  }\n\n  .underText {\n    font-size: 18px;\n    line-height: 1.8rem;\n    width: 100%;\n  }\n\n  .topmenu {\n    margin-top: 68px;\n  }\n}", ":root {\n  --color-white: #fff;\n  --color-black: #000;\n  --color-gray-light: rgb(209, 209, 214);\n  --color-gray-medium: rgb(99, 99, 102);\n  --color-gray-dark: rgb(72, 72, 74);\n  --color-text-light: #999;\n  --theme-color: #e60012;\n}\n\n.nav {\n  background-color: transparent;\n  position: fixed;\n  top: 0;\n  width: 100%;\n  z-index: 100;\n  left: 0;\n  right: 0;\n  color: var(--color-white);\n  padding: 13px 0px;\n  -webkit-transition:\n    background-color 0.3s ease,\n    color 0.3s ease;\n  -o-transition:\n    background-color 0.3s ease,\n    color 0.3s ease;\n  transition:\n    background-color 0.3s ease,\n    color 0.3s ease;\n}\n\n.nav.active {\n  background-color: var(--color-white);\n  border-bottom: 1px solid var(--color-gray-light);\n  color: var(--color-black);\n}\n\n.container {\n  width: 90%;\n  margin: auto;\n}\n\n.navPC {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n}\n\n.left {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 40px;\n}\n\n.logoWhite {\n  -webkit-filter: invert(1);\n  filter: invert(1);\n  width: 70px;\n  display: none;\n}\n\n.PclogoWhite {\n  -webkit-filter: invert(0);\n  filter: invert(0);\n  display: block;\n  width: 150px;\n  -o-object-fit: contain;\n  object-fit: contain;\n}\n\n.nav.active .PclogoWhite {\n  -webkit-filter: invert(1);\n  filter: invert(1);\n}\n\n.nav.active .logoWhite {\n  -webkit-filter: invert(0);\n  filter: invert(0);\n}\n\n.menuList {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 25px;\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.menuLink {\n  text-transform: uppercase;\n  font-size: 14px;\n  font-weight: 600;\n  color: inherit;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 4px;\n  text-decoration: none;\n  cursor: pointer;\n}\n\n.menuLink:hover {\n  color: var(--theme-color);\n  -webkit-transition: color 0.3s ease;\n  -o-transition: color 0.3s ease;\n  transition: color 0.3s ease;\n}\n\n.arrow {\n  font-size: 24px;\n  font-weight: 600;\n  -webkit-transition: -webkit-transform 0.2s ease;\n  transition: -webkit-transform 0.2s ease;\n  -o-transition: transform 0.2s ease;\n  transition: transform 0.2s ease;\n  transition:\n    transform 0.2s ease,\n    -webkit-transform 0.2s ease;\n}\n\n.arrowUp {\n  -webkit-transform: rotate(-180deg);\n  -ms-transform: rotate(-180deg);\n  transform: rotate(-180deg);\n}\n\n.right {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  gap: 20px;\n}\n\n.featureItem {\n  position: relative;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 6px;\n  color: inherit;\n  font-size: 13px;\n  font-weight: 500;\n  padding: 6px 8px;\n  border: 1px solid var(--color-white);\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.featureItem a {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 6px;\n  color: #fff;\n  position: relative;\n}\n\n.nav.active .featureItem {\n  border: 1px solid var(--color-black);\n}\n\n.featureItem:hover .tooltip {\n  opacity: 1;\n  visibility: visible;\n  -webkit-transform: translate(-50%, 10px);\n  -ms-transform: translate(-50%, 10px);\n  transform: translate(-50%, 10px);\n}\n\n.tooltip {\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  -webkit-transform: translate(-50%, 0);\n  -ms-transform: translate(-50%, 0);\n  transform: translate(-50%, 0);\n  background-color: var(--color-white);\n  color: var(--color-black);\n  padding: 6px 10px;\n  font-size: 12px;\n  font-weight: 400;\n  border-radius: 4px;\n  white-space: nowrap;\n  opacity: 0;\n  visibility: hidden;\n  -webkit-transition: all 0.2s ease;\n  -o-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n  z-index: 102;\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n}\n\n.tooltip::after {\n  content: '';\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: transparent transparent var(--color-white) transparent;\n}\n\n.featureIcon {\n  height: 24px;\n  width: 24px;\n  -webkit-filter: invert(1);\n  filter: invert(1);\n  -webkit-transition: color 0.3s;\n  -o-transition: color 0.3s;\n  transition: color 0.3s;\n}\n\n.nav.active .featureIcon {\n  -webkit-filter: invert(0);\n  filter: invert(0);\n}\n\n.mobMenuIcon {\n  display: none;\n}\n\n.mobMenu {\n  display: none;\n}\n\n@media screen and (max-width: 990px) {\n  .menuLink {\n    font-weight: 500;\n    font-size: 13px;\n  }\n\n  .logoWhite {\n    display: inline;\n  }\n\n  .PclogoWhite {\n    display: none;\n  }\n}\n\n@media screen and (max-width: 900px) {\n  .nav {\n    padding: 13px 0px;\n  }\n\n  .left {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    gap: 20px;\n  }\n\n  .logoWhite {\n    width: 45px;\n  }\n\n  .tooltip {\n    display: none;\n  }\n\n  .mobMenuIcon {\n    display: inline;\n  }\n\n  .menuList {\n    display: none;\n  }\n\n  .mobMenu {\n    height: 0%;\n    left: 0px;\n    position: fixed;\n    top: 0px;\n    -webkit-transition: all 0.3s cubic-bezier(0.48, 0.04, 0.52, 0.96);\n    -o-transition: all 0.3s cubic-bezier(0.48, 0.04, 0.52, 0.96);\n    transition: all 0.3s cubic-bezier(0.48, 0.04, 0.52, 0.96);\n    width: 100%;\n    z-index: 101;\n    background: var(--color-black);\n    overflow: hidden;\n    display: inline;\n  }\n\n  .mobMenu.show {\n    height: 100%;\n  }\n\n  .mobMenuFixed {\n    height: 100vh;\n    left: 0px;\n    position: absolute;\n    top: 0px;\n    width: 100%;\n    z-index: 99;\n  }\n\n  .blackBar {\n    position: relative;\n    width: 100%;\n    z-index: 10;\n    background: var(--color-black);\n    border-bottom: 1px solid var(--color-gray-medium);\n    padding: 13px 0px;\n  }\n\n  .contentBlackBar {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    gap: 20px;\n  }\n\n  .mobMenuIcon {\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    background-color: transparent;\n    cursor: pointer;\n    display: -webkit-inline-box;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n    border-radius: 3px;\n    padding: 8px;\n  }\n\n  .mobMenuIconClose {\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    background-color: var(--color-white);\n    cursor: pointer;\n    display: -webkit-inline-box;\n    display: -ms-inline-flexbox;\n    display: inline-flex;\n    border-radius: 3px;\n    padding: 8px;\n  }\n\n  .mobMenuContent {\n    padding: 4pc 24px;\n    color: var(--color-white);\n  }\n\n  .mobMenuList ul {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-pack: start;\n    -ms-flex-pack: start;\n    justify-content: flex-start;\n  }\n\n  .mobMenuList ul li {\n    color: var(--color-white);\n    margin-bottom: 24px;\n    width: 100%;\n    padding: 10px 8px;\n  }\n\n  .mobMenuList ul li .menuLink {\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: justify;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    font-size: 18px;\n    font-weight: 700;\n    color: var(--color-white);\n  }\n\n  .mobFetureBtns {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap;\n    -webkit-box-pack: start;\n    -ms-flex-pack: start;\n    justify-content: flex-start;\n    margin-top: 56px;\n  }\n\n  .mobFetureBtns .featureItem {\n    color: var(--color-white);\n    margin-bottom: 24px;\n    width: 100%;\n    border: 1px solid var(--color-gray-dark);\n    border-radius: 4px;\n    padding: 12px;\n    font-size: 16px;\n    line-height: 24px;\n  }\n\n  .right {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    gap: 10px;\n  }\n}\n", ":root {\n  --border-color-nav: #d1d1d6;\n  --background-white: #fff;\n  --background-black: #000;\n  --text-black: #000;\n  --text-white: #fff;\n  --text-muted: #666;\n  --theme-color: #d7000f;\n  --stock-green: #1ab01a;\n  --card-border: #ccc;\n  --card-hover-shadow: rgba(0, 0, 0, 0.1);\n}\n\n/* Общая обёртка */\n.overlay {\n  height: 0;\n  left: 0px;\n  position: fixed;\n  border-top: 1px solid var(--border-color-nav);\n  top: 78px;\n  background-color: var(--background-white);\n  width: 100%;\n  z-index: 101;\n  overflow: hidden;\n  -webkit-transition: height 0.4s ease;\n  -o-transition: height 0.4s ease;\n  transition: height 0.4s ease;\n}\n\n.overlay.show {\n  height: calc(100% - 78px);\n}\n\n/* Содержимое */\n.content {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  height: 100%;\n}\n\n/* side */\n.sidebar {\n  border-right: 1px solid var(--border-color-nav);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  overflow-y: auto;\n  padding: 24px;\n  width: 240px;\n}\n\n.services h4,\n.setting h4 {\n  font-size: 1pc;\n  line-height: 24px;\n  margin-bottom: 1pc;\n}\n\n.types {\n  border-bottom: 1.5px solid var(--border-color-nav);\n  margin-bottom: 8px;\n  padding-bottom: 8px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n}\n\n.bodyTypes li,\n.types li {\n  border: 1px solid transparent;\n  border-radius: 3px;\n  cursor: pointer;\n  margin-bottom: 8px;\n  padding: 8px;\n  width: 100%;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 700;\n  position: relative;\n}\n\n.bodyTypes li:hover,\n.types li:hover {\n  border: 1px solid var(--text-black);\n}\n\n.bodyTypes li.active,\n.types li.active {\n  background-color: var(--background-black);\n  color: var(--text-white);\n  padding-left: 24px;\n}\n\n.bodyTypes li.active:before,\n.types li.active:before {\n  background: var(--theme-color);\n  border-radius: 1px;\n  content: '';\n  height: 8px;\n  left: 8px;\n  position: absolute;\n  top: 14px;\n  width: 8px;\n  z-index: 1;\n}\n\n.services {\n  margin-top: 90px;\n}\n\n.services ul li {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  cursor: pointer;\n  margin-bottom: 8px;\n  padding-top: 8px;\n  width: 100%;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 700;\n}\n\n.services ul li a {\n  color: var(--text-black);\n}\n\n/* main */\n.main {\n  -webkit-box-flex: 1;\n  -ms-flex-positive: 1;\n  flex-grow: 1;\n  padding: 24px;\n  overflow-y: auto;\n}\n\n.tabs {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.grid {\n  margin-top: 56px;\n  display: -ms-grid;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));\n  gap: 20px;\n}\n\n.card {\n  border: 1px solid var(--card-border);\n  padding: 20px;\n  border-radius: 10px;\n  -webkit-transition: -webkit-box-shadow 0.3s ease;\n  transition: -webkit-box-shadow 0.3s ease;\n  -o-transition: box-shadow 0.3s ease;\n  transition: box-shadow 0.3s ease;\n  transition: box-shadow 0.3s ease, -webkit-box-shadow 0.3s ease;\n  background: var(--background-white);\n  position: relative;\n}\n\n.card:hover {\n  -webkit-box-shadow: 0 4px 10px var(--card-hover-shadow);\n  box-shadow: 0 4px 10px var(--card-hover-shadow);\n}\n\n.card img {\n  width: 100%;\n  height: auto;\n  -o-object-fit: cover;\n  object-fit: cover;\n}\n\n.cardInfo {\n  margin-top: 10px;\n}\n.cardInfo h4 {\n  color: #000;\n  font-size: 16px;\n}\n.label {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  right: 20px;\n}\n\n.noStok,\n.inStok {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  left: 30px;\n}\n\n.noStok::before,\n.inStok::before {\n  content: '';\n  height: 6px;\n  width: 6px;\n  border-radius: 50%;\n  top: 4px;\n  left: -10px;\n  position: absolute;\n  background-color: var(--stock-green);\n}\n\n.noStok::before {\n  background-color: var(--theme-color);\n}\n\n.desc {\n  margin-top: 10px;\n  font-size: 13px;\n  color: var(--text-black);\n}\n\n.noCarText {\n  padding: 20px;\n  font-size: 18px;\n  text-align: center;\n  line-height: 1.5;\n  color: var(--text-muted);\n  /* position: absolute; */\n}\n\n.filterBar {\n  display: none;\n}\n\n/* Мобильная версия */\n@media screen and (max-width: 900px) {\n  .sidebar {\n    display: none;\n  }\n\n  .overlay {\n    top: auto;\n    bottom: 0;\n    border-top: none;\n    border-bottom: 1px solid var(--border-color-nav);\n    height: 0;\n    -webkit-transition: height 0.4s ease;\n    -o-transition: height 0.4s ease;\n    transition: height 0.4s ease;\n  }\n\n  .overlay.show {\n    height: calc(100% - 113px);\n  }\n\n  .grid {\n    margin-top: 28px;\n  }\n\n  .filterBar {\n    display: block;\n    border: 1.5px solid var(--border-color);\n    border-radius: 8px;\n    padding: 0px 2pc;\n    margin-bottom: 20px;\n  }\n\n  .filterSelected {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: justify;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    color: var(--text-black);\n    padding: 24px 0;\n  }\n\n  .filterSelected span {\n    font-size: 18px;\n    font-weight: 700;\n    line-height: 22px;\n  }\n\n  .iconPlus,\n  .iconMunes {\n    font-size: 20px;\n    line-height: 22px;\n  }\n\n  .filterDropdownContainer {\n    -webkit-transform: translateY(10px);\n    -ms-transform: translateY(10px);\n    transform: translateY(10px);\n    opacity: 0;\n    -webkit-transition: all 0.3s ease;\n    -o-transition: all 0.3s ease;\n    transition: all 0.3s ease;\n    height: 0;\n    overflow: hidden;\n  }\n\n  .filterDropdownContainer.show {\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n    opacity: 1;\n    height: 100%;\n  }\n}\n", "/**\n * Swiper 11.2.6\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2025 Vladimir <PERSON>harlampidi\n *\n * Released under the MIT License\n *\n * Released on: March 19, 2025\n */\n\n/* FONT_START */\n@font-face {\n  font-family: 'swiper-icons';\n  src: url('data:application/font-woff;charset=utf-8;base64, 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');\n  font-weight: 400;\n  font-style: normal;\n}\n/* FONT_END */\n:root {\n  --swiper-theme-color: #007aff;\n  /*\n  --swiper-preloader-color: var(--swiper-theme-color);\n  --swiper-wrapper-transition-timing-function: initial;\n  */\n}\n:host {\n  position: relative;\n  display: block;\n  margin-left: auto;\n  margin-right: auto;\n  z-index: 1;\n}\n.swiper {\n  margin-left: auto;\n  margin-right: auto;\n  position: relative;\n  overflow: hidden;\n  list-style: none;\n  padding: 0;\n  /* Fix of Webkit flickering */\n  z-index: 1;\n  display: block;\n}\n.swiper-vertical > .swiper-wrapper {\n  flex-direction: column;\n}\n.swiper-wrapper {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  display: flex;\n  transition-property: transform;\n  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);\n  box-sizing: content-box;\n}\n.swiper-android .swiper-slide,\n.swiper-ios .swiper-slide,\n.swiper-wrapper {\n  transform: translate3d(0px, 0, 0);\n}\n.swiper-horizontal {\n  touch-action: pan-y;\n}\n.swiper-vertical {\n  touch-action: pan-x;\n}\n.swiper-slide {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  position: relative;\n  transition-property: transform;\n  display: block;\n}\n.swiper-slide-invisible-blank {\n  visibility: hidden;\n}\n/* Auto Height */\n.swiper-autoheight,\n.swiper-autoheight .swiper-slide {\n  height: auto;\n}\n.swiper-autoheight .swiper-wrapper {\n  align-items: flex-start;\n  transition-property: transform, height;\n}\n.swiper-backface-hidden .swiper-slide {\n  transform: translateZ(0);\n  -webkit-backface-visibility: hidden;\n          backface-visibility: hidden;\n}\n/* 3D Effects */\n.swiper-3d.swiper-css-mode .swiper-wrapper {\n  perspective: 1200px;\n}\n.swiper-3d .swiper-wrapper {\n  transform-style: preserve-3d;\n}\n.swiper-3d {\n  perspective: 1200px;\n}\n.swiper-3d .swiper-slide,\n.swiper-3d .swiper-cube-shadow {\n  transform-style: preserve-3d;\n}\n/* CSS Mode */\n.swiper-css-mode > .swiper-wrapper {\n  overflow: auto;\n  scrollbar-width: none;\n  /* For Firefox */\n  -ms-overflow-style: none;\n  /* For Internet Explorer and Edge */\n}\n.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {\n  display: none;\n}\n.swiper-css-mode > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: start start;\n}\n.swiper-css-mode.swiper-horizontal > .swiper-wrapper {\n  scroll-snap-type: x mandatory;\n}\n.swiper-css-mode.swiper-vertical > .swiper-wrapper {\n  scroll-snap-type: y mandatory;\n}\n.swiper-css-mode.swiper-free-mode > .swiper-wrapper {\n  scroll-snap-type: none;\n}\n.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: none;\n}\n.swiper-css-mode.swiper-centered > .swiper-wrapper::before {\n  content: '';\n  flex-shrink: 0;\n  order: 9999;\n}\n.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: center center;\n  scroll-snap-stop: always;\n}\n.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {\n  margin-inline-start: var(--swiper-centered-offset-before);\n}\n.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {\n  height: 100%;\n  min-height: 1px;\n  width: var(--swiper-centered-offset-after);\n}\n.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {\n  margin-block-start: var(--swiper-centered-offset-before);\n}\n.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {\n  width: 100%;\n  min-width: 1px;\n  height: var(--swiper-centered-offset-after);\n}\n/* Slide styles start */\n/* 3D Shadows */\n.swiper-3d .swiper-slide-shadow,\n.swiper-3d .swiper-slide-shadow-left,\n.swiper-3d .swiper-slide-shadow-right,\n.swiper-3d .swiper-slide-shadow-top,\n.swiper-3d .swiper-slide-shadow-bottom,\n.swiper-3d .swiper-slide-shadow,\n.swiper-3d .swiper-slide-shadow-left,\n.swiper-3d .swiper-slide-shadow-right,\n.swiper-3d .swiper-slide-shadow-top,\n.swiper-3d .swiper-slide-shadow-bottom {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 10;\n}\n.swiper-3d .swiper-slide-shadow {\n  background: rgba(0, 0, 0, 0.15);\n}\n.swiper-3d .swiper-slide-shadow-left {\n  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-right {\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-top {\n  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-bottom {\n  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-lazy-preloader {\n  width: 42px;\n  height: 42px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin-left: -21px;\n  margin-top: -21px;\n  z-index: 10;\n  transform-origin: 50%;\n  box-sizing: border-box;\n  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));\n  border-radius: 50%;\n  border-top-color: transparent;\n}\n.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,\n.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {\n  animation: swiper-preloader-spin 1s infinite linear;\n}\n.swiper-lazy-preloader-white {\n  --swiper-preloader-color: #fff;\n}\n.swiper-lazy-preloader-black {\n  --swiper-preloader-color: #000;\n}\n@keyframes swiper-preloader-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n/* Slide styles end */\n", ".btn {\n  font-size: 16px;\n  line-height: 1.2;\n  color: #636366;\n  cursor: pointer;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  padding: 0 12px;\n  font-weight: 600;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n  border-bottom: 4px solid transparent;\n  text-transform: uppercase;\n  height: 52px;\n  min-width: 100px;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  white-space: nowrap;\n  -webkit-transition: all 0.3s ease;\n  -o-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n}\n\n.active {\n  color: #000;\n  border-bottom: 4px solid #000;\n}\n\n.activeBar {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background-color: #d1d1d6;\n  z-index: 0;\n}\n@media screen and (max-width: 900px) {\n  .btn {\n    font-size: 14px;\n    height: 44px;\n    min-width: 88px;\n    padding: 0 10px;\n  }\n}\n\n@media screen and (max-width: 580px) {\n  .btn {\n    font-size: 13px;\n    height: 42px;\n    min-width: 76px;\n    padding: 0 8px;\n  }\n}\n", "/* SkeletonCard.module.css */\n.skeletonCard {\n  background-color: #fff;\n  border-radius: 12px;\n  padding: 16px;\n  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  gap: 12px;\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n.image {\n  width: 100%;\n  height: 180px;\n  background: #e0e0e0;\n  border-radius: 8px;\n}\n\n.title {\n  width: 60%;\n  height: 16px;\n  background: #e0e0e0;\n  border-radius: 4px;\n}\n\n.badge {\n  width: 40%;\n  height: 14px;\n  background: #e0e0e0;\n  border-radius: 6px;\n}\n\n@-webkit-keyframes pulse {\n  0% {\n    background-color: #f0f0f0;\n  }\n  50% {\n    background-color: #e0e0e0;\n  }\n  100% {\n    background-color: #f0f0f0;\n  }\n}\n\n@keyframes pulse {\n  0% {\n    background-color: #f0f0f0;\n  }\n  50% {\n    background-color: #e0e0e0;\n  }\n  100% {\n    background-color: #f0f0f0;\n  }\n}\n", ":root {\n  --overlay-bg-dark: rgba(0, 0, 0, 0.88);\n  --overlay-bg-light: white;\n  --column-border-color: #d1d1d6;\n  --column-text-color: #636366;\n}\n\n.overlay {\n  position: fixed;\n  top: 78px; /* высота navbar */\n  left: 0;\n  width: 100%;\n  height: calc(100vh - 78px); /* остальная высота */\n  background: var(--overlay-bg-dark); /* тёмный прозрачный фон */\n  z-index: 101;\n  opacity: 0;\n  -webkit-transform: translateY(-2%);\n  -ms-transform: translateY(-2%);\n  transform: translateY(-2%);\n  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.4s ease;\n  transition: opacity 0.4s ease, -webkit-transform 0.4s ease;\n  -o-transition: transform 0.4s ease, opacity 0.4s ease;\n  transition: transform 0.4s ease, opacity 0.4s ease;\n  transition: transform 0.4s ease, opacity 0.4s ease,\n    -webkit-transform 0.4s ease;\n  pointer-events: none;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  padding: 0;\n}\n\n.show {\n  -webkit-transform: translateY(0);\n  -ms-transform: translateY(0);\n  transform: translateY(0);\n  opacity: 1;\n  pointer-events: auto;\n}\n\n.menuWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  width: 100%;\n  height: -webkit-max-content;\n  height: -moz-max-content;\n  height: max-content;\n  background-color: var(--overlay-bg-light);\n  padding: 0px 60px;\n  border-top: 1px solid var(--column-border-color);\n  overflow: hidden;\n}\n\n.content {\n  padding: 0 56px;\n  width: 100%;\n  max-width: 75pc;\n  padding: 0;\n}\n\n.column {\n  padding: 24px 0 1pc;\n  position: relative;\n}\n\n.column::before {\n  background-color: var(--column-border-color);\n  content: '';\n  height: 100%;\n  position: absolute;\n  top: 0;\n  width: 1px;\n  z-index: 1;\n}\n\n.column h4 {\n  font-size: 14px;\n  margin-bottom: 20px;\n  font-weight: 600;\n  text-transform: uppercase;\n  color: var(--column-text-color);\n  padding-left: 24px;\n}\n\n.column ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  padding-left: 24px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  gap: 60px;\n}\n\n.column li {\n  margin-bottom: 10px;\n  font-size: 14px;\n  font-weight: 700;\n  cursor: pointer;\n  -webkit-transition: color 0.2s;\n  -o-transition: color 0.2s;\n  transition: color 0.2s;\n}\n\n@media screen and (max-width: 900px) {\n  .overlay {\n    position: fixed;\n    top: auto;\n    bottom: 0;\n    border-top: none;\n    left: 0;\n    width: 100%;\n    height: 0;\n    -webkit-transition: height 0.4s ease;\n    -o-transition: height 0.4s ease;\n    transition: height 0.4s ease;\n    background-color: var(--overlay-bg-light);\n    z-index: 101;\n  }\n\n  .menuWrapper {\n    padding: 4pc 24px;\n  }\n\n  .overlay.show {\n    height: calc(100% - 113px);\n  }\n\n  .column h4 {\n    padding-left: 0px;\n  }\n\n  .column ul {\n    padding-left: 0px;\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .column::before {\n    display: none;\n  }\n\n  .column {\n    padding: 24px 0 1pc;\n    position: relative;\n  }\n\n  .content {\n    border-bottom: 1px solid var(--column-border-color);\n  }\n}\n", ":root {\n  --primary-bar-bg: #000;\n  --primary-bar-text: #fff;\n}\n\n.primaryBar {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  background-color: var(--primary-bar-bg);\n  position: fixed;\n  top: 69px; /* под navbar */\n  left: 0;\n  width: 100%;\n  height: 0;\n  overflow: hidden; /* скрывает содержимое при высоте 0 */\n  padding: 0 8px; /* padding-top появится при раскрытии */\n  -webkit-transition: height 0.4s ease;\n  -o-transition: height 0.4s ease;\n  transition: height 0.4s ease;\n  visibility: hidden;\n  pointer-events: none;\n  z-index: 101;\n}\n\n.primaryBar.show {\n  height: 44px; /* желаемая высота */\n  visibility: visible;\n  pointer-events: all;\n}\n\n.primaryBar span {\n  color: var(--primary-bar-text);\n  margin-left: 8px;\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.icon {\n  color: var(--primary-bar-text);\n  font-size: 18px;\n  font-weight: 700;\n}\n\n/* Только для мобилки */\n@media (min-width: 901px) {\n  .primaryBar {\n    display: none;\n  }\n}\n", ":root {\n  --color-bg: #000;\n  --color-text: #fff;\n  --color-muted: #3a3a3c;\n  --color-link-hover: var(--theme-color, #e50914); /* fallback */\n  --spacing-lg: 5rem;\n  --spacing-md: 2rem;\n  --spacing-sm: 1rem;\n  --spacing-xs: 0.5rem;\n  --border-color: #3a3a3c;\n}\n/* Общий стиль футера */\nfooter {\n  background-color: var(--color-bg);\n  color: var(--color-text);\n}\n.iconWrapper {\n  font-size: 24px; /* или больше при необходимости */\n  color: white;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n}\n\n.social a {\n  margin-right: 10px;\n  display: -webkit-inline-box;\n  display: -ms-inline-flexbox;\n  display: inline-flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n}\n\n.content {\n  padding-top: var(--spacing-lg);\n  padding-bottom: var(--spacing-lg);\n}\n.logo img {\n  width: 151px;\n  height: auto;\n  margin-bottom: 2rem;\n  cursor: pointer;\n}\n\n/* --- Кнопки --- */\n.btnsList {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  margin-left: -20px;\n  margin-bottom: 1.75rem;\n}\n.btnItem {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  background-color: var(--color-muted);\n  border-radius: 8px;\n  height: 4.5rem;\n  margin-left: 1.5rem;\n  margin-bottom: 1.5rem;\n  padding: 0 3.4rem 0 1.5rem;\n  -webkit-box-flex: 1;\n  -ms-flex: 1 0 auto;\n  flex: 1 0 auto;\n  -webkit-transition: background-color 0.3s ease;\n  -o-transition: background-color 0.3s ease;\n  transition: background-color 0.3s ease;\n}\n.btnItem:hover {\n  background-color: #505050;\n}\n.item {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  position: relative;\n  width: 100%;\n  height: 100%;\n  padding-right: 2rem;\n}\n.item img {\n  width: 24px;\n  height: 24px;\n  margin-right: 0.5rem;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n}\n.item span {\n  font-size: 0.875rem;\n  font-weight: 700;\n  color: var(--color-text);\n}\n.item::after {\n  content: '';\n  background-image: url('../../asset/imgs/icons/arrow.svg');\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  width: 24px;\n  height: 24px;\n  -webkit-transform: translateY(-50%) translateX(0);\n  -ms-transform: translateY(-50%) translateX(0);\n  transform: translateY(-50%) translateX(0);\n  -webkit-transition: -webkit-transform 0.3s ease;\n  transition: -webkit-transform 0.3s ease;\n  -o-transition: transform 0.3s ease;\n  transition: transform 0.3s ease;\n  transition: transform 0.3s ease, -webkit-transform 0.3s ease;\n  z-index: 1;\n}\n\n.btnItem:hover .item::after {\n  -webkit-transform: translateY(-50%) translateX(6px);\n  -ms-transform: translateY(-50%) translateX(6px);\n  transform: translateY(-50%) translateX(6px); /* Смещаем вправо на 6px */\n}\n/* --- Меню --- */\n.menu {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  width: 100%;\n}\n.menuContainer {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -webkit-box-flex: 1;\n  -ms-flex-positive: 1;\n  flex-grow: 1;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n}\n.menuItem {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  width: calc(25% - 18px);\n  margin-bottom: 1.5rem;\n  margin-right: 1rem;\n}\n.menuItemTitle {\n  color: var(--color-text);\n  margin-bottom: 1.125rem;\n  font-size: 1rem;\n  font-weight: 700;\n  -webkit-transition: color 0.3s;\n  -o-transition: color 0.3s;\n  transition: color 0.3s;\n}\n.menuLinks {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  gap: 0.625rem;\n  overflow: hidden;\n}\n.menuLinks a {\n  color: var(--color-text);\n  font-size: 0.875rem;\n  font-weight: 700;\n  line-height: 1.25rem;\n  -webkit-transition: color 0.3s;\n  -o-transition: color 0.3s;\n  transition: color 0.3s;\n}\n.menuLinks a:hover,\n.menuLinks a:focus {\n  color: var(--color-link-hover);\n  outline: none;\n}\n\n/* --- Соцсети и прочее --- */\n.related {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  padding-bottom: 1rem;\n  margin: 2rem 0;\n  border-top: 1px solid var(--border-color);\n  border-bottom: 1px solid var(--border-color);\n}\n.social {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 1.25rem;\n  padding-top: 1rem;\n  margin-right: 2.25rem;\n}\n.social a {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n}\n.social a img {\n  width: 24px;\n  height: 24px;\n}\n.copy,\n.legacy {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  padding-top: 1rem;\n  margin-right: 2.25rem;\n}\n.legacy a {\n  color: var(--color-text);\n  font-size: 0.875rem;\n  font-weight: 600;\n  -webkit-transition: color 0.3s ease;\n  -o-transition: color 0.3s ease;\n  transition: color 0.3s ease;\n}\n.copy {\n  font-size: 0.75rem;\n  color: var(--color-text);\n}\n.legacy a:hover {\n  color: var(--theme-color);\n}\n.vector {\n  padding-top: 1rem;\n  margin-right: 2.25rem;\n}\n.vector img {\n  width: 151px;\n  height: auto;\n}\n\n/* --- Описание --- */\n.desc {\n  padding: 1rem 0;\n}\n.desc span {\n  font-size: 0.75rem;\n  line-height: 1.125rem;\n  color: var(--color-text);\n  font-weight: 400;\n  text-align: justify;\n}\n\n.menuLinks {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.collapsible {\n  overflow: hidden;\n  max-height: 0;\n  -webkit-transition: max-height 0.4s ease, opacity 0.3s ease;\n  -o-transition: max-height 0.4s ease, opacity 0.3s ease;\n  transition: max-height 0.4s ease, opacity 0.3s ease;\n  opacity: 0;\n}\n\n.open {\n  opacity: 1;\n}\n\n/* --- Мобильная версия --- */\n@media (max-width: 900px) {\n  .content {\n    padding-top: 1.5rem;\n    padding-bottom: 1.5rem;\n  }\n\n  .menuContainer {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n  }\n\n  .menuItem {\n    width: 100%;\n    margin-right: 0;\n    margin-bottom: 0rem;\n  }\n\n  .menuItemTitle {\n    width: 100%;\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: justify;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    cursor: pointer;\n    padding: 0.75rem 0;\n    font-size: 1.125rem;\n    margin-bottom: 0px;\n  }\n\n  .menuLinks {\n    width: 100%;\n    padding: 0.625rem 0;\n  }\n\n  .menuLinks a {\n    font-size: 1rem;\n    padding: 10px 0px;\n  }\n\n  .btnsList {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    margin-left: 0;\n  }\n\n  .btnItem {\n    width: 100%;\n    margin-left: 0;\n  }\n\n  .related {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n    gap: 0.75rem;\n  }\n\n  .copy,\n  .legacy {\n    margin-right: 0;\n    padding-top: 0.5rem;\n  }\n}\n", ".errorBoundary {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-8);\n  background-color: var(--color-neutral-50);\n}\n\n.errorContainer {\n  max-width: 600px;\n  width: 100%;\n  text-align: center;\n  background-color: var(--color-neutral-100);\n  border-radius: var(--border-radius-lg);\n  padding: var(--space-12);\n  box-shadow: var(--shadow-lg);\n  border: var(--border-width-1) solid var(--color-neutral-300);\n}\n\n.errorIcon {\n  color: var(--color-error);\n  margin-bottom: var(--space-6);\n  display: flex;\n  justify-content: center;\n}\n\n.errorTitle {\n  font-size: var(--font-size-3xl);\n  font-weight: var(--font-weight-bold);\n  color: var(--color-neutral-900);\n  margin: 0 0 var(--space-4) 0;\n  line-height: var(--line-height-tight);\n}\n\n.errorMessage {\n  font-size: var(--font-size-lg);\n  color: var(--color-neutral-600);\n  margin: 0 0 var(--space-6) 0;\n  line-height: var(--line-height-normal);\n}\n\n.errorId {\n  font-size: var(--font-size-sm);\n  color: var(--color-neutral-500);\n  margin: 0 0 var(--space-8) 0;\n  padding: var(--space-3);\n  background-color: var(--color-neutral-200);\n  border-radius: var(--border-radius-md);\n  border: var(--border-width-1) solid var(--color-neutral-300);\n}\n\n.errorId code {\n  font-family: var(--font-family-mono);\n  font-size: var(--font-size-sm);\n  color: var(--color-neutral-700);\n  background-color: transparent;\n  padding: 0;\n}\n\n.errorActions {\n  display: flex;\n  gap: var(--space-4);\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-8);\n}\n\n.button {\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--border-radius-md);\n  font-size: var(--font-size-base);\n  font-weight: var(--font-weight-medium);\n  font-family: var(--font-family-base);\n  cursor: pointer;\n  transition: var(--transition-fast);\n  border: var(--border-width-1) solid transparent;\n  min-width: 140px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.button:focus {\n  outline: 2px solid var(--color-primary);\n  outline-offset: 2px;\n}\n\n.buttonPrimary {\n  background-color: var(--color-primary);\n  color: var(--color-neutral-100);\n  border-color: var(--color-primary);\n}\n\n.buttonPrimary:hover {\n  background-color: var(--color-primary-hover);\n  border-color: var(--color-primary-hover);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.buttonPrimary:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow-sm);\n}\n\n.buttonSecondary {\n  background-color: var(--color-neutral-100);\n  color: var(--color-neutral-700);\n  border-color: var(--color-neutral-400);\n}\n\n.buttonSecondary:hover {\n  background-color: var(--color-neutral-200);\n  border-color: var(--color-neutral-500);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.buttonSecondary:active {\n  transform: translateY(0);\n  box-shadow: var(--shadow-sm);\n}\n\n.errorDetails {\n  margin-top: var(--space-8);\n  text-align: left;\n  border: var(--border-width-1) solid var(--color-neutral-300);\n  border-radius: var(--border-radius-md);\n  background-color: var(--color-neutral-50);\n}\n\n.errorDetailsSummary {\n  padding: var(--space-4);\n  font-weight: var(--font-weight-medium);\n  cursor: pointer;\n  background-color: var(--color-neutral-200);\n  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;\n  transition: var(--transition-fast);\n}\n\n.errorDetailsSummary:hover {\n  background-color: var(--color-neutral-300);\n}\n\n.errorDetailsContent {\n  padding: var(--space-4);\n}\n\n.errorDetailsContent h3 {\n  font-size: var(--font-size-base);\n  font-weight: var(--font-weight-semibold);\n  color: var(--color-neutral-800);\n  margin: var(--space-4) 0 var(--space-2) 0;\n}\n\n.errorDetailsContent h3:first-child {\n  margin-top: 0;\n}\n\n.errorStack {\n  background-color: var(--color-neutral-900);\n  color: var(--color-neutral-100);\n  padding: var(--space-4);\n  border-radius: var(--border-radius-sm);\n  font-family: var(--font-family-mono);\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-normal);\n  overflow-x: auto;\n  white-space: pre-wrap;\n  word-break: break-word;\n  margin: var(--space-2) 0 var(--space-4) 0;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .errorBoundary {\n    padding: var(--space-4);\n  }\n  \n  .errorContainer {\n    padding: var(--space-8);\n  }\n  \n  .errorTitle {\n    font-size: var(--font-size-2xl);\n  }\n  \n  .errorMessage {\n    font-size: var(--font-size-base);\n  }\n  \n  .errorActions {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .button {\n    width: 100%;\n    max-width: 280px;\n  }\n  \n  .errorStack {\n    font-size: var(--font-size-xs);\n  }\n}\n\n/* Dark mode support (if implemented) */\n@media (prefers-color-scheme: dark) {\n  .errorBoundary {\n    background-color: var(--color-neutral-800);\n  }\n  \n  .errorContainer {\n    background-color: var(--color-neutral-900);\n    border-color: var(--color-neutral-600);\n  }\n  \n  .errorTitle {\n    color: var(--color-neutral-100);\n  }\n  \n  .errorMessage {\n    color: var(--color-neutral-300);\n  }\n  \n  .errorId {\n    background-color: var(--color-neutral-800);\n    border-color: var(--color-neutral-600);\n    color: var(--color-neutral-400);\n  }\n  \n  .errorId code {\n    color: var(--color-neutral-300);\n  }\n  \n  .buttonSecondary {\n    background-color: var(--color-neutral-800);\n    color: var(--color-neutral-200);\n    border-color: var(--color-neutral-600);\n  }\n  \n  .buttonSecondary:hover {\n    background-color: var(--color-neutral-700);\n    border-color: var(--color-neutral-500);\n  }\n  \n  .errorDetails {\n    background-color: var(--color-neutral-800);\n    border-color: var(--color-neutral-600);\n  }\n  \n  .errorDetailsSummary {\n    background-color: var(--color-neutral-700);\n  }\n  \n  .errorDetailsSummary:hover {\n    background-color: var(--color-neutral-600);\n  }\n  \n  .errorDetailsContent h3 {\n    color: var(--color-neutral-200);\n  }\n}\n", ".heroSlider {\n  position: relative;\n  height: 90vh;\n  width: 100%;\n}\n\n.slide {\n  height: 90vh;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n}\n\n.slideContent {\n  color: white;\n  padding: 11pc 0px 117px;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-box-pack: justify;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  height: 100%;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n}\n\n.slideContent h1 {\n  font-size: 58px;\n  font-weight: bold;\n  margin-bottom: 30px;\n  width: 60%;\n}\n\n.description {\n  margin-bottom: 20px;\n  font-size: 18px;\n  line-height: 26px;\n  font-weight: 500;\n  width: 50%;\n}\n\n.ctaBtn {\n  margin-top: 20px;\n  padding: 12px 20px;\n  background: white;\n  color: black;\n  font-weight: 500;\n  border: none;\n  cursor: pointer;\n  border-radius: 4px;\n}\n\n.progressContainer {\n  position: absolute;\n  bottom: 30px;\n  left: 50%;\n  -webkit-transform: translateX(-50%);\n  -ms-transform: translateX(-50%);\n  transform: translateX(-50%);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 8px;\n  z-index: 20;\n}\n\n.dot {\n  width: 4px;\n  height: 4px;\n  background-color: #d1d1d6;\n  border-radius: 50%;\n  position: relative;\n}\n\n.active {\n  width: 160px;\n  height: 4px;\n  background-color: #d1d1d6;\n  border-radius: 2px;\n  overflow: hidden;\n  -webkit-transition: all 0.3s ease;\n  -o-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n}\n\n.dotFill {\n  height: 100%;\n  background-color: var(--theme-color);\n  width: 100%;\n  -webkit-animation: fillBar linear forwards;\n  animation: fillBar linear forwards;\n}\n\n@-webkit-keyframes fillBar {\n  from {\n    width: 0%;\n  }\n\n  to {\n    width: 100%;\n  }\n}\n\n@keyframes fillBar {\n  from {\n    width: 0%;\n  }\n\n  to {\n    width: 100%;\n  }\n}\n\n.pauseBtn {\n  margin-left: 10px;\n  width: 38px;\n  height: 38px;\n  background: black;\n  border: none;\n  border-radius: 6px;\n  color: white;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n.innerSele,\n.innerNew {\n  background: rgba(242, 242, 247, 0.5);\n  border-radius: 16px;\n  color: #fff;\n  cursor: default;\n  display: block;\n  margin-bottom: 16px;\n  padding: 9pt;\n  width: -webkit-max-content;\n  width: -moz-max-content;\n  width: max-content;\n  font-weight: 600;\n  font-size: 16px;\n}\n\n.innerSele {\n  background: rgba(251, 89, 102, 0.5);\n}\n\n/* /// */\n\n.skeletonSlide {\n  background-color: #e0e0e0;\n}\n\n.skeletonTag,\n.skeletonTitle,\n.skeletonDescription,\n.skeletonButton {\n  background: #f5f5f5;\n  border-radius: 4px;\n  margin-bottom: 16px;\n  animation: pulse 1.6s infinite ease-in-out;\n}\n\n.skeletonTag {\n  width: 120px;\n  height: 20px;\n}\n\n.skeletonTitle {\n  width: 60%;\n  height: 48px;\n}\n\n.skeletonDescription {\n  width: 50%;\n  height: 18px;\n}\n\n.skeletonButton {\n  width: 140px;\n  height: 40px;\n  margin-top: 12px;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 0.6;\n  }\n\n  50% {\n    opacity: 1;\n  }\n\n  100% {\n    opacity: 0.6;\n  }\n}\n\n@media screen and (max-width: 900px) {\n  .heroSlider {\n    height: 100vh;\n  }\n\n  .slide {\n    height: 100vh;\n  }\n\n  .slideContent h1 {\n    width: 100%;\n    font-size: 2pc;\n    line-height: 2.5pc;\n  }\n\n  .description {\n    width: 80%;\n  }\n}\n\n@media screen and (max-width: 700px) {\n  .description {\n    width: 100%;\n  }\n\n  .innerSele,\n  .innerNew {\n    padding: 7pt;\n    font-size: 14px;\n  }\n}", ".section {\n  padding: 60px 0;\n}\n\n.title {\n  margin-bottom: 32px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n}\n.title h2 {\n  margin-bottom: 0;\n}\n\n.context {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: stretch;\n  -ms-flex-align: stretch;\n  align-items: stretch;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  margin-bottom: 40px;\n}\n\n.item {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: row;\n  flex-direction: row;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  padding: 0px;\n  width: 100%;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  background: #f2f2f7;\n    border-radius: 20px;\n    padding: 40px;\n}\n\n.box {\n  -webkit-box-flex: 0.8;\n  -ms-flex: 0.8;\n  flex: 0.8;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n}\n\n.box h3 {\n  font-size: 36px;\n  font-weight: bold;\n  margin-bottom: 16px;\n}\n\n.box p {\n  font-size: 16px;\n  line-height: 1.6;\n  margin-bottom: 24px;\n  color: #555;\n  min-height: 96px;\n}\n\n.img {\n  -webkit-box-flex: 1.2;\n  -ms-flex: 1.2;\n  flex: 1.2;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  overflow: hidden;\n  max-height: 400px;\n}\n\n.img img {\n  width: 100%;\n  max-width: 100%;\n  height: auto;\n  -o-object-fit: contain;\n  object-fit: contain;\n  display: block;\n}\n\n.showMoreBtn {\n  background: none;\n  border: none;\n  color: #020202;\n  cursor: pointer;\n  font-size: 14px;\n  margin-left: 6px;\n  padding: 0;\n  text-decoration: underline;\n  font-weight: 500;\n}\n\n.showMoreBtn:hover {\n  text-decoration: none;\n}\n\n.skeleton {\n  background-color: #e0e0e0;\n  border-radius: 8px;\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n.skeletonTitle {\n  width: 60%;\n  height: 28px;\n  margin-bottom: 20px;\n}\n\n.skeletonText {\n  width: 100%;\n  height: 18px;\n  margin-bottom: 12px;\n}\n\n.skeletonButton {\n  width: 160px;\n  height: 40px;\n  margin-top: 24px;\n  border-radius: 10px;\n}\n\n.skeletonImg {\n  width: 100%;\n  height: 100%;\n  border-radius: 20px;\n}\n\n@-webkit-keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f5f5f5;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n@keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f5f5f5;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n/* Mobile */\n@media screen and (max-width: 900px) {\n  .context {\n    display: block;\n  }\n\n  .item {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    text-align: start;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n    padding: 30px 20px;\n    min-height: 600px; /* стабильно, но без max-height */\n  }\n\n  .box {\n    height: auto;\n  }\n\n  .box h3 {\n    font-size: 24px;\n  }\n\n  .box p {\n    font-size: 15px;\n    min-height: 96px;\n  }\n\n  .img {\n    max-height: 240px;\n    margin-bottom: 20px;\n  }\n\n  .img img {\n    width: 100%;\n    height: auto;\n  }\n}\n", ".btn {\n  font-size: 16px;\n  line-height: 1.2;\n  color: #636366;\n  cursor: pointer;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  padding: 0 12px;\n  font-weight: 600;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n  border-bottom: 4px solid transparent;\n  text-transform: uppercase;\n  height: 52px;\n  min-width: 100px;\n  max-width: 200px;\n  padding: 0 16px; /* горизонтальные отступы */\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  white-space: nowrap;\n  box-sizing: border-box;\n  white-space: nowrap; /* предотвращаем перенос текста */\n  /* overflow: hidden; */\n  text-overflow: ellipsis; /* длинный текст обрезается с ... */\n  transition: all 0.3s ease;\n  flex-shrink: 0; /* для Swiper, чтобы кнопки не сжимались */\n  -webkit-transition: all 0.3s ease;\n  -o-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n}\n\n.active {\n  color: #000;\n  border-bottom: 4px solid #000;\n}\n\n.activeBar {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background-color: #d1d1d6;\n  z-index: 0;\n}\n@media screen and (max-width: 900px) {\n  .btn {\n    font-size: 14px;\n    height: 44px;\n    min-width: 88px;\n    padding: 0 10px;\n  }\n}\n\n@media screen and (max-width: 580px) {\n  .btn {\n    font-size: 12px;\n    height: 42px;\n    min-width: 76px;\n    padding: 0 8px;\n  }\n}\n", ".section {\n  padding-top: 10px;\n}\n\n.card {\n  background-image: url('../../../../asset/imgs/home/<USER>');\n  background-position: center;\n  background-size: cover;\n  height: 400px;\n  border-radius: 20px;\n}\n\n.content {\n  padding: 20px 2pc;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  height: 100%;\n  justify-content: space-between;\n  position: relative;\n  z-index: 3;\n}\n\n.content h3 {\n  color: #fff;\n  font-size: 32px;\n  font-weight: 700;\n}\n\n@media screen and (max-width: 900px) {\n  .section {\n    padding-top: 0px;\n  }\n\n  .card {\n    background-image: url('../../../../asset/imgs/home/<USER>');\n    height: 300px;\n  }\n\n  .content h3 {\n    font-size: 24px;\n  }\n}", ".section {\n  background: #f2f2f7;\n  color: #000;\n}\n.content h3 {\n  font-size: 24px;\n  line-height: 24px;\n  font-weight: 700;\n}\n\n/* --- Кнопки --- */\n.btnsList {\n  margin-top: 56px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  margin-left: -20px;\n  margin-bottom: 1.75rem;\n}\n.btnItem {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  background-color: #fff;\n  border-radius: 8px;\n  height: 4.5rem;\n  margin-left: 1.5rem;\n  margin-bottom: 1.5rem;\n  padding: 0 3.4rem 0 1.5rem;\n  -webkit-box-flex: 1;\n  -ms-flex: 1 0 auto;\n  flex: 1 0 auto;\n  -webkit-transition: background-color 0.3s ease;\n  -o-transition: background-color 0.3s ease;\n  transition: background-color 0.3s ease;\n}\n\n.item {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  position: relative;\n  width: 100%;\n  height: 100%;\n  padding-right: 2rem;\n}\n.item img {\n  width: 24px;\n  height: 24px;\n  margin-right: 0.5rem;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n}\n.item span {\n  font-size: 0.875rem;\n  font-weight: 700;\n  color: #000;\n}\n.item::after {\n  content: '';\n  background-image: url('../../asset/imgs/icons/arrow.svg');\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  position: absolute;\n  right: 0;\n  top: 50%;\n  width: 24px;\n  height: 24px;\n  -webkit-transform: translateY(-50%) translateX(0);\n  -ms-transform: translateY(-50%) translateX(0);\n  transform: translateY(-50%) translateX(0);\n  -webkit-transition: -webkit-transform 0.3s ease;\n  transition: -webkit-transform 0.3s ease;\n  -o-transition: transform 0.3s ease;\n  transition: transform 0.3s ease;\n  transition: transform 0.3s ease, -webkit-transform 0.3s ease;\n  z-index: 1;\n}\n\n.btnItem:hover .item::after {\n  -webkit-transform: translateY(-50%) translateX(6px);\n  -ms-transform: translateY(-50%) translateX(6px);\n  transform: translateY(-50%) translateX(6px); /* Смещаем вправо на 6px */\n}\n", "/* videoCard.module.css */\n\n.wrapper {\n  position: relative;\n  width: 100%;\n  height: 80vh;\n  overflow: hidden;\n}\n\n.wrapperActive{\n  padding: 0px;\n}\n\n.wrapperFixed {\n  position: fixed;\n  top: 0;\n  left: 0;\n  height: 80vh;\n  width: 100%;\n  z-index: 1;\n}\n\n.sticky {\n  position: sticky;\n  top: 0;\n  height: 70vh;\n  overflow: hidden;\n}\n\n.container {\n  width: 80%; /* начальное состояние */\n  height: 100%;\n  margin: 0 auto;\n  position: relative;\n  transition: width 0.7s ease;\n}\n\n.containerActive {\n  width: 100%; /* активное состояние при IntersectionObserver */\n}\n\n.videoWrapper {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-transition: border-radius 0.5s ease;\n  -o-transition: border-radius 0.5s ease;\n  transition: border-radius 0.5s ease;\n}\n\n.videoWrapperActive {\n  border-radius: 0px;\n}\n\n.video {\n  width: 100%;\n  height: 100%;\n  -o-object-fit: cover;\n  object-fit: cover;\n  border-radius: 20px;\n}\n\n.videoActive{\n  border-radius: 0px;\n}\n\n.text {\n  position: absolute;\n  bottom: 50%;\n  color: white;\n  font-size: 2.5rem;\n  font-weight: 600;\n  z-index: 2;\n  text-align: center;\n}\n\n/* Адаптация под планшеты */\n@media (max-width: 1024px) {\n  .text {\n    font-size: 2rem;\n  }\n}\n\n/* Адаптация под телефоны */\n@media (max-width: 768px) {\n  .text {\n    font-size: 1.5rem;\n    bottom: 50%;\n  }\n\n  .videoWrapperActive {\n    border-radius: 0px;\n  }\n}\n\n/* Очень маленькие устройства */\n@media (max-width: 480px) {\n  .text {\n    font-size: 20px;\n  }\n}\n", ".section {\n  background-color: #f2f2f7;\n  padding: 2pc 0;\n  overflow: hidden;\n}\n\n.cards {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  gap: 2pc;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n}\n\n.card {\n  background-color: #fff;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: row;\n  flex-direction: row;\n  width: calc(50% - 1pc);\n  border-radius: 1pc;\n  height: 4in;\n  padding: 2pc 0 2pc 2pc;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  z-index: 9;\n}\n\n/* Левая часть */\n.left {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  width: 55%;\n}\n\n.left h3 {\n  font-size: 1.25rem;\n  line-height: 1.5rem;\n  margin-bottom: 1pc;\n  font-weight: 700;\n}\n\n.left p {\n  font-size: 0.8rem;\n  line-height: 1.3rem;\n  font-weight: 400;\n}\n\n.left a {\n  margin-top: auto;\n  display: -webkit-inline-box;\n  display: -ms-inline-flexbox;\n  display: inline-flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  font-size: 0.9rem;\n  text-decoration: none;\n  color: #000;\n}\n\n.linkIcon {\n  width: 14px;\n  height: 14px;\n}\n\n/* Правая часть */\n.right {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  position: relative;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: end;\n  -ms-flex-align: end;\n  align-items: flex-end;\n  -webkit-box-pack: end;\n  -ms-flex-pack: end;\n  justify-content: flex-end;\n  z-index: 1;\n}\n\n.right img {\n  max-width: 100%;\n  height: auto;\n  -o-object-fit: contain;\n  object-fit: contain;\n  position: relative;\n  right: 0;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n/* ====== Планшетная адаптация ====== */\n@media (min-width: 769px) and (max-width: 1024px) {\n  .card {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    width: 100%;\n    height: auto;\n    padding: 1.5pc;\n  }\n\n  .left {\n    width: 100%;\n  }\n\n  .right {\n    width: 100%;\n    -webkit-box-pack: end;\n    -ms-flex-pack: end;\n    justify-content: flex-end;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    margin-top: 1pc;\n  }\n\n  .right img {\n    width: auto;\n    height: 180px;\n    -o-object-fit: contain;\n    object-fit: contain;\n  }\n\n  .left h3 {\n    font-size: 1.2rem;\n    margin-bottom: 0.8rem;\n  }\n\n  .left p {\n    font-size: 0.82rem;\n    line-height: 1.5rem;\n  }\n\n  .left a {\n    font-size: 0.95rem;\n    margin-top: 1rem;\n  }\n}\n\n/* ====== Мобильная адаптация ====== */\n@media (max-width: 900px) {\n  .card {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    width: 100%;\n    height: auto;\n    padding: 1.5pc;\n  }\n\n  .left {\n    width: 100%;\n  }\n\n  .right {\n    width: 100%;\n    -webkit-box-pack: end;\n    -ms-flex-pack: end;\n    justify-content: flex-end;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n  }\n\n  .right img {\n    width: auto;\n    height: 150px;\n    -o-object-fit: contain;\n    object-fit: contain;\n  }\n\n  .left h3 {\n    font-size: 1.1rem;\n    font-weight: 700;\n    margin-bottom: 0.6rem;\n  }\n\n  .left p {\n    font-size: 0.78rem;\n    line-height: 1.4rem;\n  }\n\n  .left a {\n    font-size: 0.9rem;\n    margin-top: 1rem;\n  }\n}", "[data-aos][data-aos][data-aos-duration=\"50\"],body[data-aos-duration=\"50\"] [data-aos]{transition-duration:50ms}[data-aos][data-aos][data-aos-delay=\"50\"],body[data-aos-delay=\"50\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"50\"].aos-animate,body[data-aos-delay=\"50\"] [data-aos].aos-animate{transition-delay:50ms}[data-aos][data-aos][data-aos-duration=\"100\"],body[data-aos-duration=\"100\"] [data-aos]{transition-duration:.1s}[data-aos][data-aos][data-aos-delay=\"100\"],body[data-aos-delay=\"100\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"100\"].aos-animate,body[data-aos-delay=\"100\"] [data-aos].aos-animate{transition-delay:.1s}[data-aos][data-aos][data-aos-duration=\"150\"],body[data-aos-duration=\"150\"] [data-aos]{transition-duration:.15s}[data-aos][data-aos][data-aos-delay=\"150\"],body[data-aos-delay=\"150\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"150\"].aos-animate,body[data-aos-delay=\"150\"] [data-aos].aos-animate{transition-delay:.15s}[data-aos][data-aos][data-aos-duration=\"200\"],body[data-aos-duration=\"200\"] [data-aos]{transition-duration:.2s}[data-aos][data-aos][data-aos-delay=\"200\"],body[data-aos-delay=\"200\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"200\"].aos-animate,body[data-aos-delay=\"200\"] [data-aos].aos-animate{transition-delay:.2s}[data-aos][data-aos][data-aos-duration=\"250\"],body[data-aos-duration=\"250\"] [data-aos]{transition-duration:.25s}[data-aos][data-aos][data-aos-delay=\"250\"],body[data-aos-delay=\"250\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"250\"].aos-animate,body[data-aos-delay=\"250\"] [data-aos].aos-animate{transition-delay:.25s}[data-aos][data-aos][data-aos-duration=\"300\"],body[data-aos-duration=\"300\"] [data-aos]{transition-duration:.3s}[data-aos][data-aos][data-aos-delay=\"300\"],body[data-aos-delay=\"300\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"300\"].aos-animate,body[data-aos-delay=\"300\"] [data-aos].aos-animate{transition-delay:.3s}[data-aos][data-aos][data-aos-duration=\"350\"],body[data-aos-duration=\"350\"] [data-aos]{transition-duration:.35s}[data-aos][data-aos][data-aos-delay=\"350\"],body[data-aos-delay=\"350\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"350\"].aos-animate,body[data-aos-delay=\"350\"] [data-aos].aos-animate{transition-delay:.35s}[data-aos][data-aos][data-aos-duration=\"400\"],body[data-aos-duration=\"400\"] [data-aos]{transition-duration:.4s}[data-aos][data-aos][data-aos-delay=\"400\"],body[data-aos-delay=\"400\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"400\"].aos-animate,body[data-aos-delay=\"400\"] [data-aos].aos-animate{transition-delay:.4s}[data-aos][data-aos][data-aos-duration=\"450\"],body[data-aos-duration=\"450\"] [data-aos]{transition-duration:.45s}[data-aos][data-aos][data-aos-delay=\"450\"],body[data-aos-delay=\"450\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"450\"].aos-animate,body[data-aos-delay=\"450\"] [data-aos].aos-animate{transition-delay:.45s}[data-aos][data-aos][data-aos-duration=\"500\"],body[data-aos-duration=\"500\"] [data-aos]{transition-duration:.5s}[data-aos][data-aos][data-aos-delay=\"500\"],body[data-aos-delay=\"500\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"500\"].aos-animate,body[data-aos-delay=\"500\"] [data-aos].aos-animate{transition-delay:.5s}[data-aos][data-aos][data-aos-duration=\"550\"],body[data-aos-duration=\"550\"] [data-aos]{transition-duration:.55s}[data-aos][data-aos][data-aos-delay=\"550\"],body[data-aos-delay=\"550\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"550\"].aos-animate,body[data-aos-delay=\"550\"] [data-aos].aos-animate{transition-delay:.55s}[data-aos][data-aos][data-aos-duration=\"600\"],body[data-aos-duration=\"600\"] [data-aos]{transition-duration:.6s}[data-aos][data-aos][data-aos-delay=\"600\"],body[data-aos-delay=\"600\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"600\"].aos-animate,body[data-aos-delay=\"600\"] [data-aos].aos-animate{transition-delay:.6s}[data-aos][data-aos][data-aos-duration=\"650\"],body[data-aos-duration=\"650\"] [data-aos]{transition-duration:.65s}[data-aos][data-aos][data-aos-delay=\"650\"],body[data-aos-delay=\"650\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"650\"].aos-animate,body[data-aos-delay=\"650\"] [data-aos].aos-animate{transition-delay:.65s}[data-aos][data-aos][data-aos-duration=\"700\"],body[data-aos-duration=\"700\"] [data-aos]{transition-duration:.7s}[data-aos][data-aos][data-aos-delay=\"700\"],body[data-aos-delay=\"700\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"700\"].aos-animate,body[data-aos-delay=\"700\"] [data-aos].aos-animate{transition-delay:.7s}[data-aos][data-aos][data-aos-duration=\"750\"],body[data-aos-duration=\"750\"] [data-aos]{transition-duration:.75s}[data-aos][data-aos][data-aos-delay=\"750\"],body[data-aos-delay=\"750\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"750\"].aos-animate,body[data-aos-delay=\"750\"] [data-aos].aos-animate{transition-delay:.75s}[data-aos][data-aos][data-aos-duration=\"800\"],body[data-aos-duration=\"800\"] [data-aos]{transition-duration:.8s}[data-aos][data-aos][data-aos-delay=\"800\"],body[data-aos-delay=\"800\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"800\"].aos-animate,body[data-aos-delay=\"800\"] [data-aos].aos-animate{transition-delay:.8s}[data-aos][data-aos][data-aos-duration=\"850\"],body[data-aos-duration=\"850\"] [data-aos]{transition-duration:.85s}[data-aos][data-aos][data-aos-delay=\"850\"],body[data-aos-delay=\"850\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"850\"].aos-animate,body[data-aos-delay=\"850\"] [data-aos].aos-animate{transition-delay:.85s}[data-aos][data-aos][data-aos-duration=\"900\"],body[data-aos-duration=\"900\"] [data-aos]{transition-duration:.9s}[data-aos][data-aos][data-aos-delay=\"900\"],body[data-aos-delay=\"900\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"900\"].aos-animate,body[data-aos-delay=\"900\"] [data-aos].aos-animate{transition-delay:.9s}[data-aos][data-aos][data-aos-duration=\"950\"],body[data-aos-duration=\"950\"] [data-aos]{transition-duration:.95s}[data-aos][data-aos][data-aos-delay=\"950\"],body[data-aos-delay=\"950\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"950\"].aos-animate,body[data-aos-delay=\"950\"] [data-aos].aos-animate{transition-delay:.95s}[data-aos][data-aos][data-aos-duration=\"1000\"],body[data-aos-duration=\"1000\"] [data-aos]{transition-duration:1s}[data-aos][data-aos][data-aos-delay=\"1000\"],body[data-aos-delay=\"1000\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1000\"].aos-animate,body[data-aos-delay=\"1000\"] [data-aos].aos-animate{transition-delay:1s}[data-aos][data-aos][data-aos-duration=\"1050\"],body[data-aos-duration=\"1050\"] [data-aos]{transition-duration:1.05s}[data-aos][data-aos][data-aos-delay=\"1050\"],body[data-aos-delay=\"1050\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1050\"].aos-animate,body[data-aos-delay=\"1050\"] [data-aos].aos-animate{transition-delay:1.05s}[data-aos][data-aos][data-aos-duration=\"1100\"],body[data-aos-duration=\"1100\"] [data-aos]{transition-duration:1.1s}[data-aos][data-aos][data-aos-delay=\"1100\"],body[data-aos-delay=\"1100\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1100\"].aos-animate,body[data-aos-delay=\"1100\"] [data-aos].aos-animate{transition-delay:1.1s}[data-aos][data-aos][data-aos-duration=\"1150\"],body[data-aos-duration=\"1150\"] [data-aos]{transition-duration:1.15s}[data-aos][data-aos][data-aos-delay=\"1150\"],body[data-aos-delay=\"1150\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1150\"].aos-animate,body[data-aos-delay=\"1150\"] [data-aos].aos-animate{transition-delay:1.15s}[data-aos][data-aos][data-aos-duration=\"1200\"],body[data-aos-duration=\"1200\"] [data-aos]{transition-duration:1.2s}[data-aos][data-aos][data-aos-delay=\"1200\"],body[data-aos-delay=\"1200\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1200\"].aos-animate,body[data-aos-delay=\"1200\"] [data-aos].aos-animate{transition-delay:1.2s}[data-aos][data-aos][data-aos-duration=\"1250\"],body[data-aos-duration=\"1250\"] [data-aos]{transition-duration:1.25s}[data-aos][data-aos][data-aos-delay=\"1250\"],body[data-aos-delay=\"1250\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1250\"].aos-animate,body[data-aos-delay=\"1250\"] [data-aos].aos-animate{transition-delay:1.25s}[data-aos][data-aos][data-aos-duration=\"1300\"],body[data-aos-duration=\"1300\"] [data-aos]{transition-duration:1.3s}[data-aos][data-aos][data-aos-delay=\"1300\"],body[data-aos-delay=\"1300\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1300\"].aos-animate,body[data-aos-delay=\"1300\"] [data-aos].aos-animate{transition-delay:1.3s}[data-aos][data-aos][data-aos-duration=\"1350\"],body[data-aos-duration=\"1350\"] [data-aos]{transition-duration:1.35s}[data-aos][data-aos][data-aos-delay=\"1350\"],body[data-aos-delay=\"1350\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1350\"].aos-animate,body[data-aos-delay=\"1350\"] [data-aos].aos-animate{transition-delay:1.35s}[data-aos][data-aos][data-aos-duration=\"1400\"],body[data-aos-duration=\"1400\"] [data-aos]{transition-duration:1.4s}[data-aos][data-aos][data-aos-delay=\"1400\"],body[data-aos-delay=\"1400\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1400\"].aos-animate,body[data-aos-delay=\"1400\"] [data-aos].aos-animate{transition-delay:1.4s}[data-aos][data-aos][data-aos-duration=\"1450\"],body[data-aos-duration=\"1450\"] [data-aos]{transition-duration:1.45s}[data-aos][data-aos][data-aos-delay=\"1450\"],body[data-aos-delay=\"1450\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1450\"].aos-animate,body[data-aos-delay=\"1450\"] [data-aos].aos-animate{transition-delay:1.45s}[data-aos][data-aos][data-aos-duration=\"1500\"],body[data-aos-duration=\"1500\"] [data-aos]{transition-duration:1.5s}[data-aos][data-aos][data-aos-delay=\"1500\"],body[data-aos-delay=\"1500\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1500\"].aos-animate,body[data-aos-delay=\"1500\"] [data-aos].aos-animate{transition-delay:1.5s}[data-aos][data-aos][data-aos-duration=\"1550\"],body[data-aos-duration=\"1550\"] [data-aos]{transition-duration:1.55s}[data-aos][data-aos][data-aos-delay=\"1550\"],body[data-aos-delay=\"1550\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1550\"].aos-animate,body[data-aos-delay=\"1550\"] [data-aos].aos-animate{transition-delay:1.55s}[data-aos][data-aos][data-aos-duration=\"1600\"],body[data-aos-duration=\"1600\"] [data-aos]{transition-duration:1.6s}[data-aos][data-aos][data-aos-delay=\"1600\"],body[data-aos-delay=\"1600\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1600\"].aos-animate,body[data-aos-delay=\"1600\"] [data-aos].aos-animate{transition-delay:1.6s}[data-aos][data-aos][data-aos-duration=\"1650\"],body[data-aos-duration=\"1650\"] [data-aos]{transition-duration:1.65s}[data-aos][data-aos][data-aos-delay=\"1650\"],body[data-aos-delay=\"1650\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1650\"].aos-animate,body[data-aos-delay=\"1650\"] [data-aos].aos-animate{transition-delay:1.65s}[data-aos][data-aos][data-aos-duration=\"1700\"],body[data-aos-duration=\"1700\"] [data-aos]{transition-duration:1.7s}[data-aos][data-aos][data-aos-delay=\"1700\"],body[data-aos-delay=\"1700\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1700\"].aos-animate,body[data-aos-delay=\"1700\"] [data-aos].aos-animate{transition-delay:1.7s}[data-aos][data-aos][data-aos-duration=\"1750\"],body[data-aos-duration=\"1750\"] [data-aos]{transition-duration:1.75s}[data-aos][data-aos][data-aos-delay=\"1750\"],body[data-aos-delay=\"1750\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1750\"].aos-animate,body[data-aos-delay=\"1750\"] [data-aos].aos-animate{transition-delay:1.75s}[data-aos][data-aos][data-aos-duration=\"1800\"],body[data-aos-duration=\"1800\"] [data-aos]{transition-duration:1.8s}[data-aos][data-aos][data-aos-delay=\"1800\"],body[data-aos-delay=\"1800\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1800\"].aos-animate,body[data-aos-delay=\"1800\"] [data-aos].aos-animate{transition-delay:1.8s}[data-aos][data-aos][data-aos-duration=\"1850\"],body[data-aos-duration=\"1850\"] [data-aos]{transition-duration:1.85s}[data-aos][data-aos][data-aos-delay=\"1850\"],body[data-aos-delay=\"1850\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1850\"].aos-animate,body[data-aos-delay=\"1850\"] [data-aos].aos-animate{transition-delay:1.85s}[data-aos][data-aos][data-aos-duration=\"1900\"],body[data-aos-duration=\"1900\"] [data-aos]{transition-duration:1.9s}[data-aos][data-aos][data-aos-delay=\"1900\"],body[data-aos-delay=\"1900\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1900\"].aos-animate,body[data-aos-delay=\"1900\"] [data-aos].aos-animate{transition-delay:1.9s}[data-aos][data-aos][data-aos-duration=\"1950\"],body[data-aos-duration=\"1950\"] [data-aos]{transition-duration:1.95s}[data-aos][data-aos][data-aos-delay=\"1950\"],body[data-aos-delay=\"1950\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"1950\"].aos-animate,body[data-aos-delay=\"1950\"] [data-aos].aos-animate{transition-delay:1.95s}[data-aos][data-aos][data-aos-duration=\"2000\"],body[data-aos-duration=\"2000\"] [data-aos]{transition-duration:2s}[data-aos][data-aos][data-aos-delay=\"2000\"],body[data-aos-delay=\"2000\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2000\"].aos-animate,body[data-aos-delay=\"2000\"] [data-aos].aos-animate{transition-delay:2s}[data-aos][data-aos][data-aos-duration=\"2050\"],body[data-aos-duration=\"2050\"] [data-aos]{transition-duration:2.05s}[data-aos][data-aos][data-aos-delay=\"2050\"],body[data-aos-delay=\"2050\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2050\"].aos-animate,body[data-aos-delay=\"2050\"] [data-aos].aos-animate{transition-delay:2.05s}[data-aos][data-aos][data-aos-duration=\"2100\"],body[data-aos-duration=\"2100\"] [data-aos]{transition-duration:2.1s}[data-aos][data-aos][data-aos-delay=\"2100\"],body[data-aos-delay=\"2100\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2100\"].aos-animate,body[data-aos-delay=\"2100\"] [data-aos].aos-animate{transition-delay:2.1s}[data-aos][data-aos][data-aos-duration=\"2150\"],body[data-aos-duration=\"2150\"] [data-aos]{transition-duration:2.15s}[data-aos][data-aos][data-aos-delay=\"2150\"],body[data-aos-delay=\"2150\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2150\"].aos-animate,body[data-aos-delay=\"2150\"] [data-aos].aos-animate{transition-delay:2.15s}[data-aos][data-aos][data-aos-duration=\"2200\"],body[data-aos-duration=\"2200\"] [data-aos]{transition-duration:2.2s}[data-aos][data-aos][data-aos-delay=\"2200\"],body[data-aos-delay=\"2200\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2200\"].aos-animate,body[data-aos-delay=\"2200\"] [data-aos].aos-animate{transition-delay:2.2s}[data-aos][data-aos][data-aos-duration=\"2250\"],body[data-aos-duration=\"2250\"] [data-aos]{transition-duration:2.25s}[data-aos][data-aos][data-aos-delay=\"2250\"],body[data-aos-delay=\"2250\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2250\"].aos-animate,body[data-aos-delay=\"2250\"] [data-aos].aos-animate{transition-delay:2.25s}[data-aos][data-aos][data-aos-duration=\"2300\"],body[data-aos-duration=\"2300\"] [data-aos]{transition-duration:2.3s}[data-aos][data-aos][data-aos-delay=\"2300\"],body[data-aos-delay=\"2300\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2300\"].aos-animate,body[data-aos-delay=\"2300\"] [data-aos].aos-animate{transition-delay:2.3s}[data-aos][data-aos][data-aos-duration=\"2350\"],body[data-aos-duration=\"2350\"] [data-aos]{transition-duration:2.35s}[data-aos][data-aos][data-aos-delay=\"2350\"],body[data-aos-delay=\"2350\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2350\"].aos-animate,body[data-aos-delay=\"2350\"] [data-aos].aos-animate{transition-delay:2.35s}[data-aos][data-aos][data-aos-duration=\"2400\"],body[data-aos-duration=\"2400\"] [data-aos]{transition-duration:2.4s}[data-aos][data-aos][data-aos-delay=\"2400\"],body[data-aos-delay=\"2400\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2400\"].aos-animate,body[data-aos-delay=\"2400\"] [data-aos].aos-animate{transition-delay:2.4s}[data-aos][data-aos][data-aos-duration=\"2450\"],body[data-aos-duration=\"2450\"] [data-aos]{transition-duration:2.45s}[data-aos][data-aos][data-aos-delay=\"2450\"],body[data-aos-delay=\"2450\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2450\"].aos-animate,body[data-aos-delay=\"2450\"] [data-aos].aos-animate{transition-delay:2.45s}[data-aos][data-aos][data-aos-duration=\"2500\"],body[data-aos-duration=\"2500\"] [data-aos]{transition-duration:2.5s}[data-aos][data-aos][data-aos-delay=\"2500\"],body[data-aos-delay=\"2500\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2500\"].aos-animate,body[data-aos-delay=\"2500\"] [data-aos].aos-animate{transition-delay:2.5s}[data-aos][data-aos][data-aos-duration=\"2550\"],body[data-aos-duration=\"2550\"] [data-aos]{transition-duration:2.55s}[data-aos][data-aos][data-aos-delay=\"2550\"],body[data-aos-delay=\"2550\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2550\"].aos-animate,body[data-aos-delay=\"2550\"] [data-aos].aos-animate{transition-delay:2.55s}[data-aos][data-aos][data-aos-duration=\"2600\"],body[data-aos-duration=\"2600\"] [data-aos]{transition-duration:2.6s}[data-aos][data-aos][data-aos-delay=\"2600\"],body[data-aos-delay=\"2600\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2600\"].aos-animate,body[data-aos-delay=\"2600\"] [data-aos].aos-animate{transition-delay:2.6s}[data-aos][data-aos][data-aos-duration=\"2650\"],body[data-aos-duration=\"2650\"] [data-aos]{transition-duration:2.65s}[data-aos][data-aos][data-aos-delay=\"2650\"],body[data-aos-delay=\"2650\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2650\"].aos-animate,body[data-aos-delay=\"2650\"] [data-aos].aos-animate{transition-delay:2.65s}[data-aos][data-aos][data-aos-duration=\"2700\"],body[data-aos-duration=\"2700\"] [data-aos]{transition-duration:2.7s}[data-aos][data-aos][data-aos-delay=\"2700\"],body[data-aos-delay=\"2700\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2700\"].aos-animate,body[data-aos-delay=\"2700\"] [data-aos].aos-animate{transition-delay:2.7s}[data-aos][data-aos][data-aos-duration=\"2750\"],body[data-aos-duration=\"2750\"] [data-aos]{transition-duration:2.75s}[data-aos][data-aos][data-aos-delay=\"2750\"],body[data-aos-delay=\"2750\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2750\"].aos-animate,body[data-aos-delay=\"2750\"] [data-aos].aos-animate{transition-delay:2.75s}[data-aos][data-aos][data-aos-duration=\"2800\"],body[data-aos-duration=\"2800\"] [data-aos]{transition-duration:2.8s}[data-aos][data-aos][data-aos-delay=\"2800\"],body[data-aos-delay=\"2800\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2800\"].aos-animate,body[data-aos-delay=\"2800\"] [data-aos].aos-animate{transition-delay:2.8s}[data-aos][data-aos][data-aos-duration=\"2850\"],body[data-aos-duration=\"2850\"] [data-aos]{transition-duration:2.85s}[data-aos][data-aos][data-aos-delay=\"2850\"],body[data-aos-delay=\"2850\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2850\"].aos-animate,body[data-aos-delay=\"2850\"] [data-aos].aos-animate{transition-delay:2.85s}[data-aos][data-aos][data-aos-duration=\"2900\"],body[data-aos-duration=\"2900\"] [data-aos]{transition-duration:2.9s}[data-aos][data-aos][data-aos-delay=\"2900\"],body[data-aos-delay=\"2900\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2900\"].aos-animate,body[data-aos-delay=\"2900\"] [data-aos].aos-animate{transition-delay:2.9s}[data-aos][data-aos][data-aos-duration=\"2950\"],body[data-aos-duration=\"2950\"] [data-aos]{transition-duration:2.95s}[data-aos][data-aos][data-aos-delay=\"2950\"],body[data-aos-delay=\"2950\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"2950\"].aos-animate,body[data-aos-delay=\"2950\"] [data-aos].aos-animate{transition-delay:2.95s}[data-aos][data-aos][data-aos-duration=\"3000\"],body[data-aos-duration=\"3000\"] [data-aos]{transition-duration:3s}[data-aos][data-aos][data-aos-delay=\"3000\"],body[data-aos-delay=\"3000\"] [data-aos]{transition-delay:0}[data-aos][data-aos][data-aos-delay=\"3000\"].aos-animate,body[data-aos-delay=\"3000\"] [data-aos].aos-animate{transition-delay:3s}[data-aos][data-aos][data-aos-easing=linear],body[data-aos-easing=linear] [data-aos]{transition-timing-function:cubic-bezier(.25,.25,.75,.75)}[data-aos][data-aos][data-aos-easing=ease],body[data-aos-easing=ease] [data-aos]{transition-timing-function:ease}[data-aos][data-aos][data-aos-easing=ease-in],body[data-aos-easing=ease-in] [data-aos]{transition-timing-function:ease-in}[data-aos][data-aos][data-aos-easing=ease-out],body[data-aos-easing=ease-out] [data-aos]{transition-timing-function:ease-out}[data-aos][data-aos][data-aos-easing=ease-in-out],body[data-aos-easing=ease-in-out] [data-aos]{transition-timing-function:ease-in-out}[data-aos][data-aos][data-aos-easing=ease-in-back],body[data-aos-easing=ease-in-back] [data-aos]{transition-timing-function:cubic-bezier(.6,-.28,.735,.045)}[data-aos][data-aos][data-aos-easing=ease-out-back],body[data-aos-easing=ease-out-back] [data-aos]{transition-timing-function:cubic-bezier(.175,.885,.32,1.275)}[data-aos][data-aos][data-aos-easing=ease-in-out-back],body[data-aos-easing=ease-in-out-back] [data-aos]{transition-timing-function:cubic-bezier(.68,-.55,.265,1.55)}[data-aos][data-aos][data-aos-easing=ease-in-sine],body[data-aos-easing=ease-in-sine] [data-aos]{transition-timing-function:cubic-bezier(.47,0,.745,.715)}[data-aos][data-aos][data-aos-easing=ease-out-sine],body[data-aos-easing=ease-out-sine] [data-aos]{transition-timing-function:cubic-bezier(.39,.575,.565,1)}[data-aos][data-aos][data-aos-easing=ease-in-out-sine],body[data-aos-easing=ease-in-out-sine] [data-aos]{transition-timing-function:cubic-bezier(.445,.05,.55,.95)}[data-aos][data-aos][data-aos-easing=ease-in-quad],body[data-aos-easing=ease-in-quad] [data-aos]{transition-timing-function:cubic-bezier(.55,.085,.68,.53)}[data-aos][data-aos][data-aos-easing=ease-out-quad],body[data-aos-easing=ease-out-quad] [data-aos]{transition-timing-function:cubic-bezier(.25,.46,.45,.94)}[data-aos][data-aos][data-aos-easing=ease-in-out-quad],body[data-aos-easing=ease-in-out-quad] [data-aos]{transition-timing-function:cubic-bezier(.455,.03,.515,.955)}[data-aos][data-aos][data-aos-easing=ease-in-cubic],body[data-aos-easing=ease-in-cubic] [data-aos]{transition-timing-function:cubic-bezier(.55,.085,.68,.53)}[data-aos][data-aos][data-aos-easing=ease-out-cubic],body[data-aos-easing=ease-out-cubic] [data-aos]{transition-timing-function:cubic-bezier(.25,.46,.45,.94)}[data-aos][data-aos][data-aos-easing=ease-in-out-cubic],body[data-aos-easing=ease-in-out-cubic] [data-aos]{transition-timing-function:cubic-bezier(.455,.03,.515,.955)}[data-aos][data-aos][data-aos-easing=ease-in-quart],body[data-aos-easing=ease-in-quart] [data-aos]{transition-timing-function:cubic-bezier(.55,.085,.68,.53)}[data-aos][data-aos][data-aos-easing=ease-out-quart],body[data-aos-easing=ease-out-quart] [data-aos]{transition-timing-function:cubic-bezier(.25,.46,.45,.94)}[data-aos][data-aos][data-aos-easing=ease-in-out-quart],body[data-aos-easing=ease-in-out-quart] [data-aos]{transition-timing-function:cubic-bezier(.455,.03,.515,.955)}[data-aos^=fade][data-aos^=fade]{opacity:0;transition-property:opacity,transform}[data-aos^=fade][data-aos^=fade].aos-animate{opacity:1;transform:translateZ(0)}[data-aos=fade-up]{transform:translate3d(0,100px,0)}[data-aos=fade-down]{transform:translate3d(0,-100px,0)}[data-aos=fade-right]{transform:translate3d(-100px,0,0)}[data-aos=fade-left]{transform:translate3d(100px,0,0)}[data-aos=fade-up-right]{transform:translate3d(-100px,100px,0)}[data-aos=fade-up-left]{transform:translate3d(100px,100px,0)}[data-aos=fade-down-right]{transform:translate3d(-100px,-100px,0)}[data-aos=fade-down-left]{transform:translate3d(100px,-100px,0)}[data-aos^=zoom][data-aos^=zoom]{opacity:0;transition-property:opacity,transform}[data-aos^=zoom][data-aos^=zoom].aos-animate{opacity:1;transform:translateZ(0) scale(1)}[data-aos=zoom-in]{transform:scale(.6)}[data-aos=zoom-in-up]{transform:translate3d(0,100px,0) scale(.6)}[data-aos=zoom-in-down]{transform:translate3d(0,-100px,0) scale(.6)}[data-aos=zoom-in-right]{transform:translate3d(-100px,0,0) scale(.6)}[data-aos=zoom-in-left]{transform:translate3d(100px,0,0) scale(.6)}[data-aos=zoom-out]{transform:scale(1.2)}[data-aos=zoom-out-up]{transform:translate3d(0,100px,0) scale(1.2)}[data-aos=zoom-out-down]{transform:translate3d(0,-100px,0) scale(1.2)}[data-aos=zoom-out-right]{transform:translate3d(-100px,0,0) scale(1.2)}[data-aos=zoom-out-left]{transform:translate3d(100px,0,0) scale(1.2)}[data-aos^=slide][data-aos^=slide]{transition-property:transform}[data-aos^=slide][data-aos^=slide].aos-animate{transform:translateZ(0)}[data-aos=slide-up]{transform:translate3d(0,100%,0)}[data-aos=slide-down]{transform:translate3d(0,-100%,0)}[data-aos=slide-right]{transform:translate3d(-100%,0,0)}[data-aos=slide-left]{transform:translate3d(100%,0,0)}[data-aos^=flip][data-aos^=flip]{backface-visibility:hidden;transition-property:transform}[data-aos=flip-left]{transform:perspective(2500px) rotateY(-100deg)}[data-aos=flip-left].aos-animate{transform:perspective(2500px) rotateY(0)}[data-aos=flip-right]{transform:perspective(2500px) rotateY(100deg)}[data-aos=flip-right].aos-animate{transform:perspective(2500px) rotateY(0)}[data-aos=flip-up]{transform:perspective(2500px) rotateX(-100deg)}[data-aos=flip-up].aos-animate{transform:perspective(2500px) rotateX(0)}[data-aos=flip-down]{transform:perspective(2500px) rotateX(100deg)}[data-aos=flip-down].aos-animate{transform:perspective(2500px) rotateX(0)}", ".form {\n  margin-top: var(--space-16);\n  margin-bottom: var(--space-16);\n}\n\n.row {\n  display: flex;\n  gap: var(--space-5);\n  margin-bottom: var(--space-4);\n  width: 100%;\n}\n\n.inputGroup {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.label {\n  font-size: var(--font-size-sm);\n  font-weight: var(--font-weight-medium);\n  color: var(--color-neutral-700);\n  margin-bottom: var(--space-2);\n  display: block;\n}\n\n.required {\n  color: var(--color-error);\n  font-weight: var(--font-weight-bold);\n}\n\n.input {\n  flex: 1;\n  padding: var(--space-3) var(--space-4);\n  border: var(--border-width-1) solid var(--color-neutral-400);\n  background-color: var(--color-neutral-100);\n  border-radius: var(--border-radius-sm);\n  font-size: var(--font-size-base);\n  font-family: var(--font-family-base);\n  width: 100%;\n  box-sizing: border-box;\n  min-height: 58px;\n  transition: var(--transition-fast);\n}\n\n.input:focus {\n  outline: none;\n  border-color: var(--color-primary);\n  box-shadow: 0 0 0 3px rgba(215, 0, 15, 0.1);\n}\n\n.input:disabled {\n  background-color: var(--color-neutral-200);\n  color: var(--color-neutral-500);\n  cursor: not-allowed;\n}\n\n.inputError {\n  border-color: var(--color-error);\n}\n\n.inputError:focus {\n  border-color: var(--color-error);\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n\n.textarea {\n  resize: vertical;\n  font-family: var(--font-family-base);\n}\n\n.selectWrapper {\n  flex: 1;\n  position: relative;\n}\n\n.errorMessage {\n  color: var(--color-error);\n  font-size: var(--font-size-sm);\n  margin-top: var(--space-1);\n  display: flex;\n  align-items: center;\n  gap: var(--space-1);\n}\n\n.errorMessage::before {\n  content: \"⚠\";\n  font-size: var(--font-size-sm);\n}\n\n.helpText {\n  color: var(--color-neutral-600);\n  font-size: var(--font-size-xs);\n  margin-top: var(--space-1);\n}\n\n.checkboxGroup {\n  margin: var(--space-6) 0;\n}\n\n.react-select__control {\n  border: 1px solid #ccc !important;\n  border-radius: 2px !important;\n  min-height: 42px !important;\n  font-size: 14px;\n}\n\n.react-select__placeholder {\n  color: #888;\n}\n\n.react-select__single-value {\n  color: #000;\n}\n\n.checkboxWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  gap: 10px;\n  font-size: 14px;\n  margin-bottom: 20px;\n  margin-top: 20px;\n}\n\n.checkbox {\n  width: 20px;\n  height: 20px;\n  margin-top: 3px;\n  accent-color: black;\n}\n\n.button {\n  background-color: black;\n  color: white;\n  padding: 10px 24px;\n  border: none;\n  border-radius: 2px;\n  font-weight: bold;\n  cursor: pointer;\n  -webkit-transition: background-color 0.3s ease;\n  -o-transition: background-color 0.3s ease;\n  transition: background-color 0.3s ease;\n}\n\n.button:hover {\n  background-color: #333;\n}\n\n@media (max-width: 900px) {\n  .form {\n    margin-top: 30px;\n  }\n  .row {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .input {\n    width: 100%;\n    height: 80px;\n    padding: 20px 34px 20px 1pc;\n  }\n\n  .checkboxWrapper {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n  }\n\n  .inputGroup {\n    margin-bottom: var(--space-4);\n  }\n\n  .errorMessage {\n    font-size: var(--font-size-xs);\n  }\n}\n\n/* Enhanced styles for accessibility and UX */\n.checkboxWrapper {\n  cursor: pointer;\n  padding: var(--space-2);\n  border-radius: var(--border-radius-sm);\n  transition: var(--transition-fast);\n}\n\n.checkboxWrapper:hover {\n  background-color: var(--color-neutral-50);\n}\n\n.checkbox:focus {\n  outline: 2px solid var(--color-primary);\n  outline-offset: 2px;\n}\n\n.checkboxLabel {\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-normal);\n  color: var(--color-neutral-700);\n}\n\n.button {\n  background-color: var(--color-primary) !important;\n  color: var(--color-neutral-100) !important;\n  padding: var(--space-4) var(--space-6) !important;\n  font-size: var(--font-size-base) !important;\n  font-weight: var(--font-weight-medium) !important;\n  font-family: var(--font-family-base) !important;\n  width: 100%;\n  min-height: 58px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n}\n\n.button:hover:not(:disabled) {\n  background-color: var(--color-primary-hover) !important;\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.button:disabled {\n  background-color: var(--color-neutral-400) !important;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Form title styles */\n/* .formTitle {\n  font-size: 28px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20px;\n  text-align: center;\n}\n\n@media (max-width: 768px) {\n  .formTitle {\n    font-size: 24px;\n    margin-bottom: 16px;\n  }\n} */\n", ".notification {\n  position: fixed;\n  top: 20px;\n  left: 50%;\n  -webkit-transform: translateX(-50%);\n  -ms-transform: translateX(-50%);\n  transform: translateX(-50%);\n  padding: 16px 24px;\n  border-radius: 4px;\n  font-weight: 500;\n  z-index: 9999;\n  color: white;\n  -webkit-animation: slideDown 0.3s ease-in-out;\n  animation: slideDown 0.3s ease-in-out;\n  max-width: 90vw;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  text-align: center;\n  word-break: break-word;\n}\n\n/* Типы уведомлений */\n.success {\n  background-color: #28a745;\n}\n\n.error {\n  background-color: #d7000f;\n}\n.icon {\n  margin-right: 8px;\n  font-size: 18px;\n  vertical-align: middle;\n}\n\n/* Анимация появления сверху вниз */\n@-webkit-keyframes slideDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate(-50%, -20px);\n    transform: translate(-50%, -20px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translate(-50%, 0);\n    transform: translate(-50%, 0);\n  }\n}\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate(-50%, -20px);\n    transform: translate(-50%, -20px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translate(-50%, 0);\n    transform: translate(-50%, 0);\n  }\n}\n\n/* Мобильные устройства */\n@media (max-width: 600px) {\n  .notification {\n    width: calc(100vw - 40px);\n    padding: 12px 16px;\n    font-size: 14px;\n  }\n}\n", ".filterBar {\n  display: block;\n  border: 1.5px solid var(--border-color-nav);\n  border-radius: 8px;\n  padding: 0 2pc;\n  margin-bottom: 20px;\n}\n.setting {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 40px;\n  padding: 30px;\n  width: 100%;\n  border: 1px solid #b1b1b1;\n  border-radius: 10px;\n  margin-bottom: 50px;\n}\n\n.mobseting {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  width: 100%;\n}\n.left {\n  border-right: 1px solid var(--border-color-nav);\n  padding-right: 40px;\n}\n\n.filterSelected {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: var(--text-black);\n  padding: 24px 0;\n}\n\n.filterSelected span {\n  font-size: 18px;\n  font-weight: 700;\n  line-height: 22px;\n}\n\n.iconPlus,\n.iconMinus {\n  font-size: 20px;\n  line-height: 22px;\n}\n\n.filterDropdownContainer {\n  transform: translateY(10px);\n  opacity: 0;\n  transition: all 0.3s ease;\n  height: 0;\n  overflow: hidden;\n}\n\n.filterDropdownContainer.show {\n  transform: translateY(0);\n  opacity: 1;\n  height: 100%;\n}\n\n.filterList {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.filterItem {\n  width: 100%;\n}\n\n.setting h4 {\n  font-size: 16px;\n  line-height: 24px;\n  margin-bottom: 16px;\n}\n\n.types,\n.bodyTypes {\n  display: flex;\n  gap: 20px;\n}\n\n.types li,\n.bodyTypes li {\n  border: 1px solid transparent;\n  border-radius: 3px;\n  cursor: pointer;\n  padding: 8px;\n  width: 100%;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 700;\n  position: relative;\n}\n\n.types li:hover,\n.bodyTypes li:hover {\n  border: 1px solid var(--text-black);\n}\n\n.types li.active,\n.bodyTypes li.active {\n  background-color: var(--background-black);\n  color: var(--text-white);\n  padding-left: 24px;\n}\n\n.types li.active::before,\n.bodyTypes li.active::before {\n  background: var(--theme-color);\n  border-radius: 1px;\n  content: '';\n  height: 8px;\n  left: 8px;\n  position: absolute;\n  top: 14px;\n  width: 8px;\n  z-index: 1;\n}\n\n/* Grid + Card */\n.grid {\n  margin-top: 56px;\n  margin-bottom: 40px;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));\n  gap: 20px;\n}\n\n.card {\n  border: 1px solid var(--card-border);\n  padding: 20px;\n  border-radius: 10px;\n  transition: box-shadow 0.3s ease;\n  background: var(--background-white);\n  position: relative;\n}\n\n.card:hover {\n  box-shadow: 0 4px 10px var(--card-hover-shadow);\n}\n\n.card img {\n  width: 100%;\n  height: auto;\n  object-fit: cover;\n}\n\n.cardInfo {\n  margin-top: 10px;\n}\n\n.cardInfo h4 {\n  color: #000;\n  font-size: 16px;\n}\n\n.desc {\n  margin-top: 10px;\n  font-size: 13px;\n  color: var(--text-black);\n}\n\n.label {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  right: 20px;\n}\n\n.noStok,\n.inStok {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  left: 30px;\n}\n\n.noStok::before,\n.inStok::before {\n  content: '';\n  height: 6px;\n  width: 6px;\n  border-radius: 50%;\n  top: 4px;\n  left: -10px;\n  position: absolute;\n  background-color: var(--stock-green);\n}\n\n.noStok::before {\n  background-color: var(--theme-color);\n}\n\n.noCarText {\n  padding: 20px;\n  font-size: 18px;\n  text-align: center;\n  line-height: 1.5;\n  color: var(--text-muted);\n}\n\n.filterBar {\n  display: none;\n}\n@media screen and (max-width: 1050px) {\n  .setting {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n    padding: 30px;\n    width: 100%;\n    border: 1px solid #b1b1b1;\n    border-radius: 10px;\n    margin-bottom: 50px;\n  }\n  .left {\n    border-right: none;\n    border-bottom: 1px solid var(--border-color-nav);\n    padding-bottom: 20px;\n  }\n}\n@media screen and (max-width: 900px) {\n  .filterBar {\n    display: block;\n  }\n  .modelFilter {\n    display: none;\n  }\n  .mobseting {\n    display: flex;\n    flex-direction: column;\n    flex-wrap: wrap;\n    gap: 20px;\n    padding: 10px;\n    width: 100%;\n  }\n  .mobseting ul {\n    margin-top: 20px;\n  }\n  .types,\n  .bodyTypes {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n}\n", ".flexContent {\n  display: -ms-grid;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n}\n\n.card {\n  background-color: #fff;\n  border-radius: 12px;\n  -webkit-box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  height: 420px; /* Фиксированная высота */\n  padding: 16px;\n}\n\n.img img {\n  width: 100%;\n  height: 160px;\n  -o-object-fit: cover;\n  object-fit: cover;\n  border-radius: 8px;\n  margin-bottom: 12px;\n}\n\n.title {\n  -webkit-box-flex: 1;\n  -ms-flex-positive: 1;\n  flex-grow: 1;\n}\n\n.title h2 {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 6px;\n  color: #111;\n  line-height: 1.4;\n  margin-top: 10px;\n\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.title span {\n  font-size: 14px;\n  color: #888;\n}\n\n.btn {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: end;\n  -ms-flex-pack: end;\n  justify-content: flex-end;\n}\n\n.btn button {\n  padding: 8px 16px;\n  background-color: #000;\n  color: #fff;\n  font-weight: 600;\n  font-size: 14px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  -webkit-transition: background 0.3s ease;\n  -o-transition: background 0.3s ease;\n  transition: background 0.3s ease;\n}\n\n.btn button:hover {\n  background-color: #333;\n}\n\n/* Скелетоны */\n.skeletonCard {\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n.skeleton {\n  background-color: #d3d1d1;\n  border-radius: 8px;\n}\n.skeleton_img {\n  background-color: #d3d1d1;\n  border-radius: 8px;\n  height: 200px;\n}\n\n.textLine {\n  height: 20px;\n  margin: 8px 0;\n  width: 80%;\n}\n\n.linkLine {\n  height: 16px;\n  width: 60%;\n  margin-top: 12px;\n}\n\n/* Анимация пульсации */\n@-webkit-keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n@keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n/* Лоадер */\n.loaderWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  height: 100vh;\n  background-color: #fff;\n  z-index: 1000;\n  position: fixed;\n  width: 100%;\n  top: 0;\n  left: 0;\n}\n\n.loaderPage {\n  width: 50px;\n  height: 50px;\n  border: 6px solid #ccc;\n  border-top-color: #000;\n  border-radius: 50%;\n  -webkit-animation: spin 0.8s linear infinite;\n  animation: spin 0.8s linear infinite;\n}\n\n@-webkit-keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n", ".date {\n  font-size: 0.9rem;\n  color: #888;\n  margin-bottom: 20px;\n}\n\n.imageWrapper {\n  width: 100%;\n  overflow: hidden;\n  border-radius: 12px;\n}\n\n.imageWrapper img {\n  width: 100%;\n  height: auto;\n  border-radius: 12px;\n  -o-object-fit: cover;\n  object-fit: cover;\n  -webkit-transition: -webkit-transform 0.3s ease;\n  transition: -webkit-transform 0.3s ease;\n  -o-transition: transform 0.3s ease;\n  transition: transform 0.3s ease;\n  transition: transform 0.3s ease, -webkit-transform 0.3s ease;\n}\n\n.imageWrapper img:hover {\n  -webkit-transform: scale(1.02);\n  -ms-transform: scale(1.02);\n  transform: scale(1.02);\n}\n\n.subTitle {\n  font-size: 1.2rem;\n  font-weight: 500;\n  line-height: 1.6;\n  margin: 20px 0px;\n}\n\n.text {\n  font-size: 1.05rem;\n  line-height: 1.7;\n  color: #444;\n  white-space: pre-line;\n  margin-bottom: 20px;\n}\n\n/* Анимация появления */\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(12px);\n    transform: translateY(12px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(12px);\n    transform: translateY(12px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n/* Адаптив */\n@media (max-width: 768px) {\n  .title {\n    font-size: 1.5rem;\n  }\n\n  .subTitle,\n  .text {\n    font-size: 1rem;\n  }\n\n  .content {\n    padding: 20px 16px;\n  }\n}\n", ".flexContent {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  gap: 40px;\n  padding: 40px 0;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n}\n\n.flexContent:nth-of-type(even) {\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: reverse;\n  -ms-flex-direction: row-reverse;\n  flex-direction: row-reverse;\n}\n\n.img {\n  -webkit-box-flex: 1;\n  -ms-flex: 1 1 40%;\n  flex: 1 1 40%;\n  max-width: 500px;\n}\n\n.img img {\n  width: 100%;\n  height: auto;\n  border-radius: 12px;\n  -o-object-fit: cover;\n  object-fit: cover;\n}\n\n.text {\n  -webkit-box-flex: 1;\n  -ms-flex: 1 1 55%;\n  flex: 1 1 55%;\n}\n\n.text h2 {\n  font-size: 32px;\n  margin-bottom: 16px;\n}\n\n.text p {\n  font-size: 18px;\n  line-height: 1.6;\n  margin-bottom: 12px;\n  color: #444;\n}\n\n@media screen and (max-width: 1200px) {\n  .text h2 {\n    font-size: 28px;\n  }\n\n  .text p {\n    font-size: 16px;\n  }\n}\n/* Адаптация под мобильные устройства */\n@media (max-width: 768px) {\n  .flexContent {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    gap: 24px;\n    padding: 20px 0;\n  }\n\n  .text h2 {\n    font-size: 24px;\n  }\n\n  .text p {\n    font-size: 14px;\n  }\n}\n", ".slideContent {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  gap: 40px;\n  padding: 40px 20px;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.leftImage {\n  -webkit-box-flex: 1;\n  -ms-flex: 1 1;\n  flex: 1 1;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  max-width: 500px;\n}\n\n.leftImage img {\n  width: 100%;\n  max-width: 500px;\n  aspect-ratio: 4 / 3;\n  -o-object-fit: cover;\n  object-fit: cover;\n  border-radius: 12px;\n  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.rightText {\n  -webkit-box-flex: 1;\n  -ms-flex: 1 1;\n  flex: 1 1;\n  max-width: 500px;\n}\n\n.rightText h3 {\n  font-size: 28px;\n  font-weight: 700;\n  margin-bottom: 10px;\n}\n\n.rightText p {\n  font-size: 18px;\n  line-height: 1.6;\n}\n\n/* ===== Прогресс-бар ===== */\n.progressBarWrapper {\n  position: relative;\n  width: 100%;\n  height: 4px;\n  background-color: #eee;\n  margin-bottom: 20px;\n  overflow: hidden;\n  border-radius: 2px;\n}\n\n.progressBar {\n  height: 100%;\n  background-color: red;\n  -webkit-transition: width 0.4s ease;\n  -o-transition: width 0.4s ease;\n  transition: width 0.4s ease;\n  border-radius: 2px;\n}\n\n/* ===== Адаптив ===== */\n@media (max-width: 992px) {\n  .slideContent {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    padding: 30px 16px;\n  }\n\n  .rightText h3 {\n    font-size: 24px;\n  }\n\n  .rightText p {\n    font-size: 16px;\n  }\n\n  .leftImage img {\n    max-width: 100%;\n  }\n}\n\n@media (max-width: 600px) {\n  .slideContent {\n    gap: 24px;\n    padding: 20px 10px;\n  }\n\n  .rightText h3 {\n    font-size: 20px;\n  }\n\n  .rightText p {\n    font-size: 15px;\n    height: 100px;\n    overflow: auto;\n  }\n\n  .leftImage img {\n    aspect-ratio: 4 / 3;\n  }\n}\n", ":root {\n  --swiper-navigation-size: 44px;\n  /*\n  --swiper-navigation-top-offset: 50%;\n  --swiper-navigation-sides-offset: 10px;\n  --swiper-navigation-color: var(--swiper-theme-color);\n  */\n}\n.swiper-button-prev,\n.swiper-button-next {\n  position: absolute;\n  top: var(--swiper-navigation-top-offset, 50%);\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\n  height: var(--swiper-navigation-size);\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\n  z-index: 10;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\n}\n.swiper-button-prev.swiper-button-disabled,\n.swiper-button-next.swiper-button-disabled {\n  opacity: 0.35;\n  cursor: auto;\n  pointer-events: none;\n}\n.swiper-button-prev.swiper-button-hidden,\n.swiper-button-next.swiper-button-hidden {\n  opacity: 0;\n  cursor: auto;\n  pointer-events: none;\n}\n.swiper-navigation-disabled .swiper-button-prev,\n.swiper-navigation-disabled .swiper-button-next {\n  display: none !important;\n}\n.swiper-button-prev svg,\n.swiper-button-next svg {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  transform-origin: center;\n}\n.swiper-rtl .swiper-button-prev svg,\n.swiper-rtl .swiper-button-next svg {\n  transform: rotate(180deg);\n}\n.swiper-button-prev,\n.swiper-rtl .swiper-button-next {\n  left: var(--swiper-navigation-sides-offset, 10px);\n  right: auto;\n}\n.swiper-button-next,\n.swiper-rtl .swiper-button-prev {\n  right: var(--swiper-navigation-sides-offset, 10px);\n  left: auto;\n}\n.swiper-button-lock {\n  display: none;\n}\n/* Navigation font start */\n.swiper-button-prev:after,\n.swiper-button-next:after {\n  font-family: swiper-icons;\n  font-size: var(--swiper-navigation-size);\n  text-transform: none !important;\n  letter-spacing: 0;\n  font-variant: initial;\n  line-height: 1;\n}\n.swiper-button-prev:after,\n.swiper-rtl .swiper-button-next:after {\n  content: 'prev';\n}\n.swiper-button-next,\n.swiper-rtl .swiper-button-prev {\n  right: var(--swiper-navigation-sides-offset, 10px);\n  left: auto;\n}\n.swiper-button-next:after,\n.swiper-rtl .swiper-button-prev:after {\n  content: 'next';\n}\n/* Navigation font end */\n", ".layout {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n}\n\n.main {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  padding: 40px 0px 40px 20px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  overflow-y: hidden;\n}\n\n.mainContainer {\n  width: 100%;\n}\n.mainContainer h1 {\n  font-size: 60px;\n  margin-bottom: 40px;\n  font-weight: 700;\n}\n.underText {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 2rem;\n  width: 70%;\n  display: block;\n  margin-bottom: 10px;\n}\n.redLine {\n  width: 100px;\n  height: 6px;\n  background-color: var(--theme-color);\n  display: block;\n  margin-bottom: 40px;\n}\n.banner {\n  width: 100%;\n  height: auto;\n  border-radius: 12px;\n  margin-bottom: 24px;\n  -o-object-fit: cover;\n  object-fit: cover;\n}\n\n.textContent p {\n  margin-bottom: 16px;\n}\n\n.flexContent {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  gap: 24px;\n  margin-top: 32px;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n}\n\n.flexContent .banner {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  max-width: 50%;\n  height: 400px;\n  border-radius: 12px;\n  -o-object-fit: cover;\n  object-fit: cover;\n}\n\n.flexContent .textContent {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  font-size: 16px;\n  line-height: 1.6;\n  color: #000;\n  max-width: 50%;\n}\n.textContent ul li {\n  list-style: circle;\n  margin-left: 30px;\n}\n\n.flexImg {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n  gap: 20px;\n  -webkit-box-pack: start;\n  -ms-flex-pack: start;\n  justify-content: flex-start;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  width: 100%;\n}\n\n.flexImg img {\n  -webkit-box-flex: 0;\n  -ms-flex: 0 1 200px;\n  flex: 0 1 200px;\n  width: 100%;\n  height: 200px;\n  -o-object-fit: cover;\n  object-fit: cover;\n  border-radius: 8px;\n}\n\n/* table  */\n.tableWrapper {\n  max-height: 500px; /* или любое нужное значение */\n  overflow-y: auto;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  width: 100%;\n}\n\n.customTable {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.customTable th,\n.customTable td {\n  padding: 12px 16px;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\n.customTable thead th {\n  position: sticky;\n  top: 0;\n  background-color: #fbf9f9;\n  z-index: 1;\n  font-weight: 600;\n  color: var(--theme-color);\n  border-bottom: 1px solid #ccc;\n}\n\n.customTable tr:nth-child(even) {\n  background-color: #f6f6f6;\n}\n\n.customTable tr:hover {\n  background-color: #e9f5ff;\n}\n\n@media screen and (max-width: 1080px) {\n  .mainContainer h1 {\n    font-size: 35px;\n    margin-bottom: 40px;\n  }\n  .underText {\n    font-size: 18px;\n    line-height: 1.8rem;\n    width: 100%;\n  }\n}\n\n/* Адаптивность для мобильных устройств */\n@media (max-width: 900px) {\n  .layout {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n  }\n  .main {\n    padding: 20px 0px 40px 0px;\n  }\n  .flexContent {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-align: stretch;\n    -ms-flex-align: stretch;\n    align-items: stretch;\n  }\n\n  .flexContent .banner,\n  .flexContent .textContent {\n    max-width: 100%;\n    height: auto;\n  }\n\n  .flexContent .banner {\n    height: 300px;\n  }\n\n  .textContent {\n    font-size: 15px;\n  }\n  .flexImg img {\n    -webkit-box-flex: 0;\n    -ms-flex: 0 1 100px;\n    flex: 0 1 100px;\n    width: 100%;\n    height: 100px;\n  }\n}\n\n/* Адаптивность для мобильных устройств */\n@media (max-width: 768px) {\n  .flexContent {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n  }\n\n  .banner,\n  .textContent {\n    max-width: 100%;\n  }\n  .underText {\n    width: 100%;\n  }\n  .mainContainer h1 {\n    font-size: 30px;\n    margin-bottom: 12px;\n  }\n  .underText {\n    font-size: 18px;\n    line-height: 1.8rem;\n    width: 100%;\n  }\n  .main {\n    padding: 20px 0px 30px 0px;\n  }\n\n  .customTable th,\n  .customTable td {\n    padding: 8px 14px;\n    font-size: 13px;\n  }\n}\n", "/* side */\n.sidebar {\n  border-right: 1px solid var(--border-color-nav);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -ms-flex-negative: 0;\n  flex-shrink: 0;\n  overflow-y: auto;\n  padding: 24px;\n  width: 240px;\n}\n\n.nav h4,\n.nav h4 {\n  font-size: 1pc;\n  line-height: 24px;\n  margin-bottom: 1pc;\n}\n\n.links {\n  margin-bottom: 8px;\n  padding-bottom: 8px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n}\n\n.links a {\n  border: 1px solid transparent;\n  border-radius: 3px;\n  cursor: pointer;\n  margin-bottom: 8px;\n  padding: 8px;\n  width: 100%;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 700;\n  position: relative;\n  color: #000;\n}\n\n.links a:hover {\n  border: 1px solid var(--text-black);\n}\n\n.links a.active {\n  background-color: var(--background-black);\n  color: var(--text-white);\n  padding-left: 24px;\n}\n\n.links a.active:before {\n  background: var(--theme-color);\n  border-radius: 1px;\n  content: '';\n  height: 8px;\n  left: 8px;\n  position: absolute;\n  top: 14px;\n  width: 8px;\n  z-index: 1;\n}\n\n.filterBar {\n  display: none;\n}\n@media screen and (max-width: 900px) {\n  .sidebar {\n    display: none;\n  }\n  .filterBar {\n    display: block;\n    border: 1.5px solid var(--border-color);\n    border-radius: 8px;\n    padding: 0px 2pc;\n    margin-bottom: 20px;\n    margin-top: 20px;\n  }\n\n  .filterSelected {\n    display: -webkit-box;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: justify;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    -webkit-box-align: center;\n    -ms-flex-align: center;\n    align-items: center;\n    color: var(--text-black);\n    padding: 24px 0;\n  }\n\n  .filterSelected span {\n    font-size: 18px;\n    font-weight: 700;\n    line-height: 22px;\n  }\n\n  .iconPlus,\n  .iconMunes {\n    font-size: 20px;\n    line-height: 22px;\n  }\n\n  .filterDropdownContainer {\n    -webkit-transform: translateY(10px);\n    -ms-transform: translateY(10px);\n    transform: translateY(10px);\n    opacity: 0;\n    -webkit-transition: all 0.3s ease;\n    -o-transition: all 0.3s ease;\n    transition: all 0.3s ease;\n    height: 0;\n    overflow: hidden;\n  }\n\n  .filterDropdownContainer.show {\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n    opacity: 1;\n    height: 100%;\n  }\n}\n", ".form {\n  margin-top: 60px;\n  margin-bottom: 60px;\n}\n\n.row {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  gap: 20px;\n  margin-bottom: 15px;\n  width: 100%;\n}\n\n.input {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  padding: 8px 34px 8px 1pc;\n  border: 1px solid #8e8e93;\n  background-color: #fff;\n  border-radius: 2px;\n  font-size: 16px;\n  width: 100%;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n  height: 58px;\n}\n\n.selectWrapper {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n}\n\n.react-select__control {\n  border: 1px solid #ccc !important;\n  border-radius: 2px !important;\n  min-height: 42px !important;\n  font-size: 14px;\n}\n\n.react-select__placeholder {\n  color: #888;\n}\n\n.react-select__single-value {\n  color: #000;\n}\n\n.checkboxWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  gap: 10px;\n  font-size: 14px;\n  margin-bottom: 20px;\n  margin-top: 20px;\n}\n\n.checkbox {\n  width: 20px;\n  height: 20px;\n  margin-top: 3px;\n  accent-color: black;\n}\n\n.button {\n  background-color: black;\n  color: white;\n  padding: 10px 24px;\n  border: none;\n  border-radius: 2px;\n  font-weight: bold;\n  cursor: pointer;\n  -webkit-transition: background-color 0.3s ease;\n  -o-transition: background-color 0.3s ease;\n  transition: background-color 0.3s ease;\n}\n\n.button:hover {\n  background-color: #333;\n}\n\n@media (max-width: 900px) {\n  .form {\n    margin-top: 30px;\n  }\n  .row {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .input {\n    width: 100%;\n    height: 80px;\n    padding: 20px 34px 20px 1pc;\n  }\n\n  .checkboxWrapper {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n  }\n}\n", ".selectWrapper {\n  margin: auto;\n  margin-top: 50px;\n  width: 70%;\n}\n.cardsWrapper {\n  margin-top: 30px;\n  display: -ms-grid;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));\n  gap: 20px;\n}\n.card {\n  border: 1px solid var(--card-border);\n  padding: 20px;\n  border-radius: 10px;\n  -webkit-transition: -webkit-box-shadow 0.3s ease;\n  transition: -webkit-box-shadow 0.3s ease;\n  -o-transition: box-shadow 0.3s ease;\n  transition: box-shadow 0.3s ease;\n  transition: box-shadow 0.3s ease, -webkit-box-shadow 0.3s ease;\n  background: var(--background-white);\n  position: relative;\n}\n\n.card:hover {\n  -webkit-box-shadow: 0 4px 10px var(--card-hover-shadow);\n  box-shadow: 0 4px 10px var(--card-hover-shadow);\n}\n\n.card img {\n  width: 100%;\n  height: auto;\n  -o-object-fit: cover;\n  object-fit: cover;\n}\n\n.cardInfo {\n  margin-top: 10px;\n}\n.cardInfo h4 {\n  color: #000;\n  font-size: 16px;\n}\n.label {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  right: 20px;\n}\n\n.noStok,\n.inStok {\n  font-size: 12px;\n  color: var(--text-black);\n  font-weight: 500;\n  position: absolute;\n  top: 20px;\n  left: 30px;\n}\n\n.noStok::before,\n.inStok::before {\n  content: '';\n  height: 6px;\n  width: 6px;\n  border-radius: 50%;\n  top: 4px;\n  left: -10px;\n  position: absolute;\n  background-color: var(--stock-green);\n}\n\n.noStok::before {\n  background-color: var(--theme-color);\n}\n\n.desc {\n  margin-top: 10px;\n  font-size: 13px;\n  color: var(--text-black);\n}\n\n@media (max-width: 768px) {\n  .selectWrapper {\n    width: 100%;\n  }\n  .selectWrapper {\n    margin: auto;\n    margin-top: 20px;\n  }\n}\n", ".flexContent {\n  display: -ms-grid;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n}\n\n.card {\n  background-color: #fff;\n  border-radius: 12px;\n  -webkit-box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  height: 420px; /* Фиксированная высота */\n  padding: 16px;\n}\n\n.img img {\n  width: 100%;\n  height: 160px;\n  -o-object-fit: cover;\n  object-fit: cover;\n  border-radius: 8px;\n  margin-bottom: 12px;\n}\n\n.title {\n  -webkit-box-flex: 1;\n  -ms-flex-positive: 1;\n  flex-grow: 1;\n}\n\n.title h2 {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 6px;\n  color: #111;\n  line-height: 1.4;\n  margin-top: 10px;\n\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.title span {\n  font-size: 14px;\n  color: #888;\n}\n\n.btn {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: end;\n  -ms-flex-pack: end;\n  justify-content: flex-end;\n}\n\n.btn button {\n  padding: 8px 16px;\n  background-color: #000;\n  color: #fff;\n  font-weight: 600;\n  font-size: 14px;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  -webkit-transition: background 0.3s ease;\n  -o-transition: background 0.3s ease;\n  transition: background 0.3s ease;\n}\n\n.btn button:hover {\n  background-color: #333;\n}\n\n/* Скелетоны */\n.skeletonCard {\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n.skeleton {\n  background-color: #d3d1d1;\n  border-radius: 8px;\n}\n.skeleton_img {\n  background-color: #d3d1d1;\n  border-radius: 8px;\n  height: 200px;\n}\n\n.textLine {\n  height: 20px;\n  margin: 8px 0;\n  width: 80%;\n}\n\n.linkLine {\n  height: 16px;\n  width: 60%;\n  margin-top: 12px;\n}\n\n/* Анимация пульсации */\n@-webkit-keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n@keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n/* Лоадер */\n.loaderWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  height: 100vh;\n  background-color: #fff;\n  z-index: 1000;\n  position: fixed;\n  width: 100%;\n  top: 0;\n  left: 0;\n}\n\n.loaderPage {\n  width: 50px;\n  height: 50px;\n  border: 6px solid #ccc;\n  border-top-color: #000;\n  border-radius: 50%;\n  -webkit-animation: spin 0.8s linear infinite;\n  animation: spin 0.8s linear infinite;\n}\n\n@-webkit-keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes spin {\n  to {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n", ".content button,\n.content span {\n  color: #000;\n}\n.box {\n  background-color: #fff;\n  padding: 1rem;\n  border-radius: 8px;\n}\n\n.accordion {\n  border-top: 1px solid #ccc;\n  padding: 20px 0px;\n}\n\n.accordionHeader {\n  background: none;\n  border: none;\n  width: 100%;\n  padding: 2rem 0;\n  text-align: left;\n  font-size: 1rem;\n  font-weight: bold;\n  cursor: pointer;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: justify;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n}\n\n.accordionContentWrapper {\n  overflow: hidden;\n  -webkit-transition: max-height 0.3s ease;\n  -o-transition: max-height 0.3s ease;\n  transition: max-height 0.3s ease;\n  max-height: 0;\n}\n\n.accordionContentWrapper.open {\n  max-height: max-content;\n}\n\n.accordionContent {\n  padding: 15px 0;\n  font-size: 0.95rem;\n  color: #444;\n}\n", ".contactSection {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  gap: 40px;\n  padding: 0px 0px 30px 0px;\n}\n\n.skeletonCard {\n  background: #f3f3f3;\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n.iconSkeleton {\n  width: 32px;\n  height: 32px;\n  background: #ddd;\n  border-radius: 50%;\n  margin-right: 10px;\n}\n\n.textSkeleton {\n  height: 18px;\n  background: #e0e0e0;\n  border-radius: 8px;\n  margin: 8px 0;\n}\n\n.mapSkeleton {\n  width: 100%;\n  height: 400px;\n  background: #e0e0e0;\n  border-radius: 8px;\n  -webkit-animation: pulse 1.5s infinite ease-in-out;\n  animation: pulse 1.5s infinite ease-in-out;\n}\n\n@-webkit-keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n@keyframes pulse {\n  0% {\n    background-color: #e0e0e0;\n  }\n  50% {\n    background-color: #f0f0f0;\n  }\n  100% {\n    background-color: #e0e0e0;\n  }\n}\n\n/* Двухколоночный layout для больших экранов */\n@media (min-width: 768px) {\n  .contactSection {\n    -webkit-box-orient: horizontal;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: row;\n    flex-direction: row;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n  }\n}\n\n.leftColumn {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.rightColumn {\n  -webkit-box-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  max-width: 600px;\n}\n\n.cardWrapper {\n  display: -ms-grid;\n  display: grid;\n  -ms-grid-columns: 1fr;\n  grid-template-columns: 1fr;\n  gap: 24px;\n}\n\n@media (min-width: 768px) {\n  .cardWrapper {\n    -ms-grid-columns: 1fr;\n    grid-template-columns: 1fr;\n  }\n}\n\n.cardItem {\n  border: 1px solid #e5e7eb;\n  padding: 24px;\n  border-radius: 12px;\n  text-align: left;\n}\n\n.icon {\n  font-size: 28px;\n  color: #e30613;\n  margin-right: 12px;\n}\n\n.cardItemHeader {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.cardItem h3 {\n  font-size: 18px;\n  margin: 0;\n  color: #111827;\n}\n\n.cardItem p,\n.cardItem a {\n  font-size: 16px;\n  color: #4b5563;\n  text-decoration: none;\n}\n\n.cardItem a:hover {\n  color: #e30613;\n}\n\n.mapWrapper {\n  border-radius: 12px;\n  overflow: hidden;\n  border: 1px solid #ccc;\n  height: 400px;\n}\n\n.map {\n  width: 100%;\n  height: 100%;\n  border: 0;\n}\n", ".form {\n  margin-top: var(--space-16);\n  margin-bottom: var(--space-16);\n}\n\n.row {\n  display: flex;\n  gap: var(--space-5);\n  margin-bottom: var(--space-4);\n  width: 100%;\n}\n\n.inputGroup {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.label {\n  font-size: var(--font-size-sm);\n  font-weight: var(--font-weight-medium);\n  color: var(--color-neutral-700);\n  margin-bottom: var(--space-2);\n  display: block;\n}\n\n.required {\n  color: var(--color-error);\n  font-weight: var(--font-weight-bold);\n}\n\n.input {\n  flex: 1;\n  padding: var(--space-3) var(--space-4);\n  border: var(--border-width-1) solid var(--color-neutral-400);\n  background-color: var(--color-neutral-100);\n  border-radius: var(--border-radius-sm);\n  font-size: var(--font-size-base);\n  font-family: var(--font-family-base);\n  width: 100%;\n  box-sizing: border-box;\n  min-height: 58px;\n  transition: var(--transition-fast);\n}\n\n.input:focus {\n  outline: none;\n  border-color: var(--color-primary);\n  box-shadow: 0 0 0 3px rgba(215, 0, 15, 0.1);\n}\n\n.input:disabled {\n  background-color: var(--color-neutral-200);\n  color: var(--color-neutral-500);\n  cursor: not-allowed;\n}\n\n.inputError {\n  border-color: var(--color-error);\n}\n\n.inputError:focus {\n  border-color: var(--color-error);\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n\n.textarea {\n  resize: vertical;\n  font-family: var(--font-family-base);\n}\n\n.selectWrapper {\n  flex: 1;\n  position: relative;\n}\n\n.errorMessage {\n  color: var(--color-error);\n  font-size: var(--font-size-sm);\n  margin-top: var(--space-1);\n  display: flex;\n  align-items: center;\n  gap: var(--space-1);\n}\n\n.errorMessage::before {\n  content: '⚠';\n  font-size: var(--font-size-sm);\n}\n\n.helpText {\n  color: var(--color-neutral-600);\n  font-size: var(--font-size-xs);\n  margin-top: var(--space-1);\n}\n\n.checkboxGroup {\n  margin: var(--space-6) 0;\n}\n\n.react-select__control {\n  border: 1px solid #ccc !important;\n  border-radius: 2px !important;\n  min-height: 42px !important;\n  font-size: 14px;\n}\n\n.react-select__placeholder {\n  color: #888;\n}\n\n.react-select__single-value {\n  color: #000;\n}\n\n.checkboxWrapper {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: start;\n  -ms-flex-align: start;\n  align-items: flex-start;\n  gap: 10px;\n  font-size: 14px;\n  margin-bottom: 20px;\n  margin-top: 20px;\n}\n\n.checkbox {\n  width: 20px;\n  height: 20px;\n  margin-top: 3px;\n  accent-color: black;\n}\n\n.button {\n  background-color: black;\n  color: white;\n  padding: 10px 24px;\n  border: none;\n  border-radius: 2px;\n  font-weight: bold;\n  cursor: pointer;\n  -webkit-transition: background-color 0.3s ease;\n  -o-transition: background-color 0.3s ease;\n  transition: background-color 0.3s ease;\n}\n\n.button:hover {\n  background-color: #333;\n}\n\n@media (max-width: 900px) {\n  .form {\n    margin-top: 30px;\n  }\n  .row {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .input {\n    width: 100%;\n    height: 80px;\n    padding: 20px 34px 20px 1pc;\n  }\n\n  .checkboxWrapper {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -ms-flex-direction: column;\n    flex-direction: column;\n    -webkit-box-align: start;\n    -ms-flex-align: start;\n    align-items: flex-start;\n  }\n\n  .inputGroup {\n    margin-bottom: var(--space-4);\n  }\n\n  .errorMessage {\n    font-size: var(--font-size-xs);\n  }\n}\n\n/* Enhanced styles for accessibility and UX */\n.checkboxWrapper {\n  cursor: pointer;\n  padding: var(--space-2);\n  border-radius: var(--border-radius-sm);\n  transition: var(--transition-fast);\n}\n\n.checkboxWrapper:hover {\n  background-color: var(--color-neutral-50);\n}\n\n.checkbox:focus {\n  outline: 2px solid var(--color-primary);\n  outline-offset: 2px;\n}\n\n.checkboxLabel {\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-normal);\n  color: var(--color-neutral-700);\n}\n\n.button {\n  background-color: var(--color-primary) !important;\n  color: var(--color-neutral-100) !important;\n  padding: var(--space-4) var(--space-6) !important;\n  font-size: var(--font-size-base) !important;\n  font-weight: var(--font-weight-medium) !important;\n  font-family: var(--font-family-base) !important;\n  width: 100%;\n  min-height: 58px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n}\n\n.button:hover:not(:disabled) {\n  background-color: var(--color-primary-hover) !important;\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n\n.button:disabled {\n  background-color: var(--color-neutral-400) !important;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n", ".box {\n  background-image: url('../../asset/imgs/404/404.webp');\n  background-position: center;\n  background-size: cover;\n  background-repeat: no-repeat;\n\n  min-height: 60vh;\n  /* гнучка висота відносно екрану */\n  padding: 30px;\n  border-radius: 20px;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n/* Мобільна адаптація */\n@media (max-width: 768px) {\n  .box {\n    padding: 20px;\n    border-radius: 12px;\n    min-height: 50vh;\n  }\n}\n\n@media (max-width: 480px) {\n  .box {\n    padding: 16px;\n    min-height: 45vh;\n    border-radius: 10px;\n    background-position: top;\n  }\n}", ".header {\n  height: 100vh;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n  color: #fff;\n}\n\n.section {\n  padding: 50px 0px;\n}\n\n.slideContent {\n  color: white;\n  padding: 13pc 0px 117px;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-box-pack: justify;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  justify-content: space-between;\n}\n\n.slideContent h1 {\n  font-size: 58px;\n  font-weight: bold;\n  width: 60%;\n}\n\n.slideContent .about {\n  margin-top: 20px;\n  margin-bottom: 30px;\n}\n\n.innerSele,\n.innerNew {\n  background: rgba(242, 242, 247, 0.5);\n  border-radius: 16px;\n  color: #fff;\n  cursor: default;\n  display: block;\n  margin-bottom: 16px;\n  padding: 9pt;\n  width: max-content;\n  font-weight: 600;\n  font-size: 16px;\n}\n\n.innerSele {\n  background: rgba(251, 89, 102, 0.5);\n}\n\n/* Hero Section */\n.firstSection {\n  height: 100vh;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-size: cover;\n  background-position: center;\n  z-index: -1;\n}\n\n.bg::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.3);\n}\n\n.content {\n  text-align: center;\n  z-index: 1;\n}\n\n.content h1 {\n  font-size: 4rem;\n  font-weight: bold;\n  margin-bottom: 1rem;\n}\n\n.content p {\n  font-size: 1.5rem;\n  opacity: 0.9;\n}\n\n/* Color Section */\n.colorSection {\n  padding: 80px 0;\n  background: #f8f9fa;\n}\n\n.colorContent {\n  display: flex;\n  align-items: center;\n  gap: 60px;\n}\n\n.colorInfo {\n  flex: 1;\n}\n\n.colorInfo h2 {\n  font-size: 2.5rem;\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.colorButtons {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.colorBtn {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: 3px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.colorName {\n  font-size: 1.2rem;\n  color: #666;\n  font-weight: 500;\n}\n\n.colorImage {\n  flex: 1;\n  text-align: center;\n}\n\n.colorImage img {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Feature Section */\n.featureSection {\n  padding: 80px 0;\n}\n\n.featureSection:nth-child(even) {\n  background: #f8f9fa;\n}\n\n.content {\n  display: flex;\n  align-items: center;\n  gap: 60px;\n}\n\n.imageFirst .textContent {\n  order: 2;\n}\n\n.imageFirst .imageContent {\n  order: 1;\n}\n\n.textFirst .textContent {\n  order: 1;\n}\n\n.textFirst .imageContent {\n  order: 2;\n}\n\n.textContent {\n  flex: 1;\n}\n\n.textContent h2 {\n  font-size: 2.5rem;\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.textContent p {\n  font-size: 1.1rem;\n  line-height: 1.6;\n  color: #666;\n}\n\n.imageContent {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n}\n\n.imageWrapper {\n  flex: 1;\n}\n\n.imageWrapper img {\n  width: 100%;\n  height: auto;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.imageWrapper img:hover {\n  transform: scale(1.05);\n}\n\n/* Gallery Grid */\n.galleryGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-top: 40px;\n}\n\n.galleryItem {\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.galleryItem img {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.galleryItem img:hover {\n  transform: scale(1.05);\n}\n\n/* Performance Section */\n.performanceSection {\n  padding: 80px 0;\n  background: #f8f9fa;\n}\n\n.dowBtn {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  background: #fb5966;\n  color: white;\n  padding: 15px 30px;\n  border-radius: 8px;\n  text-decoration: none;\n  font-weight: 600;\n  transition: background 0.3s ease;\n  margin-top: 30px;\n}\n\n.dowBtn:hover {\n  background: #e54855;\n  color: white;\n}\n\n.dowBtn img {\n  width: 20px;\n  height: 20px;\n}\n\n/* Gallery Section */\n.gallerySection {\n  padding: 80px 0;\n}\n\n.galleryTabs {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 40px;\n  justify-content: center;\n}\n\n.tabBtn {\n  padding: 12px 30px;\n  border: 2px solid #ddd;\n  background: white;\n  color: #666;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.tabBtn:hover,\n.tabBtn.active {\n  border-color: black;\n  background: black;\n  color: white;\n  border-radius: 0;\n}\n\n/* colors  */\n.colorImage {\n  width: 100%;\n  height: auto;\n}\n\n.colorPickerWrapper {\n  display: flex;\n  gap: 8px;\n  padding: 8px;\n  background-color: black;\n  border-radius: 8px;\n  width: fit-content;\n  margin: 30px auto;\n}\n\n.section .colorTitle {\n  font-size: 58px;\n  text-transform: uppercase;\n  font-weight: bold;\n  text-align: center;\n}\n\n.colorBtn {\n  min-width: 40px;\n  height: 40px;\n  border: 1px solid #fff;\n  border-radius: 3px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 10px;\n  cursor: pointer;\n  box-sizing: border-box;\n  transition: all 0.2s ease;\n  padding: 0;\n  font-weight: normal;\n}\n\n.colorBtn.active {\n  min-width: 100px;\n  padding: 0 10px;\n  font-weight: bold;\n}\n\n/* SECTION - 1 */\n.cardContnet {\n  display: flex;\n  gap: 24px;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n.cardContnet img {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n  object-fit: cover;\n}\n\n/* right */\n.rightCard {\n  flex: 2;\n  display: flex;\n  flex-direction: column;\n  margin-top: auto;\n  gap: 24px;\n}\n\n.box {\n  position: relative;\n}\n\n/* left  */\n.leftCard {\n  flex: 1;\n  position: relative;\n}\n\n.top {\n  display: flex;\n  gap: 24px;\n}\n\n.bottom {\n  display: flex;\n  gap: 24px;\n}\n\n/* //// */\n\n/* SECTION - 2 */\n\n.flex {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 2rem;\n  align-items: center;\n  justify-content: center;\n  margin-top: 3rem;\n}\n\n.flex .left {\n  flex: 1 1 45%;\n  max-width: 500px;\n}\n\n.flex .left img {\n  width: 100%;\n  height: auto;\n  border-radius: 12px;\n}\n\n.flex .right {\n  flex: 1 1 45%;\n}\n\n/* SECTION - 3 */\n.right {\n  flex: 1;\n  margin-top: auto;\n  position: relative;\n  height: -webkit-fill-available;\n}\n\n.middle {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n  margin-top: auto;\n  height: -webkit-fill-available;\n}\n\n.left {\n  flex: 1;\n  margin-top: auto;\n  position: relative;\n  height: -webkit-fill-available;\n}\n\n.topRight {\n  position: relative;\n}\n\n.topLeft {\n  position: relative;\n}\n\n/* /// */\n.overlay {\n  position: absolute;\n  bottom: 16px;\n  left: 16px;\n  color: white;\n  font-size: 16px;\n  z-index: 2;\n}\n\n.plus {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  width: 32px;\n  height: 32px;\n  background-color: #000;\n  color: white;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  font-weight: bold;\n  cursor: pointer;\n  z-index: 3;\n}\n\n/* section 4 */\n.contant {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  gap: 40px;\n}\n\n.contant .text {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 40px;\n}\n\n.contant .text button {\n  width: max-content;\n}\n\n.contant .img img {\n  width: 100%;\n  border-radius: 10px;\n}\n\n.dowBtn {\n  padding: 9pt 9pt 9pt 24px;\n  border-radius: 3px;\n  align-items: center;\n  box-sizing: content-box;\n  cursor: pointer;\n  display: inline-flex !important;\n  min-height: 24px;\n  border: none;\n  color: #fff;\n  background-color: #000;\n  gap: 20px;\n  align-items: center;\n  transition: all 0.3s;\n}\n\n.dowBtn span {\n  font-size: 1pc;\n  line-height: 24px;\n  font-weight: 700;\n}\n\n.dowBtn:hover {\n  background: #48484a;\n}\n\n/* /// */\n\n/* /// */\n.tabWrapper {\n  margin-top: 40px;\n  margin-bottom: 40px;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  overflow: hidden;\n  font-family: sans-serif;\n  background-color: #fff;\n  height: 100%;\n}\n\n.dropdownOpen {\n  overflow: visible !important;\n  /* активируется, когда открыт дропдаун */\n}\n\n.tabButtons {\n  display: flex;\n  background: #f3f4f6;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.tabBtn {\n  flex: 1;\n  padding: 12px 16px;\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  font-weight: 500;\n  color: #000;\n  font-weight: 600;\n  transition: all 0.2s;\n  white-space: nowrap;\n  background-color: #fff;\n}\n\n.active {\n  background-color: #000;\n  color: #fff;\n}\n\n.tabContent {\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.tabRow {\n  display: flex;\n  border-top: 1px solid #e5e7eb;\n  padding-top: 8px;\n  align-items: center;\n}\n\n.tabRow:first-child {\n  border-top: none;\n}\n\n.tabCell {\n  flex: 1;\n  padding: 4px 8px;\n  font-size: 14px;\n}\n\n.label {\n  font-weight: bold;\n  flex: 1.5;\n  color: #595959;\n}\n\n/* // */\n.dropdownWrapper {\n  display: none;\n  position: relative;\n}\n\n.dropdownHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #000000;\n  color: #fff;\n  padding: 12px 16px;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  width: 100%;\n  font-weight: 600;\n  cursor: pointer;\n  position: relative;\n}\n\n.dropdownList {\n  height: 0;\n  z-index: 10;\n  position: absolute;\n  top: calc(100% + 4px);\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  border: 1px solid #d6d6d6;\n  border-radius: 0px;\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  opacity: 0;\n  width: 100%;\n}\n\n.dropdownListActive {\n  opacity: 1;\n  height: max-content;\n  background-color: #fff;\n  position: relative;\n  z-index: 1;\n  display: block;\n}\n\n.dropdownItem {\n  display: block;\n  opacity: 1;\n  width: 100%;\n  padding: 12px 16px;\n  text-align: left;\n  background: #fff;\n  border: none;\n  font-size: 16px;\n  color: #000;\n  cursor: pointer;\n  -webkit-transition: background 0.2s;\n  -o-transition: background 0.2s;\n  transition: background 0.2s;\n}\n\n.dropdownItem:hover {\n  background: #f3f4f6;\n}\n\n.bgSection {\n  margin-top: 50px;\n  position: relative;\n  width: 100%;\n  height: 700px;\n  background-size: cover;\n  background-position: center;\n  background-repeat: no-repeat;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.bgSection .title {\n  color: white;\n  font-size: 2.5rem;\n  font-weight: 600;\n  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.7);\n}\n\n/* section 0 */\n\n.cardContnetBox {\n  display: flex;\n  gap: 24px;\n  margin-top: 40px;\n  align-items: stretch;\n  /* Чтобы блоки по высоте были равны */\n}\n\n/* Левая и правая колонки — примерно по 2 части ширины */\n.leftBox,\n.rightBox {\n  flex: 2 1 0;\n  /* grow=2, shrink=1, basis=0 */\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n/* Средняя колонка — 1 часть */\n.middleBox {\n  flex: 1 1 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Обёртки для фото с фиксированной высотой и соотношением сторон */\n.imgBox,\n.middleImgBox {\n  width: 100%;\n  aspect-ratio: 1 / 1;\n  /* квадрат */\n  border-radius: 8px;\n  overflow: hidden;\n  flex-shrink: 0;\n  /* не сжимать контейнер */\n}\n\n.middleImgBox {\n  height: 100%;\n  /* чтоб занимал всю высоту middleBox */\n}\n\n/* Само фото подстраивается под контейнер */\n.imgBox img,\n.middleImgBox img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* Заголовки */\n.titleBox {\n  background-color: #000;\n  padding: 24px;\n  border-radius: 8px;\n  font-size: 20px;\n  font-weight: bold;\n  color: #fff;\n  flex-shrink: 0;\n  /* не сжимать */\n}\n\n/* Для адаптива на мобилках */\n@media screen and (max-width: 990px) {\n  .tabButtons {\n    display: none;\n  }\n\n  .dropdownWrapper {\n    display: inline;\n  }\n\n  .tabContentActive {\n    min-height: 300px;\n  }\n}\n\n@media screen and (max-width: 900px) {\n  .slideContent h1 {\n    width: 100%;\n    font-size: 2pc;\n    line-height: 2.5pc;\n  }\n}\n\n@media screen and (max-width: 860px) {\n  .cardContnet {\n    flex-direction: column;\n  }\n\n  .flex2 {\n    flex-direction: column;\n  }\n\n  .flex3 {\n    flex-direction: column;\n  }\n}\n\n@media (max-width: 768px) {\n  .flex {\n    flex-direction: column;\n  }\n\n  .left,\n  .right {\n    flex: 1 1 100%;\n  }\n\n  .right h2 {\n    font-size: 1.5rem;\n  }\n\n  .right p {\n    font-size: 0.95rem;\n  }\n\n  .flex11 {\n    flex-direction: column;\n  }\n}\n\n@media screen and (max-width: 700px) {\n  .tabWrapper {\n    margin-top: 20px;\n  }\n\n  .contant .text {\n    gap: 10px;\n  }\n\n  .innerSele,\n  .innerNew {\n    padding: 7pt;\n    font-size: 14px;\n  }\n\n  .section {\n    padding-top: 30px;\n    padding-bottom: 0px;\n  }\n\n  .section h2 {\n    font-size: 20px;\n    margin-bottom: 30px;\n  }\n\n  .section p {\n    margin-bottom: 10px;\n  }\n\n  .section .colorTitle {\n    font-size: 30px;\n  }\n}\n\n/* Features Grid Styles for POER */\n.featuresGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 30px;\n  margin-top: 40px;\n}\n\n.featureCard {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 30px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition:\n    transform 0.3s ease,\n    box-shadow 0.3s ease;\n}\n\n.featureCard:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n\n.featureCard h3 {\n  color: #d7000f;\n  font-size: 20px;\n  font-weight: 600;\n  margin-bottom: 15px;\n}\n\n.featureCard p {\n  color: #666;\n  font-size: 16px;\n  line-height: 1.6;\n  margin: 0;\n}\n\n.subtitle {\n  font-size: 24px;\n  font-weight: 400;\n  margin-bottom: 20px;\n  opacity: 0.9;\n}\n\n/* Responsive adjustments for features grid */\n@media (max-width: 768px) {\n  .featuresGrid {\n    grid-template-columns: 1fr;\n    gap: 20px;\n    margin-top: 30px;\n  }\n\n  .featureCard {\n    padding: 20px;\n  }\n\n  .featureCard h3 {\n    font-size: 18px;\n  }\n\n  .featureCard p {\n    font-size: 14px;\n  }\n\n  .subtitle {\n    font-size: 20px;\n  }\n}\n\n@media screen and (max-width: 460px) {\n  .top {\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n  .bottom {\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n}\n", ".overlay {\n  position: fixed;\n  inset: 0;\n  background: rgba(0, 0, 0, 0.7);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 1rem;\n}\n\n.modal {\n  background: #fff;\n  border-radius: 12px;\n  max-width: 960px;\n  width: 100%;\n  padding: 2rem;\n  position: relative;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\n  max-height: 90vh;\n  overflow-y: auto;\n  color: #000;\n}\n\n.flex {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  gap: 20px;\n}\n.close {\n  background: #000;\n  color: #fff;\n  font-size: 18px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10px;\n}\n\n.title {\n  font-size: 1.5rem;\n  font-weight: bold;\n  margin-bottom: 1.5rem;\n  color: #111;\n}\n\n.desc p {\n  margin-bottom: 1rem;\n  line-height: 1.6;\n  font-size: 1rem;\n  color: #333;\n}\n\n/* Адаптивность */\n@media (max-width: 768px) {\n  .modal {\n    padding: 1rem;\n  }\n\n  .title {\n    font-size: 1.25rem;\n  }\n\n  .desc p {\n    font-size: 0.95rem;\n  }\n\n  .close {\n    width: 28px;\n    height: 28px;\n    font-size: 16px;\n  }\n}\n", ".btn {\n  font-size: 16px;\n  line-height: 1.2;\n  color: #636366;\n  cursor: pointer;\n  flex-shrink: 0;\n  padding: 0 12px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n  border-bottom: 4px solid transparent;\n  text-transform: uppercase;\n  height: 52px;\n  min-width: 100px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  transition: all 0.3s ease;\n}\n\n.active {\n  color: #000;\n  border-bottom: 4px solid #000;\n}\n\n.activeBar {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background-color: #d1d1d6;\n  z-index: 0;\n}\n@media screen and (max-width: 900px) {\n  .btn {\n    font-size: 14px;\n    height: 44px;\n    min-width: 88px;\n    padding: 0 10px;\n  }\n}\n\n@media screen and (max-width: 580px) {\n  .btn {\n    font-size: 12px;\n    height: 42px;\n    min-width: 76px;\n    padding: 0 8px;\n  }\n}\n", ".date {\n  font-size: 0.9rem;\n  color: #888;\n  margin-bottom: 20px;\n}\n\n.imageWrapper {\n  width: 100%;\n  overflow: hidden;\n  border-radius: 12px;\n}\n\n.imageWrapper img {\n  width: 100%;\n  height: auto;\n  border-radius: 12px;\n  -o-object-fit: cover;\n  object-fit: cover;\n  -webkit-transition: -webkit-transform 0.3s ease;\n  transition: -webkit-transform 0.3s ease;\n  -o-transition: transform 0.3s ease;\n  transition: transform 0.3s ease;\n  transition:\n    transform 0.3s ease,\n    -webkit-transform 0.3s ease;\n}\n\n.imageWrapper img:hover {\n  -webkit-transform: scale(1.02);\n  -ms-transform: scale(1.02);\n  transform: scale(1.02);\n}\n\n.subTitle {\n  font-size: 1.2rem;\n  font-weight: 500;\n  line-height: 1.6;\n  margin: 20px 0px;\n}\n\n.text {\n  font-size: 1.05rem;\n  line-height: 1.7;\n  color: #444;\n  white-space: pre-line;\n  margin-bottom: 20px;\n}\n\n/* Анимация появления */\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(12px);\n    transform: translateY(12px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(12px);\n    transform: translateY(12px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n/* Адаптив */\n@media (max-width: 768px) {\n  .title {\n    font-size: 1.5rem;\n  }\n\n  .subTitle,\n  .text {\n    font-size: 1rem;\n  }\n\n  .content {\n    padding: 20px 16px;\n  }\n}\n"], "names": [], "sourceRoot": ""}